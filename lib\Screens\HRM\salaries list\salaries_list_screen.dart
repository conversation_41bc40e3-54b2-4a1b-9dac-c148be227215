import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/models/hrm_models.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:intl/intl.dart';
import 'model/pay_salary_model.dart';
import 'provider/salary_provider.dart';
import 'pay_salary_screen.dart';

class SalariesListScreen extends ConsumerStatefulWidget {
  const SalariesListScreen({super.key});

  static const String route = '/hrm/salaries-list';

  @override
  ConsumerState<SalariesListScreen> createState() => _SalariesListScreenState();
}

class _SalariesListScreenState extends ConsumerState<SalariesListScreen> {
  String searchQuery = '';

  @override
  Widget build(BuildContext context) {
    final salariesAsyncValue = ref.watch(paidSalaryListProvider);

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('قائمة الرواتب'),
        backgroundColor: kMainColor,
        centerTitle: true,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        titleTextStyle: const TextStyle(
            color: Colors.white, fontSize: 20, fontWeight: FontWeight.bold),
      ),
      body: salariesAsyncValue.when(
        data: (salaries) {
          // تطبيق البحث
          List<PaySalaryModel> filteredSalaries = salaries.where((salary) {
            return searchQuery.isEmpty ||
                salary.employeeName
                    .toLowerCase()
                    .contains(searchQuery.toLowerCase());
          }).toList();

          return Column(
            children: [
              // شريط البحث
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    // حقل البحث
                    TextField(
                      decoration: InputDecoration(
                        hintText: 'بحث باسم الموظف',
                        prefixIcon: const Icon(Icons.search),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                      onChanged: (value) {
                        setState(() {
                          searchQuery = value;
                        });
                      },
                    ),
                    const SizedBox(height: 16),

                    // زر إضافة راتب
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        ElevatedButton.icon(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: kMainColor,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 8),
                          ),
                          onPressed: () {
                            _fetchEmployeesAndShowPayDialog(context);
                          },
                          icon: const Icon(Icons.add,
                              color: Colors.white, size: 18),
                          label: const Text('دفع راتب',
                              style: TextStyle(color: Colors.white)),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // قائمة الرواتب
              Expanded(
                child: filteredSalaries.isEmpty
                    ? const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.money_off, size: 80, color: Colors.grey),
                            SizedBox(height: 16),
                            Text(
                              'لا توجد رواتب مدفوعة',
                              style: TextStyle(
                                  fontSize: 18, fontWeight: FontWeight.bold),
                            ),
                            SizedBox(height: 8),
                            Text(
                              'قم بدفع رواتب الموظفين',
                              style: TextStyle(color: Colors.grey),
                            ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        itemCount: filteredSalaries.length,
                        itemBuilder: (context, index) {
                          final salary = filteredSalaries[index];
                          return Card(
                            margin: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 8),
                            elevation: 2,
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12)),
                            child: ListTile(
                              title: Text(
                                salary.employeeName,
                                style: const TextStyle(
                                    fontWeight: FontWeight.bold),
                              ),
                              subtitle: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('الشهر: ${salary.month}-${salary.year}'),
                                  Text('الراتب الأساسي: ${salary.netSalary}'),
                                  Text('المبلغ المدفوع: ${salary.paySalary}'),
                                  Text(
                                      'تاريخ الدفع: ${DateFormat.yMd().format(salary.payingDate)}'),
                                ],
                              ),
                              trailing: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  IconButton(
                                    icon: const Icon(Icons.edit,
                                        color: Colors.blue),
                                    onPressed: () {
                                      _fetchEmployeesAndShowEditDialog(
                                          context, salary);
                                    },
                                  ),
                                  IconButton(
                                    icon: const Icon(Icons.delete,
                                        color: Colors.red),
                                    onPressed: () {
                                      _deleteSalary(salary);
                                    },
                                  ),
                                ],
                              ),
                              isThreeLine: true,
                            ),
                          );
                        },
                      ),
              ),
            ],
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Text('حدث خطأ: $error',
              style: const TextStyle(color: Colors.red)),
        ),
      ),
    );
  }

  void _fetchEmployeesAndShowPayDialog(BuildContext context) {
    // استخدام قائمة موظفين وهمية للتجربة
    final employees = [
      EmployeeModel(
        id: '1',
        name: 'موظف 1',
        email: '<EMAIL>',
        phone: '123456789',
        address: 'العنوان',
        designation: 'المسمى الوظيفي',
        department: 'القسم',
        salary: 5000,
        joiningDate: DateTime.now(),
        imageUrl: '',
        isActive: true,
      ),
      EmployeeModel(
        id: '2',
        name: 'موظف 2',
        email: '<EMAIL>',
        phone: '987654321',
        address: 'العنوان',
        designation: 'المسمى الوظيفي',
        department: 'القسم',
        salary: 6000,
        joiningDate: DateTime.now(),
        imageUrl: '',
        isActive: true,
      ),
    ];

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PaySalaryScreen(
          listOfEmployees: employees,
        ),
      ),
    ).then((_) => ref.invalidate(paidSalaryListProvider));
  }

  void _fetchEmployeesAndShowEditDialog(
      BuildContext context, PaySalaryModel salary) {
    // استخدام قائمة موظفين وهمية للتجربة
    final employees = [
      EmployeeModel(
        id: '1',
        name: 'موظف 1',
        email: '<EMAIL>',
        phone: '123456789',
        address: 'العنوان',
        designation: 'المسمى الوظيفي',
        department: 'القسم',
        salary: 5000,
        joiningDate: DateTime.now(),
        imageUrl: '',
        isActive: true,
      ),
      EmployeeModel(
        id: '2',
        name: 'موظف 2',
        email: '<EMAIL>',
        phone: '987654321',
        address: 'العنوان',
        designation: 'المسمى الوظيفي',
        department: 'القسم',
        salary: 6000,
        joiningDate: DateTime.now(),
        imageUrl: '',
        isActive: true,
      ),
    ];

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PaySalaryScreen(
          listOfEmployees: employees,
          payedSalary: salary,
        ),
      ),
    ).then((_) => ref.invalidate(paidSalaryListProvider));
  }

  void _deleteSalary(PaySalaryModel salary) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذا الراتب؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);

              try {
                final notifier = ref.read(salaryNotifierProvider.notifier);
                await notifier.deleteSalary(salary.id);
                toast('تم حذف الراتب بنجاح');
              } catch (e) {
                toast('حدث خطأ أثناء الحذف: $e');
              }
            },
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}
