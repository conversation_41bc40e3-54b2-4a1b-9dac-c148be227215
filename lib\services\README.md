# مدير مستمعي Firebase

هذا الملف يشرح كيفية استخدام مدير مستمعي Firebase الجديد لمنع مشكلة الاستماع المزدوج التي تسبب خروج مفاجئ في التطبيق.

## المشكلة

كان التطبيق يعاني من مشكلة خروج مفاجئ (كراش) بسبب خطأ `AssertionError` عند استدعاء `listen()` مرتين لنفس `QuerySpec` في Firebase Realtime Database. هذا يحدث عندما يتم إنشاء مستمعين لنفس المسار في Firebase، مما يؤدي إلى تعارض وخطأ.

## الحل

تم إنشاء نظام مركزي لإدارة المستمعين في Firebase يمنع الاستماع المزدوج ويدير دورة حياة المستمعين بشكل صحيح. يتكون الحل من:

1. **مدير مستمعي Firebase**: فئة مركزية تدير جميع المستمعين وتمنع الاستماع المزدوج.
2. **مساعد مستمعي Firebase**: مزيج (mixin) يمكن استخدامه في الشاشات لإدارة المستمعين بشكل آمن.
3. **تعديل خدمة Firebase Database**: تم تعديل الخدمة لاستخدام مدير المستمعين الجديد.

## كيفية الاستخدام

### 1. استخدام مساعد مستمعي Firebase في الشاشات

```dart
// استيراد المساعد
import 'package:mobile_pos/utils/firebase_listener_helper.dart';

// استخدام المساعد في الشاشة
class MyScreenState extends State<MyScreen> with FirebaseListenerHelper {
  
  @override
  void initState() {
    super.initState();
    
    // الاستماع للتغييرات في مسار معين
    listenToPath('path/to/data').listen((event) {
      // معالجة الحدث
    });
  }
  
  // لا حاجة لإلغاء المستمعين يدويًا، سيتم إلغاؤها تلقائيًا عند التخلص من الشاشة
}
```

### 2. استخدام مساعد مستمعي Firebase مع Riverpod

```dart
// استيراد المساعد
import 'package:mobile_pos/utils/firebase_listener_helper.dart';

// استخدام المساعد في الشاشة
class MyScreenState extends ConsumerState<MyScreen> with FirebaseListenerConsumerHelper {
  
  @override
  void initState() {
    super.initState();
    
    // الاستماع للتغييرات في مسار معين
    listenToPath('path/to/data').listen((event) {
      // معالجة الحدث
    });
  }
  
  // لا حاجة لإلغاء المستمعين يدويًا، سيتم إلغاؤها تلقائيًا عند التخلص من الشاشة
}
```

### 3. استخدام مدير المستمعين مباشرة

```dart
import 'package:mobile_pos/services/firebase_listener_manager.dart';

// الاستماع للتغييرات في مسار معين
Stream<DatabaseEvent> stream = FirebaseListenerManager().listenToPath(
  'path/to/data',
  ownerId: 'my_screen_id', // معرف فريد للشاشة أو الكائن
  eventType: 'value', // نوع الحدث (value, child_added, child_changed, child_removed, child_moved)
  keepSynced: true, // تعيين المزامنة
);

// الاستماع للأحداث
stream.listen((event) {
  // معالجة الحدث
});

// إلغاء المستمع عند الانتهاء
FirebaseListenerManager().cancelListener(
  'path/to/data',
  ownerId: 'my_screen_id',
);

// إلغاء جميع المستمعين لمالك معين
FirebaseListenerManager().cancelListenersByOwner('my_screen_id');
```

### 4. استخدام خدمة Firebase Database المحسنة

```dart
import 'package:mobile_pos/services/firebase_database_service.dart';

// الاستماع للتغييرات في مسار معين
Stream<DatabaseEvent> stream = FirebaseDatabaseService.listenToPath(
  'path/to/data',
  ownerId: 'my_screen_id', // معرف فريد للشاشة أو الكائن
  eventType: 'value', // نوع الحدث
  keepSynced: true, // تعيين المزامنة
);

// الاستماع للأحداث
stream.listen((event) {
  // معالجة الحدث
});

// إلغاء المستمع عند الانتهاء
FirebaseDatabaseService.cancelListener(
  'path/to/data',
  ownerId: 'my_screen_id',
);
```

## اختبار مدير المستمعين

يمكن استخدام أداة اختبار مدير المستمعين للتأكد من عدم وجود مشاكل:

```dart
import 'package:mobile_pos/utils/firebase_listener_tester.dart';

// عرض شاشة اختبار مدير المستمعين
Navigator.of(context).push(
  MaterialPageRoute(
    builder: (context) => const FirebaseListenerTester(),
  ),
);
```

## ملاحظات هامة

1. **معرف المالك**: يجب استخدام معرف فريد لكل شاشة أو كائن يستخدم المستمعين.
2. **إلغاء المستمعين**: عند استخدام المساعد، سيتم إلغاء المستمعين تلقائيًا عند التخلص من الشاشة.
3. **المزامنة**: يمكن تعيين المزامنة لتحسين الأداء وتقليل استهلاك البيانات.
4. **أنواع الأحداث**: يمكن الاستماع لأنواع مختلفة من الأحداث (value, child_added, child_changed, child_removed, child_moved).
