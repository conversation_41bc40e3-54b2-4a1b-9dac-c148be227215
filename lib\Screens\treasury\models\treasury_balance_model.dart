class TreasuryBalanceModel {
  late String currentBalance;
  late String totalIncome;
  late String totalExpense;
  late String lastUpdated;
  late String lastTransactionId;

  TreasuryBalanceModel({
    required this.currentBalance,
    required this.totalIncome,
    required this.totalExpense,
    required this.lastUpdated,
    required this.lastTransactionId,
  });

  TreasuryBalanceModel.fromJson(Map<dynamic, dynamic> json) {
    currentBalance = json['currentBalance']?.toString() ?? '0';
    totalIncome = json['totalIncome']?.toString() ?? '0';
    totalExpense = json['totalExpense']?.toString() ?? '0';
    lastUpdated = json['lastUpdated']?.toString() ?? '';
    lastTransactionId = json['lastTransactionId']?.toString() ?? '';
  }

  Map<dynamic, dynamic> toJson() => <dynamic, dynamic>{
        'currentBalance': currentBalance,
        'totalIncome': totalIncome,
        'totalExpense': totalExpense,
        'lastUpdated': lastUpdated,
        'lastTransactionId': lastTransactionId,
      };

  // Helper methods
  double get currentBalanceAsDouble => double.tryParse(currentBalance) ?? 0.0;
  double get totalIncomeAsDouble => double.tryParse(totalIncome) ?? 0.0;
  double get totalExpenseAsDouble => double.tryParse(totalExpense) ?? 0.0;
  
  DateTime get lastUpdatedAsDateTime => DateTime.tryParse(lastUpdated) ?? DateTime.now();
  
  // Calculate net profit/loss
  double get netAmount => totalIncomeAsDouble - totalExpenseAsDouble;
  bool get isProfit => netAmount >= 0;
  
  // Create empty balance
  static TreasuryBalanceModel empty() {
    return TreasuryBalanceModel(
      currentBalance: '0',
      totalIncome: '0',
      totalExpense: '0',
      lastUpdated: DateTime.now().toIso8601String(),
      lastTransactionId: '',
    );
  }
}
