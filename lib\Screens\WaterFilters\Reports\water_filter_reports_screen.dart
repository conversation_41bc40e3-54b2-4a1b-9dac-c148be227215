import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/Screens/WaterFilters/Maintenance/maintenance_reports_screen.dart';
import 'package:mobile_pos/Screens/WaterFilters/Reports/customers_report_screen.dart';
import 'package:mobile_pos/Screens/WaterFilters/Reports/systems_report_screen.dart';
import 'package:mobile_pos/Screens/WaterFilters/Reports/installments_report_screen.dart';
import 'package:mobile_pos/Screens/WaterFilters/Reports/sales_report_screen.dart';
import 'package:mobile_pos/Screens/WaterFilters/Reports/financial_report_screen.dart';
import 'package:mobile_pos/Screens/WaterFilters/Reports/products_report_screen.dart';
import 'package:mobile_pos/Screens/WaterFilters/Reports/performance_report_screen.dart';
import 'package:mobile_pos/Screens/WaterFilters/Expenses/expenses_statistics_screen.dart';
import 'package:nb_utils/nb_utils.dart';

class WaterFilterReportsScreen extends StatefulWidget {
  const WaterFilterReportsScreen({super.key});

  @override
  State<WaterFilterReportsScreen> createState() =>
      _WaterFilterReportsScreenState();
}

class _WaterFilterReportsScreenState extends State<WaterFilterReportsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        title: Text(
          'تقارير فلاتر المياه',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: Container(
        alignment: Alignment.topCenter,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(30),
            topLeft: Radius.circular(30),
          ),
        ),
        child: SingleChildScrollView(
          child: Column(
            children: [
              const SizedBox(height: 20),
              WaterFilterReportCard(
                pressed: () {
                  const CustomersReportScreen().launch(context);
                },
                icon: Icons.people,
                title: 'تقرير العملاء',
              ),
              WaterFilterReportCard(
                pressed: () {
                  const SystemsReportScreen().launch(context);
                },
                icon: Icons.settings,
                title: 'تقرير الأنظمة المركبة',
              ),
              WaterFilterReportCard(
                pressed: () {
                  const InstallmentsReportScreen().launch(context);
                },
                icon: Icons.payment,
                title: 'تقرير الأقساط',
              ),
              WaterFilterReportCard(
                pressed: () {
                  const MaintenanceReportsScreen().launch(context);
                },
                icon: Icons.build,
                title: 'تقرير الصيانة',
              ),
              WaterFilterReportCard(
                pressed: () {
                  const SalesReportScreen().launch(context);
                },
                icon: Icons.shopping_cart,
                title: 'تقرير المبيعات',
              ),
              WaterFilterReportCard(
                pressed: () {
                  const FinancialReportScreen().launch(context);
                },
                icon: Icons.account_balance,
                title: 'التقرير المالي الشامل',
              ),
              WaterFilterReportCard(
                pressed: () {
                  const ProductsReportScreen().launch(context);
                },
                icon: Icons.inventory,
                title: 'تقرير المنتجات',
              ),
              WaterFilterReportCard(
                pressed: () {
                  const PerformanceReportScreen().launch(context);
                },
                icon: Icons.trending_up,
                title: 'تقرير الأداء',
              ),
              WaterFilterReportCard(
                pressed: () {
                  const ExpensesStatisticsScreen().launch(context);
                },
                icon: Icons.receipt_long,
                title: 'تقرير المصروفات',
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// ignore: must_be_immutable
class WaterFilterReportCard extends StatelessWidget {
  WaterFilterReportCard({
    super.key,
    required this.pressed,
    required this.icon,
    required this.title,
  });

  // ignore: prefer_typing_uninitialized_variables
  var pressed;
  IconData icon;
  String title;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 10.0, right: 10.0, bottom: 10.0),
      child: Container(
        width: context.width(),
        height: 80,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10.0),
          border: Border.all(color: kBorderColorTextField, width: 1),
        ),
        child: ListTile(
          onTap: pressed,
          leading: Container(
            height: 50.0,
            width: 50.0,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8.0),
              color: kMainColor.withOpacity(0.1),
            ),
            child: Center(
              child: Icon(
                icon,
                size: 25.0,
                color: kMainColor,
              ),
            ),
          ),
          title: Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 16.0,
              color: kTitleColor,
              fontWeight: FontWeight.w500,
            ),
          ),
          trailing: const Icon(
            Icons.arrow_forward_ios,
            color: kGreyTextColor,
          ),
        ),
      ),
    );
  }
}
