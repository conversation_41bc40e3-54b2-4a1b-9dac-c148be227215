import 'package:flutter/material.dart';
import 'package:mobile_pos/services/firebase_database_service.dart';
import 'package:mobile_pos/services/local_notification_service.dart';

class InventoryNotificationService {
  // فحص المخزون المنخفض وإرسال إشعارات
  static Future<void> checkLowInventory() async {
    try {
      // الحصول على مرجع المنتجات
      final userId = FirebaseDatabaseService.getCurrentUserId();
      final ref = FirebaseDatabaseService.getReference("$userId/Products",
          keepSynced: true);

      // الحصول على بيانات المنتجات
      final snapshot = await ref.get();

      if (snapshot.exists) {
        // قائمة المنتجات ذات المخزون المنخفض
        final List<Map<String, dynamic>> lowStockProducts = [];

        // فحص كل منتج
        for (var child in snapshot.children) {
          final data = child.value as Map<dynamic, dynamic>;
          final productName = data['productName'] as String? ?? 'منتج';
          final productStock =
              int.tryParse(data['productStock'].toString()) ?? 0;
          final productCode = data['productCode'] as String? ?? '';

          // التحقق من المخزون المنخفض (أقل من 10 وحدات)
          if (productStock < 10) {
            lowStockProducts.add({
              'productName': productName,
              'productStock': productStock,
              'productCode': productCode,
            });
          }
        }

        // إرسال إشعارات للمنتجات ذات المخزون المنخفض
        for (var product in lowStockProducts) {
          await LocalNotificationService.sendLocalNotification(
            title: 'تنبيه: مخزون منخفض',
            body:
                'المنتج "${product['productName']}" متبقي منه ${product['productStock']} وحدة فقط',
            data: {
              'type': 'low_inventory',
              'productCode': product['productCode'],
              'screen': 'inventory',
            },
          );
        }

        debugPrint('تم فحص المخزون وإرسال ${lowStockProducts.length} إشعار');
      }
    } catch (e) {
      debugPrint('خطأ في فحص المخزون: $e');
    }
  }
}
