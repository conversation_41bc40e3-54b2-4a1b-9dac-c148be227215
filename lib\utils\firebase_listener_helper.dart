import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mobile_pos/services/firebase_listener_manager.dart';

/// مساعد لإدارة مستمعي Firebase في الشاشات المختلفة
/// يوفر واجهة سهلة لإدارة المستمعين في دورة حياة الشاشة
mixin FirebaseListenerHelper<T extends StatefulWidget> on State<T> {
  // معرف فريد للشاشة
  String get screenId => '${widget.runtimeType}_${identityHashCode(this)}';

  @override
  void initState() {
    super.initState();
    debugPrint('تهيئة مساعد مستمعي Firebase للشاشة: $screenId');
  }

  @override
  void dispose() {
    // إلغاء جميع المستمعين المرتبطة بهذه الشاشة
    _cancelAllListeners();
    super.dispose();
  }

  /// الاستماع للتغييرات في مسار معين
  Stream<R> listenToPath<R>(
    String path, {
    R Function(dynamic)? converter,
    String? eventType,
    String? queryType,
  }) {
    // استخدام مدير المستمعين للاستماع للمسار
    final stream = FirebaseListenerManager().listenToPath(
      path,
      ownerId: screenId,
      eventType: eventType,
      queryType: queryType,
    );

    // إذا تم توفير محول، استخدمه لتحويل البيانات
    if (converter != null) {
      return stream.map((event) {
        try {
          final data = event.snapshot.value;
          return converter(data);
        } catch (e) {
          debugPrint('خطأ في تحويل البيانات للمسار $path: $e');
          rethrow;
        }
      });
    }

    // إرجاع التدفق كما هو إذا لم يتم توفير محول
    return stream as Stream<R>;
  }

  /// إلغاء الاستماع لمسار معين
  Future<void> cancelListener(String path, {String? eventType}) async {
    await FirebaseListenerManager().cancelListener(
      path,
      ownerId: screenId,
      eventType: eventType,
    );
  }

  /// إلغاء جميع المستمعين المرتبطة بهذه الشاشة
  Future<void> _cancelAllListeners() async {
    debugPrint('إلغاء جميع مستمعي Firebase للشاشة: $screenId');
    await FirebaseListenerManager().cancelListenersByOwner(screenId);
  }
}

/// مساعد لإدارة مستمعي Firebase في الشاشات التي تستخدم Riverpod
/// يمكن استخدامه مع ConsumerStatefulWidget
mixin FirebaseListenerConsumerHelper<T extends ConsumerStatefulWidget>
    on ConsumerState<T> {
  // معرف فريد للشاشة
  String get screenId => '${widget.runtimeType}_${identityHashCode(this)}';

  @override
  void initState() {
    super.initState();
    debugPrint('تهيئة مساعد مستمعي Firebase للشاشة: $screenId');
  }

  @override
  void dispose() {
    // إلغاء جميع المستمعين المرتبطة بهذه الشاشة
    _cancelAllListeners();
    super.dispose();
  }

  /// الاستماع للتغييرات في مسار معين
  Stream<R> listenToPath<R>(
    String path, {
    R Function(dynamic)? converter,
    String? eventType,
    String? queryType,
  }) {
    // استخدام مدير المستمعين للاستماع للمسار
    final stream = FirebaseListenerManager().listenToPath(
      path,
      ownerId: screenId,
      eventType: eventType,
      queryType: queryType,
    );

    // إذا تم توفير محول، استخدمه لتحويل البيانات
    if (converter != null) {
      return stream.map((event) {
        try {
          final data = event.snapshot.value;
          return converter(data);
        } catch (e) {
          debugPrint('خطأ في تحويل البيانات للمسار $path: $e');
          rethrow;
        }
      });
    }

    // إرجاع التدفق كما هو إذا لم يتم توفير محول
    return stream as Stream<R>;
  }

  /// إلغاء الاستماع لمسار معين
  Future<void> cancelListener(String path, {String? eventType}) async {
    await FirebaseListenerManager().cancelListener(
      path,
      ownerId: screenId,
      eventType: eventType,
    );
  }

  /// إلغاء جميع المستمعين المرتبطة بهذه الشاشة
  Future<void> _cancelAllListeners() async {
    debugPrint('إلغاء جميع مستمعي Firebase للشاشة: $screenId');
    await FirebaseListenerManager().cancelListenersByOwner(screenId);
  }
}
