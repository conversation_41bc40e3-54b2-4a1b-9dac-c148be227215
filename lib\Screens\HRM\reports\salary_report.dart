import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:mobile_pos/Screens/HRM/salaries%20list/model/pay_salary_model.dart';
import 'package:mobile_pos/Screens/HRM/salaries%20list/provider/salary_provider.dart';
import 'package:mobile_pos/constant.dart';

class SalaryReport extends ConsumerStatefulWidget {
  const SalaryReport({super.key});

  @override
  ConsumerState<SalaryReport> createState() => _SalaryReportState();
}

class _SalaryReportState extends ConsumerState<SalaryReport> {
  String selectedMonth = 'الكل';
  String selectedYear = '2025';
  String selectedEmployee = 'الكل';
  List<String> employees = ['الكل'];

  final List<String> months = [
    'الكل',
    'يناير',
    'فبراير',
    'مارس',
    'أبريل',
    'مايو',
    'يونيو',
    'يوليو',
    'أغسطس',
    'سبتمبر',
    'أكتوبر',
    'نوفمبر',
    'ديسمبر'
  ];

  final List<String> years = [
    '2025',
    '2026',
    '2027',
    '2028',
    '2029',
    '2030',
    '2031',
    '2032',
    '2033',
    '2034',
    '2035'
  ];

  @override
  Widget build(BuildContext context) {
    final salariesAsyncValue = ref.watch(paidSalaryListProvider);

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('تقرير الرواتب'),
        backgroundColor: kMainColor,
        centerTitle: true,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        titleTextStyle: const TextStyle(
            color: Colors.white, fontSize: 20, fontWeight: FontWeight.bold),
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: () {
              salariesAsyncValue.whenData((salaries) {
                _printReport(salaries);
              });
            },
          ),
        ],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                // فلاتر
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'الشهر',
                          border: OutlineInputBorder(),
                        ),
                        value: selectedMonth,
                        items: months.map((month) {
                          return DropdownMenuItem<String>(
                            value: month,
                            child: Text(month),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            selectedMonth = value!;
                          });
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'السنة',
                          border: OutlineInputBorder(),
                        ),
                        value: selectedYear,
                        items: years.map((year) {
                          return DropdownMenuItem<String>(
                            value: year,
                            child: Text(year),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            selectedYear = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                salariesAsyncValue.when(
                  data: (salaries) {
                    // استخراج أسماء الموظفين الفريدة
                    if (employees.length <= 1) {
                      final uniqueEmployees =
                          salaries.map((e) => e.employeeName).toSet().toList();
                      employees = ['الكل', ...uniqueEmployees];
                    }

                    return DropdownButtonFormField<String>(
                      decoration: const InputDecoration(
                        labelText: 'الموظف',
                        border: OutlineInputBorder(),
                      ),
                      value: selectedEmployee,
                      items: employees.map((employee) {
                        return DropdownMenuItem<String>(
                          value: employee,
                          child: Text(employee),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          selectedEmployee = value!;
                        });
                      },
                    );
                  },
                  loading: () => const CircularProgressIndicator(),
                  error: (_, __) => const Text('خطأ في تحميل الموظفين'),
                ),
              ],
            ),
          ),
          Expanded(
            child: salariesAsyncValue.when(
              data: (salaries) {
                // تطبيق الفلاتر
                List<PaySalaryModel> filteredSalaries =
                    salaries.where((salary) {
                  final matchesMonth =
                      selectedMonth == 'الكل' || salary.month == selectedMonth;
                  final matchesYear =
                      selectedYear == 'الكل' || salary.year == selectedYear;
                  final matchesEmployee = selectedEmployee == 'الكل' ||
                      salary.employeeName == selectedEmployee;

                  return matchesMonth && matchesYear && matchesEmployee;
                }).toList();

                if (filteredSalaries.isEmpty) {
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.money_off, size: 64, color: Colors.grey),
                        SizedBox(height: 16),
                        Text(
                          'لا توجد رواتب مطابقة للبحث',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  );
                }

                // حساب إجمالي الرواتب
                double totalSalaries = 0;
                for (var salary in filteredSalaries) {
                  totalSalaries += salary.paySalary;
                }

                return SingleChildScrollView(
                  child: Column(
                    children: [
                      // ملخص الرواتب
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Card(
                          elevation: 4,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'ملخص الرواتب',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 16),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    _buildSummaryItem(
                                      'عدد الموظفين',
                                      filteredSalaries
                                          .map((e) => e.employeeName)
                                          .toSet()
                                          .length
                                          .toString(),
                                      Icons.people,
                                      Colors.blue,
                                    ),
                                    _buildSummaryItem(
                                      'إجمالي الرواتب',
                                      totalSalaries.toStringAsFixed(2),
                                      Icons.attach_money,
                                      Colors.green,
                                    ),
                                    _buildSummaryItem(
                                      'متوسط الراتب',
                                      (totalSalaries / filteredSalaries.length)
                                          .toStringAsFixed(2),
                                      Icons.trending_up,
                                      Colors.orange,
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),

                      // رسم بياني للرواتب (تم إزالته بسبب عدم توفر المكتبة)
                      if (filteredSalaries.length > 1)
                        Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Card(
                            elevation: 4,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    'رسم بياني للرواتب',
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 16),
                                  SizedBox(
                                    height: 300,
                                    child: Center(
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          const Icon(
                                            Icons.bar_chart,
                                            size: 64,
                                            color: Colors.grey,
                                          ),
                                          const SizedBox(height: 16),
                                          const Text(
                                            'سيتم دعم الرسوم البيانية قريباً',
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.bold,
                                              color: Colors.grey,
                                            ),
                                          ),
                                          const SizedBox(height: 8),
                                          Text(
                                            'عدد الرواتب: ${filteredSalaries.length}',
                                            style: const TextStyle(
                                              color: Colors.grey,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),

                      // جدول الرواتب
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Card(
                          elevation: 4,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'تفاصيل الرواتب',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 16),
                                SingleChildScrollView(
                                  scrollDirection: Axis.horizontal,
                                  child: DataTable(
                                    columns: const [
                                      DataColumn(label: Text('الموظف')),
                                      DataColumn(label: Text('الشهر')),
                                      DataColumn(label: Text('السنة')),
                                      DataColumn(label: Text('الراتب الأساسي')),
                                      DataColumn(label: Text('المبلغ المدفوع')),
                                      DataColumn(label: Text('تاريخ الدفع')),
                                    ],
                                    rows: filteredSalaries.map((salary) {
                                      return DataRow(
                                        cells: [
                                          DataCell(Text(salary.employeeName)),
                                          DataCell(Text(salary.month)),
                                          DataCell(Text(salary.year)),
                                          DataCell(Text(
                                              salary.netSalary.toString())),
                                          DataCell(Text(
                                              salary.paySalary.toString())),
                                          DataCell(Text(DateFormat.yMd()
                                              .format(salary.payingDate))),
                                        ],
                                      );
                                    }).toList(),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.error_outline,
                        size: 60, color: Colors.red),
                    const SizedBox(height: 16),
                    const Text(
                      'حدث خطأ أثناء تحميل البيانات',
                      style: TextStyle(color: Colors.red, fontSize: 18),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '$error',
                      style: const TextStyle(color: Colors.grey, fontSize: 14),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        final _ = ref.refresh(paidSalaryListProvider);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: kMainColor,
                      ),
                      child: const Text('إعادة المحاولة'),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(
      String title, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(
          icon,
          color: color,
          size: 32,
        ),
        const SizedBox(height: 8),
        Text(
          title,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  void _printReport(List<PaySalaryModel> salaries) {
    // عرض رسالة للمستخدم
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('طباعة التقرير'),
        content: const Text(
            'سيتم دعم طباعة التقرير قريباً. يمكنك حالياً مشاهدة التقرير على الشاشة.'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
}
