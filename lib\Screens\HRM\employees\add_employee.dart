import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/models/hrm_models.dart';
import 'package:nb_utils/nb_utils.dart';
import '../Designation/provider/designation_provider.dart';
import 'provider/employee_provider.dart';

class AddEmployeeScreen extends ConsumerStatefulWidget {
  const AddEmployeeScreen(
      {super.key, required this.listOfEmployees, this.employeeModel});

  final List<EmployeeModel> listOfEmployees;
  final EmployeeModel? employeeModel;

  @override
  ConsumerState<AddEmployeeScreen> createState() => _AddEmployeeScreenState();
}

class _AddEmployeeScreenState extends ConsumerState<AddEmployeeScreen> {
  final TextEditingController nameController = TextEditingController();
  final TextEditingController phoneNumberController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController addressController = TextEditingController();
  final TextEditingController salaryController = TextEditingController();
  final TextEditingController departmentController = TextEditingController();
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  final List<String> genderList = ['ذكر', 'أنثى', 'آخر'];
  final List<String> typeList = ['دوام كامل', 'دوام جزئي', 'آخر'];

  String? selectedGender;
  String? selectedType;
  DesignationModel? selectedDesignation;
  bool isActive = true;

  DateTime joiningDate = DateTime.now();

  @override
  void initState() {
    super.initState();

    if (widget.employeeModel != null) {
      nameController.text = widget.employeeModel?.name ?? '';
      phoneNumberController.text = widget.employeeModel?.phone ?? '';
      emailController.text = widget.employeeModel?.email ?? '';
      addressController.text = widget.employeeModel?.address ?? '';
      salaryController.text = widget.employeeModel?.salary.toString() ?? '';
      departmentController.text = widget.employeeModel?.department ?? '';
      joiningDate = widget.employeeModel?.joiningDate ?? DateTime.now();
      isActive = widget.employeeModel?.isActive ?? true;
    }
  }

  @override
  void dispose() {
    nameController.dispose();
    phoneNumberController.dispose();
    emailController.dispose();
    addressController.dispose();
    salaryController.dispose();
    departmentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final designationsAsyncValue = ref.watch(designationListProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.employeeModel == null
            ? 'إضافة موظف جديد'
            : 'تعديل بيانات الموظف'),
        backgroundColor: kMainColor,
        centerTitle: true,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        titleTextStyle: const TextStyle(
            color: Colors.white, fontSize: 20, fontWeight: FontWeight.bold),
      ),
      body: designationsAsyncValue.when(
        data: (designations) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Form(
              key: formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // بيانات الموظف الأساسية
                  const Text(
                    'البيانات الأساسية',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),

                  // الاسم
                  TextFormField(
                    controller: nameController,
                    decoration: const InputDecoration(
                      labelText: 'اسم الموظف',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.person),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال اسم الموظف';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // رقم الهاتف والبريد الإلكتروني
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          controller: phoneNumberController,
                          decoration: const InputDecoration(
                            labelText: 'رقم الهاتف',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.phone),
                          ),
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'يرجى إدخال رقم الهاتف';
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: TextFormField(
                          controller: emailController,
                          decoration: const InputDecoration(
                            labelText: 'البريد الإلكتروني',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.email),
                          ),
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'يرجى إدخال البريد الإلكتروني';
                            }
                            return null;
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // العنوان
                  TextFormField(
                    controller: addressController,
                    decoration: const InputDecoration(
                      labelText: 'العنوان',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.location_on),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال العنوان';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 32),

                  // بيانات الوظيفة
                  const Text(
                    'بيانات الوظيفة',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),

                  // المسمى الوظيفي والقسم
                  Row(
                    children: [
                      Expanded(
                        child: DropdownButtonFormField<DesignationModel>(
                          decoration: const InputDecoration(
                            labelText: 'المسمى الوظيفي',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.work),
                          ),
                          hint: const Text('اختر المسمى الوظيفي'),
                          items: designations.map((designation) {
                            return DropdownMenuItem<DesignationModel>(
                              value: designation,
                              child: Text(designation.title),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              selectedDesignation = value;
                              departmentController.text =
                                  value?.department ?? '';
                            });
                          },
                          validator: (value) {
                            if (value == null) {
                              return 'يرجى اختيار المسمى الوظيفي';
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: TextFormField(
                          controller: departmentController,
                          decoration: const InputDecoration(
                            labelText: 'القسم',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.business),
                          ),
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'يرجى إدخال القسم';
                            }
                            return null;
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // الراتب ونوع التوظيف
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          controller: salaryController,
                          keyboardType: TextInputType.number,
                          decoration: const InputDecoration(
                            labelText: 'الراتب',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.attach_money),
                          ),
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'يرجى إدخال الراتب';
                            }
                            if (double.tryParse(value) == null) {
                              return 'يرجى إدخال رقم صحيح';
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          decoration: const InputDecoration(
                            labelText: 'نوع التوظيف',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.category),
                          ),
                          hint: const Text('اختر نوع التوظيف'),
                          items: typeList.map((type) {
                            return DropdownMenuItem<String>(
                              value: type,
                              child: Text(type),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              selectedType = value;
                            });
                          },
                          validator: (value) {
                            if (value == null) {
                              return 'يرجى اختيار نوع التوظيف';
                            }
                            return null;
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // الجنس وتاريخ التعيين
                  Row(
                    children: [
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          decoration: const InputDecoration(
                            labelText: 'الجنس',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.person_outline),
                          ),
                          hint: const Text('اختر الجنس'),
                          items: genderList.map((gender) {
                            return DropdownMenuItem<String>(
                              value: gender,
                              child: Text(gender),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              selectedGender = value;
                            });
                          },
                          validator: (value) {
                            if (value == null) {
                              return 'يرجى اختيار الجنس';
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: InkWell(
                          onTap: () async {
                            final DateTime? picked = await showDatePicker(
                              context: context,
                              initialDate: joiningDate,
                              firstDate: DateTime(2000),
                              lastDate: DateTime(2100),
                            );
                            if (picked != null && picked != joiningDate) {
                              setState(() {
                                joiningDate = picked;
                              });
                            }
                          },
                          child: InputDecorator(
                            decoration: const InputDecoration(
                              labelText: 'تاريخ التعيين',
                              border: OutlineInputBorder(),
                              prefixIcon: Icon(Icons.calendar_today),
                            ),
                            child: Text(
                              '${joiningDate.day}/${joiningDate.month}/${joiningDate.year}',
                              style: const TextStyle(fontSize: 16),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // حالة الموظف
                  SwitchListTile(
                    title: const Text('الموظف نشط',
                        style: TextStyle(fontSize: 16)),
                    value: isActive,
                    onChanged: (value) {
                      setState(() {
                        isActive = value;
                      });
                    },
                    activeColor: kMainColor,
                  ),
                  const SizedBox(height: 32),

                  // أزرار الحفظ والإلغاء
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 32, vertical: 12),
                        ),
                        child: const Text('إلغاء',
                            style: TextStyle(color: Colors.white)),
                      ),
                      const SizedBox(width: 16),
                      ElevatedButton(
                        onPressed: () {
                          _saveEmployee();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: kMainColor,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 32, vertical: 12),
                        ),
                        child: Text(
                          widget.employeeModel == null ? 'إضافة' : 'تحديث',
                          style: const TextStyle(color: Colors.white),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Text('حدث خطأ: $error',
              style: const TextStyle(color: Colors.red)),
        ),
      ),
    );
  }

  void _saveEmployee() async {
    if (formKey.currentState?.validate() ?? false) {
      if (selectedDesignation == null) {
        toast('يرجى اختيار المسمى الوظيفي');
        return;
      }

      if (selectedGender == null) {
        toast('يرجى اختيار الجنس');
        return;
      }

      if (selectedType == null) {
        toast('يرجى اختيار نوع التوظيف');
        return;
      }

      // لا نحتاج إلى حفظ نسخة من السياق لأننا نستخدم mounted

      try {
        final employee = EmployeeModel(
          id: widget.employeeModel?.id ??
              DateTime.now().millisecondsSinceEpoch.toString(),
          name: nameController.text.trim(),
          phone: phoneNumberController.text.trim(),
          email: emailController.text.trim(),
          address: addressController.text.trim(),
          designation: selectedDesignation!.title,
          department: departmentController.text.trim(),
          salary: double.parse(salaryController.text.trim()),
          joiningDate: joiningDate,
          imageUrl: widget.employeeModel?.imageUrl ?? '',
          isActive: isActive,
        );

        final notifier = ref.read(employeeNotifierProvider.notifier);

        if (widget.employeeModel == null) {
          await notifier.addEmployee(employee);
          toast('تمت إضافة الموظف بنجاح');
        } else {
          await notifier.updateEmployee(employee);
          toast('تم تحديث بيانات الموظف بنجاح');
        }

        // التحقق من أن الويدجت لا تزال مثبتة قبل استخدام السياق
        if (mounted) {
          Navigator.pop(context);
        }
      } catch (e) {
        toast('حدث خطأ: $e');
      }
    }
  }
}
