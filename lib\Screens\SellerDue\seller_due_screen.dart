// شاشة مستحقات البائع الحالي
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/Screens/Due%20Calculation/due_collection_screen.dart';
import 'package:mobile_pos/const_commas.dart';
import 'package:mobile_pos/Screens/Customers/Model/customer_model.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/currency.dart';
import 'package:mobile_pos/empty_screen_widget.dart';
import 'package:mobile_pos/services/global_voice_assistant/voice_assistant_exports.dart';
import 'package:mobile_pos/model/due_transaction_model.dart';

/// مزود لبيانات المديونية الخاصة بالبائع الحالي
final sellerDueProvider =
    FutureProvider<List<DueTransactionModel>>((ref) async {
  List<DueTransactionModel> transactions = [];
  String sellerName = '';

  try {
    // الحصول على اسم البائع الحالي
    final prefs = await SharedPreferences.getInstance();
    sellerName = prefs.getString('userTitle') ?? '';

    if (sellerName.isEmpty) {
      // إذا لم يكن هناك عنوان للمستخدم، استخدم اسم الشركة من الملف الشخصي
      final companyRef = FirebaseDatabase.instance
          .ref('$constUserId/Personal Information/companyName');
      final snapshot = await companyRef.get();
      if (snapshot.exists && snapshot.value != null) {
        sellerName = snapshot.value.toString();
      }
    }

    if (sellerName.isEmpty) {
      sellerName = 'المستخدم الرئيسي';
    }

    // الحصول على مرجع لجدول المديونية
    final dueRef =
        FirebaseDatabase.instance.ref('$constUserId/Due Transaction');
    final snapshot = await dueRef.get();

    if (snapshot.exists) {
      for (var child in snapshot.children) {
        final data = child.value as Map<dynamic, dynamic>?;
        if (data != null) {
          // التحقق من وجود حقل sellerName
          final transactionSellerName =
              data['sellerName'] as String? ?? 'غير معروف';

          // إضافة المعاملة فقط إذا كانت تخص البائع الحالي
          if (transactionSellerName == sellerName) {
            // التحقق من أن المعاملة لم يتم سدادها بالكامل
            final isPaid = data['isPaid'] as bool? ?? false;
            final dueAmountAfterPay =
                double.tryParse(data['dueAmountAfterPay']?.toString() ?? '0') ??
                    0;

            if (!isPaid && dueAmountAfterPay > 0) {
              transactions.add(DueTransactionModel.fromJson(data));
            }
          }
        }
      }
    }

    // ترتيب المعاملات من الأحدث للأقدم
    transactions.sort((a, b) => DateTime.parse(b.purchaseDate)
        .compareTo(DateTime.parse(a.purchaseDate)));

    return transactions;
  } catch (e) {
    debugPrint('خطأ في الحصول على بيانات المديونية: $e');
    return [];
  }
});

/// شاشة مستحقات البائع الحالي
class SellerDueScreen extends ConsumerStatefulWidget {
  const SellerDueScreen({super.key});

  @override
  ConsumerState<SellerDueScreen> createState() => _SellerDueScreenState();
}

class _SellerDueScreenState extends ConsumerState<SellerDueScreen> {
  final TextEditingController _searchController = TextEditingController();
  final VoiceSearchService _voiceSearchService = VoiceSearchService();
  final GlobalKey<RefreshIndicatorState> _refreshIndicatorKey =
      GlobalKey<RefreshIndicatorState>();
  double totalDueAmount = 0.0;

  @override
  void initState() {
    super.initState();
    // تهيئة الشاشة
  }

  Future<void> _startVoiceSearch() async {
    final String voiceResult =
        await _voiceSearchService.listenForVoiceInput(context);
    if (voiceResult.isNotEmpty) {
      setState(() {
        _searchController.text = voiceResult;
      });
    }
  }

  // دالة لتحديث البيانات عند السحب للتحديث
  Future<void> _refreshData() async {
    try {
      // تحديث مزود البيانات
      final _ = await ref.refresh(sellerDueProvider.future);

      // تأخير بسيط لإظهار رسالة التحديث
      await Future.delayed(const Duration(milliseconds: 1000));

      // عرض رسالة نجاح التحديث
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث البيانات بنجاح'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      // عرض رسالة خطأ في حالة فشل التحديث
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحديث البيانات: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        title: Text(
          'مستحقاتي',
          style: GoogleFonts.poppins(
            color: Colors.white,
          ),
        ),
        centerTitle: true,
        iconTheme: const IconThemeData(color: Colors.white),
        elevation: 0.0,
      ),
      body: Container(
        alignment: Alignment.topCenter,
        decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
                topRight: Radius.circular(30), topLeft: Radius.circular(30))),
        child: RefreshIndicator(
          key: _refreshIndicatorKey,
          onRefresh: _refreshData,
          color: kMainColor,
          backgroundColor: Colors.white,
          strokeWidth: 3.0,
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(10.0),
              child: Column(
                children: [
                  // Search TextField
                  TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'بحث في المستحقات...',
                      prefixIcon: const Icon(Icons.search),
                      suffixIcon: IconButton(
                        icon: const Icon(Icons.mic),
                        onPressed: _startVoiceSearch,
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(25),
                      ),
                    ),
                    onChanged: (value) => setState(() {}),
                  ),

                  const SizedBox(height: 10),

                  // Due Transactions List
                  Consumer(
                    builder: (context, ref, __) {
                      final providerData = ref.watch(sellerDueProvider);

                      return providerData.when(
                        data: (transactions) {
                          // Filter based on search
                          final filteredTransactions = transactions
                              .where((t) =>
                                  t.customerName.toLowerCase().contains(
                                      _searchController.text.toLowerCase()) ||
                                  t.invoiceNumber.toLowerCase().contains(
                                      _searchController.text.toLowerCase()))
                              .toList();

                          // Calculate total due amount
                          totalDueAmount = filteredTransactions.fold(
                              0,
                              (sum, t) =>
                                  sum + (t.dueAmountAfterPay?.toDouble() ?? 0));

                          if (filteredTransactions.isEmpty) {
                            return const Padding(
                              padding: EdgeInsets.only(top: 50),
                              child: EmptyScreenWidget(),
                            );
                          }

                          return Column(
                            children: [
                              // Total Due Amount Display
                              Container(
                                padding: const EdgeInsets.all(10),
                                decoration: BoxDecoration(
                                  color: Colors.grey[100],
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: Text(
                                  'إجمالي المستحقات:  ${myFormat.format(totalDueAmount)} $currency',
                                  style: GoogleFonts.poppins(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              const SizedBox(height: 10),

                              // Transactions List
                              Text(
                                'قائمة المستحقات',
                                style: GoogleFonts.poppins(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),

                              ListView.builder(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                itemCount: filteredTransactions.length,
                                itemBuilder: (_, index) {
                                  final transaction =
                                      filteredTransactions[index];
                                  final date =
                                      DateTime.parse(transaction.purchaseDate);
                                  final formattedDate =
                                      '${date.year}/${date.month}/${date.day}';

                                  return GestureDetector(
                                    onTap: () {
                                      // الانتقال إلى شاشة تحصيل المديونية
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) =>
                                              DueCollectionScreen(
                                            customerModel: CustomerModel(
                                              transaction.customerName,
                                              transaction.customerPhone,
                                              transaction.customerType,
                                              '', // profilePicture
                                              '', // emailAddress
                                              transaction.customerAddress,
                                              (transaction.dueAmountAfterPay ??
                                                      0)
                                                  .toString(),
                                              '0', // openingBalance
                                              (transaction.dueAmountAfterPay ??
                                                      0)
                                                  .toString(), // remainedBalance
                                              '', // note
                                              gst: transaction.customerGst,
                                            ),
                                          ),
                                        ),
                                      );
                                    },
                                    child: Column(
                                      children: [
                                        ListTile(
                                          leading: SizedBox(
                                            height: 50.0,
                                            width: 50.0,
                                            child: CircleAvatar(
                                              foregroundColor: Colors.blue,
                                              backgroundColor: kMainColor,
                                              radius: 50.0,
                                              child: Text(
                                                transaction
                                                        .customerName.isNotEmpty
                                                    ? transaction.customerName
                                                        .substring(0, 1)
                                                    : '',
                                                style: const TextStyle(
                                                    color: Colors.white),
                                              ),
                                            ),
                                          ),
                                          title: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Flexible(
                                                child: Text(
                                                  transaction.customerName
                                                          .isNotEmpty
                                                      ? transaction.customerName
                                                      : transaction
                                                          .customerPhone,
                                                  style: GoogleFonts.poppins(
                                                    color: Colors.black,
                                                    fontSize: 15.0,
                                                  ),
                                                  maxLines: 1,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                ),
                                              ),
                                              Text(
                                                '$currency ${myFormat.format(transaction.dueAmountAfterPay ?? 0)}',
                                                style: GoogleFonts.poppins(
                                                  color: Colors.black,
                                                  fontSize: 15.0,
                                                ),
                                              ),
                                            ],
                                          ),
                                          subtitle: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(
                                                'فاتورة #${transaction.invoiceNumber}',
                                                style: GoogleFonts.poppins(
                                                  color: kGreyTextColor,
                                                  fontSize: 13.0,
                                                ),
                                              ),
                                              Text(
                                                formattedDate,
                                                style: GoogleFonts.poppins(
                                                  color: kGreyTextColor,
                                                  fontSize: 13.0,
                                                ),
                                              ),
                                            ],
                                          ),
                                          trailing: const Icon(
                                            Icons.arrow_forward_ios,
                                            color: kGreyTextColor,
                                          ),
                                          contentPadding: const EdgeInsets.only(
                                              left: 8.0, right: 8.0),
                                        ),
                                        const Divider(
                                          height: 1,
                                          thickness: 1.0,
                                          color: Colors.grey,
                                        )
                                      ],
                                    ),
                                  );
                                },
                              ),
                            ],
                          );
                        },
                        loading: () => const Center(
                          child: Padding(
                            padding: EdgeInsets.only(top: 50),
                            child: CircularProgressIndicator(
                              color: kMainColor,
                            ),
                          ),
                        ),
                        error: (error, stackTrace) => Center(
                          child: Padding(
                            padding: const EdgeInsets.only(top: 50),
                            child: Text(
                              'حدث خطأ: $error',
                              style: const TextStyle(color: Colors.red),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
