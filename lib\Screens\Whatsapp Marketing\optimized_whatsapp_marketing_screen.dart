// import 'package:flutter/material.dart';
// import 'package:flutter_easyloading/flutter_easyloading.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:nb_utils/nb_utils.dart';
// import 'package:mobile_pos/Provider/sms_template_provider.dart';
// import 'package:mobile_pos/constant.dart';
// import 'package:mobile_pos/models/sms_template_model.dart';
// import 'package:mobile_pos/Provider/paginated_customer_provider.dart';

// class OptimizedWhatsappMarketingScreen extends ConsumerStatefulWidget {
//   const OptimizedWhatsappMarketingScreen({super.key});

//   static const String route = '/optimized_whatsapp_marketing';

//   @override
//   ConsumerState<OptimizedWhatsappMarketingScreen> createState() =>
//       _OptimizedWhatsappMarketingScreenState();
// }

// class _OptimizedWhatsappMarketingScreenState
//     extends ConsumerState<OptimizedWhatsappMarketingScreen>
//     with SingleTickerProviderStateMixin {
//   ScrollController mainScroll = ScrollController();
//   TextEditingController salesTemplateController = TextEditingController();
//   TextEditingController salesReturnTemplateController = TextEditingController();
//   TextEditingController quotationTemplateController = TextEditingController();
//   TextEditingController purchaseTemplateController = TextEditingController();
//   TextEditingController purchaseReturnTemplateController =
//       TextEditingController();
//   TextEditingController dueTemplateController = TextEditingController();
//   TextEditingController bulkTemplateController = TextEditingController();
  
//   // للتبديل بين علامات التبويب
//   late TabController _tabController;
  
//   // للبحث عن العملاء
//   TextEditingController searchController = TextEditingController();
//   String searchQuery = '';
  
//   // قائمة العملاء المحددين للإرسال
//   List<String> selectedCustomerIds = [];

//   // Sales list of strings
//   List<String> salesFields = [
//     '{{CUSTOMER_NAME}}',
//     '{{CUSTOMER_ADDRESS}}',
//     '{{CUSTOMER_GST}}',
//     '{{INVOICE_NUMBER}}',
//     '{{PURCHASE_DATE}}',
//     '{{TOTAL_AMOUNT}}',
//     '{{DUE_AMOUNT}}',
//     '{{SERVICE_CHARGE}}',
//     '{{VAT}}',
//     '{{DISCOUNT_AMOUNT}}',
//     '{{TOTAL_QUANTITY}}',
//     '{{PAYMENT_TYPE}}',
//   ];

//   // Purchase list of strings
//   List<String> purchaseFields = [
//     '{{CUSTOMER_NAME}}',
//     '{{CUSTOMER_ADDRESS}}',
//     '{{INVOICE_NUMBER}}',
//     '{{PURCHASE_DATE}}',
//     '{{TOTAL_AMOUNT}}',
//     '{{DUE_AMOUNT}}',
//     '{{DISCOUNT_AMOUNT}}',
//     '{{PAYMENT_TYPE}}',
//   ];

//   // Due list of strings
//   List<String> dueFields = [
//     '{{CUSTOMER_NAME}}',
//     '{{CUSTOMER_ADDRESS}}',
//     '{{CUSTOMER_GST}}',
//     '{{INVOICE_NUMBER}}',
//     '{{PURCHASE_DATE}}',
//     '{{TOTAL_DUE}}',
//     '{{DUE_AMOUNT_AFTER_PAY}}',
//     '{{PAY_DUE_AMOUNT}}',
//     '{{PAYMENT_TYPE}}',
//   ];
  
//   @override
//   void initState() {
//     super.initState();
//     _tabController = TabController(length: 2, vsync: this);
    
//     // تحميل العملاء عند بدء الشاشة
//     WidgetsBinding.instance.addPostFrameCallback((_) {
//       ref.read(paginatedCustomersProvider.notifier).loadInitialData();
//     });
//   }
  
//   @override
//   void dispose() {
//     mainScroll.dispose();
//     salesTemplateController.dispose();
//     salesReturnTemplateController.dispose();
//     quotationTemplateController.dispose();
//     purchaseTemplateController.dispose();
//     purchaseReturnTemplateController.dispose();
//     dueTemplateController.dispose();
//     bulkTemplateController.dispose();
//     searchController.dispose();
//     _tabController.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return SafeArea(
//       child: Scaffold(
//         backgroundColor: Colors.white,
//         appBar: AppBar(
//           title: const Text('إدارة رسائل الواتساب'),
//           backgroundColor: kMainColor,
//           centerTitle: true,
//           elevation: 0,
//           iconTheme: const IconThemeData(color: Colors.white),
//           titleTextStyle: const TextStyle(
//               color: Colors.white, fontSize: 20, fontWeight: FontWeight.bold),
//           bottom: TabBar(
//             controller: _tabController,
//             indicatorColor: Colors.white,
//             labelColor: Colors.white,
//             unselectedLabelColor: Colors.white70,
//             tabs: const [
//               Tab(text: 'القوالب'),
//               Tab(text: 'إرسال رسائل'),
//             ],
//           ),
//         ),
//         body: TabBarView(
//           controller: _tabController,
//           children: [
//             // علامة تبويب القوالب
//             _buildTemplatesTab(),
            
//             // علامة تبويب إرسال الرسائل
//             _buildSendMessagesTab(),
//           ],
//         ),
//       ),
//     );
//   }
  
//   // بناء علامة تبويب القوالب
//   Widget _buildTemplatesTab() {
//     return Scrollbar(
//       controller: mainScroll,
//       child: SingleChildScrollView(
//         controller: mainScroll,
//         child: Consumer(builder: (_, ref, watch) {
//           final templates = ref.watch(smsTemplateProvider);
//           return templates.when(
//             data: (templates) {
//               salesTemplateController.text = templates.saleTemplate ?? "";
//               salesReturnTemplateController.text =
//                   templates.saleReturnTemplate ?? "";
//               quotationTemplateController.text =
//                   templates.quotationTemplate ?? "";
//               purchaseTemplateController.text =
//                   templates.purchaseTemplate ?? "";
//               purchaseReturnTemplateController.text =
//                   templates.purchaseReturnTemplate ?? "";
//               dueTemplateController.text = templates.dueTemplate ?? "";
//               bulkTemplateController.text = templates.bulkSmsTemplate ?? "";

//               return Padding(
//                 padding: const EdgeInsets.all(20.0),
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Container(
//                       decoration: BoxDecoration(
//                         color: Colors.white,
//                         borderRadius: BorderRadius.circular(10),
//                         boxShadow: defaultBoxShadow(),
//                       ),
//                       child: Column(
//                         children: [
//                           Padding(
//                             padding: const EdgeInsets.all(20.0),
//                             child: Column(
//                               crossAxisAlignment: CrossAxisAlignment.start,
//                               children: [
//                                 Text(
//                                   "قوالب رسائل الواتساب التسويقية",
//                                   style: boldTextStyle(size: 20),
//                                 ),
//                                 20.height,
//                                 _buildTemplateSection(
//                                   title: "قالب المبيعات:",
//                                   controller: salesTemplateController,
//                                   hintText: "أدخل قالب المبيعات",
//                                   fields: salesFields,
//                                 ),
//                                 20.height,
//                                 _buildTemplateSection(
//                                   title: "قالب مرتجع المبيعات:",
//                                   controller: salesReturnTemplateController,
//                                   hintText: "أدخل قالب مرتجع المبيعات",
//                                   fields: salesFields,
//                                 ),
//                                 20.height,
//                                 _buildTemplateSection(
//                                   title: "قالب عرض السعر:",
//                                   controller: quotationTemplateController,
//                                   hintText: "أدخل قالب عرض السعر",
//                                   fields: salesFields,
//                                 ),
//                                 20.height,
//                                 _buildTemplateSection(
//                                   title: "قالب المشتريات:",
//                                   controller: purchaseTemplateController,
//                                   hintText: "أدخل قالب المشتريات",
//                                   fields: purchaseFields,
//                                 ),
//                                 20.height,
//                                 _buildTemplateSection(
//                                   title: "قالب مرتجع المشتريات:",
//                                   controller:
//                                       purchaseReturnTemplateController,
//                                   hintText: "أدخل قالب مرتجع المشتريات",
//                                   fields: purchaseFields,
//                                 ),
//                                 20.height,
//                                 _buildTemplateSection(
//                                   title: "قالب المديونية:",
//                                   controller: dueTemplateController,
//                                   hintText: "أدخل قالب المديونية",
//                                   fields: dueFields,
//                                 ),
//                                 20.height,
//                                 _buildTemplateSection(
//                                   title: "قالب الرسائل الجماعية:",
//                                   controller: bulkTemplateController,
//                                   hintText: "أدخل قالب الرسائل الجماعية",
//                                   fields: salesFields,
//                                 ),
//                                 20.height,
//                                 Row(
//                                   mainAxisAlignment:
//                                       MainAxisAlignment.center,
//                                   children: [
//                                     ElevatedButton(
//                                       onPressed: () {
//                                         _saveTemplates(ref);
//                                       },
//                                       style: ElevatedButton.styleFrom(
//                                         backgroundColor: kMainColor,
//                                         padding: const EdgeInsets.symmetric(
//                                             horizontal: 30, vertical: 15),
//                                         shape: RoundedRectangleBorder(
//                                           borderRadius:
//                                               BorderRadius.circular(10),
//                                         ),
//                                       ),
//                                       child: const Text(
//                                         "حفظ القوالب",
//                                         style: TextStyle(
//                                           color: Colors.white,
//                                           fontWeight: FontWeight.bold,
//                                         ),
//                                       ),
//                                     ),
//                                   ],
//                                 ),
//                               ],
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),
//                   ],
//                 ),
//               );
//             },
//             loading: () => const Center(
//               child: CircularProgressIndicator(),
//             ),
//             error: (error, stackTrace) => Center(
//               child: Text(
//                 'حدث خطأ: $error',
//                 style: const TextStyle(
//                   color: Colors.red,
//                 ),
//               ),
//             ),
//           );
//         }),
//       ),
//     );
//   }
  
//   // بناء علامة تبويب إرسال الرسائل
//   Widget _buildSendMessagesTab() {
//     return Column(
//       children: [
//         // حقل البحث
//         Padding(
//           padding: const EdgeInsets.all(16.0),
//           child: TextField(
//             controller: searchController,
//             decoration: InputDecoration(
//               hintText: 'البحث عن عميل...',
//               prefixIcon: const Icon(Icons.search),
//               border: OutlineInputBorder(
//                 borderRadius: BorderRadius.circular(10),
//               ),
//               contentPadding: const EdgeInsets.symmetric(vertical: 0),
//             ),
//             onChanged: (value) {
//               setState(() {
//                 searchQuery = value;
//               });
//             },
//           ),
//         ),
        
//         // خيارات التصفية
//         SingleChildScrollView(
//           scrollDirection: Axis.horizontal,
//           child: Padding(
//             padding: const EdgeInsets.symmetric(horizontal: 16.0),
//             child: Row(
//               children: [
//                 FilterChip(
//                   label: const Text('الكل'),
//                   selected: true,
//                   onSelected: (selected) {
//                     // تطبيق التصفية
//                   },
//                 ),
//                 const SizedBox(width: 8),
//                 FilterChip(
//                   label: const Text('العملاء النشطين'),
//                   selected: false,
//                   onSelected: (selected) {
//                     // تطبيق التصفية
//                   },
//                 ),
//                 const SizedBox(width: 8),
//                 FilterChip(
//                   label: const Text('العملاء ذوي المديونية'),
//                   selected: false,
//                   onSelected: (selected) {
//                     // تطبيق التصفية
//                   },
//                 ),
//               ],
//             ),
//           ),
//         ),
        
//         // قائمة العملاء
//         Expanded(
//           child: _buildCustomerList(),
//         ),
        
//         // زر إرسال الرسائل
//         Padding(
//           padding: const EdgeInsets.all(16.0),
//           child: ElevatedButton(
//             onPressed: selectedCustomerIds.isEmpty
//                 ? null
//                 : () {
//                     _sendWhatsAppMessages();
//                   },
//             style: ElevatedButton.styleFrom(
//               backgroundColor: kMainColor,
//               padding: const EdgeInsets.symmetric(vertical: 15),
//               shape: RoundedRectangleBorder(
//                 borderRadius: BorderRadius.circular(10),
//               ),
//               minimumSize: const Size(double.infinity, 50),
//             ),
//             child: Text(
//               "إرسال رسائل إلى ${selectedCustomerIds.length} عميل",
//               style: const TextStyle(
//                 color: Colors.white,
//                 fontWeight: FontWeight.bold,
//               ),
//             ),
//           ),
//         ),
//       ],
//     );
//   }
  
//   // بناء قائمة العملاء
//   Widget _buildCustomerList() {
//     return Consumer(
//       builder: (context, ref, _) {
//         final customersData = ref.watch(paginatedCustomersProvider);
//         final filteredCustomers = ref.watch(filteredCustomersProvider);
        
//         // استخدام القائمة المصفاة إذا كان هناك بحث
//         final displayedCustomers = searchQuery.isNotEmpty 
//             ? filteredCustomers.where((customer) => 
//                 customer.customerName.toLowerCase().contains(searchQuery.toLowerCase()) ||
//                 customer.phoneNumber.contains(searchQuery)).toList()
//             : customersData.items;
        
//         if (displayedCustomers.isEmpty) {
//           return const Center(
//             child: Text('لا يوجد عملاء'),
//           );
//         }
        
//         return ListView.builder(
//           itemCount: displayedCustomers.length,
//           itemBuilder: (context, index) {
//             final customer = displayedCustomers[index];
//             final isSelected = selectedCustomerIds.contains(customer.id);
            
//             return CheckboxListTile(
//               value: isSelected,
//               onChanged: (value) {
//                 setState(() {
//                   if (value == true) {
//                     selectedCustomerIds.add(customer.id);
//                   } else {
//                     selectedCustomerIds.remove(customer.id);
//                   }
//                 });
//               },
//               title: Text(
//                 customer.customerName.isNotEmpty
//                     ? customer.customerName
//                     : customer.phoneNumber,
//                 style: const TextStyle(
//                   fontWeight: FontWeight.bold,
//                 ),
//               ),
//               subtitle: Text(customer.phoneNumber),
//               secondary: CircleAvatar(
//                 backgroundColor: kMainColor,
//                 child: Text(
//                   customer.customerName.isNotEmpty
//                       ? customer.customerName.substring(0, 1)
//                       : '?',
//                   style: const TextStyle(color: Colors.white),
//                 ),
//               ),
//             );
//           },
//         );
//       },
//     );
//   }

//   Widget _buildTemplateSection({
//     required String title,
//     required TextEditingController controller,
//     required String hintText,
//     required List<String> fields,
//   }) {
//     return Row(
//       mainAxisAlignment: MainAxisAlignment.start,
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         SizedBox(
//           width: 100,
//           child: Text(
//             title,
//             style: secondaryTextStyle(size: 16),
//           ),
//         ),
//         10.width,
//         Expanded(
//           child: TextFormField(
//             controller: controller,
//             maxLines: 5,
//             decoration: InputDecoration(
//               contentPadding:
//                   const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
//               hintText: hintText,
//               hintStyle: secondaryTextStyle(size: 16),
//               border: OutlineInputBorder(
//                 borderRadius: BorderRadius.circular(10),
//               ),
//               enabledBorder: OutlineInputBorder(
//                 borderRadius: BorderRadius.circular(10),
//               ),
//               focusedBorder: OutlineInputBorder(
//                 borderRadius: BorderRadius.circular(10),
//               ),
//             ),
//           ),
//         ),
//         10.width,
//         Expanded(
//           child: Column(
//             children: [
//               Text("الاختصارات", style: boldTextStyle(size: 16)),
//               10.height,
//               Wrap(
//                 children: List.generate(fields.length, (index) {
//                   return InkWell(
//                     onTap: () {
//                       controller.text = controller.text + fields[index];
//                     },
//                     child: Container(
//                       padding: const EdgeInsets.all(5),
//                       margin: const EdgeInsets.all(5),
//                       decoration: BoxDecoration(
//                         color: kMainColor,
//                         borderRadius: BorderRadius.circular(5),
//                       ),
//                       child: Text(
//                         fields[index],
//                         style: secondaryTextStyle(color: Colors.white),
//                       ),
//                     ),
//                   );
//                 }),
//               ),
//             ],
//           ),
//         ),
//       ],
//     );
//   }

//   void _saveTemplates(WidgetRef ref) async {
//     EasyLoading.show(status: 'جاري الحفظ...');

//     try {
//       final templates = SmsTemplateModel(
//         saleTemplate: salesTemplateController.text,
//         saleReturnTemplate: salesReturnTemplateController.text,
//         quotationTemplate: quotationTemplateController.text,
//         purchaseTemplate: purchaseTemplateController.text,
//         purchaseReturnTemplate: purchaseReturnTemplateController.text,
//         dueTemplate: dueTemplateController.text,
//         bulkSmsTemplate: bulkTemplateController.text,
//       );

//       await ref
//           .read(smsTemplateNotifierProvider.notifier)
//           .saveSmsTemplates(templates);

//       EasyLoading.dismiss();
//       EasyLoading.showSuccess('تم حفظ القوالب بنجاح');
//     } catch (e) {
//       EasyLoading.dismiss();
//       EasyLoading.showError('حدث خطأ أثناء الحفظ: $e');
//     }
//   }
  
//   // إرسال رسائل واتساب
//   void _sendWhatsAppMessages() async {
//     if (selectedCustomerIds.isEmpty) {
//       EasyLoading.showInfo('الرجاء اختيار عميل واحد على الأقل');
//       return;
//     }
    
//     EasyLoading.show(status: 'جاري الإرسال...');
    
//     try {
//       // الحصول على العملاء المحددين
//       final allCustomers = ref.read(paginatedCustomersProvider).items;
//       final selectedCustomers = allCustomers.where(
//         (customer) => selectedCustomerIds.contains(customer.id)
//       ).toList();
      
//       // الحصول على قالب الرسالة
//       final templates = await ref.read(smsTemplateProvider.future);
//       final messageTemplate = templates.bulkSmsTemplate ?? '';
      
//       if (messageTemplate.isEmpty) {
//         EasyLoading.dismiss();
//         EasyLoading.showError('الرجاء إدخال قالب للرسائل الجماعية أولاً');
//         return;
//       }
      
//       // إرسال الرسائل (هنا يمكن إضافة الكود الفعلي لإرسال الرسائل)
//       for (var customer in selectedCustomers) {
//         // استبدال المتغيرات في القالب
//         String personalizedMessage = messageTemplate
//             .replaceAll('{{CUSTOMER_NAME}}', customer.customerName)
//             .replaceAll('{{CUSTOMER_ADDRESS}}', customer.customerAddress);
        
//         // هنا يمكن إضافة كود لإرسال الرسالة عبر واتساب
//         debugPrint('إرسال رسالة إلى ${customer.customerName}: $personalizedMessage');
        
//         // تأخير قصير بين الرسائل لتجنب الحظر
//         await Future.delayed(const Duration(milliseconds: 100));
//       }
      
//       EasyLoading.dismiss();
//       EasyLoading.showSuccess('تم إرسال ${selectedCustomers.length} رسالة بنجاح');
      
//       // إعادة تعيين القائمة المحددة
//       setState(() {
//         selectedCustomerIds.clear();
//       });
//     } catch (e) {
//       EasyLoading.dismiss();
//       EasyLoading.showError('حدث خطأ أثناء إرسال الرسائل: $e');
//     }
//   }
// }
