import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/model/water_filter_models.dart';
import 'package:mobile_pos/services/water_filter_service.dart';
import 'package:mobile_pos/Screens/WaterFilters/Installments/payment_screen.dart';

class SystemDetailsScreen extends StatefulWidget {
  final WaterFilterSystem system;

  const SystemDetailsScreen({super.key, required this.system});

  @override
  State<SystemDetailsScreen> createState() => _SystemDetailsScreenState();
}

class _SystemDetailsScreenState extends State<SystemDetailsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  WaterFilterCustomer? _customer;
  WaterFilterProduct? _product;
  List<WaterFilterInstallment> _installments = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadSystemDetails();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadSystemDetails() async {
    setState(() => _isLoading = true);

    try {
      // تحميل بيانات العميل
      final customerData = await WaterFilterService.getData(
          'Customers/${widget.system.customerId}');
      if (customerData.isNotEmpty) {
        _customer = WaterFilterCustomer.fromJson(customerData);
      }

      // تحميل بيانات المنتج
      final productData = await WaterFilterService.getData(
          'Products/${widget.system.productId}');
      if (productData.isNotEmpty) {
        _product = WaterFilterProduct.fromJson(productData);
      }

      // تحميل الأقساط
      final installmentsData = await WaterFilterService.getData('Installments');
      final installments = <WaterFilterInstallment>[];

      installmentsData.forEach((key, value) {
        try {
          final installment = WaterFilterInstallment.fromJson(
            Map<String, dynamic>.from(value),
          );
          if (installment.systemId == widget.system.id) {
            installments.add(installment);
          }
        } catch (e) {
          debugPrint('خطأ في معالجة قسط: $e');
        }
      });

      // ترتيب الأقساط حسب الرقم
      installments
          .sort((a, b) => a.installmentNumber.compareTo(b.installmentNumber));

      setState(() {
        _installments = installments;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('خطأ في تحميل تفاصيل النظام: $e');
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        title: Text(
          'تفاصيل النظام',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        actions: [
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert, color: Colors.white),
            onSelected: (value) {
              switch (value) {
                case 'edit':
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('تعديل النظام قيد التطوير')),
                  );
                  break;
                case 'maintenance':
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('جدولة الصيانة قيد التطوير')),
                  );
                  break;
                case 'delete':
                  _showDeleteConfirmation();
                  break;
              }
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    const Icon(Icons.edit, color: Colors.blue),
                    const SizedBox(width: 8),
                    Text('تعديل النظام', style: GoogleFonts.cairo()),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'maintenance',
                child: Row(
                  children: [
                    const Icon(Icons.build, color: Colors.orange),
                    const SizedBox(width: 8),
                    Text('جدولة صيانة', style: GoogleFonts.cairo()),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    const Icon(Icons.delete, color: Colors.red),
                    const SizedBox(width: 8),
                    Text('حذف النظام', style: GoogleFonts.cairo()),
                  ],
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          labelStyle: GoogleFonts.cairo(fontWeight: FontWeight.bold),
          tabs: [
            Tab(
              text: 'معلومات عامة',
              icon: const Icon(Icons.info),
            ),
            Tab(
              text: 'الأقساط (${_installments.length})',
              icon: const Icon(Icons.payment),
            ),
            Tab(
              text: 'الصيانة',
              icon: const Icon(Icons.build),
            ),
          ],
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : TabBarView(
                controller: _tabController,
                children: [
                  _buildGeneralInfoTab(),
                  _buildInstallmentsTab(),
                  _buildMaintenanceTab(),
                ],
              ),
      ),
    );
  }

  Widget _buildGeneralInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // معلومات النظام الأساسية
          _buildInfoCard(
            title: 'معلومات النظام',
            icon: Icons.settings,
            color: kMainColor,
            children: [
              _buildInfoRow('الرقم التسلسلي', widget.system.serialNumber),
              _buildInfoRow(
                  'تاريخ التركيب', _formatDate(widget.system.installationDate)),
              _buildInfoRow('الحالة', widget.system.status.arabicName,
                  valueColor: widget.system.status.color),
              if (widget.system.installationNotes != null)
                _buildInfoRow(
                    'ملاحظات التركيب', widget.system.installationNotes!),
            ],
          ),

          const SizedBox(height: 16),

          // معلومات العميل
          if (_customer != null)
            _buildInfoCard(
              title: 'معلومات العميل',
              icon: Icons.person,
              color: Colors.blue,
              children: [
                _buildInfoRow('الاسم', _customer!.name),
                _buildInfoRow('الهاتف', _customer!.phone),
                _buildInfoRow('البريد الإلكتروني',
                    _customer!.email.isEmpty ? 'غير محدد' : _customer!.email),
                _buildInfoRow('العنوان', _customer!.address),
                _buildInfoRow('المنطقة', _customer!.area),
              ],
            ),

          const SizedBox(height: 16),

          // معلومات المنتج
          if (_product != null)
            _buildInfoCard(
              title: 'معلومات المنتج',
              icon: Icons.water_drop,
              color: Colors.cyan,
              children: [
                _buildInfoRow('اسم المنتج', _product!.name),
                _buildInfoRow('العلامة التجارية', _product!.brand),
                _buildInfoRow('الفئة', _product!.category.arabicName),
                _buildInfoRow(
                    'سعر المنتج', '${_product!.price.toStringAsFixed(2)} ج.م'),
                _buildInfoRow('تكلفة التركيب',
                    '${_product!.installationCost.toStringAsFixed(2)} ج.م'),
                if (_product!.specifications.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Text(
                    'المواصفات:',
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade700,
                    ),
                  ),
                  const SizedBox(height: 4),
                  ..._product!.specifications.map(
                    (spec) => Padding(
                      padding: const EdgeInsets.only(left: 16, bottom: 2),
                      child: Text(
                        '• $spec',
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ),
                  ),
                ],
              ],
            ),

          const SizedBox(height: 16),

          // معلومات مالية
          _buildInfoCard(
            title: 'المعلومات المالية',
            icon: Icons.attach_money,
            color: Colors.green,
            children: [
              _buildInfoRow('إجمالي التكلفة',
                  '${widget.system.totalCost.toStringAsFixed(2)} ج.م'),
              _buildInfoRow('المبلغ المدفوع',
                  '${widget.system.paidAmount.toStringAsFixed(2)} ج.م',
                  valueColor: Colors.green),
              _buildInfoRow('المبلغ المتبقي',
                  '${widget.system.remainingAmount.toStringAsFixed(2)} ج.م',
                  valueColor: widget.system.remainingAmount > 0
                      ? Colors.red
                      : Colors.green),
              _buildInfoRow('نسبة الدفع',
                  '${widget.system.paymentPercentage.toStringAsFixed(1)}%',
                  valueColor: Colors.purple),
            ],
          ),

          const SizedBox(height: 16),

          // معلومات الضمان والصيانة
          _buildInfoCard(
            title: 'الضمان والصيانة',
            icon: Icons.verified_user,
            color: Colors.orange,
            children: [
              _buildInfoRow('حالة الضمان',
                  widget.system.isUnderWarranty ? 'ساري' : 'منتهي',
                  valueColor: widget.system.isUnderWarranty
                      ? Colors.green
                      : Colors.red),
              _buildInfoRow(
                  'انتهاء الضمان', _formatDate(widget.system.warrantyEndDate)),
              _buildInfoRow('الصيانة التالية',
                  _formatDate(widget.system.nextMaintenanceDate)),
              _buildInfoRow('حالة الصيانة',
                  widget.system.isMaintenanceOverdue ? 'متأخرة' : 'في الموعد',
                  valueColor: widget.system.isMaintenanceOverdue
                      ? Colors.red
                      : Colors.green),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInstallmentsTab() {
    if (_installments.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.payment_outlined,
              size: 80,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 20),
            Text(
              'لا توجد أقساط',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 10),
            Text(
              'لم يتم إنشاء أقساط لهذا النظام بعد',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.grey.shade500,
              ),
            ),
            if (widget.system.remainingAmount > 0) ...[
              const SizedBox(height: 20),
              ElevatedButton.icon(
                onPressed: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                        content: Text('إنشاء خطة أقساط قيد التطوير')),
                  );
                },
                icon: const Icon(Icons.add),
                label: Text(
                  'إنشاء خطة أقساط',
                  style: GoogleFonts.cairo(),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: kMainColor,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _installments.length,
      itemBuilder: (context, index) {
        final installment = _installments[index];
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.shade200,
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
            border: Border.all(
              color: installment.status.color.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // الصف الأول
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'قسط رقم ${installment.installmentNumber}',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: kMainColor,
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: installment.status.color.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: installment.status.color.withOpacity(0.3),
                        ),
                      ),
                      child: Text(
                        installment.status.arabicName,
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: installment.status.color,
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                // معلومات القسط
                Row(
                  children: [
                    Expanded(
                      child: _buildInstallmentInfo(
                        icon: Icons.attach_money,
                        label: 'المبلغ',
                        value: '${installment.amount.toStringAsFixed(2)} ج.م',
                        color: Colors.green,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildInstallmentInfo(
                        icon: Icons.calendar_today,
                        label: 'تاريخ الاستحقاق',
                        value: _formatDate(installment.dueDate),
                        color: installment.isOverdue ? Colors.red : Colors.blue,
                      ),
                    ),
                  ],
                ),

                if (installment.status == InstallmentStatus.paid) ...[
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: Colors.green.shade600,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'تم الدفع في ${_formatDate(installment.paidDate!)}',
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          color: Colors.green.shade600,
                        ),
                      ),
                    ],
                  ),
                ],

                if (installment.isOverdue) ...[
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.red.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.red.shade200),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.warning_amber,
                          color: Colors.red.shade600,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'متأخر ${installment.daysOverdue} يوم',
                          style: GoogleFonts.cairo(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Colors.red.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],

                // أزرار الإجراءات
                if (installment.status != InstallmentStatus.paid) ...[
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => PaymentScreen(
                                  installment: installment,
                                ),
                              ),
                            ).then((result) {
                              if (result == true) {
                                _loadSystemDetails();
                              }
                            });
                          },
                          icon: const Icon(Icons.payment, size: 16),
                          label: Text(
                            'دفع',
                            style: GoogleFonts.cairo(fontSize: 12),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 8),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildMaintenanceTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // حالة الصيانة الحالية
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: widget.system.isMaintenanceOverdue
                  ? Colors.red.shade50
                  : Colors.green.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: widget.system.isMaintenanceOverdue
                    ? Colors.red.shade200
                    : Colors.green.shade200,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  widget.system.isMaintenanceOverdue
                      ? Icons.warning_amber
                      : Icons.check_circle,
                  color: widget.system.isMaintenanceOverdue
                      ? Colors.red.shade600
                      : Colors.green.shade600,
                  size: 32,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.system.isMaintenanceOverdue
                            ? 'يحتاج صيانة فورية'
                            : 'الصيانة في الموعد',
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: widget.system.isMaintenanceOverdue
                              ? Colors.red.shade700
                              : Colors.green.shade700,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'الصيانة التالية: ${_formatDate(widget.system.nextMaintenanceDate)}',
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // معلومات الصيانة
          _buildInfoCard(
            title: 'معلومات الصيانة',
            icon: Icons.build,
            color: Colors.orange,
            children: [
              _buildInfoRow('تاريخ آخر صيانة', 'لم يتم تسجيل صيانة بعد'),
              _buildInfoRow('الصيانة التالية',
                  _formatDate(widget.system.nextMaintenanceDate)),
              _buildInfoRow('فترة الصيانة', 'كل 6 شهور'),
              _buildInfoRow('حالة الضمان',
                  widget.system.isUnderWarranty ? 'ساري' : 'منتهي'),
            ],
          ),

          const SizedBox(height: 20),

          // أزرار الإجراءات
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                          content: Text('جدولة الصيانة قيد التطوير')),
                    );
                  },
                  icon: const Icon(Icons.schedule),
                  label: Text(
                    'جدولة صيانة',
                    style: GoogleFonts.cairo(),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                          content: Text('تسجيل الصيانة قيد التطوير')),
                    );
                  },
                  icon: const Icon(Icons.build_circle),
                  label: Text(
                    'تسجيل صيانة',
                    style: GoogleFonts.cairo(),
                  ),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.orange,
                    side: const BorderSide(color: Colors.orange),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // تاريخ الصيانة
          Text(
            'تاريخ الصيانة',
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: kMainColor,
            ),
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Center(
              child: Text(
                'لم يتم تسجيل أي صيانة لهذا النظام بعد',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard({
    required String title,
    required IconData icon,
    required Color color,
    required List<Widget> children,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: color.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(icon, color: color, size: 24),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: children,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, {Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: GoogleFonts.cairo(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: valueColor ?? Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInstallmentInfo({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Row(
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(width: 6),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: GoogleFonts.cairo(
                  fontSize: 10,
                  color: Colors.grey.shade600,
                ),
              ),
              Text(
                value,
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'تأكيد الحذف',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        content: Text(
          'هل أنت متأكد من حذف هذا النظام؟\nسيتم حذف جميع الأقساط المرتبطة به أيضاً.',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إلغاء',
              style: GoogleFonts.cairo(),
            ),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await _deleteSystem();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: Text(
              'حذف',
              style: GoogleFonts.cairo(),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteSystem() async {
    try {
      // حذف الأقساط المرتبطة
      for (final installment in _installments) {
        await WaterFilterService.deleteData('Installments/${installment.id}');
      }

      // حذف النظام
      await WaterFilterService.deleteData('Systems/${widget.system.id}');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حذف النظام بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context, true);
      }
    } catch (e) {
      debugPrint('خطأ في حذف النظام: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حذف النظام: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
