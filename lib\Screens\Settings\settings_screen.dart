// ignore_for_file: unused_import, deprecated_member_use

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/Language/language_screen.dart';
import 'package:mobile_pos/Screens/Profile%20Screen/profile_details.dart';
import 'package:mobile_pos/Screens/Settings/currency_screen.dart';
import 'package:mobile_pos/Screens/Settings/feedback_screen.dart';
import 'package:mobile_pos/Screens/Settings/invoice_settings.dart';
import 'package:mobile_pos/Screens/Settings/live_chat_support_screen.dart';
import 'package:mobile_pos/Screens/Settings/app_update_screen.dart';
import 'package:mobile_pos/Screens/Settings/advanced_app_update_screen.dart';
import 'package:mobile_pos/Screens/Settings/user_switch_screen.dart';
import 'package:mobile_pos/Screens/Settings/device_management_screen.dart';
import 'package:mobile_pos/utils/invoice_duplicate_fixer.dart';
import 'package:mobile_pos/Screens/Notifications/notification_test_screen.dart';
import 'package:mobile_pos/Screens/SalesTargets/sales_targets_management_screen.dart';
import 'package:mobile_pos/test/test_menu_screen.dart';
import 'package:mobile_pos/generated/l10n.dart' as lang;
import 'package:mobile_pos/repository/login_repo.dart';
import 'package:mobile_pos/services/firebase_database_service.dart';
import 'package:mobile_pos/services/device_session_service.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:restart_app/restart_app.dart';
import '../../Provider/profile_provider.dart';
import '../../constant.dart';

import '../../currency.dart';
import '../../model/personal_information_model.dart';
import '../Shimmers/home_screen_appbar_shimmer.dart';
import '../Terms & Privacy/terms_and_privacy_screen.dart';
import '../User Logs/user_logs_menu_screen.dart';
import '../User Roles/user_role_screen.dart';
import '../Warehouse/warehouse_list.dart';
import '../subscription/package_screen.dart';
import 'Help & Support/contact_us.dart';
import '../../services/global_voice_assistant/overlay/voice_assistant_settings_screen.dart';

class SettingScreen extends StatefulWidget {
  const SettingScreen({super.key});

  @override
  // ignore: library_private_types_in_public_api
  _SettingScreenState createState() => _SettingScreenState();
}

class _SettingScreenState extends State<SettingScreen> {
  String? dropdownValue = '\$ (جنية)';
  bool expanded = true; // جعل قائمة الإعدادات مفتوحة افتراضيًا
  bool expandedHelp = false;
  bool expandedAbout = false;
  bool selected = false;

  // متغيرات حالة إصلاح الفواتير

  Future<void> _signOut() async {
    final currentUser = FirebaseAuth.instance.currentUser;

    // إنهاء جلسة الجهاز
    if (currentUser != null) {
      try {
        final deviceSessionService = DeviceSessionService();
        final sessionId = await deviceSessionService.getCurrentSessionId();
        if (sessionId != null) {
          await deviceSessionService.endDeviceSession(currentUser.uid, sessionId);
          debugPrint('تم إنهاء جلسة الجهاز بنجاح');
        }
      } catch (e) {
        debugPrint('خطأ في إنهاء جلسة الجهاز: $e');
      }
    }

    // إلغاء مزامنة جميع المراجع قبل تسجيل الخروج
    FirebaseDatabaseService.clearAllSyncedReferences();

    // تسجيل الخروج من Firebase Auth
    await FirebaseAuth.instance.signOut();

    EasyLoading.showSuccess('تم تسجيل الخروج بنجاح');
  }

  @override
  void initState() {
    //
    super.initState();
    printerIsEnable();
    getCurrency();
  }

  getCurrency() async {
    final prefs = await SharedPreferences.getInstance();
    String? data = prefs.getString('currency');
    if (!data.isEmptyOrNull) {
      for (var element in items) {
        if (element.substring(0, 2).contains(data!) ||
            element.substring(0, 5).contains(data)) {
          setState(() {
            dropdownValue = element;
          });
          break;
        }
      }
    } else {
      setState(() {
        dropdownValue = items[0];
      });
    }
  }

  void printerIsEnable() async {
    final prefs = await SharedPreferences.getInstance();

    isPrintEnable = prefs.getBool('isPrintEnable') ?? true;
  }

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: ThemeData(
        scaffoldBackgroundColor: Colors.white,
        appBarTheme: const AppBarTheme(
          backgroundColor: kMainColor,
          foregroundColor: Colors.white,
        ),
        cardTheme: const CardThemeData(color: Colors.white),
        expansionTileTheme: const ExpansionTileThemeData(
          backgroundColor: Colors.white,
          collapsedBackgroundColor: Colors.white,
        ),
      ),
      child: SafeArea(
        child: Consumer(
          builder: (context, ref, _) {
            AsyncValue<PersonalInformationModel> userProfileDetails = ref.watch(
              profileDetailsProvider,
            );
            return Scaffold(
              backgroundColor: kMainColor,
              body: Column(
                children: [
                  Card(
                    elevation: 0.0,
                    color: kMainColor,
                    child: Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: userProfileDetails.when(
                        data: (details) {
                          return Row(
                            children: [
                              GestureDetector(
                                onTap: () {
                                  const ProfileDetails().launch(context);
                                },
                                child: Container(
                                  height: 42,
                                  width: 42,
                                  decoration: BoxDecoration(
                                    image: DecorationImage(
                                      image: NetworkImage(
                                        details.pictureUrl ?? '',
                                      ),
                                      fit: BoxFit.cover,
                                    ),
                                    borderRadius: BorderRadius.circular(50),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 10.0),
                              Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    details.companyName ?? '',
                                    style: GoogleFonts.poppins(
                                      fontSize: 20.0,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                  ),
                                  Text(
                                    details.businessCategory ?? '',
                                    style: GoogleFonts.poppins(
                                      fontSize: 15.0,
                                      fontWeight: FontWeight.normal,
                                      color: Colors.white,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          );
                        },
                        error: (e, stack) {
                          return Text(e.toString());
                        },
                        loading: () {
                          return const HomeScreenAppBarShimmer();
                        },
                      ),
                    ),
                  ),
                  Expanded(
                    child: Container(
                      alignment: Alignment.center,
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.only(
                          topRight: Radius.circular(30),
                          topLeft: Radius.circular(30),
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.only(top: 5),
                        child: SingleChildScrollView(
                          child: Padding(
                            padding: const EdgeInsets.only(
                              top: 15,
                              left: 15,
                              right: 15,
                            ),
                            child: Column(
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                // قسم الحساب الشخصي
                                _buildSectionHeader(
                                  'الحساب الشخصي',
                                  Icons.person,
                                ),
                                const SizedBox(height: 12),
                                _buildSettingsCard(
                                  title: lang.S.of(context).profile,
                                  subtitle: 'إدارة بيانات الحساب الشخصي',
                                  icon: Icons.person_outline_rounded,
                                  onTap:
                                      () => const ProfileDetails().launch(
                                        context,
                                      ),
                                ),
                                _buildSettingsCard(
                                  title: lang.S.of(context).printingOption,
                                  subtitle: 'تفعيل أو إلغاء خيارات الطباعة',
                                  icon: Icons.print_outlined,
                                  onTap: () {},
                                  trailing: Transform.scale(
                                    scale: 0.9,
                                    child: Switch.adaptive(
                                      value: isPrintEnable,
                                      activeColor: kMainColor,
                                      onChanged: (bool value) async {
                                        final prefs =
                                            await SharedPreferences.getInstance();
                                        await prefs.setBool(
                                          'isPrintEnable',
                                          value,
                                        );
                                        setState(() {
                                          isPrintEnable = value;
                                        });
                                      },
                                    ),
                                  ),
                                ),

                                const SizedBox(height: 24),
                                // قسم الإعدادات المتقدمة
                                _buildSectionHeader(
                                  'الإعدادات المتقدمة',
                                  Icons.settings,
                                ),
                                const SizedBox(height: 12),

                                Container(
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(16),
                                    boxShadow: [
                                      BoxShadow(
                                        color: kMainColor.withOpacity(0.08),
                                        offset: const Offset(0, 2),
                                        blurRadius: 12,
                                        spreadRadius: 0,
                                      ),
                                    ],
                                    border: Border.all(
                                      color: kMainColor.withOpacity(0.1),
                                      width: 1,
                                    ),
                                  ),
                                  child: ExpansionPanelList(
                                    expandedHeaderPadding: EdgeInsets.zero,
                                    expansionCallback: (
                                      int index,
                                      bool isExpanded,
                                    ) {
                                      setState(() {
                                        expanded = !expanded;
                                      });
                                    },
                                    animationDuration: const Duration(
                                      milliseconds: 300,
                                    ),
                                    elevation: 0,
                                    dividerColor: Colors.transparent,
                                    children: [
                                      ExpansionPanel(
                                        headerBuilder: (
                                          BuildContext context,
                                          bool isExpanded,
                                        ) {
                                          return Container(
                                            padding: const EdgeInsets.all(16),
                                            child: Row(
                                              children: [
                                                Container(
                                                  padding: const EdgeInsets.all(
                                                    8,
                                                  ),
                                                  decoration: BoxDecoration(
                                                    color: kMainColor
                                                        .withOpacity(0.1),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                          8,
                                                        ),
                                                  ),
                                                  child: Icon(
                                                    Icons.tune,
                                                    color: kMainColor,
                                                    size: 20,
                                                  ),
                                                ),
                                                const SizedBox(width: 12),
                                                Text(
                                                  'أدوات المطور',
                                                  style: GoogleFonts.cairo(
                                                    fontSize: 16,
                                                    fontWeight: FontWeight.w600,
                                                    color:
                                                        expanded
                                                            ? kMainColor
                                                            : Colors.black87,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          );
                                        },
                                        body: Container(
                                          padding: const EdgeInsets.all(16),
                                          child: Column(
                                            children: [
                                              _buildDeveloperToolItem(
                                                'تحديث التطبيق',
                                                Icons.system_update,
                                                () => Navigator.push(
                                                  context,
                                                  MaterialPageRoute(
                                                    builder:
                                                        (context) =>
                                                            const AdvancedAppUpdateScreen(),
                                                  ),
                                                ),
                                              ),
                                              _buildDeveloperToolItem(
                                                'أهداف البائعين',
                                                Icons.trending_up,
                                                () => Navigator.push(
                                                  context,
                                                  MaterialPageRoute(
                                                    builder:
                                                        (context) =>
                                                            const SalesTargetsManagementScreen(),
                                                  ),
                                                ),
                                              ),
                                              _buildDeveloperToolItem(
                                                'اختبارات المستحقات',
                                                Icons.assessment,
                                                () => Navigator.push(
                                                  context,
                                                  MaterialPageRoute(
                                                    builder:
                                                        (context) =>
                                                            const TestMenuScreen(),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        isExpanded: expanded,
                                      ),
                                    ],
                                  ),
                                ).visible(
                                  !isSubUser,
                                ), // أدوات المطور فقط للمستخدم الأساسي

                                const SizedBox(height: 12),
                                // قسم إعدادات الفواتير والمخازن
                                _buildSectionHeader(
                                  'إعدادات الفواتير والمخازن',
                                  Icons.receipt_long,
                                ),
                                const SizedBox(height: 12),

                                _buildSettingsCard(
                                  title: 'إعدادات الفاتورة',
                                  subtitle: 'تخصيص شكل ومحتوى الفواتير',
                                  icon: Icons.receipt_outlined,
                                  onTap:
                                      () => const InvoiceSettings().launch(
                                        context,
                                      ),
                                ),

                                _buildSettingsCard(
                                  title: 'المخازن الرئيسية',
                                  subtitle: 'إدارة المخازن والمواقع',
                                  icon: Icons.warehouse_outlined,
                                  onTap:
                                      () =>
                                          const WarehouseList().launch(context),
                                ),

                                const SizedBox(height: 24),
                                // قسم إدارة المستخدمين والاشتراكات
                                _buildSectionHeader(
                                  'إدارة المستخدمين والاشتراكات',
                                  Icons.people,
                                ),
                                const SizedBox(height: 12),

                                _buildSettingsCard(
                                  title: 'تبديل المستخدمين',
                                  subtitle: 'التنقل بين حسابات المستخدمين',
                                  icon: Icons.switch_account_outlined,
                                  onTap:
                                      () => const UserSwitchScreen().launch(
                                        context,
                                      ),
                                ).visible(!isSubUser), // فقط للمستخدم الأساسي

                                _buildSettingsCard(
                                  title: lang.S.of(context).subscription,
                                  subtitle: 'إدارة الاشتراك والباقات',
                                  icon: Icons.account_balance_wallet_outlined,
                                  onTap:
                                      () =>
                                          const PackageScreen().launch(context),
                                ).visible(!isSubUser), // فقط للمستخدم الأساسي
                                const SizedBox(height: 24),
                                // قسم الذكاء الاصطناعي والأدوار
                                _buildSectionHeader(
                                  'الذكاء الاصطناعي والأدوار',
                                  Icons.smart_toy,
                                ),
                                const SizedBox(height: 12),

                                _buildSettingsCard(
                                  title: 'المساعد الصوتي العالمي',
                                  subtitle:
                                      'إعدادات المساعد الصوتي وبصمة الصوت',
                                  icon: Icons.mic_outlined,
                                  onTap:
                                      () => Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder:
                                              (context) =>
                                                  const VoiceAssistantSettingsScreen(),
                                        ),
                                      ),
                                ),

                                _buildSettingsCard(
                                  title: lang.S.of(context).userRole,
                                  subtitle: 'إدارة أدوار وصلاحيات المستخدمين',
                                  icon: Icons.admin_panel_settings_outlined,
                                  onTap: () {
                                    debugPrint(
                                      '🔍 User Role clicked - isSubUser: $isSubUser',
                                    );
                                    debugPrint(
                                      '🔍 userManagementPermission: ${finalUserRoleModel.userManagementPermission}',
                                    );
                                    const UserRoleScreen().launch(context);
                                  },
                                ).visible(() {
                                  bool shouldShow =
                                      !isSubUser ||
                                      (isSubUser &&
                                          finalUserRoleModel
                                              .userManagementPermission);
                                  debugPrint(
                                    '🔍 User Role visibility: $shouldShow',
                                  );
                                  debugPrint('🔍 isSubUser: $isSubUser');
                                  debugPrint(
                                    '🔍 userManagementPermission: ${finalUserRoleModel.userManagementPermission}',
                                  );
                                  return shouldShow;
                                }()),

                                _buildSettingsCard(
                                  title: 'إدارة الإشعارات',
                                  subtitle: 'إرسال وإدارة الإشعارات الإدارية',
                                  icon: Icons.notifications_active_outlined,
                                  onTap: () {
                                    Navigator.pushNamed(
                                      context,
                                      '/AdminNotifications',
                                    );
                                  },
                                ).visible(!isSubUser), // فقط للمستخدم الأساسي

                                //_buildSettingsCard(
                                //  title: 'لوحة التحكم الشاملة',
                                //  subtitle:
                                //      'الوصول إلى لوحة التحكم الإدارية الكاملة',
                                //  icon: Icons.dashboard_outlined,
                                //  iconColor: Colors.purple,
                                //  onTap: () {
                                //    Navigator.pushNamed(
                                //      context,
                                //      '/super_admin_dashboard',
                                //    );
                                //  },
                                //).visible(!isSubUser), // فقط للمستخدم الأساسي

                                //_buildSettingsCard(
                                //  title: 'اختبار سريع للإشعارات',
                                //  subtitle:
                                //      'اختبار وتجربة نظام الإشعارات الإدارية',
                                //  icon: Icons.science_outlined,
                                //  iconColor: Colors.teal,
                                //  onTap: () {
                                //    Navigator.pushNamed(
                                //      context,
                                //      '/QuickAdminTest',
                                //    );
                                //  },
                                //).visible(!isSubUser), // فقط للمستخدم الأساسي

                                const SizedBox(height: 24),
                                // قسم النظام
                                _buildSectionHeader(
                                  'النظام',
                                  Icons.settings_applications,
                                ),
                                const SizedBox(height: 12),

                                _buildSettingsCard(
                                  title: 'إدارة الأجهزة المتصلة',
                                  subtitle: 'عرض وإدارة الأجهزة المسجل عليها الدخول',
                                  icon: Icons.devices_other_outlined,
                                  iconColor: Colors.blue,
                                  onTap: () {
                                    const DeviceManagementScreen().launch(context);
                                  },
                                ),

                                _buildSettingsCard(
                                  title: lang.S.of(context).logOUt,
                                  subtitle: 'تسجيل الخروج من التطبيق',
                                  icon: Icons.logout_outlined,
                                  iconColor: Colors.red,
                                  onTap: () async {
                                    EasyLoading.show(status: 'بنسجل الخروج...');
                                    await _signOut();
                                    Future.delayed(
                                      const Duration(milliseconds: 1000),
                                      () {
                                        Restart.restartApp();
                                      },
                                    );
                                  },
                                ),
                                const SizedBox(height: 24),
                                // معلومات الإصدار
                                Container(
                                  width: double.infinity,
                                  padding: const EdgeInsets.all(20),
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: [
                                        kMainColor.withOpacity(0.05),
                                        kMainColor.withOpacity(0.02),
                                      ],
                                      begin: Alignment.topCenter,
                                      end: Alignment.bottomCenter,
                                    ),
                                    borderRadius: BorderRadius.circular(16),
                                    border: Border.all(
                                      color: kMainColor.withOpacity(0.1),
                                      width: 1,
                                    ),
                                  ),
                                  child: Column(
                                    children: [
                                      Icon(
                                        Icons.info_outline,
                                        color: kMainColor.withOpacity(0.7),
                                        size: 32,
                                      ),
                                      const SizedBox(height: 12),
                                      Text(
                                        appName,
                                        style: GoogleFonts.cairo(
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                          color: kMainColor,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        'الإصدار $appVersion',
                                        style: GoogleFonts.cairo(
                                          fontSize: 14,
                                          color: kGreyTextColor,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 20),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  // دالة لبناء عنوان القسم
  Widget _buildSectionHeader(String title, IconData icon) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [kMainColor.withOpacity(0.1), kMainColor.withOpacity(0.05)],
          begin: Alignment.centerRight,
          end: Alignment.centerLeft,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: kMainColor.withOpacity(0.2), width: 1),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: kMainColor,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: Colors.white, size: 20),
          ),
          const SizedBox(width: 12),
          Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: kMainColor,
            ),
          ),
        ],
      ),
    );
  }

  // دالة لبناء كارت الإعدادات المحسن
  Widget _buildSettingsCard({
    required String title,
    String? subtitle,
    required IconData icon,
    required VoidCallback onTap,
    Widget? trailing,
    Color? iconColor,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: kMainColor.withOpacity(0.08),
            offset: const Offset(0, 2),
            blurRadius: 12,
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            offset: const Offset(0, 1),
            blurRadius: 4,
            spreadRadius: 0,
          ),
        ],
        border: Border.all(color: kMainColor.withOpacity(0.1), width: 1),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: (iconColor ?? kMainColor).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(icon, color: iconColor ?? kMainColor, size: 24),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                      if (subtitle != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          subtitle,
                          style: GoogleFonts.cairo(
                            fontSize: 13,
                            color: kGreyTextColor,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                trailing ??
                    Icon(
                      Icons.arrow_forward_ios,
                      color: kGreyTextColor,
                      size: 16,
                    ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // دالة لبناء عنصر أدوات المطور
  Widget _buildDeveloperToolItem(
    String title,
    IconData icon,
    VoidCallback onTap,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(8),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              children: [
                Icon(icon, color: kMainColor, size: 20),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: Colors.black87,
                    ),
                  ),
                ),
                Icon(Icons.arrow_forward_ios, color: kGreyTextColor, size: 14),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class NoticationSettings extends StatefulWidget {
  const NoticationSettings({super.key});

  @override
  // ignore: library_private_types_in_public_api
  _NoticationSettingsState createState() => _NoticationSettingsState();
}

class _NoticationSettingsState extends State<NoticationSettings> {
  bool notify = false;
  String notificationText = 'Off';

  @override
  Widget build(BuildContext context) {
    // ignore: sized_box_for_whitespace
    return Container(
      height: 350.0,
      width: MediaQuery.of(context).size.width - 80,
      child: Column(
        children: [
          Row(
            children: [
              const Spacer(),
              IconButton(
                color: kGreyTextColor,
                icon: const Icon(Icons.cancel_outlined),
                onPressed: () {
                  Navigator.pop(context);
                },
              ),
            ],
          ),
          Container(
            height: 100.0,
            width: 100.0,
            decoration: BoxDecoration(
              color: kDarkWhite,
              borderRadius: BorderRadius.circular(10.0),
            ),
            child: const Center(
              child: Icon(
                Icons.notifications_none_outlined,
                size: 50.0,
                color: kMainColor,
              ),
            ),
          ),
          const SizedBox(height: 20.0),
          Center(
            child: Text(
              lang.S.of(context).doNotDistrub,
              style: GoogleFonts.poppins(
                color: Colors.black,
                fontSize: 20.0,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Center(
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                'اختبار عمرو سيد',
                maxLines: 2,
                textAlign: TextAlign.center,
                style: GoogleFonts.poppins(
                  color: kGreyTextColor,
                  fontSize: 16.0,
                ),
              ),
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                notificationText,
                style: GoogleFonts.poppins(color: Colors.black, fontSize: 16.0),
              ),
              Switch(
                value: notify,
                onChanged: (val) {
                  setState(() {
                    notify = val;
                    val ? notificationText = 'مفعل' : notificationText = 'معطل';
                  });
                },
              ),
            ],
          ),
        ],
      ),
    );
  }
}
