# 🏦 وحدة إدارة الخزينة - Treasury Module

## 📋 نظرة عامة

وحدة إدارة الخزينة هي وحدة مستقلة ومتكاملة لإدارة الشؤون المالية في تطبيق AmrDevPOS. تم تصميمها لتكون قابلة للإزالة بسهولة دون التأثير على باقي أجزاء التطبيق.

## ✨ الميزات الرئيسية

### 💰 إدارة الرصيد
- تتبع الرصيد الحالي للخزينة
- عرض إجمالي الإيرادات والمصروفات
- حساب صافي الربح/الخسارة تلقائياً

### 📊 إدارة المعاملات
- إضافة معاملات الإيرادات والمصروفات
- تصنيف المعاملات حسب الفئات
- دعم طرق دفع متعددة
- إضافة ملاحظات ومراجع للمعاملات

### 📈 التقارير المالية
- تقارير يومية، أسبوعية، شهرية، وسنوية
- تقارير مخصصة لفترات محددة
- تحليل المعاملات حسب الفئات
- مقارنة الأداء الشهري

### 🔍 البحث والفلترة
- البحث في المعاملات بالوصف أو الفئة
- فلترة حسب نوع المعاملة (إيراد/مصروف)
- فلترة حسب الفئة
- فلترة حسب نطاق التاريخ

### 📤 التصدير
- تصدير التقارير بصيغة CSV
- تصدير التقارير بصيغة JSON
- إمكانية مشاركة التقارير

## 🏗️ هيكل الوحدة

```
lib/features/treasury/
├── models/                          # نماذج البيانات
│   ├── treasury_transaction_model.dart
│   ├── treasury_balance_model.dart
│   └── treasury_report_model.dart
├── screens/                         # الشاشات
│   ├── treasury_main_screen.dart
│   ├── add_treasury_transaction_screen.dart
│   ├── treasury_reports_screen.dart
│   └── treasury_history_screen.dart
├── services/                        # الخدمات
│   ├── treasury_service.dart
│   └── treasury_report_service.dart
├── widgets/                         # الويدجت المساعدة
│   ├── treasury_card_widget.dart
│   ├── transaction_item_widget.dart
│   └── balance_summary_widget.dart
├── providers/                       # مقدمي البيانات
│   ├── treasury_provider.dart
│   └── treasury_report_provider.dart
├── treasury_feature.dart           # ملف الميزة الرئيسي
└── README.md                        # هذا الملف
```

## 🔧 التكامل مع Firebase

### هيكل قاعدة البيانات
```
constUserId/
└── Treasury/
    ├── Balance/                     # رصيد الخزينة
    │   ├── currentBalance
    │   ├── totalIncome
    │   ├── totalExpense
    │   ├── lastUpdated
    │   └── lastTransactionId
    └── Transactions/                # المعاملات
        └── [transactionId]/
            ├── id
            ├── date
            ├── type
            ├── category
            ├── description
            ├── amount
            ├── paymentMethod
            ├── referenceNumber
            ├── notes
            ├── createdBy
            ├── createdAt
            └── updatedAt
```

## 🚀 كيفية الاستخدام

### 1. التنقل إلى الوحدة
```dart
Navigator.pushNamed(context, '/treasury');
```

### 2. استخدام الخدمات
```dart
// الحصول على خدمة الخزينة
final treasuryService = ref.read(treasuryServiceProvider);

// إضافة معاملة جديدة
await treasuryService.addTransaction(transaction);

// الحصول على الرصيد
final balance = await treasuryService.getBalance();
```

### 3. استخدام مقدمي البيانات
```dart
// مراقبة المعاملات
final transactionsAsync = ref.watch(treasuryTransactionsProvider);

// مراقبة الرصيد
final balanceAsync = ref.watch(treasuryBalanceProvider);

// إنشاء تقرير
ref.read(generateReportProvider.notifier).generateMonthlyReport(DateTime.now());
```

## 📱 الشاشات المتاحة

### 1. الشاشة الرئيسية (`TreasuryMainScreen`)
- عرض ملخص الرصيد
- إحصائيات سريعة (اليوم، الشهر)
- قائمة المعاملات الأخيرة
- أزرار التنقل السريع

### 2. شاشة إضافة المعاملات (`AddTreasuryTransactionScreen`)
- نموذج إضافة/تعديل المعاملات
- اختيار نوع المعاملة (إيراد/مصروف)
- تحديد الفئة وطريقة الدفع
- إضافة ملاحظات ومراجع

### 3. شاشة التقارير (`TreasuryReportsScreen`)
- تقارير سريعة (يومي، أسبوعي، شهري، سنوي)
- تقارير مخصصة
- تصدير التقارير
- تحليل مفصل للبيانات

### 4. شاشة تاريخ المعاملات (`TreasuryHistoryScreen`)
- عرض جميع المعاملات
- بحث وفلترة متقدمة
- تعديل وحذف المعاملات
- عرض تفاصيل المعاملات

## 🎨 التصميم والواجهة

### الألوان المستخدمة
- اللون الرئيسي: `kMainColor` (نبيتي غامق)
- الإيرادات: أخضر (`Colors.green`)
- المصروفات: أحمر (`Colors.red`)
- النصوص: `kTitleColor`

### الخطوط
- الخط الرئيسي: Cairo (للنصوص العربية)
- خط الأرقام: Poppins

### التخطيط
- تصميم متجاوب يدعم جميع أحجام الشاشات
- استخدام Material Design
- انحناءات ناعمة (15-25px)
- ظلال خفيفة للعمق

## 🔒 الأمان والأذونات

### الأذونات المطلوبة
- `treasury.view`: عرض بيانات الخزينة
- `treasury.add`: إضافة معاملات جديدة
- `treasury.edit`: تعديل المعاملات
- `treasury.delete`: حذف المعاملات
- `treasury.reports`: عرض التقارير
- `treasury.export`: تصدير البيانات

### التحقق من الصحة
- التحقق من صحة البيانات المدخلة
- منع إدخال قيم سالبة للمبالغ
- التحقق من تطابق الأرصدة

## 📊 الإحصائيات والتحليلات

### المقاييس المتاحة
- إجمالي المعاملات
- إجمالي الإيرادات والمصروفات
- الرصيد الحالي
- تاريخ آخر معاملة
- الفئة الأكثر استخداماً

### التقارير المتاحة
- تقرير يومي
- تقرير أسبوعي
- تقرير شهري
- تقرير سنوي
- تقرير مخصص

## 🔧 الصيانة والتطوير

### إضافة فئات جديدة
يمكن تعديل قوائم الفئات في:
- `treasury_provider.dart` (incomeCategoriesProvider, expenseCategoriesProvider)

### إضافة طرق دفع جديدة
يمكن تعديل قائمة طرق الدفع في:
- `treasury_provider.dart` (paymentMethodsProvider)

### تخصيص التقارير
يمكن إضافة أنواع تقارير جديدة في:
- `treasury_report_service.dart`

## 🚫 إزالة الوحدة

لإزالة وحدة الخزينة بالكامل:

1. احذف مجلد `lib/features/treasury/`
2. أزل المسار من `lib/main.dart`:
   ```dart
   '/treasury': (context) => const TreasuryMainScreen(),
   ```
3. أزل الاستيراد من `lib/main.dart`:
   ```dart
   import 'package:mobile_pos/features/treasury/screens/treasury_main_screen.dart';
   ```

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

1. **خطأ في تحميل البيانات**
   - تأكد من تهيئة Firebase بشكل صحيح
   - تحقق من صحة `constUserId`

2. **عدم تحديث الرصيد**
   - تأكد من استدعاء `_updateBalance()` بعد كل معاملة
   - تحقق من صحة حسابات المبالغ

3. **مشاكل في التصدير**
   - تأكد من أذونات الكتابة على التخزين
   - تحقق من توفر مساحة كافية

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- راجع الكود المصدري في المجلد
- تحقق من رسائل الخطأ في وحدة التحكم
- استخدم أدوات التطوير لفحص البيانات

---

**ملاحظة**: هذه الوحدة مصممة لتكون مستقلة تماماً ويمكن إزالتها أو تعديلها دون التأثير على باقي أجزاء التطبيق.
