import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mobile_pos/Screens/HRM/employees/provider/employee_provider.dart';
import 'package:mobile_pos/Screens/HRM/salaries%20list/provider/salary_provider.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/models/hrm_models.dart';

class EmployeePerformanceReport extends ConsumerStatefulWidget {
  const EmployeePerformanceReport({super.key});

  @override
  ConsumerState<EmployeePerformanceReport> createState() =>
      _EmployeePerformanceReportState();
}

class _EmployeePerformanceReportState
    extends ConsumerState<EmployeePerformanceReport> {
  String selectedEmployee = 'الكل';
  List<String> employees = ['الكل'];
  String selectedYear = '2025';

  final List<String> years = [
    '2025',
    '2026',
    '2027',
    '2028',
    '2029',
    '2030',
    '2031',
    '2032',
    '2033',
    '2034',
    '2035'
  ];

  // بيانات افتراضية للأداء (في التطبيق الحقيقي، يجب استرجاع هذه البيانات من قاعدة البيانات)
  final Map<String, Map<String, double>> performanceData = {
    'موظف 1': {
      'الحضور': 95,
      'الإنتاجية': 85,
      'الالتزام بالمواعيد': 90,
      'جودة العمل': 88,
      'العمل الجماعي': 92,
    },
    'موظف 2': {
      'الحضور': 88,
      'الإنتاجية': 92,
      'الالتزام بالمواعيد': 85,
      'جودة العمل': 90,
      'العمل الجماعي': 87,
    },
    'موظف 3': {
      'الحضور': 90,
      'الإنتاجية': 80,
      'الالتزام بالمواعيد': 95,
      'جودة العمل': 85,
      'العمل الجماعي': 90,
    },
  };

  @override
  Widget build(BuildContext context) {
    final employeesAsyncValue = ref.watch(employeeListProvider);
    final salariesAsyncValue = ref.watch(paidSalaryListProvider);

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('تقرير أداء الموظفين'),
        backgroundColor: kMainColor,
        centerTitle: true,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        titleTextStyle: const TextStyle(
            color: Colors.white, fontSize: 20, fontWeight: FontWeight.bold),
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: () {
              employeesAsyncValue.whenData((employees) {
                _printReport(employees);
              });
            },
          ),
        ],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                // فلاتر
                Row(
                  children: [
                    Expanded(
                      child: employeesAsyncValue.when(
                        data: (employeesList) {
                          // استخراج أسماء الموظفين الفريدة
                          if (employees.length <= 1) {
                            final uniqueEmployees = employeesList
                                .map((e) => e.name)
                                .toSet()
                                .toList();
                            employees = ['الكل', ...uniqueEmployees];
                          }

                          return DropdownButtonFormField<String>(
                            decoration: const InputDecoration(
                              labelText: 'الموظف',
                              border: OutlineInputBorder(),
                            ),
                            value: selectedEmployee,
                            items: employees.map((employee) {
                              return DropdownMenuItem<String>(
                                value: employee,
                                child: Text(employee),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                selectedEmployee = value!;
                              });
                            },
                          );
                        },
                        loading: () => const CircularProgressIndicator(),
                        error: (_, __) => const Text('خطأ في تحميل الموظفين'),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'السنة',
                          border: OutlineInputBorder(),
                        ),
                        value: selectedYear,
                        items: years.map((year) {
                          return DropdownMenuItem<String>(
                            value: year,
                            child: Text(year),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            selectedYear = value!;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Expanded(
            child: employeesAsyncValue.when(
              data: (employeesList) {
                // تطبيق الفلاتر
                List<EmployeeModel> filteredEmployees =
                    employeesList.where((employee) {
                  final matchesEmployee = selectedEmployee == 'الكل' ||
                      employee.name == selectedEmployee;

                  return matchesEmployee;
                }).toList();

                if (filteredEmployees.isEmpty) {
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.person_off, size: 64, color: Colors.grey),
                        SizedBox(height: 16),
                        Text(
                          'لا يوجد موظفين مطابقين للبحث',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return salariesAsyncValue.when(
                  data: (salaries) {
                    // تطبيق الفلاتر على الرواتب
                    final filteredSalaries = salaries.where((salary) {
                      final matchesYear = salary.year == selectedYear;
                      final matchesEmployee = selectedEmployee == 'الكل' ||
                          salary.employeeName == selectedEmployee;

                      return matchesYear && matchesEmployee;
                    }).toList();

                    return SingleChildScrollView(
                      child: Column(
                        children: [
                          // بطاقات الأداء للموظفين
                          ...filteredEmployees.map((employee) =>
                              _buildEmployeePerformanceCard(
                                  employee, filteredSalaries)),
                        ],
                      ),
                    );
                  },
                  loading: () =>
                      const Center(child: CircularProgressIndicator()),
                  error: (_, __) =>
                      const Center(child: Text('خطأ في تحميل بيانات الرواتب')),
                );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.error_outline,
                        size: 60, color: Colors.red),
                    const SizedBox(height: 16),
                    const Text(
                      'حدث خطأ أثناء تحميل البيانات',
                      style: TextStyle(color: Colors.red, fontSize: 18),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '$error',
                      style: const TextStyle(color: Colors.grey, fontSize: 14),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        final _ = ref.refresh(employeeListProvider);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: kMainColor,
                      ),
                      child: const Text('إعادة المحاولة'),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmployeePerformanceCard(
      EmployeeModel employee, List<dynamic> salaries) {
    // حساب متوسط الراتب
    double totalSalary = 0;
    int salaryCount = 0;
    for (var salary in salaries) {
      if (salary.employeeName == employee.name) {
        totalSalary += salary.paySalary;
        salaryCount++;
      }
    }
    double averageSalary = salaryCount > 0 ? totalSalary / salaryCount : 0;

    // الحصول على بيانات الأداء للموظف (استخدام بيانات افتراضية إذا لم تكن متوفرة)
    Map<String, double> performance = performanceData[employee.name] ??
        {
          'الحضور': 85,
          'الإنتاجية': 80,
          'الالتزام بالمواعيد': 85,
          'جودة العمل': 80,
          'العمل الجماعي': 85,
        };

    // حساب متوسط الأداء
    double averagePerformance = 0;
    performance.forEach((key, value) {
      averagePerformance += value;
    });
    averagePerformance = averagePerformance / performance.length;

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Card(
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات الموظف
              Row(
                children: [
                  CircleAvatar(
                    backgroundColor: kMainColor,
                    radius: 30,
                    child: Text(
                      employee.name.isNotEmpty
                          ? employee.name[0].toUpperCase()
                          : '?',
                      style: const TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          employee.name,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'المسمى الوظيفي: ${employee.designation}',
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),
                        Text(
                          'القسم: ${employee.department}',
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 16),

              // ملخص الأداء
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildPerformanceSummaryItem(
                      'متوسط الأداء',
                      '${averagePerformance.toStringAsFixed(1)}%',
                      Icons.trending_up,
                      _getPerformanceColor(averagePerformance),
                    ),
                    const SizedBox(width: 16),
                    _buildPerformanceSummaryItem(
                      'متوسط الراتب',
                      averageSalary.toStringAsFixed(2),
                      Icons.attach_money,
                      Colors.green,
                    ),
                    const SizedBox(width: 16),
                    _buildPerformanceSummaryItem(
                      'مدة الخدمة',
                      '${DateTime.now().difference(employee.joiningDate).inDays ~/ 30} شهر',
                      Icons.calendar_today,
                      Colors.blue,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 16),

              // تفاصيل الأداء
              const Text(
                'تفاصيل الأداء',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              SizedBox(
                height: 300,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.radar,
                        size: 64,
                        color: Colors.grey,
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'سيتم دعم الرسوم البيانية قريباً',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'متوسط الأداء: ${averagePerformance.toStringAsFixed(1)}%',
                        style: const TextStyle(
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 16),

              // جدول تفاصيل الأداء
              const Text(
                'تقييم الأداء',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              DataTable(
                columns: const [
                  DataColumn(label: Text('المعيار')),
                  DataColumn(label: Text('النسبة')),
                  DataColumn(label: Text('التقييم')),
                ],
                rows: performance.entries.map((entry) {
                  return DataRow(
                    cells: [
                      DataCell(Text(entry.key)),
                      DataCell(Text('${entry.value}%')),
                      DataCell(
                        Row(
                          children: [
                            Icon(
                              _getPerformanceIcon(entry.value),
                              color: _getPerformanceColor(entry.value),
                              size: 16,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              _getPerformanceRating(entry.value),
                              style: TextStyle(
                                color: _getPerformanceColor(entry.value),
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  );
                }).toList(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPerformanceSummaryItem(
      String title, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(
          icon,
          color: color,
          size: 32,
        ),
        const SizedBox(height: 8),
        Text(
          title,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  IconData _getPerformanceIcon(double value) {
    if (value >= 90) {
      return Icons.star;
    } else if (value >= 80) {
      return Icons.thumb_up;
    } else if (value >= 70) {
      return Icons.trending_up;
    } else if (value >= 60) {
      return Icons.trending_flat;
    } else {
      return Icons.trending_down;
    }
  }

  Color _getPerformanceColor(double value) {
    if (value >= 90) {
      return Colors.green;
    } else if (value >= 80) {
      return Colors.lightGreen;
    } else if (value >= 70) {
      return Colors.amber;
    } else if (value >= 60) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }

  String _getPerformanceRating(double value) {
    if (value >= 90) {
      return 'ممتاز';
    } else if (value >= 80) {
      return 'جيد جداً';
    } else if (value >= 70) {
      return 'جيد';
    } else if (value >= 60) {
      return 'مقبول';
    } else {
      return 'ضعيف';
    }
  }

  void _printReport(List<EmployeeModel> employees) {
    // عرض رسالة للمستخدم
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('طباعة التقرير'),
        content: const Text(
            'سيتم دعم طباعة التقرير قريباً. يمكنك حالياً مشاهدة التقرير على الشاشة.'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
}

class PerformanceData {
  final String category;
  final double value;

  PerformanceData(this.category, this.value);
}
