import 'package:rive/rive.dart' as rive;

/// أدوات مساعدة لإدارة أنيميشن Rive
class RiveUtils {
  /// الحصول على SMI input من artboard
  static rive.SMIBool? getRiveInput(rive.Artboard artboard,
      {required String stateMachineName}) {
    try {
      // print('🔍 البحث عن state machine: $stateMachineName');
      rive.StateMachineController? controller =
          rive.StateMachineController.fromArtboard(artboard, stateMachineName);

      if (controller != null) {
        artboard.addController(controller);
        //  print('✅ تم العثور على state machine: $stateMachineName');

        // البحث عن input مع أسماء مختلفة - قائمة شاملة
        final inputNames = [
          "active",
          "isActive",
          "trigger",
          "pressed",
          "click",
          "tap",
          "button",
          "state",
          "toggle",
          "animate",
          "play",
          "start"
        ];

        rive.SMIBool? input;
        for (String name in inputNames) {
          try {
            input = controller.findInput<bool>(name) as rive.SMIBool?;
            if (input != null) {
              // print('✅ تم العثور على Boolean input: ${input.name}');
              break;
            }
          } catch (e) {
            // تجاهل الأخطاء والمتابعة للاسم التالي
          }
        }

        if (input == null) {
          // print(
          //     '⚠️ لم يتم العثور على Boolean input مناسب في $stateMachineName');
          // print(
          //     '📋 Available inputs: ${controller.inputs.map((i) => '${i.name} (${i.runtimeType})').toList()}');
        }

        return input;
      } else {
        // print('❌ لم يتم العثور على state machine: $stateMachineName');
      }
      return null;
    } catch (e) {
      // print('❌ خطأ في الحصول على Rive input: $e');
      return null;
    }
  }

  /// تغيير حالة SMI Boolean مع تأخير
  static void changeSMIBoolState(rive.SMIBool? input) {
    if (input != null) {
      input.change(true);
      Future.delayed(
        const Duration(milliseconds: 800),
        () {
          input.change(false);
        },
      );
    }
  }

  /// تشغيل أنيميشن فوري
  static void triggerAnimation(rive.SMIBool? input) {
    if (input != null) {
      try {
        input.change(true);
        // print('▶️ تم تشغيل الأنيميشن: ${input.name}');
      } catch (e) {
        // print('❌ خطأ في تشغيل الأنيميشن: $e');
      }
    }
  }

  /// إيقاف أنيميشن
  static void stopAnimation(rive.SMIBool? input) {
    if (input != null) {
      try {
        input.change(false);
        //  print('⏹️ تم إيقاف الأنيميشن: ${input.name}');
      } catch (e) {
        // print('❌ خطأ في إيقاف الأنيميشن: $e');
      }
    }
  }
}

/// نموذج بيانات Rive
class RiveModel {
  final String src, artboard, stateMachineName;
  rive.SMIBool? status;

  RiveModel({
    required this.src,
    required this.artboard,
    required this.stateMachineName,
    this.status,
  });

  set setStatus(rive.SMIBool state) {
    status = state;
  }
}
