// ignore_for_file: deprecated_member_use

import 'dart:convert';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/Screens/subscription/purchase_premium_plan_screen.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/generated/l10n.dart' as lang;
import 'package:nb_utils/nb_utils.dart';

import '../../model/subscription_model.dart';
import '../../model/subscription_plan_model.dart';
import '../../subscription.dart';

class PackageScreen extends StatefulWidget {
  const PackageScreen({super.key});

  @override
  State<PackageScreen> createState() => _PackageScreenState();
}

class _PackageScreenState extends State<PackageScreen> {
  String? initialSelectedPackage; // سيتم تحديده من قاعدة البيانات
  SubscriptionModel subscriptionModel = SubscriptionModel(
    subscriptionName: '',
    subscriptionDate: DateTime.now().toString(),
    saleNumber: 0,
    purchaseNumber: 0,
    partiesNumber: 0,
    dueNumber: 0,
    duration: 0,
    products: 0,
  );
  Duration? remainTime;
  SubscriptionPlanModel? subscriptionPlanModel;
  List<String>? initialPackageService;
  List<String> originalPackageService = ['0', '0', '0', '0', '0'];
  List<String> nameList = [
    'Sales',
    'Purchase',
    'Due collection',
    'Parties',
    'Products'
  ];
  List<String> imageList = [
    'assets/images/sales_2.png',
    'assets/images/purchase_2.png',
    'assets/images/due_collection_2.png',
    'assets/images/parties_2.png',
    'assets/images/product1.png',
  ];
  void checkSubscriptionData() async {
    EasyLoading.show(status: 'Loading');

    try {
      // إذا كان مستخدم فرعي، استخدم بيانات الاشتراك من الـ Admin
      String subscriptionUserId = constUserId;
      if (isSubUser) {
        // للمستخدم الفرعي، استخدم databaseId من finalUserRoleModel
        subscriptionUserId = finalUserRoleModel.databaseId;
        debugPrint(
            '🔄 مستخدم فرعي - استخدام اشتراك الـ Admin: $subscriptionUserId');
      }

      // جلب بيانات الاشتراك من قاعدة البيانات
      DatabaseReference ref =
          FirebaseDatabase.instance.ref('$subscriptionUserId/Subscription');
      final model = await ref.get();

      if (!model.exists || model.value == null) {
        // إذا لم توجد بيانات اشتراك، جرب جلب البيانات من Admin Panel/Seller List
        await _loadSubscriptionFromAdminPanel();
        return;
      }

      var data = jsonDecode(jsonEncode(model.value));
      final finalModel = SubscriptionModel.fromJson(data);

      // الحصول على اسم الباقة الحقيقي من قاعدة البيانات
      String realSubscriptionName = finalModel.subscriptionName;

      // إذا كان الاسم فارغ، جرب جلب البيانات من Admin Panel
      if (realSubscriptionName.isEmpty) {
        await _loadSubscriptionFromAdminPanel();
        return;
      }

      // تصحيح اسم الاشتراك إذا كان Free إلى Admin
      String correctedSubscriptionName = realSubscriptionName;
      if (correctedSubscriptionName == 'Free') {
        correctedSubscriptionName = 'Admin';
        debugPrint('تم تصحيح اسم الاشتراك من Free إلى Admin في package_screen');
      }

      // تعيين اسم الباقة المصحح
      Subscription.selectedItem = correctedSubscriptionName;
      initialSelectedPackage = correctedSubscriptionName;
      subscriptionModel = finalModel;
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات الاشتراك: $e');
      // في حالة الخطأ، جرب جلب البيانات من Admin Panel
      await _loadSubscriptionFromAdminPanel();
      return;
    }
    initialPackageService = [
      subscriptionModel.saleNumber.toString(),
      subscriptionModel.purchaseNumber.toString(),
      subscriptionModel.dueNumber.toString(),
      subscriptionModel.partiesNumber.toString(),
      subscriptionModel.products.toString(),
    ];
    subscriptionPlanModel = await CurrentSubscriptionPlanRepo()
        .getSubscriptionPlanByName(subscriptionModel.subscriptionName);
    originalPackageService[0] =
        subscriptionPlanModel?.saleNumber.toString() ?? '0';
    originalPackageService[1] =
        subscriptionPlanModel?.purchaseNumber.toString() ?? '0';
    originalPackageService[2] =
        subscriptionPlanModel?.dueNumber.toString() ?? '0';
    originalPackageService[3] =
        subscriptionPlanModel?.partiesNumber.toString() ?? '0';
    originalPackageService[4] =
        subscriptionPlanModel?.products.toString() ?? '0';

    EasyLoading.dismiss();
    setState(() {});
  }

  /// دالة لجلب بيانات الاشتراك من Admin Panel/Seller List
  Future<void> _loadSubscriptionFromAdminPanel() async {
    try {
      debugPrint('جاري جلب بيانات الاشتراك من Admin Panel/Seller List...');

      // جلب بيانات المستخدم من Admin Panel/Seller List
      final sellerListRef =
          FirebaseDatabase.instance.ref('Admin Panel/Seller List');
      final sellerListSnapshot = await sellerListRef.get();

      if (sellerListSnapshot.exists) {
        String? userSubscriptionName;

        // البحث عن المستخدم الحالي في قائمة البائعين
        for (var element in sellerListSnapshot.children) {
          try {
            var userData = jsonDecode(jsonEncode(element.value));

            // التحقق من أن هذا هو المستخدم الحالي
            if (userData['userId'] == constUserId ||
                userData['email'] == FirebaseAuth.instance.currentUser?.email) {
              userSubscriptionName = userData['subscriptionName']?.toString();
              debugPrint(
                  'تم العثور على اسم الباقة في Admin Panel: $userSubscriptionName');
              break;
            }
          } catch (e) {
            debugPrint('خطأ في معالجة بيانات المستخدم: $e');
            continue;
          }
        }

        // إذا تم العثور على اسم الباقة، استخدمه مع التصحيح
        if (userSubscriptionName != null && userSubscriptionName.isNotEmpty) {
          // تصحيح اسم الاشتراك إذا كان Free إلى Admin
          String correctedSubscriptionName = userSubscriptionName;
          if (correctedSubscriptionName == 'Free') {
            correctedSubscriptionName = 'Admin';
            debugPrint(
                'تم تصحيح اسم الاشتراك من Free إلى Admin في Admin Panel');
          }

          Subscription.selectedItem = correctedSubscriptionName;
          initialSelectedPackage = correctedSubscriptionName;

          // إنشاء نموذج اشتراك افتراضي بالاسم المصحح
          subscriptionModel = SubscriptionModel(
            subscriptionName: correctedSubscriptionName,
            subscriptionDate: DateTime.now().toString(),
            saleNumber: -202, // قيمة غير محدودة
            purchaseNumber: -202,
            partiesNumber: -202,
            dueNumber: -202,
            duration: 365,
            products: -202,
          );

          debugPrint('تم تعيين اسم الباقة المصحح: $correctedSubscriptionName');
        } else {
          // إذا لم يتم العثور على اسم باقة صالح، استخدم قيمة افتراضية
          debugPrint(
              'لم يتم العثور على اسم باقة صالح، استخدام القيمة الافتراضية');
          _setDefaultSubscription();
        }
      } else {
        debugPrint('لا توجد بيانات في Admin Panel/Seller List');
        _setDefaultSubscription();
      }
    } catch (e) {
      debugPrint('خطأ في جلب البيانات من Admin Panel: $e');
      _setDefaultSubscription();
    }
  }

  /// دالة لتعيين اشتراك افتراضي
  void _setDefaultSubscription() {
    const defaultSubscriptionName = 'Admin';
    Subscription.selectedItem = defaultSubscriptionName;
    initialSelectedPackage = defaultSubscriptionName;

    subscriptionModel = SubscriptionModel(
      subscriptionName: defaultSubscriptionName,
      subscriptionDate: DateTime.now().toString(),
      saleNumber: -202,
      purchaseNumber: -202,
      partiesNumber: -202,
      dueNumber: -202,
      duration: 365,
      products: -202,
    );

    debugPrint('تم تعيين الاشتراك الافتراضي: $defaultSubscriptionName');
  }

  /// التحقق من نوع الباقة - باقة أساسية
  bool _isBasicPlan() {
    if (initialSelectedPackage == null) return false;
    // قائمة الباقات الأساسية التي تحتاج عرض معلومات بسيطة
    final basicPlans = ['Free', 'Basic', 'Starter'];
    return basicPlans.contains(initialSelectedPackage);
  }

  /// التحقق من نوع الباقة - باقة متقدمة
  bool _isPremiumPlan() {
    if (initialSelectedPackage == null) return true; // افتراضي للباقات المتقدمة
    // قائمة الباقات المتقدمة
    final premiumPlans = [
      'Premium',
      'Pro',
      'Business',
      'Enterprise',
      'Admin',
      'Month',
      'Year'
    ];
    return premiumPlans.contains(initialSelectedPackage);
  }

  /// التحقق من إمكانية الترقية
  bool _canUpgrade() {
    if (initialSelectedPackage == null) return true;
    // الباقات التي لا تحتاج ترقية
    final noUpgradeNeeded = ['Lifetime', 'Enterprise', 'Admin', 'Ultimate'];
    return !noUpgradeNeeded.contains(initialSelectedPackage);
  }

  @override
  void initState() {
    checkSubscriptionData();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        title: Text(
          lang.S.of(context).yourPackage,
          style: GoogleFonts.poppins(
            color: Colors.white,
          ),
        ),
        centerTitle: true,
        iconTheme: const IconThemeData(color: Colors.white),
        elevation: 0.0,
      ),
      body: Container(
        alignment: Alignment.topCenter,
        decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
                topRight: Radius.circular(30), topLeft: Radius.circular(30))),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(25.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  height: 80,
                  width: double.infinity,
                  decoration: BoxDecoration(
                      color: kPremiumPlanColor.withOpacity(0.2),
                      borderRadius:
                          const BorderRadius.all(Radius.circular(10))),
                  child: Row(
                    children: [
                      const SizedBox(width: 10),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${subscriptionModel.subscriptionName} Plan',
                            style: const TextStyle(fontSize: 18),
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Text(
                                lang.S.of(context).youAreUsing,
                                style: const TextStyle(fontSize: 14),
                              ),
                              const SizedBox(width: 5.0),
                              Text(
                                '${subscriptionModel.subscriptionName} Plan',
                                style: const TextStyle(
                                    fontSize: 14,
                                    color: kMainColor,
                                    fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                        ],
                      ),
                      const Spacer(),
                      Container(
                        height: 63,
                        width: 63,
                        decoration: const BoxDecoration(
                          color: kMainColor,
                          borderRadius: BorderRadius.all(
                            Radius.circular(50),
                          ),
                        ),
                        child: Center(
                            child: Text(
                          '${(DateTime.parse(subscriptionModel.subscriptionDate).difference(DateTime.now()).inDays.abs() - subscriptionModel.duration).abs()} \nDays Left',
                          textAlign: TextAlign.center,
                          style: const TextStyle(
                              fontSize: 12, color: Colors.white),
                        )),
                      ),
                      const SizedBox(width: 20),
                    ],
                  ),
                ).visible(_isBasicPlan()), // عرض للباقات الأساسية
                Container(
                  height: 80,
                  width: double.infinity,
                  decoration: BoxDecoration(
                      color: kPremiumPlanColor.withOpacity(0.2),
                      borderRadius:
                          const BorderRadius.all(Radius.circular(10))),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const SizedBox(width: 20),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            lang.S.of(context).premiumPlan,
                            style: const TextStyle(fontSize: 18),
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Text(
                                lang.S.of(context).youAreUsing,
                                style: const TextStyle(fontSize: 14),
                              ),
                              Text(
                                '$initialSelectedPackage',
                                style: const TextStyle(
                                    fontSize: 14,
                                    color: kMainColor,
                                    fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                        ],
                      ),
                      const Spacer(),
                      Container(
                        height: 63,
                        width: 63,
                        decoration: const BoxDecoration(
                          color: kMainColor,
                          borderRadius: BorderRadius.all(
                            Radius.circular(50),
                          ),
                        ),
                        child: Center(
                            child: Text(
                          '${(DateTime.parse(subscriptionModel.subscriptionDate).difference(DateTime.now()).inDays.abs() - subscriptionModel.duration).abs()} \nDays Left',
                          textAlign: TextAlign.center,
                          style: const TextStyle(
                              fontSize: 12, color: Colors.white),
                        )),
                      ).visible(
                          subscriptionModel.subscriptionName != 'Lifetime'),
                      const SizedBox(width: 20),
                    ],
                  ),
                ).visible(_isPremiumPlan()), // عرض للباقات المتقدمة
                const SizedBox(height: 20),
                Text(
                  lang.S.of(context).packageFeatures,
                  style: const TextStyle(
                      fontSize: 20, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 10),
                ListView.builder(
                    itemCount: nameList.length,
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemBuilder: (_, i) {
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 10),
                        child: GestureDetector(
                          onTap: () {},
                          child: Card(
                            elevation: 1,
                            child: Padding(
                              padding: const EdgeInsets.all(5.0),
                              child: ListTile(
                                leading: SizedBox(
                                  height: 40,
                                  width: 40,
                                  child: Image(
                                    image: AssetImage(imageList[i]),
                                  ),
                                ),
                                title: Text(
                                  nameList[i],
                                  style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16),
                                ),
                                trailing: Text(
                                  initialPackageService?[i] == '-202'
                                      ? 'Unlimited'
                                      : '(${initialPackageService?[i] ?? ''}/${originalPackageService[i]})',
                                  style: const TextStyle(color: Colors.grey),
                                ),
                              ),
                            ),
                          ),
                        ),
                      );
                    }),
                const SizedBox(height: 20),
                Text(
                  lang.S.of(context).forUnlimitedUses,
                  style: const TextStyle(
                      fontSize: 20, fontWeight: FontWeight.bold),
                ).visible(_canUpgrade()),
                const SizedBox(height: 20).visible(_canUpgrade()),
                GestureDetector(
                  onTap: () {
                    const PurchasePremiumPlanScreen(
                      isCameBack: true,
                      initialSelectedPackage: 'Yearly',
                      initPackageValue: 0,
                    ).launch(context);
                  },
                  child: Container(
                    height: 50,
                    decoration: const BoxDecoration(
                      color: kMainColor,
                      borderRadius: BorderRadius.all(Radius.circular(30)),
                    ),
                    child: Center(
                      child: Text(
                        lang.S.of(context).updateNow,
                        style:
                            const TextStyle(fontSize: 18, color: Colors.white),
                      ),
                    ),
                  ),
                ).visible(_canUpgrade() && !isSubUser),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
