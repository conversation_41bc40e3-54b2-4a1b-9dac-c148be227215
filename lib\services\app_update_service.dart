import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:mobile_pos/constant.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AppUpdateService {
  static const String _lastCheckKey = 'last_update_check';
  static const Duration _checkInterval =
      Duration(hours: 24); // التحقق مرة واحدة يوميًا

  // التحقق من وجود تحديثات جديدة
  static Future<Map<String, dynamic>?> checkForUpdates(
      {bool force = false}) async {
    try {
      // التحقق من اتصال الإنترنت
      bool hasInternet = await InternetConnectionChecker().hasConnection;
      if (!hasInternet) {
        return null;
      }

      // التحقق مما إذا كان قد تم التحقق من التحديثات مؤخرًا (إلا إذا كان التحقق إجباريًا)
      if (!force) {
        final prefs = await SharedPreferences.getInstance();
        final lastCheck = prefs.getInt(_lastCheckKey) ?? 0;
        final now = DateTime.now().millisecondsSinceEpoch;

        if (now - lastCheck < _checkInterval.inMilliseconds) {
          return null; // تم التحقق مؤخرًا، لا داعي للتحقق مرة أخرى
        }
      }

      // استخدام رقم الإصدار من constant.dart
      String currentVersion = appVersion;

      // الحصول على معلومات أحدث إصدار من Firebase
      // الوصول إلى عقدة AppUpdate تحت Admin Panel
      final updateRef = FirebaseDatabase.instance.ref('Admin Panel/AppUpdate');
      final snapshot = await updateRef.get();

      if (snapshot.exists) {
        // تحديث وقت آخر تحقق
        final prefs = await SharedPreferences.getInstance();
        await prefs.setInt(
            _lastCheckKey, DateTime.now().millisecondsSinceEpoch);

        final data = snapshot.value as Map<dynamic, dynamic>;
        String latestVersion = data['latestVersion'] ?? '';
        String updateUrl = data['updateUrl'] ?? '';
        String releaseNotes = data['releaseNotes'] ?? '';
        bool forceUpdate = data['forceUpdate'] ?? false;
        String minRequiredVersion = data['minRequiredVersion'] ?? '';

        // مقارنة الإصدارات
        bool hasUpdate = _compareVersions(currentVersion, latestVersion) < 0;
        bool isUpdateRequired = minRequiredVersion.isNotEmpty &&
            _compareVersions(currentVersion, minRequiredVersion) < 0;

        if (hasUpdate) {
          return {
            'hasUpdate': true,
            'currentVersion': currentVersion,
            'latestVersion': latestVersion,
            'updateUrl': updateUrl,
            'releaseNotes': releaseNotes,
            'forceUpdate': forceUpdate || isUpdateRequired,
          };
        }
      }

      return {'hasUpdate': false, 'currentVersion': currentVersion};
    } catch (e) {
      debugPrint('خطأ في التحقق من التحديثات: $e');
      return null;
    }
  }

  // دالة لمقارنة الإصدارات (مثل 1.0.0 و 1.0.1)
  static int _compareVersions(String version1, String version2) {
    try {
      List<int> v1Parts = version1.split('.').map(int.parse).toList();
      List<int> v2Parts = version2.split('.').map(int.parse).toList();

      for (int i = 0; i < v1Parts.length && i < v2Parts.length; i++) {
        if (v1Parts[i] < v2Parts[i]) {
          return -1;
        } else if (v1Parts[i] > v2Parts[i]) {
          return 1;
        }
      }

      if (v1Parts.length < v2Parts.length) {
        return -1;
      } else if (v1Parts.length > v2Parts.length) {
        return 1;
      }

      return 0;
    } catch (e) {
      debugPrint('خطأ في مقارنة الإصدارات: $e');
      return 0;
    }
  }
}
