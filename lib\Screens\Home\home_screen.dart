// ignore_for_file: unused_result, deprecated_member_use, use_build_context_synchronously, unnecessary_import
import 'dart:convert';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:intl/intl.dart';
import 'package:mobile_pos/Provider/homepage_image_provider.dart';
import 'package:mobile_pos/Screens/Home/components/grid_items.dart';
import 'package:mobile_pos/Screens/Profile%20Screen/profile_details.dart';
import 'package:mobile_pos/Screens/Chat/chat_main_screen.dart';
import 'package:mobile_pos/Screens/Chat/services/chat_notification_service.dart';
import 'package:mobile_pos/Screens/Notifications/notifications_screen.dart';
import 'package:mobile_pos/Screens/Settings/settings_screen.dart';
import 'package:mobile_pos/Screens/WaterFilters/water_filters_home_screen.dart';
import 'package:mobile_pos/Screens/WaterFilters/Products/water_filter_products_screen.dart';
import 'package:mobile_pos/Screens/WaterFilters/Customers/water_filter_customers_screen.dart';
import 'package:mobile_pos/Screens/WaterFilters/Systems/water_filter_systems_screen.dart';
import 'package:mobile_pos/Screens/WaterFilters/Maintenance/maintenance_schedule_screen.dart';
import 'package:mobile_pos/Screens/WaterFilters/Installments/installments_tracking_screen.dart';
import 'package:mobile_pos/Screens/WaterFilters/Reports/water_filter_reports_screen.dart';
import 'package:mobile_pos/widgets/admin_notification_widget.dart';

import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/generated/l10n.dart' as lang;
import 'package:mobile_pos/model/subscription_model.dart';
import 'package:nb_utils/nb_utils.dart';
import '../../Provider/profile_provider.dart';
import '../../Provider/transactions_provider.dart';
import '../../const_commas.dart';
import '../../currency.dart';
import '../../subscription.dart';
import '../subscription/package_screen.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  // ignore: library_private_types_in_public_api
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen>
    with RouteAware, WidgetsBindingObserver {
  void updateNotifier() {
    if (mounted) {
      ref.refresh(purchaseTransitionProvider);
      ref.refresh(transitionProvider);
      ref.refresh(profileDetailsProvider);
      ref.refresh(homepageImageProvider);
      // تحديث بيانات الاشتراك أيضاً
      _loadSubscriptionData();
    }
  }

  // دالة التحديث للـ RefreshIndicator
  Future<void> _refreshData() async {
    try {
      // التحقق من الاتصال بالإنترنت
      bool hasConnection = await InternetConnectionChecker().hasConnection;
      if (!hasConnection) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('لا يوجد اتصال بالإنترنت'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      // التحقق من صحة الاشتراك
      await _verifySubscriptionAccess();

      // تحديث البيانات
      updateNotifier();
      if (mounted) {
        setState(() {
          _initializeData();
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء التحديث: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // دالة تحميل بيانات الاشتراك باستخدام نفس منطق package_screen
  Future<bool> _loadSubscriptionData() async {
    try {
      debugPrint('🔄 تحميل بيانات الاشتراك باستخدام منطق package_screen...');

      // الحصول على معرف المستخدم
      final userId = FirebaseAuth.instance.currentUser?.uid;
      if (userId == null) {
        debugPrint('❌ لا يوجد مستخدم مسجل دخول');
        return false;
      }

      // الحصول على بيانات الاشتراك من Firebase (نفس منطق package_screen)
      DatabaseReference ref = FirebaseDatabase.instance.ref(
        '$userId/Subscription',
      );
      final model = await ref.get();

      if (!model.exists || model.value == null) {
        debugPrint('❌ لا توجد بيانات اشتراك في Firebase');
        return false;
      }

      var data = jsonDecode(jsonEncode(model.value));
      final finalModel = SubscriptionModel.fromJson(data);

      // تصحيح اسم الاشتراك إذا كان Free إلى Admin
      String correctedSubscriptionName = finalModel.subscriptionName;
      if (correctedSubscriptionName == 'Free') {
        correctedSubscriptionName = 'Admin';
        debugPrint('تم تصحيح اسم الاشتراك من Free إلى Admin');
      }

      Subscription.selectedItem = correctedSubscriptionName;

      // debugPrint(
      //   '🔍 الاشتراك الحقيقي من قاعدة البيانات: ${finalModel.subscriptionName}',
      // );
      // debugPrint(
      //   '🔍 تم تعيين Subscription.selectedItem إلى: ${Subscription.selectedItem}',
      // );

      // التحقق من صحة اسم الاشتراك
      if (finalModel.subscriptionName.isEmpty) {
        debugPrint('❌ اسم الاشتراك فارغ');
        return false;
      }

      // تحديث الواجهة بعد تحميل البيانات
      if (mounted) {
        setState(() {});
      }

      debugPrint('✅ تم تحميل بيانات الاشتراك بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل بيانات الاشتراك: $e');
      return false;
    }
  }

  // دالة التحقق من صحة الاشتراك مع منع الوصول
  Future<void> _verifySubscriptionAccess() async {
    try {
      // تحميل بيانات الاشتراك مباشرة أولاً
      final success = await _loadSubscriptionData();

      if (!success) {
        // فشل في تحميل بيانات الاشتراك - منع الوصول
        debugPrint(
          '❌ فشل في تحميل بيانات الاشتراك - منع الوصول للشاشة الرئيسية',
        );
        _handleSubscriptionError('فشل في تحميل بيانات الاشتراك');
        return;
      }

      // التحقق من وجود اشتراك صالح
      final currentSubscription = Subscription.selectedItem;

      if (currentSubscription == null || currentSubscription.isEmpty) {
        // لا يوجد اشتراك صالح - منع الوصول
        debugPrint('❌ لا يوجد اشتراك صالح - منع الوصول للشاشة الرئيسية');
        _handleSubscriptionError('لا يوجد اشتراك صالح للمستخدم');
        return;
      }

      // عرض معلومات الاشتراك الصالح
      debugPrint('✅ تم التحقق من الاشتراك بنجاح: $currentSubscription');
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من الاشتراك: $e');
      _handleSubscriptionError('خطأ في التحقق من حالة الاشتراك');
    }
  }

  // دالة معالجة أخطاء الاشتراك
  void _handleSubscriptionError(String message) {
    if (mounted) {
      // عرض رسالة خطأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 5),
          action: SnackBarAction(
            label: 'إعادة المحاولة',
            textColor: Colors.white,
            onPressed: () {
              _verifySubscriptionAccess();
            },
          ),
        ),
      );

      // العودة لشاشة تسجيل الدخول بعد تأخير قصير
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted) {
          Navigator.of(context).pushReplacementNamed('/login');
        }
      });
    }
  }

  List<Color> color = [
    const Color(0xffEBD7FF),
    const Color(0xffFFD6E2),
    const Color(0xffBBFFC1),
    const Color(0xffD9EEFF),
    const Color(0xffFFE4C1),
    const Color(0xffBFFFEF),
    const Color(0xffFFD6E2),
    const Color(0xffEDDBFF),
    const Color(0xffF7CCFE),
    const Color(0xffBBFFC1),
    const Color(0xffD4ECFF),
    const Color(0xffFFE4C1),
    const Color(0xffEBD7FF),
    const Color(0xffE3F2FD), // لون أزرق فاتح للدردشة
    const Color(0xffE8F5E8), // لون أخضر فاتح لإدارة التوصيل الذكي
    // const Color(0xffFFF6ED),
  ];
  TextEditingController fromDateTextEditingController = TextEditingController(
    text: DateFormat.yMMMd().format(DateTime.now()),
  );
  TextEditingController toDateTextEditingController = TextEditingController(
    text: DateFormat.yMMMd().format(DateTime.now()),
  );
  DateTime fromDate = DateTime(
    DateTime.now().year,
    DateTime.now().month,
    DateTime.now().day,
  );
  DateTime toDate = DateTime.now();
  DateTime selectedDate = DateTime.now();
  double totalProfit = 0;
  double totalLoss = 0;
  bool isPicked = false;
  List<Map<String, dynamic>> sliderList = [
    {"icon": 'assets/images/banner1.png'},
    {"icon": 'assets/images/banner2.png'},
  ];
  PageController pageController = PageController(initialPage: 0);

  List<String> dayList = ['اليوم', 'أسبوعي', 'شهري', 'سنوي'];
  String selectedDay = 'اليوم';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializePageController();
    getCurrency();
    _initializeChatNotifications();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _initializeData();
        // تحميل بيانات الاشتراك فوراً عند تهيئة الشاشة
        _loadSubscriptionData();
      }
    });
  }

  /// تهيئة إشعارات الدردشة
  void _initializeChatNotifications() {
    try {
      // تهيئة خدمة إشعارات الدردشة
      ChatNotificationService.initialize();

      // إضافة مستمع لتحديث عدد الرسائل غير المقروءة
      ChatNotificationService.addCountListener((count) {
        if (mounted) {
          setState(() {
            _unreadMessagesCount = count;
          });
        }
      });

      // الحصول على العدد الحالي
      _unreadMessagesCount = ChatNotificationService.unreadMessagesCount;
    } catch (e) {
      debugPrint('خطأ في تهيئة إشعارات الدردشة: $e');
    }
  }

  void _initializePageController() {
    pageController = PageController(initialPage: 0);
  }

  void _initializeData() {
    if (mounted) {
      setState(() {
        fromDate = DateTime(
          DateTime.now().year,
          DateTime.now().month,
          DateTime.now().day,
        );
        toDate = DateTime.now();
        selectedDate = DateTime.now();
      });
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    pageController.dispose();
    fromDateTextEditingController.dispose();
    toDateTextEditingController.dispose();
    super.dispose();
    debugPrint('Screen disposed');
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.paused) {
      // Clean up resources when app goes to background
      pageController.dispose();
      _initializePageController();
    } else if (state == AppLifecycleState.resumed) {
      // تحديث بيانات الاشتراك عند العودة للتطبيق
      _loadSubscriptionData();
    }
  }

  // Removed unused field _isLoading
  Future<bool> _onWillPop() async {
    if (Navigator.of(context).userGestureInProgress) {
      return false;
    }
    return false;
  }

  double totalSell = 0;
  double totalDue = 0;
  double totalPurchase = 0;
  int count = 0;
  int _unreadMessagesCount = 0;

  setCurrency(String currency) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('currency', currency);
    getCurrency();
  }

  void getCurrency() async {
    final prefs = await SharedPreferences.getInstance();
    String? data = prefs.getString('currency');
    if (!data.isEmptyOrNull) {
      currency = data!;
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Consumer(
        builder: (_, ref, __) {
          final purchaseProviderData = ref.watch(purchaseTransitionProvider);
          final userProfileDetails = ref.watch(profileDetailsProvider);
          final homePageImageProvider = ref.watch(homepageImageProvider);
          final salesProviderData = ref.watch(transitionProvider);
          return WillPopScope(
            onWillPop: _onWillPop,
            child: userProfileDetails.when(
              data: (details) {
                if (mounted) {
                  setCurrency(details.currency.toString());
                }
                return Scaffold(
                  backgroundColor: kMainColor,
                  resizeToAvoidBottomInset: true,
                  appBar: AppBar(
                    backgroundColor: kMainColor,
                    leading: Padding(
                      padding: const EdgeInsets.only(left: 10),
                      child: GestureDetector(
                        onTap: () {
                          isSubUser
                              ? null
                              : const ProfileDetails().launch(context);
                        },
                        child: Container(
                          height: 40,
                          width: 40,
                          decoration: BoxDecoration(
                            image: DecorationImage(
                              image: NetworkImage(details.pictureUrl ?? ''),
                              fit: BoxFit.cover,
                            ),
                            shape: BoxShape.circle,
                            border: Border.all(color: kBorderColorTextField),
                          ),
                        ),
                      ),
                    ),
                    actions: [
                      // أيقونة الدردشة
                      IconButton(
                        icon: const Icon(
                          Icons.chat_bubble_outline,
                          color: Colors.white,
                        ),
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const ChatMainScreen(),
                            ),
                          );
                        },
                      ),
                      // أيقونة إدارة الإشعارات (للمستخدم الأساسي فقط)
                      if (!isSubUser)
                        IconButton(
                          icon: const Icon(
                            Icons.admin_panel_settings_outlined,
                            color: Colors.white,
                          ),
                          onPressed: () {
                            Navigator.pushNamed(context, '/AdminNotifications');
                          },
                          tooltip: 'إدارة الإشعارات',
                        ),
                      // أيقونة الإشعارات مع عداد
                      Stack(
                        children: [
                          IconButton(
                            icon: const Icon(
                              Icons.notifications_outlined,
                              color: Colors.white,
                            ),
                            onPressed: () {
                              // التنقل إلى شاشة الإشعارات
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) =>
                                      const NotificationsScreen(),
                                ),
                              );
                            },
                          ),
                          if (_unreadMessagesCount > 0)
                            Positioned(
                              right: 8,
                              top: 8,
                              child: Container(
                                padding: const EdgeInsets.all(2),
                                decoration: BoxDecoration(
                                  color: Colors.red,
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                constraints: const BoxConstraints(
                                  minWidth: 16,
                                  minHeight: 16,
                                ),
                                child: Text(
                                  '$_unreadMessagesCount',
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ],
                    title: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          details.companyName ?? '',
                          style: GoogleFonts.poppins(
                            fontSize: 20.0,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                        Text(
                          isSubUser
                              ? '${Subscription.selectedItem} Plan | $subUserTitle'
                              : '${Subscription.selectedItem} Plan',
                          style: GoogleFonts.poppins(
                            color: Colors.white,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                  ),
                  body: RefreshIndicator(
                    onRefresh: _refreshData,
                    color: kMainColor,
                    backgroundColor: Colors.white,
                    strokeWidth: 3.0,
                    child: Padding(
                      padding: const EdgeInsets.only(top: 15),
                      child: Container(
                        decoration: const BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.only(
                            topRight: Radius.circular(25),
                            topLeft: Radius.circular(25),
                          ),
                        ),
                        child: Column(
                          children: [
                            // الإشعارات الإدارية
                            const AdminNotificationWidget(
                              maxNotifications: 2,
                              showDismissButton: true,
                            ),
                            Container(
                              decoration: const BoxDecoration(
                                borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(25),
                                  topRight: Radius.circular(25),
                                ),
                                color: kDarkWhite,
                              ),
                              child: Padding(
                                padding: const EdgeInsets.only(
                                  left: 15,
                                  top: 10,
                                  bottom: 10,
                                ),
                                child: Row(
                                  children: [
                                    Text(
                                      lang.S.of(context).dashBoardOverView,
                                      style: kTextStyle.copyWith(
                                        fontWeight: FontWeight.bold,
                                        color: kTitleColor,
                                      ),
                                    ),
                                    const Spacer(),
                                    Transform.scale(
                                      scale: 0.6,
                                      child: CupertinoSwitch(
                                        activeColor: kMainColor,
                                        value: isReportShow,
                                        onChanged: (newValue) {
                                          setState(() {
                                            isReportShow = newValue;
                                          });
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            isReportShow
                                ? Padding(
                                    padding: const EdgeInsets.all(10.0),
                                    child: Column(
                                      children: [
                                        Padding(
                                          padding: const EdgeInsets.all(8.0),
                                          child: Column(
                                            children: [
                                              Row(
                                                children: [
                                                  Text(
                                                    lang.S
                                                        .of(context)
                                                        .salesAndPurchaseReports,
                                                    style: kTextStyle.copyWith(
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      fontSize: 16,
                                                      color: kTitleColor,
                                                    ),
                                                  ),
                                                  const Spacer(),
                                                  DropdownButtonHideUnderline(
                                                    child: DropdownButton(
                                                      icon: const Icon(
                                                        Icons
                                                            .keyboard_arrow_down_sharp,
                                                        color: kGreyTextColor,
                                                        size: 18,
                                                      ),
                                                      value: selectedDay,
                                                      items: dayList
                                                          .map(
                                                            (
                                                              e,
                                                            ) =>
                                                                DropdownMenuItem(
                                                              value: e,
                                                              child: Text(
                                                                e,
                                                                style: kTextStyle
                                                                    .copyWith(
                                                                  color:
                                                                      kTitleColor,
                                                                  fontSize: 14,
                                                                ),
                                                              ),
                                                            ),
                                                          )
                                                          .toList(),
                                                      onChanged: (
                                                        String? newValue,
                                                      ) {
                                                        setState(() {
                                                          selectedDay =
                                                              newValue!;
                                                          if (newValue ==
                                                              'اليوم') {
                                                            fromDate = DateTime(
                                                              DateTime.now()
                                                                  .year,
                                                              DateTime.now()
                                                                  .month,
                                                              DateTime.now()
                                                                  .day,
                                                            );
                                                          } else if (newValue ==
                                                              'أسبوعي') {
                                                            fromDate = DateTime(
                                                              DateTime.now()
                                                                  .year,
                                                              DateTime.now()
                                                                  .month,
                                                              DateTime.now()
                                                                  .day,
                                                            ).subtract(
                                                              const Duration(
                                                                days: 7,
                                                              ),
                                                            );
                                                          } else if (newValue ==
                                                              'شهري') {
                                                            fromDate = DateTime(
                                                              DateTime.now()
                                                                  .year,
                                                              DateTime.now()
                                                                  .month,
                                                              1,
                                                            );
                                                          } else if (newValue ==
                                                              'سنوي') {
                                                            fromDate = DateTime(
                                                              DateTime.now()
                                                                  .year,
                                                              1,
                                                              1,
                                                            );
                                                          }
                                                        });
                                                      },
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              const SizedBox(height: 10),
                                              Container(
                                                alignment: Alignment.center,
                                                height: 64,
                                                decoration: BoxDecoration(
                                                  color:
                                                      const Color(0xffCEFFE2),
                                                  borderRadius:
                                                      BorderRadius.circular(8),
                                                ),
                                                child: Column(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.center,
                                                  children: [
                                                    salesProviderData.when(
                                                      data: (transaction) {
                                                        totalSell = 0;
                                                        totalDue = 0;
                                                        final reTransaction =
                                                            transaction.reversed
                                                                .toList();
                                                        for (var element
                                                            in transaction) {
                                                          if ((fromDate
                                                                      .isBefore(
                                                                    DateTime
                                                                        .parse(
                                                                      element
                                                                          .purchaseDate,
                                                                    ),
                                                                  ) ||
                                                                  DateTime
                                                                      .parse(
                                                                    element
                                                                        .purchaseDate,
                                                                  ).isAtSameMomentAs(
                                                                    fromDate,
                                                                  )) &&
                                                              (toDate.isAfter(
                                                                    DateTime
                                                                        .parse(
                                                                      element
                                                                          .purchaseDate,
                                                                    ),
                                                                  ) ||
                                                                  DateTime.parse(
                                                                    element
                                                                        .purchaseDate,
                                                                  ).isAtSameMomentAs(
                                                                    toDate,
                                                                  ))) {
                                                            totalSell = totalSell +
                                                                element
                                                                    .totalAmount!
                                                                    .toDouble();
                                                            totalDue = totalDue +
                                                                element
                                                                    .dueAmount!
                                                                    .toDouble();
                                                          }
                                                        }
                                                        return reTransaction
                                                                .isNotEmpty
                                                            ? Text(
                                                                '$currency${myFormat.format(totalSell)}',
                                                                style: kTextStyle
                                                                    .copyWith(
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .bold,
                                                                  color:
                                                                      kTitleColor,
                                                                ),
                                                              )
                                                            : Text(
                                                                '$currency 0');
                                                      },
                                                      error: (e, stack) {
                                                        return Text(
                                                            e.toString());
                                                      },
                                                      loading: () {
                                                        return const Center(
                                                          child:
                                                              CircularProgressIndicator(),
                                                        );
                                                      },
                                                    ),
                                                    const SizedBox(height: 5),
                                                    Text(
                                                      lang.S.of(context).sales,
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              const SizedBox(height: 10),
                                              Row(
                                                children: [
                                                  Expanded(
                                                    child: Container(
                                                      alignment:
                                                          Alignment.center,
                                                      height: 64,
                                                      decoration: BoxDecoration(
                                                        color: const Color(
                                                          0xffDFD3FF,
                                                        ),
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(
                                                          8,
                                                        ),
                                                      ),
                                                      child: Column(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .center,
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .center,
                                                        children: [
                                                          purchaseProviderData
                                                              .when(
                                                            data:
                                                                (transaction) {
                                                              final reTransaction =
                                                                  transaction
                                                                      .reversed
                                                                      .toList();
                                                              totalPurchase = 0;
                                                              for (var element
                                                                  in reTransaction) {
                                                                if ((fromDate
                                                                            .isBefore(
                                                                          DateTime
                                                                              .parse(
                                                                            element.purchaseDate,
                                                                          ),
                                                                        ) ||
                                                                        DateTime
                                                                            .parse(
                                                                          element
                                                                              .purchaseDate,
                                                                        ).isAtSameMomentAs(
                                                                          fromDate,
                                                                        )) &&
                                                                    (toDate.isAfter(
                                                                          DateTime
                                                                              .parse(
                                                                            element.purchaseDate,
                                                                          ),
                                                                        ) ||
                                                                        DateTime.parse(
                                                                          element
                                                                              .purchaseDate,
                                                                        ).isAtSameMomentAs(
                                                                          toDate,
                                                                        ))) {
                                                                  totalPurchase =
                                                                      totalPurchase +
                                                                          element
                                                                              .totalAmount!
                                                                              .toDouble();
                                                                }
                                                              }
                                                              return reTransaction
                                                                      .isNotEmpty
                                                                  ? Text(
                                                                      '$currency${myFormat.format(totalPurchase)}',
                                                                      style: kTextStyle
                                                                          .copyWith(
                                                                        fontWeight:
                                                                            FontWeight.bold,
                                                                        color:
                                                                            kTitleColor,
                                                                      ),
                                                                    )
                                                                  : Text(
                                                                      '$currency 0',
                                                                    );
                                                            },
                                                            error: (e, stack) {
                                                              return Text(
                                                                e.toString(),
                                                              );
                                                            },
                                                            loading: () {
                                                              return const Center(
                                                                child:
                                                                    CircularProgressIndicator(),
                                                              );
                                                            },
                                                          ),
                                                          const SizedBox(
                                                            height: 5,
                                                          ),
                                                          Text(
                                                            lang.S
                                                                .of(context)
                                                                .purchase,
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ),
                                                  const SizedBox(width: 15),
                                                  Expanded(
                                                    child: Container(
                                                      alignment:
                                                          Alignment.center,
                                                      height: 64,
                                                      decoration: BoxDecoration(
                                                        color: const Color(
                                                          0xffFFDBDB,
                                                        ),
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(
                                                          8,
                                                        ),
                                                      ),
                                                      child: Column(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .center,
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .center,
                                                        children: [
                                                          Text(
                                                            '$currency${myFormat.format(totalDue)}',
                                                            style: kTextStyle
                                                                .copyWith(
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold,
                                                              color:
                                                                  kTitleColor,
                                                            ),
                                                          ),
                                                          const SizedBox(
                                                            height: 5,
                                                          ),
                                                          Text(
                                                            lang.S
                                                                .of(context)
                                                                .due,
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  )
                                : Expanded(
                                    child: SingleChildScrollView(
                                      physics:
                                          const AlwaysScrollableScrollPhysics(),
                                      child: Column(
                                        children: [
                                          Container(
                                            padding: const EdgeInsets.all(10.0),
                                            child:
                                                FutureBuilder<List<GridItems>>(
                                              future: getFreeIcons(
                                                context: context,
                                              ),
                                              builder: (context, snapshot) {
                                                if (snapshot.connectionState ==
                                                    ConnectionState.waiting) {
                                                  return const Center(
                                                    child:
                                                        CircularProgressIndicator(
                                                      color: kMainColor,
                                                    ),
                                                  );
                                                }

                                                if (snapshot.hasError) {
                                                  return Center(
                                                    child: Text(
                                                      'حدث خطأ في تحميل البيانات',
                                                      style: GoogleFonts.cairo(
                                                        color: Colors.red,
                                                        fontSize: 16,
                                                      ),
                                                    ),
                                                  );
                                                }

                                                final icons =
                                                    snapshot.data ?? [];

                                                if (icons.isEmpty) {
                                                  return Center(
                                                    child: Padding(
                                                      padding:
                                                          const EdgeInsets.all(
                                                        20.0,
                                                      ),
                                                      child: Text(
                                                        'لا توجد خدمات متاحة لك حالياً',
                                                        style:
                                                            GoogleFonts.cairo(
                                                          color: Colors
                                                              .grey.shade600,
                                                          fontSize: 16,
                                                        ),
                                                        textAlign:
                                                            TextAlign.center,
                                                      ),
                                                    ),
                                                  );
                                                }

                                                return GridView.count(
                                                  physics:
                                                      const NeverScrollableScrollPhysics(),
                                                  shrinkWrap: true,
                                                  childAspectRatio: 1.0,
                                                  crossAxisSpacing: 0,
                                                  mainAxisSpacing: 0,
                                                  crossAxisCount: 3,
                                                  children: List.generate(
                                                    icons.length,
                                                    (index) => HomeGridCards(
                                                      gridItems: icons[index],
                                                      color: color[
                                                          index % color.length],
                                                    ),
                                                  ),
                                                );
                                              },
                                            ),
                                          ),
                                          const SizedBox(height: 20),
                                          Container(
                                            height: 1,
                                            width: double.infinity,
                                            color: Colors.grey.shade300,
                                          ),
                                          const SizedBox(height: 10),
                                          homePageImageProvider.when(
                                            data: (images) {
                                              if (images.isNotEmpty &&
                                                  mounted) {
                                                return SizedBox(
                                                  width: double.infinity,
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .center,
                                                    children: [
                                                      Text(
                                                        lang.S
                                                            .of(context)
                                                            .whatsNew,
                                                        style:
                                                            GoogleFonts.poppins(
                                                          color: Colors.black,
                                                          fontWeight:
                                                              FontWeight.bold,
                                                          fontSize: 20.0,
                                                        ),
                                                      ),
                                                      Row(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .spaceAround,
                                                        children: [
                                                          GestureDetector(
                                                            child: const Icon(
                                                              Icons
                                                                  .keyboard_arrow_left,
                                                            ),
                                                            onTap: () {
                                                              if (mounted) {
                                                                pageController
                                                                    .previousPage(
                                                                  duration:
                                                                      const Duration(
                                                                    milliseconds:
                                                                        300,
                                                                  ),
                                                                  curve: Curves
                                                                      .linear,
                                                                );
                                                              }
                                                            },
                                                          ),
                                                          Container(
                                                            height: 180,
                                                            width: 310,
                                                            padding:
                                                                const EdgeInsets
                                                                    .all(
                                                              10,
                                                            ),
                                                            child: PageView
                                                                .builder(
                                                              pageSnapping:
                                                                  true,
                                                              itemCount:
                                                                  images.length,
                                                              controller:
                                                                  pageController,
                                                              itemBuilder: (
                                                                _,
                                                                index,
                                                              ) {
                                                                // Simplified to only show images
                                                                return GestureDetector(
                                                                  onTap: () {
                                                                    const PackageScreen()
                                                                        .launch(
                                                                      context,
                                                                    );
                                                                  },
                                                                  child:
                                                                      Container(
                                                                    decoration:
                                                                        BoxDecoration(
                                                                      borderRadius:
                                                                          BorderRadius
                                                                              .circular(
                                                                        8,
                                                                      ),
                                                                      image:
                                                                          DecorationImage(
                                                                        fit: BoxFit
                                                                            .fill,
                                                                        image:
                                                                            NetworkImage(
                                                                          images[index]
                                                                              .imageUrl,
                                                                        ),
                                                                      ),
                                                                    ),
                                                                  ),
                                                                );
                                                              },
                                                            ),
                                                          ),
                                                          GestureDetector(
                                                            child: const Icon(
                                                              Icons
                                                                  .keyboard_arrow_right,
                                                            ),
                                                            onTap: () {
                                                              if (mounted) {
                                                                pageController
                                                                    .nextPage(
                                                                  duration:
                                                                      const Duration(
                                                                    milliseconds:
                                                                        300,
                                                                  ),
                                                                  curve: Curves
                                                                      .linear,
                                                                );
                                                              }
                                                            },
                                                          ),
                                                        ],
                                                      ),
                                                      const SizedBox(
                                                          height: 30),
                                                    ],
                                                  ),
                                                );
                                              } else {
                                                return Container(
                                                  padding: const EdgeInsets.all(
                                                    10,
                                                  ),
                                                  height: 180,
                                                  width: 320,
                                                  decoration:
                                                      const BoxDecoration(
                                                    image: DecorationImage(
                                                      image: AssetImage(
                                                        'assets/images/banner1.png',
                                                      ),
                                                    ),
                                                  ),
                                                );
                                              }
                                            },
                                            error: (e, stack) {
                                              return Container(
                                                padding:
                                                    const EdgeInsets.all(10),
                                                height: 180,
                                                width: 320,
                                                decoration: const BoxDecoration(
                                                  image: DecorationImage(
                                                    image: AssetImage(
                                                      'assets/images/banner1.png',
                                                    ),
                                                  ),
                                                ),
                                              );
                                            },
                                            loading: () {
                                              return const CircularProgressIndicator();
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              },
              error: (e, stack) {
                return Container(
                  padding: const EdgeInsets.all(10),
                  height: 180,
                  width: 320,
                  decoration: const BoxDecoration(
                    image: DecorationImage(
                      image: AssetImage('assets/images/banner1.png'),
                    ),
                  ),
                );
              },
              loading: () {
                return const Scaffold(
                  body: Center(child: CircularProgressIndicator()),
                );
              },
            ),
          );
        },
      ),
    );
  }
}

class HomeGridCards extends StatefulWidget {
  const HomeGridCards({
    super.key,
    required this.gridItems,
    required this.color,
  });
  final GridItems gridItems;
  final Color color;

  @override
  State<HomeGridCards> createState() => _HomeGridCardsState();
}

class _HomeGridCardsState extends State<HomeGridCards> with RouteAware {
  bool _isNavigating = false;
  DateTime? _lastTapTime;
  bool _mounted = true;

  @override
  void initState() {
    super.initState();
    _mounted = true;
  }

  @override
  void dispose() {
    _mounted = false;
    super.dispose();
  }

  Future<bool> checkUserRolePermission({required String type}) async {
    try {
      if (!isSubUser) return true;

      switch (type) {
        // الصلاحيات الأساسية
        case 'Sales':
          return finalUserRoleModel.salePermission;
        case 'Sale List':
          return finalUserRoleModel.salesListPermission;
        case "Expense":
          return finalUserRoleModel.addExpensePermission;
        case "Due List":
          return finalUserRoleModel.dueListPermission;
        case "Loss/Profit":
          return finalUserRoleModel.lossProfitPermission;
        case 'Parties':
          return finalUserRoleModel.partiesPermission;
        case 'Product':
          return finalUserRoleModel.productPermission;
        case 'Purchase List':
          return finalUserRoleModel.purchaseListPermission;
        case 'Purchase':
          return finalUserRoleModel.purchasePermission;
        case "Reports":
          return finalUserRoleModel.reportsPermission;
        case 'Stock List':
          return finalUserRoleModel.stockPermission;
        case 'Settings':
          return finalUserRoleModel.settingsPermission;
        case 'profileEdit':
          return finalUserRoleModel.profileEditPermission;
        case 'Ledger':
          return finalUserRoleModel.ledgerPermission;

        // الصلاحيات الجديدة - الذكاء الاصطناعي
        case 'Chat':
        case 'AI Chat':
          return finalUserRoleModel.aiChatPermission;
        case 'AI Assistant':
          return finalUserRoleModel.aiAssistantPermission;
        case 'Voice Assistant':
          return finalUserRoleModel.voiceAssistantPermission;

        // الخزينة
        case 'Treasury':
        case 'CashBox':
          return finalUserRoleModel.treasuryPermission;
        case 'Cash Box':
          return finalUserRoleModel.cashBoxPermission;

        // إدارة التوصيل
        case 'DeliveryManagement':
        case 'Delivery Management':
          return finalUserRoleModel.deliveryManagementPermission;

        // الموارد البشرية
        case 'HRM':
          return finalUserRoleModel.hrmPermission;
        case 'Employee':
        case 'Employees':
          return finalUserRoleModel.employeesPermission;
        case 'Designation':
          return finalUserRoleModel.designationPermission;
        case 'Salaries':
          return finalUserRoleModel.salariesPermission;

        // التقارير المتقدمة
        case 'Financial Reports':
          return finalUserRoleModel.financialReportsPermission;
        case 'Sales Targets':
          return finalUserRoleModel.salesTargetsPermission;
        case 'Tax Reports':
          return finalUserRoleModel.taxReportsPermission;

        // الإعدادات المتقدمة
        case 'User Logs':
          return finalUserRoleModel.userLogsPermission;
        case 'Notifications':
          return finalUserRoleModel.notificationsPermission;
        case 'Warranty':
          return finalUserRoleModel.warrantyPermission;

        default:
          return true;
      }
    } catch (e) {
      debugPrint('Permission check error: $e');
      return false;
    }
  }

  Future<void> handleNavigation(BuildContext context) async {
    if (_isNavigating || !_mounted) return;

    final now = DateTime.now();
    if (_lastTapTime != null &&
        now.difference(_lastTapTime!) < const Duration(milliseconds: 500)) {
      return;
    }
    _lastTapTime = now;

    try {
      _isNavigating = true;

      if (!_mounted) return;

      final hasPermission = await checkUserRolePermission(
        type: widget.gridItems.route,
      );

      if (!_mounted) return;

      if (!hasPermission) {
        if (_mounted) {
          EasyLoading.showError('معلش، مش مسموحلك تدخل الصفحة دي');
        }
        return;
      }

      final hasValidSubscription = await Subscription.subscriptionChecker(
        item: widget.gridItems.route,
      );

      if (!_mounted) return;

      if (!hasValidSubscription) {
        if (_mounted) {
          EasyLoading.showError(
            'لازم تجدد الاشتراك الأول علشان الحد بتاعك خلص',
          );
        }
        return;
      }

      if (_mounted && context.mounted) {
        // التنقل للدردشة
        if (widget.gridItems.route == 'Chat') {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const ChatMainScreen()),
          );
        } else if (widget.gridItems.route == 'Settings') {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const SettingScreen()),
          );
        } else if (widget.gridItems.route == 'WaterFilters') {
          Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => const WaterFiltersHomeScreen()),
          );
        } else if (widget.gridItems.route == 'WaterFilterProducts') {
          Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => const WaterFilterProductsScreen()),
          );
        } else if (widget.gridItems.route == 'WaterFilterCustomers') {
          Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => const WaterFilterCustomersScreen()),
          );
        } else if (widget.gridItems.route == 'WaterFilterSystems') {
          Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => const WaterFilterSystemsScreen()),
          );
        } else if (widget.gridItems.route == 'WaterFilterMaintenance') {
          Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => const MaintenanceScheduleScreen()),
          );
        } else if (widget.gridItems.route == 'WaterFilterInstallments') {
          Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => const InstallmentsTrackingScreen()),
          );
        } else if (widget.gridItems.route == 'WaterFilterReports') {
          Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => const WaterFilterReportsScreen()),
          );
        } else {
          await Navigator.of(context).pushNamed('/${widget.gridItems.route}');
        }
      }
    } catch (e) {
      debugPrint('Navigation error: $e');
      if (_mounted) {
        EasyLoading.showError('في مشكلة حصلت، جرب تاني كمان شوية');
      }
    } finally {
      if (_mounted) {
        _isNavigating = false;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer(
      builder: (context, ref, __) {
        return Column(
          children: [
            Container(
              height: 80,
              width: 80,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: widget.color,
                border: Border.all(color: widget.color),
              ),
              child: TextButton(
                onPressed: () => handleNavigation(context),
                child: SvgPicture.asset(
                  widget.gridItems.icon,
                  height: 40.0,
                  width: 40.0,
                  allowDrawingOutsideViewBox: false,
                  fit: BoxFit.cover,
                ),
              ),
            ),
            Text(
              widget.gridItems.title.toString(),
              style: const TextStyle(fontSize: 13, color: Colors.black),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ],
        );
      },
    );
  }
}
