import 'dart:convert';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/Screens/Customers/Model/customer_model.dart';
import 'package:mobile_pos/model/transition_model.dart';
import 'package:mobile_pos/currency.dart';

class DueInvoiceTestScreen extends StatefulWidget {
  const DueInvoiceTestScreen({super.key});

  @override
  State<DueInvoiceTestScreen> createState() => _DueInvoiceTestScreenState();
}

class _DueInvoiceTestScreenState extends State<DueInvoiceTestScreen> {
  final List<String> _testResults = [];
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار فواتير الآجل'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            ElevatedButton(
              onPressed: _isLoading ? null : _runAllTests,
              child: _isLoading
                  ? const CircularProgressIndicator()
                  : const Text('تشغيل جميع الاختبارات'),
            ),
            const SizedBox(height: 20),
            Expanded(
              child: ListView.builder(
                itemCount: _testResults.length,
                itemBuilder: (context, index) {
                  return Card(
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        _testResults[index],
                        style: const TextStyle(fontSize: 12),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _runAllTests() async {
    setState(() {
      _isLoading = true;
      _testResults.clear();
    });

    await _testCustomersData();
    await _testSalesInvoices();
    await _testDataConsistency();
    await _testPhoneNumberMatching();
    await _testDueAmountCalculation();

    setState(() {
      _isLoading = false;
    });
  }

  // اختبار 1: فحص بيانات العملاء
  Future<void> _testCustomersData() async {
    _addResult('🔍 اختبار 1: فحص بيانات العملاء');

    try {
      final customersRef =
          FirebaseDatabase.instance.ref(constUserId).child('Customers');
      final snapshot = await customersRef.get();

      if (!snapshot.exists) {
        _addResult('❌ لا توجد بيانات عملاء في قاعدة البيانات');
        return;
      }

      List<CustomerModel> customers = [];
      int customersWithDue = 0;
      int customersWithZeroDue = 0;
      List<String> phoneNumbers = [];
      List<String> duplicatePhones = [];

      for (var element in snapshot.children) {
        try {
          var customerData = jsonDecode(jsonEncode(element.value));
          var customer = CustomerModel.fromJson(customerData);
          customers.add(customer);

          // فحص المديونية
          double dueAmount = double.tryParse(customer.dueAmount) ?? 0;
          if (dueAmount > 0) {
            customersWithDue++;
          } else {
            customersWithZeroDue++;
          }

          // فحص أرقام الهواتف المكررة
          if (phoneNumbers.contains(customer.phoneNumber)) {
            duplicatePhones.add(customer.phoneNumber);
          } else {
            phoneNumbers.add(customer.phoneNumber);
          }
        } catch (e) {
          _addResult('⚠️ خطأ في قراءة بيانات عميل: $e');
        }
      }

      _addResult('✅ إجمالي العملاء: ${customers.length}');
      _addResult('📊 عملاء لديهم مديونية: $customersWithDue');
      _addResult('📊 عملاء بدون مديونية: $customersWithZeroDue');

      if (duplicatePhones.isNotEmpty) {
        _addResult('⚠️ أرقام هواتف مكررة: ${duplicatePhones.join(', ')}');
      }
    } catch (e) {
      _addResult('❌ خطأ في اختبار بيانات العملاء: $e');
    }
  }

  // اختبار 2: فحص فواتير المبيعات
  Future<void> _testSalesInvoices() async {
    _addResult('\n🔍 اختبار 2: فحص فواتير المبيعات');

    try {
      final salesRef =
          FirebaseDatabase.instance.ref(constUserId).child('Sales Transition');
      final snapshot = await salesRef.get();

      if (!snapshot.exists) {
        _addResult('❌ لا توجد فواتير مبيعات في قاعدة البيانات');
        return;
      }

      List<SalesTransitionModel> allInvoices = [];
      List<SalesTransitionModel> dueInvoices = [];
      List<SalesTransitionModel> paidInvoices = [];
      Map<String, List<SalesTransitionModel>> invoicesByPhone = {};

      for (var element in snapshot.children) {
        try {
          var invoiceData = jsonDecode(jsonEncode(element.value));
          var invoice = SalesTransitionModel.fromJson(invoiceData);
          allInvoices.add(invoice);

          // تصنيف الفواتير
          double dueAmount = invoice.dueAmount ?? 0;
          if (dueAmount > 0) {
            dueInvoices.add(invoice);
          } else {
            paidInvoices.add(invoice);
          }

          // تجميع الفواتير حسب رقم الهاتف
          String phone = invoice.customerPhone;
          if (!invoicesByPhone.containsKey(phone)) {
            invoicesByPhone[phone] = [];
          }
          invoicesByPhone[phone]!.add(invoice);
        } catch (e) {
          _addResult('⚠️ خطأ في قراءة فاتورة: $e');
        }
      }

      _addResult('✅ إجمالي الفواتير: ${allInvoices.length}');
      _addResult('📊 فواتير آجلة: ${dueInvoices.length}');
      _addResult('📊 فواتير مدفوعة: ${paidInvoices.length}');
      _addResult('📊 عملاء لديهم فواتير: ${invoicesByPhone.length}');

      // عرض تفاصيل أول 5 فواتير آجلة
      if (dueInvoices.isNotEmpty) {
        _addResult('\n📋 تفاصيل أول 5 فواتير آجلة:');
        for (int i = 0; i < dueInvoices.length && i < 5; i++) {
          var invoice = dueInvoices[i];
          _addResult(
              '- فاتورة ${invoice.invoiceNumber}: ${invoice.customerName} (${invoice.customerPhone}) - مديونية: ${invoice.dueAmount}');
        }
      }
    } catch (e) {
      _addResult('❌ خطأ في اختبار فواتير المبيعات: $e');
    }
  }

  // اختبار 3: فحص تطابق البيانات
  Future<void> _testDataConsistency() async {
    _addResult('\n🔍 اختبار 3: فحص تطابق البيانات');

    try {
      // جلب العملاء
      final customersRef =
          FirebaseDatabase.instance.ref(constUserId).child('Customers');
      final customersSnapshot = await customersRef.get();

      // جلب الفواتير
      final salesRef =
          FirebaseDatabase.instance.ref(constUserId).child('Sales Transition');
      final salesSnapshot = await salesRef.get();

      if (!customersSnapshot.exists || !salesSnapshot.exists) {
        _addResult('❌ بيانات غير مكتملة للاختبار');
        return;
      }

      Map<String, CustomerModel> customersByPhone = {};
      Map<String, double> calculatedDueByPhone = {};

      // معالجة العملاء
      for (var element in customersSnapshot.children) {
        try {
          var customerData = jsonDecode(jsonEncode(element.value));
          var customer = CustomerModel.fromJson(customerData);
          customersByPhone[customer.phoneNumber] = customer;
          calculatedDueByPhone[customer.phoneNumber] = 0.0;
        } catch (e) {
          _addResult('⚠️ خطأ في معالجة عميل: $e');
        }
      }

      // حساب المديونية من الفواتير
      for (var element in salesSnapshot.children) {
        try {
          var invoiceData = jsonDecode(jsonEncode(element.value));
          var invoice = SalesTransitionModel.fromJson(invoiceData);

          String phone = invoice.customerPhone;
          double dueAmount = invoice.dueAmount ?? 0;

          if (calculatedDueByPhone.containsKey(phone)) {
            calculatedDueByPhone[phone] =
                calculatedDueByPhone[phone]! + dueAmount;
          }
        } catch (e) {
          _addResult('⚠️ خطأ في معالجة فاتورة: $e');
        }
      }

      // مقارنة البيانات
      int consistentCustomers = 0;
      int inconsistentCustomers = 0;
      List<String> inconsistencies = [];

      customersByPhone.forEach((phone, customer) {
        double storedDue = double.tryParse(customer.dueAmount) ?? 0;
        double calculatedDue = calculatedDueByPhone[phone] ?? 0;

        if ((storedDue - calculatedDue).abs() < 0.01) {
          // تسامح في الفروق الصغيرة
          consistentCustomers++;
        } else {
          inconsistentCustomers++;
          inconsistencies.add(
              '${customer.customerName} ($phone): مخزن=$storedDue, محسوب=$calculatedDue');
        }
      });

      _addResult('✅ عملاء متطابقون: $consistentCustomers');
      _addResult('❌ عملاء غير متطابقين: $inconsistentCustomers');

      if (inconsistencies.isNotEmpty) {
        _addResult('\n📋 تفاصيل عدم التطابق:');
        for (int i = 0; i < inconsistencies.length && i < 10; i++) {
          _addResult('- ${inconsistencies[i]}');
        }
      }
    } catch (e) {
      _addResult('❌ خطأ في اختبار تطابق البيانات: $e');
    }
  }

  // اختبار 4: فحص تطابق أرقام الهواتف
  Future<void> _testPhoneNumberMatching() async {
    _addResult('\n🔍 اختبار 4: فحص تطابق أرقام الهواتف');

    try {
      // جلب أرقام الهواتف من العملاء
      final customersRef =
          FirebaseDatabase.instance.ref(constUserId).child('Customers');
      final customersSnapshot = await customersRef.get();

      // جلب أرقام الهواتف من الفواتير
      final salesRef =
          FirebaseDatabase.instance.ref(constUserId).child('Sales Transition');
      final salesSnapshot = await salesRef.get();

      Set<String> customerPhones = {};
      Set<String> invoicePhones = {};
      Map<String, int> phoneFormats = {};

      // جمع أرقام الهواتف من العملاء
      if (customersSnapshot.exists) {
        for (var element in customersSnapshot.children) {
          try {
            var customerData = jsonDecode(jsonEncode(element.value));
            String phone = customerData['phoneNumber']?.toString() ?? '';
            if (phone.isNotEmpty) {
              customerPhones.add(phone);
              _analyzePhoneFormat(phone, phoneFormats);
            }
          } catch (e) {
            _addResult('⚠️ خطأ في قراءة رقم هاتف عميل: $e');
          }
        }
      }

      // جمع أرقام الهواتف من الفواتير
      if (salesSnapshot.exists) {
        for (var element in salesSnapshot.children) {
          try {
            var invoiceData = jsonDecode(jsonEncode(element.value));
            String phone = invoiceData['customerPhone']?.toString() ?? '';
            if (phone.isNotEmpty) {
              invoicePhones.add(phone);
              _analyzePhoneFormat(phone, phoneFormats);
            }
          } catch (e) {
            _addResult('⚠️ خطأ في قراءة رقم هاتف من فاتورة: $e');
          }
        }
      }

      // تحليل النتائج
      Set<String> commonPhones = customerPhones.intersection(invoicePhones);
      Set<String> customerOnlyPhones = customerPhones.difference(invoicePhones);
      Set<String> invoiceOnlyPhones = invoicePhones.difference(customerPhones);

      _addResult('✅ أرقام هواتف العملاء: ${customerPhones.length}');
      _addResult('✅ أرقام هواتف الفواتير: ${invoicePhones.length}');
      _addResult('✅ أرقام مشتركة: ${commonPhones.length}');
      _addResult('⚠️ أرقام في العملاء فقط: ${customerOnlyPhones.length}');
      _addResult('⚠️ أرقام في الفواتير فقط: ${invoiceOnlyPhones.length}');

      // عرض تنسيقات أرقام الهواتف
      _addResult('\n📋 تنسيقات أرقام الهواتف:');
      phoneFormats.forEach((format, count) {
        _addResult('- $format: $count مرة');
      });
    } catch (e) {
      _addResult('❌ خطأ في اختبار تطابق أرقام الهواتف: $e');
    }
  }

  // اختبار 5: فحص حساب المديونية
  Future<void> _testDueAmountCalculation() async {
    _addResult('\n🔍 اختبار 5: فحص حساب المديونية');

    try {
      final salesRef =
          FirebaseDatabase.instance.ref(constUserId).child('Sales Transition');
      final snapshot = await salesRef.get();

      if (!snapshot.exists) {
        _addResult('❌ لا توجد فواتير للاختبار');
        return;
      }

      Map<String, List<Map<String, dynamic>>> invoicesByCustomer = {};

      for (var element in snapshot.children) {
        try {
          var invoiceData = jsonDecode(jsonEncode(element.value));
          String phone = invoiceData['customerPhone']?.toString() ?? '';

          if (phone.isNotEmpty) {
            if (!invoicesByCustomer.containsKey(phone)) {
              invoicesByCustomer[phone] = [];
            }

            invoicesByCustomer[phone]!.add({
              'invoiceNumber': invoiceData['invoiceNumber']?.toString() ?? '',
              'totalAmount': double.tryParse(
                      invoiceData['totalAmount']?.toString() ?? '0') ??
                  0,
              'dueAmount': double.tryParse(
                      invoiceData['dueAmount']?.toString() ?? '0') ??
                  0,
              'paymentType': invoiceData['paymentType']?.toString() ?? '',
              'isPaid': invoiceData['isPaid'] ?? false,
            });
          }
        } catch (e) {
          _addResult('⚠️ خطأ في معالجة فاتورة: $e');
        }
      }

      _addResult('✅ عملاء لديهم فواتير: ${invoicesByCustomer.length}');

      // تحليل أول 5 عملاء
      int customerCount = 0;
      invoicesByCustomer.forEach((phone, invoices) {
        if (customerCount < 5) {
          double totalDue = 0;
          int dueInvoicesCount = 0;

          for (var invoice in invoices) {
            if (invoice['dueAmount'] > 0) {
              totalDue += invoice['dueAmount'];
              dueInvoicesCount++;
            }
          }

          _addResult(
              '📊 عميل $phone: ${invoices.length} فاتورة، $dueInvoicesCount آجلة، إجمالي المديونية: $totalDue');
          customerCount++;
        }
      });
    } catch (e) {
      _addResult('❌ خطأ في اختبار حساب المديونية: $e');
    }
  }

  void _analyzePhoneFormat(String phone, Map<String, int> phoneFormats) {
    String format = '';
    if (phone.startsWith('+2')) {
      format = 'دولي (+2)';
    } else if (phone.startsWith('02')) {
      format = 'محلي (02)';
    } else if (phone.startsWith('01')) {
      format = 'محلي (01)';
    } else if (phone.length == 11) {
      format = '11 رقم';
    } else if (phone.length == 10) {
      format = '10 أرقام';
    } else {
      format = 'غير معروف (${phone.length} رقم)';
    }

    phoneFormats[format] = (phoneFormats[format] ?? 0) + 1;
  }

  void _addResult(String result) {
    setState(() {
      _testResults.add(result);
    });
  }
}
