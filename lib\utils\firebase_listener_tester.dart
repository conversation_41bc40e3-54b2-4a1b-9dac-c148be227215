import 'package:flutter/material.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:mobile_pos/services/firebase_database_service.dart';
import 'package:mobile_pos/services/firebase_listener_manager.dart';
import 'package:mobile_pos/utils/firebase_listener_helper.dart';

/// أداة اختبار لمدير مستمعي Firebase
/// تستخدم لاختبار مدير المستمعين الجديد والتأكد من عدم وجود مشاكل
class FirebaseListenerTester extends StatefulWidget {
  const FirebaseListenerTester({super.key});

  @override
  State<FirebaseListenerTester> createState() => _FirebaseListenerTesterState();
}

class _FirebaseListenerTesterState extends State<FirebaseListenerTester>
    with FirebaseListenerHelper {
  // قائمة المسارات التي تم اختبارها
  final List<String> _testedPaths = [];

  // قائمة الأخطاء التي تم اكتشافها
  final List<String> _errors = [];

  // حالة الاختبار
  bool _isRunning = false;

  // عدد المستمعين النشطة
  int _activeListenersCount = 0;

  @override
  void initState() {
    super.initState();
    _initializeTest();
  }

  /// تهيئة الاختبار
  Future<void> _initializeTest() async {
    // تهيئة خدمة Firebase Database
    await FirebaseDatabaseService.initialize();

    // تهيئة مدير المستمعين
    FirebaseListenerManager().initialize();

    // تحديث عدد المستمعين النشطة
    _updateActiveListenersCount();
  }

  /// تحديث عدد المستمعين النشطة
  void _updateActiveListenersCount() {
    setState(() {
      _activeListenersCount = FirebaseListenerManager().activeListenersCount;
    });
  }

  /// بدء اختبار الاستماع المزدوج
  Future<void> _startDuplicateListenerTest() async {
    setState(() {
      _isRunning = true;
      _testedPaths.clear();
      _errors.clear();
    });

    try {
      // اختبار الاستماع المزدوج لنفس المسار
      await _testDuplicateListeners();

      // اختبار إلغاء المستمعين
      await _testListenerCancellation();

      // اختبار الاستماع المتعدد لمسارات مختلفة
      await _testMultipleListeners();

      // اختبار الاستماع المتزامن
      await _testConcurrentListeners();
    } catch (e) {
      _errors.add('خطأ غير متوقع: $e');
    } finally {
      setState(() {
        _isRunning = false;
      });

      // تحديث عدد المستمعين النشطة
      _updateActiveListenersCount();
    }
  }

  /// اختبار الاستماع المزدوج لنفس المسار
  Future<void> _testDuplicateListeners() async {
    const path = 'test/duplicate_listener';
    _testedPaths.add(path);

    try {
      // إنشاء مستمع أول
      final stream1 = listenToPath(path);

      // إنشاء مستمع ثاني لنفس المسار
      final stream2 = listenToPath(path);

      // التحقق من أن المستمعين يعملان بشكل صحيح
      stream1.listen((event) {
        debugPrint('مستمع 1: تم استلام حدث من المسار $path');
      });

      stream2.listen((event) {
        debugPrint('مستمع 2: تم استلام حدث من المسار $path');
      });

      // إرسال بيانات اختبار
      await FirebaseDatabase.instance.ref(path).set({'test': 'data1'});

      // انتظار لحظة للتأكد من استلام الأحداث
      await Future.delayed(const Duration(seconds: 1));

      // تحديث عدد المستمعين النشطة
      _updateActiveListenersCount();
    } catch (e) {
      _errors.add('خطأ في اختبار الاستماع المزدوج: $e');
    }
  }

  /// اختبار إلغاء المستمعين
  Future<void> _testListenerCancellation() async {
    const path = 'test/cancellation_test';
    _testedPaths.add(path);

    try {
      // إنشاء مستمع
      final stream = listenToPath(path);

      // الاستماع للأحداث
      stream.listen((event) {
        debugPrint('تم استلام حدث من المسار $path');
      });

      // إرسال بيانات اختبار
      await FirebaseDatabase.instance.ref(path).set({'test': 'data2'});

      // انتظار لحظة
      await Future.delayed(const Duration(seconds: 1));

      // إلغاء الاستماع
      await cancelListener(path);

      // تحديث عدد المستمعين النشطة
      _updateActiveListenersCount();

      // إرسال بيانات أخرى (لن يتم استلامها)
      await FirebaseDatabase.instance.ref(path).set({'test': 'data3'});
    } catch (e) {
      _errors.add('خطأ في اختبار إلغاء المستمعين: $e');
    }
  }

  /// اختبار الاستماع المتعدد لمسارات مختلفة
  Future<void> _testMultipleListeners() async {
    final paths = [
      'test/multiple_listener_1',
      'test/multiple_listener_2',
      'test/multiple_listener_3',
    ];

    _testedPaths.addAll(paths);

    try {
      // إنشاء مستمعين لمسارات مختلفة
      for (final path in paths) {
        final stream = listenToPath(path);

        stream.listen((event) {
          debugPrint('تم استلام حدث من المسار $path');
        });

        // إرسال بيانات اختبار
        await FirebaseDatabase.instance
            .ref(path)
            .set({'test': 'data_multiple'});
      }

      // انتظار لحظة
      await Future.delayed(const Duration(seconds: 1));

      // تحديث عدد المستمعين النشطة
      _updateActiveListenersCount();
    } catch (e) {
      _errors.add('خطأ في اختبار الاستماع المتعدد: $e');
    }
  }

  /// اختبار الاستماع المتزامن
  Future<void> _testConcurrentListeners() async {
    const path = 'test/concurrent_listener';
    _testedPaths.add(path);

    try {
      // إنشاء عدة مستمعين في نفس الوقت
      final futures = List.generate(5, (index) {
        return Future(() {
          final stream = listenToPath('$path/$index');

          stream.listen((event) {
            debugPrint('مستمع متزامن $index: تم استلام حدث');
          });

          return index;
        });
      });

      // انتظار اكتمال جميع العمليات
      await Future.wait(futures);

      // إرسال بيانات اختبار
      for (int i = 0; i < 5; i++) {
        await FirebaseDatabase.instance
            .ref('$path/$i')
            .set({'test': 'data_concurrent_$i'});
      }

      // انتظار لحظة
      await Future.delayed(const Duration(seconds: 1));

      // تحديث عدد المستمعين النشطة
      _updateActiveListenersCount();
    } catch (e) {
      _errors.add('خطأ في اختبار الاستماع المتزامن: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار مدير مستمعي Firebase'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'عدد المستمعين النشطة: $_activeListenersCount',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _isRunning ? null : _startDuplicateListenerTest,
              child: Text(_isRunning
                  ? 'جاري الاختبار...'
                  : 'بدء اختبار الاستماع المزدوج'),
            ),
            const SizedBox(height: 16),
            const Text(
              'المسارات التي تم اختبارها:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            Expanded(
              flex: 1,
              child: ListView.builder(
                itemCount: _testedPaths.length,
                itemBuilder: (context, index) {
                  return ListTile(
                    title: Text(_testedPaths[index]),
                    leading:
                        const Icon(Icons.check_circle, color: Colors.green),
                  );
                },
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'الأخطاء:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            Expanded(
              flex: 1,
              child: _errors.isEmpty
                  ? const Center(child: Text('لا توجد أخطاء'))
                  : ListView.builder(
                      itemCount: _errors.length,
                      itemBuilder: (context, index) {
                        return ListTile(
                          title: Text(_errors[index]),
                          leading: const Icon(Icons.error, color: Colors.red),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }
}
