# 🚀 دليل الاستخدام السريع - وحدة الخزينة

## 📱 كيفية الوصول للوحدة

### من أي مكان في التطبيق:
```dart
Navigator.pushNamed(context, '/treasury');
```

### أو باستخدام MaterialPageRoute:
```dart
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const TreasuryMainScreen(),
  ),
);
```

## 🔧 إضافة زر التنقل للخزينة

### في الشاشة الرئيسية أو أي شاشة أخرى:
```dart
ElevatedButton(
  onPressed: () {
    Navigator.pushNamed(context, '/treasury');
  },
  child: Text('إدارة الخزينة'),
)
```

### أو كبطاقة في الشاشة الرئيسية:
```dart
GestureDetector(
  onTap: () => Navigator.pushNamed(context, '/treasury'),
  child: Container(
    padding: EdgeInsets.all(16),
    decoration: BoxDecoration(
      color: kMainColor,
      borderRadius: BorderRadius.circular(12),
    ),
    child: Column(
      children: [
        Icon(FeatherIcons.creditCard, color: Colors.white, size: 32),
        SizedBox(height: 8),
        Text('الخزينة', style: TextStyle(color: Colors.white)),
      ],
    ),
  ),
)
```

## 💡 أمثلة للاستخدام المتقدم

### 1. الحصول على رصيد الخزينة:
```dart
class MyWidget extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final balanceAsync = ref.watch(treasuryBalanceProvider);

    return balanceAsync.when(
      data: (balance) => Text('الرصيد: ${balance.currentBalance}'),
      loading: () => CircularProgressIndicator(),
      error: (error, stack) => Text('خطأ: $error'),
    );
  }
}
```

### 2. عرض آخر المعاملات:
```dart
class RecentTransactions extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final transactionsAsync = ref.watch(treasuryTransactionsProvider);

    return transactionsAsync.when(
      data: (transactions) => ListView.builder(
        itemCount: transactions.length > 5 ? 5 : transactions.length,
        itemBuilder: (context, index) {
          final transaction = transactions[index];
          return ListTile(
            title: Text(transaction.description),
            subtitle: Text(transaction.category),
            trailing: Text('${transaction.amount} جنيه'),
          );
        },
      ),
      loading: () => CircularProgressIndicator(),
      error: (error, stack) => Text('خطأ: $error'),
    );
  }
}
```

### 3. إضافة معاملة برمجياً:
```dart
Future<void> addIncomeTransaction() async {
  final transaction = TreasuryTransactionModel(
    id: '',
    date: DateTime.now().toIso8601String(),
    type: 'income',
    category: 'مبيعات',
    description: 'بيع منتجات',
    amount: '1000',
    paymentMethod: 'نقدي',
    referenceNumber: 'INV-001',
    notes: 'مبيعات اليوم',
    createdBy: 'current_user',
    createdAt: DateTime.now().toIso8601String(),
    updatedAt: DateTime.now().toIso8601String(),
  );

  final treasuryService = TreasuryService();
  final success = await treasuryService.addTransaction(transaction);

  if (success) {
    print('تم إضافة المعاملة بنجاح');
  }
}
```

## 🎯 نصائح للاستخدام الأمثل

### 1. استخدم الفئات المناسبة:
- **للإيرادات**: مبيعات، خدمات، استثمارات، فوائد
- **للمصروفات**: رواتب، إيجار، كهرباء، صيانة، تسويق

### 2. أضف مراجع للمعاملات:
- رقم الفاتورة للمبيعات
- رقم الإيصال للمصروفات
- رقم التحويل للمعاملات البنكية

### 3. استخدم الملاحظات:
- أضف تفاصيل إضافية مهمة
- اذكر اسم العميل أو المورد
- أضف أي معلومات مرجعية

## 📊 إنشاء التقارير

### تقرير سريع لهذا الشهر:
```dart
final reportNotifier = ref.read(generateReportProvider.notifier);
await reportNotifier.generateMonthlyReport(DateTime.now());
```

### تقرير مخصص:
```dart
final startDate = DateTime(2024, 1, 1);
final endDate = DateTime(2024, 12, 31);
await reportNotifier.generateCustomReport(startDate, endDate);
```

## 🔍 البحث والفلترة

### فلترة المعاملات:
```dart
final filters = {
  'type': 'income', // أو 'expense' أو null للكل
  'category': 'مبيعات', // أو null للكل
  'searchQuery': 'فاتورة', // البحث في الوصف
  'startDate': DateTime(2024, 1, 1),
  'endDate': DateTime.now(),
};

final filteredTransactions = ref.watch(filteredTransactionsProvider(filters));
```

## 🚫 إزالة الوحدة

إذا كنت تريد إزالة الوحدة:

1. احذف مجلد `lib/features/treasury/`
2. أزل المسار من `main.dart`:
   ```dart
   '/treasury': (context) => const TreasuryMainScreen(),
   ```
3. أزل الاستيراد من `main.dart`:
   ```dart
   import 'package:mobile_pos/features/treasury/screens/treasury_main_screen.dart';
   ```

## 🆘 استكشاف الأخطاء

### مشكلة: لا تظهر المعاملات
**الحل**: تأكد من أن `constUserId` محدد بشكل صحيح

### مشكلة: خطأ في حفظ المعاملة
**الحل**: تأكد من اتصال الإنترنت وأذونات Firebase

### مشكلة: الرصيد غير صحيح
**الحل**: استخدم `_recalculateBalance()` لإعادة حساب الرصيد

---

**ملاحظة**: هذه الوحدة مصممة لتكون مستقلة تماماً ولا تؤثر على باقي أجزاء التطبيق.
