// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/Screens/Customers/customer_list.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/generated/l10n.dart' as lang;
import 'package:nb_utils/nb_utils.dart';

/// شاشة الأطراف (العملاء والموردين)
class PartiesScreen extends StatefulWidget {
  const PartiesScreen({super.key});

  @override
  State<PartiesScreen> createState() => _PartiesScreenState();
}

class _PartiesScreenState extends State<PartiesScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        title: Text(
          lang.S.of(context).parties,
          style: GoogleFonts.poppins(
            color: Colors.white,
            fontSize: 20.0,
          ),
        ),
        centerTitle: true,
        iconTheme: const IconThemeData(color: Colors.white),
        elevation: 0.0,
      ),
      body: Container(
        padding: const EdgeInsets.all(20.0),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(30.0),
            topRight: Radius.circular(30.0),
          ),
        ),
        child: Column(
          children: [
            const SizedBox(height: 20.0),
            // بطاقة العملاء
            _buildOptionCard(
              title: 'العملاء',
              subtitle: 'إدارة قائمة العملاء والمعلومات الخاصة بهم',
              icon: Icons.people,
              color: const Color(0xFF56da87),
              onTap: () {
                const CustomerList().launch(context);
              },
            ),
            const SizedBox(height: 20.0),
            // بطاقة الموردين
            _buildOptionCard(
              title: 'الموردين',
              subtitle: 'إدارة قائمة الموردين والمعلومات الخاصة بهم',
              icon: Icons.business,
              color: const Color(0xFFA569BD),
              onTap: () {
                // توجيه المستخدم إلى شاشة الموردين
                Navigator.pushNamed(context, '/Purchase');
              },
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة خيار
  Widget _buildOptionCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(15.0),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 20.0,
          vertical: 15.0,
        ),
        leading: Container(
          padding: const EdgeInsets.all(10.0),
          decoration: BoxDecoration(
            color: color.withAlpha(51),
            borderRadius: BorderRadius.circular(10.0),
          ),
          child: Icon(
            icon,
            color: color,
            size: 30.0,
          ),
        ),
        title: Text(
          title,
          style: GoogleFonts.poppins(
            fontSize: 18.0,
            fontWeight: FontWeight.bold,
            color: kMainColor,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: GoogleFonts.poppins(
            fontSize: 14.0,
            color: kGreyTextColor,
          ),
        ),
        trailing: const Icon(
          Icons.arrow_forward_ios,
          color: kGreyTextColor,
          size: 16.0,
        ),
        onTap: onTap,
      ),
    );
  }
}
