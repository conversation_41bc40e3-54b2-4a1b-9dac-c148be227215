// شاشة تقرير المخزون المتقدم
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:mobile_pos/Provider/advanced_inventory_provider.dart';
import 'package:mobile_pos/Provider/report_consistency_provider.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/const_commas.dart';
import 'package:mobile_pos/empty_screen_widget.dart';
import 'package:mobile_pos/models/advanced_inventory_model.dart';
import 'package:mobile_pos/pdf/advanced_inventory_pdf.dart';
import 'package:mobile_pos/widgets/inventory_period_selector.dart';
import 'package:nb_utils/nb_utils.dart';

/// شاشة تقرير المخزون المتقدم
class AdvancedInventoryReport extends ConsumerStatefulWidget {
  const AdvancedInventoryReport({super.key});

  @override
  ConsumerState<AdvancedInventoryReport> createState() =>
      _AdvancedInventoryReportState();
}

class _AdvancedInventoryReportState
    extends ConsumerState<AdvancedInventoryReport> {
  final TextEditingController _searchController = TextEditingController();
  String? _selectedCategory;
  String? _selectedBrand;
  InventorySortBy _sortBy = InventorySortBy.name;
  bool _sortAscending = true;

  @override
  void initState() {
    super.initState();

    // تحقق من اتساق بيانات المخزون
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(reportConsistencyProvider).checkInventoryReportConsistency();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final inventoryData = ref.watch(advancedInventoryProvider);
    final consistencyState = ref.watch(reportConsistencyStateProvider);
    final notifier = ref.read(advancedInventoryProvider.notifier);
    final period = ref.watch(inventoryPeriodProvider);

    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        title: Text(
          'تقرير المخزون المتقدم',
          style: GoogleFonts.poppins(
            color: Colors.white,
            fontSize: 20.0,
          ),
        ),
        iconTheme: const IconThemeData(color: Colors.white),
        centerTitle: true,
        backgroundColor: kMainColor,
        elevation: 0.0,
        actions: [
          IconButton(
            icon: const Icon(Icons.download_outlined),
            onPressed: () async {
              if (inventoryData.value != null &&
                  inventoryData.value!.isNotEmpty) {
                final pdfGenerator = AdvancedInventoryPdfGenerator();
                await pdfGenerator.generatePdf(
                  inventoryData.value!,
                  period.startDate,
                  period.endDate,
                  context,
                );
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('لا توجد بيانات لتصدير التقرير'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
          ),
        ],
      ),
      body: Container(
        alignment: Alignment.topCenter,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(30),
            topLeft: Radius.circular(30),
          ),
        ),
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            children: [
              const SizedBox(height: 20),

              // اختيار الفترة
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 15),
                child: InventoryPeriodSelector(
                  onPeriodChanged: (startDate, endDate) {
                    notifier.updatePeriod(startDate, endDate);
                  },
                ),
              ),

              const SizedBox(height: 20),

              // مؤشر دقة البيانات
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 15),
                child: Card(
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(15),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            const Icon(
                              Icons.analytics_outlined,
                              color: kMainColor,
                              size: 24,
                            ),
                            const SizedBox(width: 10),
                            const Text(
                              'دقة بيانات المخزون',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            const Spacer(),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: consistencyState.inconsistentItems > 0
                                    ? const Color(0xFFFFEBEE)
                                    : const Color(0xFFE8F5E9),
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    consistencyState.inconsistentItems > 0
                                        ? Icons.error_outline
                                        : Icons.check_circle_outline,
                                    color:
                                        consistencyState.inconsistentItems > 0
                                            ? Colors.red
                                            : Colors.green,
                                    size: 16,
                                  ),
                                  const SizedBox(width: 5),
                                  Text(
                                    consistencyState.inconsistentItems > 0
                                        ? 'توجد مشاكل'
                                        : 'متسقة',
                                    style: TextStyle(
                                      color:
                                          consistencyState.inconsistentItems > 0
                                              ? Colors.red
                                              : Colors.green,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 15),
                        LinearProgressIndicator(
                          value: consistencyState.totalChecks > 0
                              ? ((consistencyState.totalChecks -
                                      consistencyState.inconsistentItems) /
                                  consistencyState.totalChecks)
                              : 1.0,
                          minHeight: 10,
                          backgroundColor: Colors.grey.shade200,
                          color: consistencyState.inconsistentItems > 0
                              ? Colors.orange
                              : Colors.green,
                          borderRadius: BorderRadius.circular(5),
                        ),
                        const SizedBox(height: 10),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'نسبة الدقة: ${consistencyState.totalChecks > 0 ? ((consistencyState.totalChecks - consistencyState.inconsistentItems) / consistencyState.totalChecks * 100).toStringAsFixed(1) : '100'}%',
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              'عناصر غير متسقة: ${consistencyState.inconsistentItems} من ${consistencyState.totalChecks}',
                              style: const TextStyle(
                                fontSize: 14,
                                color: kGreyTextColor,
                              ),
                            ),
                          ],
                        ),
                        if (consistencyState.inconsistentItems > 0) ...[
                          const SizedBox(height: 15),
                          InkWell(
                            onTap: () {
                              // عرض تفاصيل عدم الاتساق
                              showDialog(
                                context: context,
                                builder: (context) => AlertDialog(
                                  title:
                                      const Text('تفاصيل عدم اتساق البيانات'),
                                  content: SingleChildScrollView(
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                            'عدد العناصر غير المتسقة: ${consistencyState.inconsistentItems}'),
                                        const SizedBox(height: 10),
                                        ...consistencyState.inconsistencyResults
                                            .map((result) {
                                          return ListTile(
                                            title: Text(result['name'] ?? ''),
                                            subtitle:
                                                Text(result['details'] ?? ''),
                                            contentPadding: EdgeInsets.zero,
                                            leading: const Icon(
                                                Icons.error_outline,
                                                color: Colors.red),
                                          );
                                        }),
                                      ],
                                    ),
                                  ),
                                  actions: [
                                    TextButton(
                                      onPressed: () {
                                        Navigator.pop(context);
                                      },
                                      child: const Text('إغلاق'),
                                    ),
                                  ],
                                ),
                              );
                            },
                            child: Container(
                              width: double.infinity,
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              decoration: BoxDecoration(
                                color: const Color(0xFFE3F2FD),
                                borderRadius: BorderRadius.circular(5),
                                border: Border.all(color: kMainColor),
                              ),
                              child: const Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.info_outline, color: kMainColor),
                                  SizedBox(width: 10),
                                  Text(
                                    'عرض تفاصيل المشاكل',
                                    style: TextStyle(
                                      color: kMainColor,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 20),

              // البحث والتصفية
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 15),
                child: Row(
                  children: [
                    Expanded(
                      child: AppTextField(
                        textFieldType: TextFieldType.NAME,
                        controller: _searchController,
                        decoration: InputDecoration(
                          labelText: 'بحث',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(5),
                          ),
                          prefixIcon: const Icon(Icons.search),
                        ),
                        onChanged: (value) {
                          notifier.updateFilter(InventoryFilter(
                            category: _selectedCategory,
                            brand: _selectedBrand,
                            searchQuery: value,
                            sortBy: _sortBy,
                            sortAscending: _sortAscending,
                          ));
                        },
                      ),
                    ),
                    const SizedBox(width: 10),
                    PopupMenuButton<String>(
                      icon: const Icon(Icons.filter_list),
                      tooltip: 'تصفية',
                      onSelected: (value) {
                        if (value == 'clear') {
                          setState(() {
                            _selectedCategory = null;
                            _selectedBrand = null;
                            _searchController.clear();
                          });

                          notifier.updateFilter(InventoryFilter(
                            category: null,
                            brand: null,
                            searchQuery: '',
                            sortBy: _sortBy,
                            sortAscending: _sortAscending,
                          ));
                        }
                      },
                      itemBuilder: (context) => [
                        PopupMenuItem(
                          value: 'category',
                          child: const Text('تصفية حسب الفئة'),
                          onTap: () {
                            // عرض قائمة الفئات
                            WidgetsBinding.instance.addPostFrameCallback((_) {
                              _showCategoryFilterDialog(
                                  notifier.getCategories());
                            });
                          },
                        ),
                        PopupMenuItem(
                          value: 'brand',
                          child: const Text('تصفية حسب العلامة التجارية'),
                          onTap: () {
                            // عرض قائمة العلامات التجارية
                            WidgetsBinding.instance.addPostFrameCallback((_) {
                              _showBrandFilterDialog(notifier.getBrands());
                            });
                          },
                        ),
                        const PopupMenuDivider(),
                        const PopupMenuItem(
                          value: 'clear',
                          child: Text('مسح التصفية'),
                        ),
                      ],
                    ),
                    const SizedBox(width: 10),
                    PopupMenuButton<InventorySortBy>(
                      icon: const Icon(Icons.sort),
                      tooltip: 'ترتيب',
                      onSelected: (value) {
                        setState(() {
                          if (_sortBy == value) {
                            _sortAscending = !_sortAscending;
                          } else {
                            _sortBy = value;
                            _sortAscending = true;
                          }
                        });

                        notifier.updateFilter(InventoryFilter(
                          category: _selectedCategory,
                          brand: _selectedBrand,
                          searchQuery: _searchController.text,
                          sortBy: _sortBy,
                          sortAscending: _sortAscending,
                        ));
                      },
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: InventorySortBy.name,
                          child: Text('ترتيب حسب الاسم'),
                        ),
                        const PopupMenuItem(
                          value: InventorySortBy.code,
                          child: Text('ترتيب حسب الكود'),
                        ),
                        const PopupMenuItem(
                          value: InventorySortBy.category,
                          child: Text('ترتيب حسب الفئة'),
                        ),
                        const PopupMenuItem(
                          value: InventorySortBy.openingStock,
                          child: Text('ترتيب حسب رصيد أول المدة'),
                        ),
                        const PopupMenuItem(
                          value: InventorySortBy.closingStock,
                          child: Text('ترتيب حسب رصيد آخر المدة'),
                        ),
                        const PopupMenuItem(
                          value: InventorySortBy.sales,
                          child: Text('ترتيب حسب المبيعات'),
                        ),
                        const PopupMenuItem(
                          value: InventorySortBy.purchases,
                          child: Text('ترتيب حسب المشتريات'),
                        ),
                        const PopupMenuItem(
                          value: InventorySortBy.turnoverRate,
                          child: Text('ترتيب حسب معدل الدوران'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 20),

              // ملخص التقرير
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 15),
                child: inventoryData.when(
                  data: (data) {
                    if (data.isEmpty) {
                      return const SizedBox();
                    }

                    return _buildSummaryCard(notifier);
                  },
                  loading: () => const SizedBox(),
                  error: (_, __) => const SizedBox(),
                ),
              ),

              const SizedBox(height: 20),

              // عنوان الجدول
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 15),
                child: Row(
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'تفاصيل المخزون',
                          style: GoogleFonts.poppins(
                            color: kMainColor,
                            fontSize: 16.0,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'الفترة: ${DateFormat('yyyy/MM/dd').format(period.startDate)} - ${DateFormat('yyyy/MM/dd').format(period.endDate)}',
                          style: GoogleFonts.poppins(
                            color: kGreyTextColor,
                            fontSize: 12.0,
                          ),
                        ),
                      ],
                    ),
                    const Spacer(),
                    if (_selectedCategory != null || _selectedBrand != null)
                      Text(
                        _getFilterText(),
                        style: GoogleFonts.poppins(
                          color: kGreyTextColor,
                          fontSize: 12.0,
                        ),
                      ),
                  ],
                ),
              ),

              const SizedBox(height: 10),

              // جدول البيانات
              inventoryData.when(
                data: (data) {
                  if (data.isEmpty) {
                    return const Padding(
                      padding: EdgeInsets.all(20),
                      child: EmptyScreenWidget(),
                    );
                  }

                  return ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    padding: const EdgeInsets.symmetric(horizontal: 15),
                    itemCount: data.length,
                    itemBuilder: (context, index) {
                      return _buildInventoryCard(data[index]);
                    },
                  );
                },
                loading: () => const Center(
                  child: Padding(
                    padding: EdgeInsets.all(20),
                    child: CircularProgressIndicator(),
                  ),
                ),
                error: (error, stackTrace) => Center(
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Text(
                      'حدث خطأ: $error',
                      style: const TextStyle(
                        color: Colors.red,
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSummaryCard(AdvancedInventoryNotifier notifier) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      child: Padding(
        padding: const EdgeInsets.all(15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص التقرير',
              style: GoogleFonts.poppins(
                color: kMainColor,
                fontSize: 16.0,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 15),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    title: 'رصيد أول المدة',
                    value:
                        myFormat.format(notifier.getTotalOpeningStockValue()),
                    subtitle: 'القيمة',
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    title: 'رصيد آخر المدة',
                    value:
                        myFormat.format(notifier.getTotalClosingStockValue()),
                    subtitle: 'القيمة',
                  ),
                ),
              ],
            ),
            const SizedBox(height: 15),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    title: 'المشتريات',
                    value: myFormat.format(notifier.getTotalPurchasesValue()),
                    subtitle: 'القيمة',
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    title: 'المبيعات',
                    value: myFormat.format(notifier.getTotalSalesValue()),
                    subtitle: 'القيمة',
                  ),
                ),
              ],
            ),
            const SizedBox(height: 15),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    title: 'الربح الإجمالي',
                    value: myFormat.format(notifier.getTotalGrossProfit()),
                    subtitle: 'القيمة',
                  ),
                ),
                const Expanded(
                  child: SizedBox(),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem({
    required String title,
    required String value,
    required String subtitle,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: GoogleFonts.poppins(
            color: kGreyTextColor,
            fontSize: 12.0,
          ),
        ),
        const SizedBox(height: 5),
        Text(
          value,
          style: GoogleFonts.poppins(
            color: kMainColor,
            fontSize: 16.0,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          subtitle,
          style: GoogleFonts.poppins(
            color: kGreyTextColor,
            fontSize: 10.0,
          ),
        ),
      ],
    );
  }

  Widget _buildInventoryCard(AdvancedInventoryModel inventory) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      child: ExpansionTile(
        title: Text(
          inventory.productName,
          style: GoogleFonts.poppins(
            color: kMainColor,
            fontSize: 14.0,
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Text(
          'كود: ${inventory.productCode} | فئة: ${inventory.category}',
          style: GoogleFonts.poppins(
            color: kGreyTextColor,
            fontSize: 12.0,
          ),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(15),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Divider(),
                const SizedBox(height: 10),
                Row(
                  children: [
                    Expanded(
                      child: _buildInventoryItem(
                        title: 'رصيد أول المدة',
                        value: inventory.openingStock.toString(),
                        subtitle: myFormat.format(inventory.openingStockValue),
                      ),
                    ),
                    Expanded(
                      child: _buildInventoryItem(
                        title: 'رصيد آخر المدة',
                        value: inventory.closingStock.toString(),
                        subtitle: myFormat.format(inventory.closingStockValue),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 15),
                Row(
                  children: [
                    Expanded(
                      child: _buildInventoryItem(
                        title: 'المشتريات',
                        value: inventory.purchases.toString(),
                        subtitle: myFormat.format(inventory.purchasesValue),
                      ),
                    ),
                    Expanded(
                      child: _buildInventoryItem(
                        title: 'المبيعات',
                        value: inventory.sales.toString(),
                        subtitle: myFormat.format(inventory.salesValue),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 15),
                Row(
                  children: [
                    Expanded(
                      child: _buildInventoryItem(
                        title: 'معدل الدوران',
                        value: inventory.turnoverRate.toStringAsFixed(2),
                        subtitle: 'مرة',
                      ),
                    ),
                    Expanded(
                      child: _buildInventoryItem(
                        title: 'أيام المخزون',
                        value: inventory.daysInStock.toStringAsFixed(0),
                        subtitle: 'يوم',
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 15),
                Row(
                  children: [
                    Expanded(
                      child: _buildInventoryItem(
                        title: 'الربح الإجمالي',
                        value: myFormat.format(inventory.grossProfit),
                        subtitle:
                            'نسبة الربح: ${inventory.profitMargin.toStringAsFixed(2)}%',
                      ),
                    ),
                    const Expanded(
                      child: SizedBox(),
                    ),
                  ],
                ),
                const SizedBox(height: 15),
                const Divider(),
                const SizedBox(height: 10),
                Text(
                  'حركات المخزون',
                  style: GoogleFonts.poppins(
                    color: kMainColor,
                    fontSize: 14.0,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 10),
                if (inventory.movements.isEmpty)
                  const Text(
                    'لا توجد حركات مخزون خلال هذه الفترة',
                    style: TextStyle(
                      color: kGreyTextColor,
                      fontSize: 12.0,
                    ),
                  )
                else
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: inventory.movements.length,
                    itemBuilder: (context, index) {
                      final movement = inventory.movements[index];
                      return ListTile(
                        contentPadding: EdgeInsets.zero,
                        title: Text(
                          movement.type == 'purchase' ? 'مشتريات' : 'مبيعات',
                          style: GoogleFonts.poppins(
                            color: movement.type == 'purchase'
                                ? Colors.green
                                : Colors.red,
                            fontSize: 14.0,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        subtitle: Text(
                          'رقم المرجع: ${movement.reference} | التاريخ: ${DateFormat('yyyy/MM/dd').format(movement.date)}',
                          style: GoogleFonts.poppins(
                            color: kGreyTextColor,
                            fontSize: 12.0,
                          ),
                        ),
                        trailing: Text(
                          '${movement.quantity.abs()} × ${myFormat.format(movement.price)} = ${myFormat.format(movement.value.abs())}',
                          style: GoogleFonts.poppins(
                            color: kMainColor,
                            fontSize: 12.0,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      );
                    },
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInventoryItem({
    required String title,
    required String value,
    required String subtitle,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: GoogleFonts.poppins(
            color: kGreyTextColor,
            fontSize: 12.0,
          ),
        ),
        const SizedBox(height: 5),
        Text(
          value,
          style: GoogleFonts.poppins(
            color: kMainColor,
            fontSize: 14.0,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          subtitle,
          style: GoogleFonts.poppins(
            color: kGreyTextColor,
            fontSize: 10.0,
          ),
        ),
      ],
    );
  }

  void _showCategoryFilterDialog(List<String> categories) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر الفئة'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: categories.length,
            itemBuilder: (context, index) {
              return ListTile(
                title: Text(categories[index]),
                onTap: () {
                  setState(() {
                    _selectedCategory = categories[index];
                  });

                  ref
                      .read(advancedInventoryProvider.notifier)
                      .updateFilter(InventoryFilter(
                        category: _selectedCategory,
                        brand: _selectedBrand,
                        searchQuery: _searchController.text,
                        sortBy: _sortBy,
                        sortAscending: _sortAscending,
                      ));

                  Navigator.pop(context);
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _showBrandFilterDialog(List<String> brands) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر العلامة التجارية'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: brands.length,
            itemBuilder: (context, index) {
              return ListTile(
                title: Text(brands[index]),
                onTap: () {
                  setState(() {
                    _selectedBrand = brands[index];
                  });

                  ref
                      .read(advancedInventoryProvider.notifier)
                      .updateFilter(InventoryFilter(
                        category: _selectedCategory,
                        brand: _selectedBrand,
                        searchQuery: _searchController.text,
                        sortBy: _sortBy,
                        sortAscending: _sortAscending,
                      ));

                  Navigator.pop(context);
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  String _getFilterText() {
    final List<String> filters = [];

    if (_selectedCategory != null) {
      filters.add('الفئة: $_selectedCategory');
    }

    if (_selectedBrand != null) {
      filters.add('العلامة التجارية: $_selectedBrand');
    }

    return filters.join(' | ');
  }
}
