import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mobile_pos/Provider/quotation_provider.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/models/quotation_model.dart';
import 'package:nb_utils/nb_utils.dart';

class QuotationListScreen extends StatefulWidget {
  const QuotationListScreen({super.key});

  static const String route = '/quotation_list';

  @override
  State<QuotationListScreen> createState() => _QuotationListScreenState();
}

class _QuotationListScreenState extends State<QuotationListScreen> {
  String searchQuery = '';
  String selectedFilter = 'all';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('قائمة عروض الأسعار'),
        backgroundColor: kMainColor,
        centerTitle: true,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        titleTextStyle: const TextStyle(
            color: Colors.white, fontSize: 20, fontWeight: FontWeight.bold),
      ),
      body: Consumer(
        builder: (context, ref, _) {
          final quotationsAsyncValue = ref.watch(quotationListProvider);

          return quotationsAsyncValue.when(
            data: (quotations) {
              // تطبيق البحث والفلترة
              List<QuotationModel> filteredQuotations =
                  quotations.where((quotation) {
                // تطبيق البحث
                bool matchesSearch = searchQuery.isEmpty ||
                    quotation.customerName
                        .toLowerCase()
                        .contains(searchQuery.toLowerCase()) ||
                    quotation.id
                        .toLowerCase()
                        .contains(searchQuery.toLowerCase());

                // تطبيق الفلترة
                bool matchesFilter = selectedFilter == 'all' ||
                    quotation.status == selectedFilter;

                return matchesSearch && matchesFilter;
              }).toList();

              return Column(
                children: [
                  // شريط البحث والفلترة
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 3,
                          child: TextField(
                            decoration: InputDecoration(
                              hintText: 'بحث بالاسم أو رقم العرض',
                              prefixIcon: const Icon(Icons.search),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                            onChanged: (value) {
                              setState(() {
                                searchQuery = value;
                              });
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          flex: 2,
                          child: DropdownButtonFormField<String>(
                            decoration: InputDecoration(
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                            value: selectedFilter,
                            items: const [
                              DropdownMenuItem(
                                  value: 'all', child: Text('جميع العروض')),
                              DropdownMenuItem(
                                  value: 'pending',
                                  child: Text('قيد الانتظار')),
                              DropdownMenuItem(
                                  value: 'accepted', child: Text('مقبولة')),
                              DropdownMenuItem(
                                  value: 'rejected', child: Text('مرفوضة')),
                            ],
                            onChanged: (value) {
                              setState(() {
                                selectedFilter = value!;
                              });
                            },
                          ),
                        ),
                      ],
                    ),
                  ),

                  // قائمة عروض الأسعار
                  Expanded(
                    child: filteredQuotations.isEmpty
                        ? const Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.receipt_long,
                                    size: 80, color: Colors.grey),
                                SizedBox(height: 16),
                                Text(
                                  'لا توجد عروض أسعار',
                                  style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold),
                                ),
                                SizedBox(height: 8),
                                Text(
                                  'لم يتم العثور على عروض أسعار مطابقة',
                                  style: TextStyle(color: Colors.grey),
                                ),
                              ],
                            ),
                          )
                        : ListView.builder(
                            itemCount: filteredQuotations.length,
                            itemBuilder: (context, index) {
                              final quotation = filteredQuotations[index];
                              return QuotationCard(
                                quotation: quotation,
                                onDelete: () =>
                                    _deleteQuotation(ref, quotation),
                                onConvert: () =>
                                    _convertToInvoice(ref, quotation),
                                onView: () => _viewQuotation(quotation),
                              );
                            },
                          ),
                  ),
                ],
              );
            },
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stack) => Center(
              child: Text('حدث خطأ: $error',
                  style: const TextStyle(color: Colors.red)),
            ),
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: kMainColor,
        onPressed: () {
          _createNewQuotation(context);
        },
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  void _deleteQuotation(WidgetRef ref, QuotationModel quotation) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف عرض السعر هذا؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              EasyLoading.show(status: 'جاري الحذف...');
              try {
                await ref
                    .read(quotationNotifierProvider.notifier)
                    .deleteQuotation(quotation.id);
                EasyLoading.showSuccess('تم حذف عرض السعر بنجاح');
              } catch (e) {
                EasyLoading.showError('حدث خطأ أثناء الحذف: $e');
              }
            },
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _convertToInvoice(WidgetRef ref, QuotationModel quotation) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تحويل إلى فاتورة'),
        content: const Text('هل تريد تحويل عرض السعر هذا إلى فاتورة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              EasyLoading.show(status: 'جاري التحويل...');
              try {
                await ref
                    .read(quotationNotifierProvider.notifier)
                    .convertToInvoice(quotation);
                EasyLoading.showSuccess('تم تحويل عرض السعر إلى فاتورة بنجاح');
              } catch (e) {
                EasyLoading.showError('حدث خطأ أثناء التحويل: $e');
              }
            },
            child: const Text('تحويل', style: TextStyle(color: Colors.green)),
          ),
        ],
      ),
    );
  }

  void _viewQuotation(QuotationModel quotation) {
    // عرض تفاصيل عرض السعر
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        height: MediaQuery.of(context).size.height * 0.8,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'تفاصيل عرض السعر',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 10),
            _buildDetailRow('رقم العرض:', quotation.id),
            _buildDetailRow('العميل:', quotation.customerName),
            _buildDetailRow('الهاتف:', quotation.customerPhone),
            _buildDetailRow('التاريخ:', _formatDate(quotation.date)),
            _buildDetailRow('الإجمالي:', '${quotation.totalAmount} ريال'),
            _buildDetailRow('الخصم:', '${quotation.discount} ريال'),
            _buildDetailRow('الضريبة:', '${quotation.tax} ريال'),
            _buildDetailRow('المبلغ النهائي:', '${quotation.finalAmount} ريال'),
            _buildDetailRow('الحالة:', _getStatusText(quotation.status)),
            const SizedBox(height: 20),
            const Text(
              'المنتجات:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 10),
            Expanded(
              child: ListView.builder(
                itemCount: quotation.items.length,
                itemBuilder: (context, index) {
                  final item = quotation.items[index];
                  return Card(
                    elevation: 2,
                    margin: const EdgeInsets.only(bottom: 10),
                    child: ListTile(
                      title: Text(item.productName),
                      subtitle: Text(
                          'الكمية: ${item.quantity} × ${item.unitPrice} ريال'),
                      trailing: Text(
                        '${item.totalPrice} ريال',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.year}/${date.month}/${date.day}';
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'pending':
        return 'قيد الانتظار';
      case 'accepted':
        return 'مقبول';
      case 'rejected':
        return 'مرفوض';
      case 'converted':
        return 'تم التحويل إلى فاتورة';
      default:
        return status;
    }
  }

  void _createNewQuotation(BuildContext context) {
    // انتقل إلى شاشة إنشاء عرض سعر جديد
    // Navigator.pushNamed(context, CreateQuotationScreen.route);
    toast('سيتم تنفيذ هذه الميزة قريبًا');
  }
}

class QuotationCard extends StatelessWidget {
  final QuotationModel quotation;
  final VoidCallback onDelete;
  final VoidCallback onConvert;
  final VoidCallback onView;

  const QuotationCard({
    super.key,
    required this.quotation,
    required this.onDelete,
    required this.onConvert,
    required this.onView,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'عرض سعر #${quotation.id}',
                  style: const TextStyle(
                      fontWeight: FontWeight.bold, fontSize: 16),
                ),
                _buildStatusBadge(quotation.status),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                const Icon(Icons.person, size: 16, color: Colors.grey),
                const SizedBox(width: 8),
                Text(quotation.customerName),
                const SizedBox(width: 16),
                const Icon(Icons.phone, size: 16, color: Colors.grey),
                const SizedBox(width: 8),
                Text(quotation.customerPhone),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.calendar_today, size: 16, color: Colors.grey),
                const SizedBox(width: 8),
                Text(_formatDate(quotation.date)),
                const SizedBox(width: 16),
                const Icon(Icons.attach_money, size: 16, color: Colors.grey),
                const SizedBox(width: 8),
                Text('${quotation.finalAmount} ريال'),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  onPressed: onView,
                  icon: const Icon(Icons.visibility, size: 18),
                  label: const Text('عرض'),
                  style: TextButton.styleFrom(foregroundColor: Colors.blue),
                ),
                TextButton.icon(
                  onPressed: onDelete,
                  icon: const Icon(Icons.delete, size: 18),
                  label: const Text('حذف'),
                  style: TextButton.styleFrom(foregroundColor: Colors.red),
                ),
                TextButton.icon(
                  onPressed: quotation.status == 'converted' ? null : onConvert,
                  icon: const Icon(Icons.transform, size: 18),
                  label: const Text('تحويل إلى فاتورة'),
                  style: TextButton.styleFrom(
                    foregroundColor: quotation.status == 'converted'
                        ? Colors.grey
                        : Colors.green,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusBadge(String status) {
    Color color;
    String text;

    switch (status) {
      case 'pending':
        color = Colors.orange;
        text = 'قيد الانتظار';
        break;
      case 'accepted':
        color = Colors.green;
        text = 'مقبول';
        break;
      case 'rejected':
        color = Colors.red;
        text = 'مرفوض';
        break;
      case 'converted':
        color = Colors.blue;
        text = 'تم التحويل';
        break;
      default:
        color = Colors.grey;
        text = status;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withAlpha(51),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(
        text,
        style:
            TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.bold),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.year}/${date.month}/${date.day}';
  }
}
