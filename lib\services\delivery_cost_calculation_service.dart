import 'dart:math';
import 'package:flutter/material.dart';
import '../model/delivery_trip_model.dart';
import '../model/delivery_vehicle_model.dart';
import '../model/delivery_cost_model.dart';

class DeliveryCostCalculationService {
  
  /// حساب تكاليف الرحلة الكاملة
  static DeliveryCostModel calculateTripCosts({
    required DeliveryTripModel trip,
    required DeliveryVehicleModel vehicle,
    required double fuelPricePerLiter,
    required double oilPricePerLiter,
  }) {
    try {
      // حساب المسافة المقطوعة
      final distance = _calculateDistance(
        double.parse(trip.startMeterReading),
        double.parse(trip.endMeterReading),
      );

      // حساب استهلاك البنزين
      final fuelConsumption = _calculateFuelConsumption(
        distance,
        double.parse(vehicle.fuelConsumptionRate),
      );

      // حساب تكلفة البنزين
      final fuelCost = _calculateFuelCost(
        fuelConsumption,
        fuelPricePerLiter,
      );

      // حساب تكلفة الزيت
      final oilCost = _calculateOilCost(
        distance,
        double.parse(vehicle.oilChangeInterval),
        oilPricePerLiter,
      );

      // حساب تكلفة الصيانة
      final maintenanceCost = _calculateMaintenanceCost(
        distance,
        double.parse(vehicle.maintenanceCostPerKm),
      );

      // حساب راتب السائق
      final driverSalary = _calculateDriverSalary(
        distance,
        double.parse(vehicle.driverSalaryPerKm),
      );

      // حساب إجمالي التكلفة
      final totalCost = fuelCost + oilCost + maintenanceCost + driverSalary;

      // حساب تكلفة الكيلومتر
      final costPerKm = distance > 0 ? totalCost / distance : 0.0;

      return DeliveryCostModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        tripId: trip.id,
        distanceTraveled: distance.toStringAsFixed(2),
        fuelPrice: fuelPricePerLiter.toStringAsFixed(2),
        fuelConsumption: fuelConsumption.toStringAsFixed(2),
        fuelCost: fuelCost.toStringAsFixed(2),
        oilCost: oilCost.toStringAsFixed(2),
        maintenanceCost: maintenanceCost.toStringAsFixed(2),
        driverSalary: driverSalary.toStringAsFixed(2),
        otherCosts: '0.00',
        totalCost: totalCost.toStringAsFixed(2),
        costPerKm: costPerKm.toStringAsFixed(2),
        calculationDate: DateTime.now().toIso8601String(),
        notes: 'تم الحساب تلقائياً',
      );

    } catch (e) {
      debugPrint('خطأ في حساب التكاليف: $e');
      return DeliveryCostModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        tripId: trip.id,
        distanceTraveled: '0.00',
        fuelPrice: '0.00',
        fuelConsumption: '0.00',
        fuelCost: '0.00',
        oilCost: '0.00',
        maintenanceCost: '0.00',
        driverSalary: '0.00',
        otherCosts: '0.00',
        totalCost: '0.00',
        costPerKm: '0.00',
        calculationDate: DateTime.now().toIso8601String(),
        notes: 'خطأ في الحساب: $e',
      );
    }
  }

  /// حساب المسافة المقطوعة
  static double _calculateDistance(double startReading, double endReading) {
    if (endReading < startReading) {
      // في حالة إعادة تصفير العداد
      return endReading + (999999 - startReading);
    }
    return endReading - startReading;
  }

  /// حساب استهلاك البنزين
  static double _calculateFuelConsumption(double distance, double consumptionRate) {
    if (consumptionRate <= 0) return 0.0;
    return distance / consumptionRate;
  }

  /// حساب تكلفة البنزين
  static double _calculateFuelCost(double fuelConsumption, double fuelPrice) {
    return fuelConsumption * fuelPrice;
  }

  /// حساب تكلفة الزيت
  static double _calculateOilCost(double distance, double oilChangeInterval, double oilPrice) {
    if (oilChangeInterval <= 0) return 0.0;
    
    // حساب نسبة المسافة من فترة تغيير الزيت
    final oilUsageRatio = distance / oilChangeInterval;
    
    // تكلفة الزيت الكاملة (افتراض 4 لتر زيت)
    final fullOilCost = oilPrice * 4;
    
    return fullOilCost * oilUsageRatio;
  }

  /// حساب تكلفة الصيانة
  static double _calculateMaintenanceCost(double distance, double maintenanceCostPerKm) {
    return distance * maintenanceCostPerKm;
  }

  /// حساب راتب السائق
  static double _calculateDriverSalary(double distance, double salaryPerKm) {
    return distance * salaryPerKm;
  }

  /// حساب متوسط استهلاك البنزين لعدة رحلات
  static double calculateAverageFuelConsumption(List<DeliveryCostModel> trips) {
    if (trips.isEmpty) return 0.0;

    double totalDistance = 0.0;
    double totalFuelConsumption = 0.0;

    for (var trip in trips) {
      totalDistance += double.parse(trip.distanceTraveled);
      totalFuelConsumption += double.parse(trip.fuelConsumption);
    }

    if (totalFuelConsumption == 0) return 0.0;
    return totalDistance / totalFuelConsumption;
  }

  /// حساب إجمالي التكاليف لفترة معينة
  static Map<String, double> calculatePeriodTotals(List<DeliveryCostModel> trips) {
    double totalDistance = 0.0;
    double totalFuelCost = 0.0;
    double totalOilCost = 0.0;
    double totalMaintenanceCost = 0.0;
    double totalDriverSalary = 0.0;
    double totalCost = 0.0;

    for (var trip in trips) {
      totalDistance += double.parse(trip.distanceTraveled);
      totalFuelCost += double.parse(trip.fuelCost);
      totalOilCost += double.parse(trip.oilCost);
      totalMaintenanceCost += double.parse(trip.maintenanceCost);
      totalDriverSalary += double.parse(trip.driverSalary);
      totalCost += double.parse(trip.totalCost);
    }

    return {
      'totalDistance': totalDistance,
      'totalFuelCost': totalFuelCost,
      'totalOilCost': totalOilCost,
      'totalMaintenanceCost': totalMaintenanceCost,
      'totalDriverSalary': totalDriverSalary,
      'totalCost': totalCost,
      'averageCostPerKm': totalDistance > 0 ? totalCost / totalDistance : 0.0,
    };
  }

  /// التحقق من الحاجة لتغيير الزيت
  static bool needsOilChange(DeliveryVehicleModel vehicle, double currentMeterReading) {
    try {
      final lastOilChange = double.parse(vehicle.lastOilChange);
      final oilChangeInterval = double.parse(vehicle.oilChangeInterval);
      final distanceSinceLastChange = currentMeterReading - lastOilChange;
      
      return distanceSinceLastChange >= oilChangeInterval;
    } catch (e) {
      return false;
    }
  }

  /// حساب المسافة المتبقية لتغيير الزيت
  static double remainingDistanceForOilChange(DeliveryVehicleModel vehicle, double currentMeterReading) {
    try {
      final lastOilChange = double.parse(vehicle.lastOilChange);
      final oilChangeInterval = double.parse(vehicle.oilChangeInterval);
      final distanceSinceLastChange = currentMeterReading - lastOilChange;
      
      return max(0, oilChangeInterval - distanceSinceLastChange);
    } catch (e) {
      return 0.0;
    }
  }
}
