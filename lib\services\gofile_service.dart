import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:path/path.dart' as path;

/// خدمة للتعامل مع رفع الملفات إلى Gofile.io
class GoFileService {
  // معلومات حساب GoFile
  static const String _accountToken = 'w4MTMPLpxFYsHoxndRQc5QoiBJeUAWKv';

  // عنوان الحصول على أفضل خادم
  static const String _getServerUrl = 'https://api.gofile.io/getServer';

  // عنوان رفع الملفات الافتراضي
  static const String _defaultUploadUrl = 'https://store1.gofile.io/uploadFile';

  /// الحصول على أفضل خادم للرفع
  static Future<String> _getBestServer() async {
    try {
      final response = await http.get(Uri.parse(_getServerUrl));
      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        if (jsonResponse['status'] == 'ok') {
          final server = jsonResponse['data']['server'];
          if (kDebugMode) {
            debugPrint('تم الحصول على أفضل خادم: $server');
          }
          return 'https://$server.gofile.io/uploadFile';
        }
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('خطأ في الحصول على الخادم: $e');
      }
    }
    // العودة للخادم الافتراضي في حالة الخطأ
    return _defaultUploadUrl;
  }

  /// رفع ملف إلى Gofile.io
  ///
  /// يقوم برفع الملف إلى Gofile.io ويعيد رابط الملف المرفوع
  ///
  /// [file] الملف المراد رفعه
  ///
  /// يعيد خريطة تحتوي على معلومات الملف المرفوع:
  /// - downloadPage: رابط صفحة التحميل
  /// - directLink: الرابط المباشر للملف (إذا كان متاحًا)
  /// - fileName: اسم الملف
  /// - fileId: معرف الملف
  /// - fileType: نوع الملف (image, video, audio, document, other)
  /// - success: حالة نجاح العملية
  Future<Map<String, dynamic>> uploadFile(File file) async {
    try {
      if (kDebugMode) {
        debugPrint('جاري رفع الملف إلى GoFile...');
      }

      // التحقق من وجود الملف
      if (!await file.exists()) {
        throw Exception('الملف غير موجود');
      }

      // التحقق من حجم الملف (الحد الأقصى 5GB)
      final fileSize = await file.length();
      if (fileSize > 5 * 1024 * 1024 * 1024) {
        throw Exception('حجم الملف كبير جداً (الحد الأقصى 5GB)');
      }

      // الحصول على أفضل خادم
      final uploadUrl = await _getBestServer();

      // إنشاء طلب متعدد الأجزاء
      final request = http.MultipartRequest('POST', Uri.parse(uploadUrl));

      // إضافة معلومات الحساب
      if (_accountToken.isNotEmpty) {
        request.fields['token'] = _accountToken;
      }

      // إضافة الملف إلى الطلب
      final fileStream = http.ByteStream(file.openRead());
      final fileLength = await file.length();
      final fileName = path.basename(file.path);

      final multipartFile = http.MultipartFile(
        'file',
        fileStream,
        fileLength,
        filename: fileName,
      );

      request.files.add(multipartFile);

      // إرسال الطلب
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      if (kDebugMode) {
        debugPrint('استجابة GoFile: ${response.statusCode}');
        if (response.statusCode != 200) {
          debugPrint('محتوى الاستجابة: ${response.body}');
        }
      }

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);

        if (jsonResponse['status'] == 'ok') {
          final data = jsonResponse['data'];

          // استخراج معلومات الملف
          final downloadPage = data['downloadPage'] ?? '';
          final fileId = data['fileId'] ?? '';
          final fileName = data['fileName'] ?? path.basename(file.path);

          // تحديد نوع الملف
          final fileType = _getFileType(fileName);

          // استخراج كود الملف من رابط التحميل
          final fileCode = downloadPage.split('/').last;

          // استخراج اسم الخادم من البيانات أو من رابط التحميل
          String server = 'store1'; // القيمة الافتراضية
          if (data['server'] != null) {
            server = data['server'];
          } else if (downloadPage.isNotEmpty) {
            // استخراج الخادم من رابط التحميل
            final uri = Uri.parse(downloadPage);
            final hostParts = uri.host.split('.');
            if (hostParts.isNotEmpty) {
              server = hostParts[0];
            }
          }

          // بناء رابط مباشر للملف باستخدام معلومات الخادم
          final directLink =
              'https://$server.gofile.io/download/$fileCode/${Uri.encodeComponent(fileName)}';

          if (kDebugMode) {
            debugPrint('رابط مباشر مبني: $directLink');
          }

          if (kDebugMode) {
            debugPrint('تم رفع الملف بنجاح: $downloadPage');
            debugPrint('الرابط المباشر: $directLink');
          }

          return {
            'success': true,
            'downloadPage': downloadPage,
            'directLink': directLink,
            'fileName': fileName,
            'fileId': fileId,
            'fileType': fileType,
            'server': server,
            'contentId': fileCode,
          };
        } else {
          final errorMessage = jsonResponse['data']?['error'] ??
              jsonResponse['status'] ??
              'خطأ غير معروف';
          throw Exception('فشل في رفع الملف: $errorMessage');
        }
      } else if (response.statusCode == 413) {
        throw Exception('حجم الملف كبير جداً');
      } else if (response.statusCode == 400) {
        throw Exception('بيانات الطلب غير صحيحة');
      } else if (response.statusCode == 401) {
        throw Exception('رمز المصادقة غير صحيح');
      } else if (response.statusCode == 429) {
        throw Exception('تم تجاوز الحد المسموح من الطلبات');
      } else {
        throw Exception(
            'فشل في رفع الملف: ${response.statusCode} - ${response.reasonPhrase}');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('خطأ في رفع الملف إلى GoFile: $e');
      }
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// الحصول على نوع الملف بناءً على امتداده
  String _getFileType(String fileName) {
    final extension = path.extension(fileName).toLowerCase();

    // الصور
    if ([
      '.jpg',
      '.jpeg',
      '.png',
      '.gif',
      '.webp',
      '.bmp',
      '.svg',
      '.ico',
      '.tiff',
      '.tif'
    ].contains(extension)) {
      return 'image';
    }

    // الفيديو
    if ([
      '.mp4',
      '.avi',
      '.mov',
      '.wmv',
      '.flv',
      '.mkv',
      '.webm',
      '.3gp',
      '.m4v',
      '.mpg',
      '.mpeg'
    ].contains(extension)) {
      return 'video';
    }

    // الصوت (مهم للدردشة)
    if ([
      '.mp3',
      '.wav',
      '.ogg',
      '.m4a',
      '.aac',
      '.flac',
      '.wma',
      '.amr',
      '.3ga'
    ].contains(extension)) {
      return 'audio';
    }

    // المستندات
    if ([
      '.pdf',
      '.doc',
      '.docx',
      '.xls',
      '.xlsx',
      '.ppt',
      '.pptx',
      '.txt',
      '.rtf',
      '.odt',
      '.ods',
      '.odp',
      '.csv'
    ].contains(extension)) {
      return 'document';
    }

    // ملفات مضغوطة
    if (['.zip', '.rar', '.7z', '.tar', '.gz', '.bz2'].contains(extension)) {
      return 'archive';
    }

    // أنواع أخرى
    return 'other';
  }

  /// إنشاء رابط مباشر للملف (للاستخدام الخارجي)
  static String createDirectLink(String downloadPage, String fileName,
      {String? server}) {
    try {
      final fileCode = downloadPage.split('/').last;
      final serverName = server ?? 'store1';
      return 'https://$serverName.gofile.io/download/$fileCode/${Uri.encodeComponent(fileName)}';
    } catch (e) {
      if (kDebugMode) {
        debugPrint('خطأ في إنشاء الرابط المباشر: $e');
      }
      return downloadPage; // العودة لرابط التحميل الأصلي
    }
  }
}
