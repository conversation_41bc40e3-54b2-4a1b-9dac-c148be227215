{"buildFiles": ["C:\\tools\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\ok\\other--packages\\pos\\romany\\android\\app\\.cxx\\Debug\\1qt684o4\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\ok\\other--packages\\pos\\romany\\android\\app\\.cxx\\Debug\\1qt684o4\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}