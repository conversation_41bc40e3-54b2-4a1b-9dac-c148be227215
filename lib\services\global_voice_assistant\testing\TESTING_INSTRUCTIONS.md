# تعليمات تشغيل اختبارات المستحقات

## الهدف من الاختبارات
تشخيص مشكلة عدم ظهور بعض فواتير الآجل في شاشة المستحقات دون تعديل أي بيانات.

## خطوات التشغيل

### 1. الوصول إلى الاختبارات
```
الإعدادات → الإعدادات (توسيع القائمة) → اختبارات المستحقات
```

### 2. تشغيل الاختبار الأول: بيانات الفواتير والعملاء
- اضغط على "اختبار بيانات الفواتير والعملاء"
- اضغط على "تشغيل جميع الاختبارات"
- انتظر حتى انتهاء جميع الاختبارات (قد يستغرق 1-2 دقيقة)

### 3. تشغيل الاختبار الثاني: منطق شاشة المستحقات
- ارجع للقائمة الرئيسية
- اضغط على "اختبار منطق شاشة المستحقات"
- جرب كل زر على حدة:
  - "اختبار منطق الشاشة"
  - "البحث عن الفواتير المفقودة"
  - "اختبار أخطاء الحساب"
  - "محاكاة فلتر الشاشة"

## ما تبحث عنه في النتائج

### 🔍 مؤشرات المشاكل الرئيسية:

#### 1. عدم تطابق أرقام الهواتف
```
⚠️ أرقام في العملاء فقط: X
⚠️ أرقام في الفواتير فقط: Y
```
**المعنى**: هناك عدم تطابق في أرقام الهواتف

#### 2. أخطاء في حساب المديونية
```
❌ عملاء غير متطابقين: X
📋 تفاصيل عدم التطابق:
- اسم العميل (رقم الهاتف): مخزن=100, محسوب=150
```
**المعنى**: المديونية المخزنة لا تطابق المحسوبة من الفواتير

#### 3. فواتير آجلة مفقودة
```
❌ فواتير آجلة لا تظهر في شاشة المستحقات: X
📋 تفاصيل الفواتير المفقودة:
- فاتورة 123: اسم العميل (رقم الهاتف) - مديونية: 100
```
**المعنى**: هناك فواتير آجلة لا تظهر في الشاشة

#### 4. مشاكل في أنواع البيانات
```
📊 تحليل أنواع بيانات المديونية:
- صفر نصي: X عميل
- غير رقمي: Y عميل
```
**المعنى**: هناك مشاكل في تنسيق البيانات

### ✅ مؤشرات عدم وجود مشاكل:

```
✅ عملاء متطابقون: X
❌ عملاء غير متطابقين: 0
✅ أرقام مشتركة: X
⚠️ أرقام في العملاء فقط: 0
⚠️ أرقام في الفواتير فقط: 0
```

## تحليل النتائج

### إذا وجدت مشاكل:

#### 1. عدم تطابق أرقام الهواتف
- **السبب**: تنسيقات مختلفة لأرقام الهواتف
- **الحل**: توحيد تنسيق أرقام الهواتف في قاعدة البيانات

#### 2. أخطاء في حساب المديونية
- **السبب**: عدم تحديث مديونية العميل عند إنشاء/تعديل الفواتير
- **الحل**: إعادة حساب المديونية لجميع العملاء

#### 3. فواتير آجلة مفقودة
- **السبب**: مديونية العميل = 0 رغم وجود فواتير آجلة
- **الحل**: تحديث مديونية العملاء المتأثرين

#### 4. مشاكل في أنواع البيانات
- **السبب**: حفظ المديونية كنص بدلاً من رقم
- **الحل**: تحويل البيانات إلى النوع الصحيح

### إذا لم تجد مشاكل:
- المشكلة قد تكون في منطق الشاشة نفسها
- فحص كود شاشة المستحقات
- التحقق من شروط الفلترة

## حفظ النتائج

### طريقة حفظ النتائج:
1. التقط لقطات شاشة لجميع النتائج
2. اكتب ملخص بالمشاكل المكتشفة
3. حدد أولويات الإصلاح

### معلومات مهمة للحفظ:
- عدد العملاء الإجمالي
- عدد العملاء الذين لديهم مديونية
- عدد الفواتير الآجلة
- عدد العملاء الذين لديهم مشاكل
- تفاصيل المشاكل المحددة

## الخطوات التالية

### بعد تحديد المشاكل:
1. **لا تعدل البيانات يدوياً**
2. وثق جميع المشاكل المكتشفة
3. حدد أولويات الإصلاح
4. اطلب المساعدة الفنية إذا لزم الأمر

### للمطورين:
- استخدم النتائج لتحديد نقاط الضعف في الكود
- ركز على إصلاح آلية تحديث المديونية
- تحسين التحقق من صحة البيانات
- إضافة آليات للتحقق من تكامل البيانات

## استكشاف الأخطاء

### إذا لم تعمل الاختبارات:
- تأكد من وجود اتصال بالإنترنت
- تأكد من وجود بيانات في قاعدة البيانات
- أعد تشغيل التطبيق وحاول مرة أخرى

### إذا كانت النتائج غير واضحة:
- شغل الاختبارات مرة أخرى
- قارن النتائج مع البيانات الفعلية في الشاشات
- راجع الكود المصدري للاختبارات

---

**تذكير مهم**: هذه الاختبارات للتشخيص فقط ولن تعدل أي بيانات. استخدم النتائج لفهم المشكلة وتحديد الحلول المناسبة.
