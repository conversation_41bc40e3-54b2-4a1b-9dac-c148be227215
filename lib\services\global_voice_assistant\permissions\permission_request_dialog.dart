import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'permission_manager.dart';

/// حوار تفاعلي لطلب أذونات المساعد الذكي العالمي
class PermissionRequestDialog extends StatefulWidget {
  final VoidCallback? onPermissionsGranted;
  final VoidCallback? onPermissionsDenied;

  const PermissionRequestDialog({
    super.key,
    this.onPermissionsGranted,
    this.onPermissionsDenied,
  });

  /// عرض الحوار
  static Future<bool?> show(
    BuildContext context, {
    VoidCallback? onPermissionsGranted,
    VoidCallback? onPermissionsDenied,
  }) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => PermissionRequestDialog(
        onPermissionsGranted: onPermissionsGranted,
        onPermissionsDenied: onPermissionsDenied,
      ),
    );
  }

  @override
  State<PermissionRequestDialog> createState() =>
      _PermissionRequestDialogState();
}

class _PermissionRequestDialogState extends State<PermissionRequestDialog>
    with TickerProviderStateMixin {
  final PermissionManager _permissionManager = PermissionManager();

  bool _isLoading = false;
  bool _isChecking = true;
  Map<String, dynamic>? _permissionStatus;
  String _currentStep = 'فحص الأذونات الحالية...';

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _animationController.forward();
    _checkCurrentPermissions();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// فحص الأذونات الحالية
  Future<void> _checkCurrentPermissions() async {
    setState(() {
      _isChecking = true;
      _currentStep = 'فحص الأذونات الحالية...';
    });

    try {
      final status = await _permissionManager.checkAllPermissions();
      setState(() {
        _permissionStatus = status;
        _isChecking = false;
      });
    } catch (e) {
      setState(() {
        _isChecking = false;
        _currentStep = 'خطأ في فحص الأذونات: $e';
      });
    }
  }

  /// طلب جميع الأذونات
  Future<void> _requestPermissions() async {
    setState(() {
      _isLoading = true;
      _currentStep = 'طلب الأذونات...';
    });

    try {
      // إضافة اهتزاز خفيف
      HapticFeedback.lightImpact();

      final result = await _permissionManager.requestAllPermissions();

      setState(() {
        _isLoading = false;
      });

      if (result['success'] == true) {
        // نجح في الحصول على الأذونات الأساسية
        HapticFeedback.heavyImpact();
        _showSuccessDialog(result);
      } else {
        // فشل في الحصول على الأذونات
        HapticFeedback.vibrate();
        _showErrorDialog(result);
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _currentStep = 'خطأ في طلب الأذونات: $e';
      });
    }
  }

  /// عرض حوار النجاح
  void _showSuccessDialog(Map<String, dynamic> result) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        icon: const Icon(Icons.check_circle, color: Colors.green, size: 48),
        title: const Text('تم منح الأذونات!'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(result['message'] ?? 'تم منح الأذونات بنجاح'),
            const SizedBox(height: 16),
            _buildPermissionSummary(result['results'] ?? {}),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop(); // إغلاق حوار النجاح
              Navigator.of(context).pop(true); // إغلاق الحوار الرئيسي
              widget.onPermissionsGranted?.call();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
            child: const Text('متابعة'),
          ),
        ],
      ),
    );
  }

  /// عرض حوار الخطأ
  void _showErrorDialog(Map<String, dynamic> result) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        icon: const Icon(Icons.error, color: Colors.red, size: 48),
        title: const Text('فشل في منح الأذونات'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(result['message'] ?? 'فشل في الحصول على الأذونات المطلوبة'),
            const SizedBox(height: 16),
            _buildPermissionSummary(result['results'] ?? {}),
            const SizedBox(height: 16),
            const Text(
              'يمكنك منح الأذونات يدوياً من إعدادات التطبيق',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // إغلاق حوار الخطأ
              Navigator.of(context).pop(false); // إغلاق الحوار الرئيسي
              widget.onPermissionsDenied?.call();
            },
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop(); // إغلاق حوار الخطأ
              await _permissionManager.openAppSettings();
            },
            child: const Text('فتح الإعدادات'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop(); // إغلاق حوار الخطأ
              _requestPermissions(); // إعادة المحاولة
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  /// بناء ملخص الأذونات
  Widget _buildPermissionSummary(Map<String, bool> permissions) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: permissions.entries.map((entry) {
          return Row(
            children: [
              Icon(
                entry.value ? Icons.check_circle : Icons.cancel,
                color: entry.value ? Colors.green : Colors.red,
                size: 16,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  _getPermissionDisplayName(entry.key),
                  style: const TextStyle(fontSize: 12),
                ),
              ),
            ],
          );
        }).toList(),
      ),
    );
  }

  /// الحصول على اسم الإذن للعرض
  String _getPermissionDisplayName(String permission) {
    switch (permission) {
      case 'microphone':
        return 'الميكروفون';
      case 'systemAlertWindow':
        return 'النوافذ العائمة';
      case 'ignoreBatteryOptimizations':
        return 'تحسين البطارية';
      case 'notification':
        return 'الإشعارات';
      case 'storage':
        return 'التخزين';
      default:
        return permission;
    }
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(
              Icons.security,
              color: Theme.of(context).primaryColor,
              size: 28,
            ),
            const SizedBox(width: 12),
            const Expanded(
              child: Text(
                'أذونات المساعد الذكي',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
        content: SizedBox(
          width: double.maxFinite,
          child:
              _isChecking ? _buildCheckingWidget() : _buildPermissionContent(),
        ),
        actions: _buildActions(),
      ),
    );
  }

  /// بناء واجهة الفحص
  Widget _buildCheckingWidget() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const CircularProgressIndicator(),
        const SizedBox(height: 16),
        Text(_currentStep),
      ],
    );
  }

  /// بناء محتوى الأذونات
  Widget _buildPermissionContent() {
    if (_permissionStatus == null) {
      return const Text('خطأ في تحميل حالة الأذونات');
    }

    final summary = _permissionStatus!['summary'] as Map<String, dynamic>;
    final permissions = _permissionStatus!['permissions'] as Map<String, bool>;

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // ملخص الحالة
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: summary['all_granted'] == true
                ? Colors.green[50]
                : Colors.orange[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color:
                  summary['all_granted'] == true ? Colors.green : Colors.orange,
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Icon(
                summary['all_granted'] == true
                    ? Icons.check_circle
                    : Icons.warning,
                color: summary['all_granted'] == true
                    ? Colors.green
                    : Colors.orange,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  summary['all_granted'] == true
                      ? 'جميع الأذونات ممنوحة!'
                      : 'بعض الأذونات مطلوبة',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: summary['all_granted'] == true
                        ? Colors.green[700]
                        : Colors.orange[700],
                  ),
                ),
              ),
              Text(
                '${summary['percentage']}%',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: summary['all_granted'] == true
                      ? Colors.green[700]
                      : Colors.orange[700],
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 16),

        // قائمة الأذونات
        const Text(
          'الأذونات المطلوبة:',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),

        ...permissions.entries
            .map((entry) => _buildPermissionItem(entry.key, entry.value)),

        const SizedBox(height: 16),

        // معلومات إضافية
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.blue[50],
            borderRadius: BorderRadius.circular(6),
          ),
          child: const Text(
            'هذه الأذونات مطلوبة لعمل المساعد الذكي العالمي بشكل صحيح. '
            'يمكنك منح الأذونات الآن أو لاحقاً من إعدادات التطبيق.',
            style: TextStyle(fontSize: 12, color: Colors.blue),
          ),
        ),
      ],
    );
  }

  /// بناء عنصر إذن واحد
  Widget _buildPermissionItem(String permission, bool granted) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            granted ? Icons.check_circle : Icons.radio_button_unchecked,
            color: granted ? Colors.green : Colors.grey,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              _getPermissionDisplayName(permission),
              style: TextStyle(
                color: granted ? Colors.green[700] : Colors.black87,
                fontWeight: granted ? FontWeight.w500 : FontWeight.normal,
              ),
            ),
          ),
          Text(
            granted ? 'ممنوح' : 'مطلوب',
            style: TextStyle(
              fontSize: 12,
              color: granted ? Colors.green : Colors.orange,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء أزرار الحوار
  List<Widget> _buildActions() {
    if (_isChecking) {
      return [];
    }

    final summary = _permissionStatus?['summary'] as Map<String, dynamic>?;
    final allGranted = summary?['all_granted'] == true;

    return [
      TextButton(
        onPressed: _isLoading
            ? null
            : () {
                Navigator.of(context).pop(false);
                widget.onPermissionsDenied?.call();
              },
        child: const Text('تخطي'),
      ),
      if (!allGranted) ...[
        ElevatedButton(
          onPressed: _isLoading ? null : _requestPermissions,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('منح الأذونات'),
        ),
      ] else ...[
        ElevatedButton(
          onPressed: () {
            Navigator.of(context).pop(true);
            widget.onPermissionsGranted?.call();
          },
          style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
          child: const Text('متابعة'),
        ),
      ],
    ];
  }
}
