// ignore_for_file: deprecated_member_use, unused_local_variable

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:mobile_pos/const_commas.dart';
import 'package:mobile_pos/generated/l10n.dart' as lang;
import 'package:nb_utils/nb_utils.dart';

import '../../../Provider/product_provider.dart';
import '../../../constant.dart';
import '../../../currency.dart';
import '../../../empty_screen_widget.dart';
import '../../Warehouse/warehouse_model.dart';

class InventoryReport extends StatefulWidget {
  const InventoryReport({super.key});

  @override
  // ignore: library_private_types_in_public_api
  _InventoryReportState createState() => _InventoryReportState();
}

class _InventoryReportState extends State<InventoryReport> {
  num totalStock = 0;
  double totalSalePrice = 0;
  double totalParPrice = 0;
  String? productName;
  int count = 0;
  DateTime? selectedDate;

  @override
  void initState() {
    super.initState();
    selectedDate = DateTime.now();
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedDate ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );
    if (picked != null) {
      setState(() {
        selectedDate = picked;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        title: Text(
          lang.S.of(context).currentStock,
          style: GoogleFonts.poppins(
            color: Colors.white,
          ),
        ),
        iconTheme: const IconThemeData(color: Colors.white),
        centerTitle: true,
        backgroundColor: kMainColor,
        elevation: 0.0,
      ),
      body: Consumer(builder: (context, ref, __) {
        final providerData = ref.watch(productProvider);
        final wareHouseList = ref.watch(warehouseProvider);
        return Container(
          alignment: Alignment.topCenter,
          decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                  topRight: Radius.circular(30), topLeft: Radius.circular(30))),
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(10.0),
              child: Column(
                children: [
                  Column(
                    children: [
                      providerData.when(data: (product) {
                        if (count == 0) {
                          count++;
                          for (var element in product) {
                            totalStock = totalStock +
                                (num.tryParse(element.productStock) ?? 0);
                            totalSalePrice = totalSalePrice +
                                ((num.tryParse(element.productStock) ?? 0) *
                                    element.productSalePrice.toInt());
                            totalParPrice = totalParPrice +
                                ((num.tryParse(element.productStock) ?? 0) *
                                    (int.tryParse(
                                            element.productPurchasePrice) ??
                                        0));
                          }
                        }
                        return product.isNotEmpty
                            ? Column(
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.all(20.0),
                                    child: Container(
                                      height: 100,
                                      width: double.infinity,
                                      decoration: BoxDecoration(
                                          color: kMainColor.withOpacity(0.1),
                                          border: Border.all(
                                              width: 1, color: kMainColor),
                                          borderRadius: const BorderRadius.all(
                                              Radius.circular(15))),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceAround,
                                        children: [
                                          Column(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.center,
                                            children: [
                                              Text(
                                                myFormat.format(totalStock),
                                                style: const TextStyle(
                                                  color: Colors.green,
                                                  fontSize: 20,
                                                ),
                                              ),
                                              const SizedBox(height: 10),
                                              Text(
                                                lang.S.of(context).totalStock,
                                                style: const TextStyle(
                                                  color: Colors.black,
                                                  fontSize: 16,
                                                ),
                                              ),
                                            ],
                                          ),
                                          Container(
                                            width: 1,
                                            height: 60,
                                            color: kMainColor,
                                          ),
                                          Column(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.center,
                                            children: [
                                              Text(
                                                '$currency ${myFormat.format(totalParPrice)}',
                                                style: const TextStyle(
                                                  color: Colors.orange,
                                                  fontSize: 20,
                                                ),
                                              ),
                                              const SizedBox(height: 10),
                                              Text(
                                                lang.S.of(context).totalPrice,
                                                style: const TextStyle(
                                                  color: Colors.black,
                                                  fontSize: 16,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  AppTextField(
                                    textFieldType: TextFieldType.NAME,
                                    onChanged: (value) {
                                      setState(() {
                                        productName = value;
                                      });
                                    },
                                    decoration: InputDecoration(
                                      contentPadding: const EdgeInsets.only(
                                          left: 8.0, right: 8.0),
                                      floatingLabelBehavior:
                                          FloatingLabelBehavior.never,
                                      labelText: lang.S.of(context).productName,
                                      hintText:
                                          lang.S.of(context).enterProductName,
                                      prefixIcon: const Icon(Icons.search),
                                      border: const OutlineInputBorder(),
                                    ),
                                  ),
                                  const SizedBox(height: 10),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      TextButton.icon(
                                        onPressed: () => _selectDate(context),
                                        icon: const Icon(Icons.calendar_today),
                                        label: Text(
                                          selectedDate != null
                                              ? DateFormat('yyyy-MM-dd')
                                                  .format(selectedDate!)
                                              : 'اختر التاريخ',
                                        ),
                                      ),
                                      if (selectedDate != null)
                                        TextButton(
                                          onPressed: () {
                                            setState(() {
                                              selectedDate = null;
                                            });
                                          },
                                          child:
                                              const Text('عرض الرصيد الحالي'),
                                        ),
                                    ],
                                  ),
                                  const SizedBox(height: 10),
                                  ElevatedButton(
                                    onPressed: () async {
                                      if (selectedDate != null) {
                                        setState(() {});
                                      }
                                    },
                                    child: const Text('تحديث'),
                                  ),
                                  const SizedBox(height: 10),
                                  ListView.builder(
                                    shrinkWrap: true,
                                    physics:
                                        const NeverScrollableScrollPhysics(),
                                    itemCount: product.length,
                                    itemBuilder: (context, index) {
                                      if (productName == null ||
                                          product[index]
                                              .productName
                                              .toLowerCase()
                                              .contains(
                                                  productName!.toLowerCase())) {
                                        return FutureBuilder<
                                            Map<String, dynamic>>(
                                          future: selectedDate != null
                                              ? Future(() async {
                                                  final historicalStock =
                                                      await productRepo
                                                          .getStockAtDate(
                                                    product[index].productCode,
                                                    selectedDate!,
                                                    int.parse(product[index]
                                                        .productStock),
                                                  );
                                                  final movements =
                                                      await productRepo
                                                          .getStockMovements(
                                                    product[index].productCode,
                                                    selectedDate!,
                                                    DateTime.now(),
                                                  );
                                                  return {
                                                    'historicalStock':
                                                        historicalStock,
                                                    'movements': movements,
                                                  };
                                                })
                                              : Future.value({
                                                  'historicalStock': int.parse(
                                                      product[index]
                                                          .productStock),
                                                  'movements': null,
                                                }),
                                          builder: (context, snapshot) {
                                            final historicalStock = snapshot
                                                    .data?['historicalStock'] ??
                                                int.parse(product[index]
                                                    .productStock);
                                            final movements =
                                                snapshot.data?['movements'];
                                            final purchasePrice = double.parse(
                                                product[index]
                                                    .productPurchasePrice);
                                            final totalPrice =
                                                purchasePrice * historicalStock;

                                            return Card(
                                              child: ExpansionTile(
                                                title: Text(
                                                    product[index].productName),
                                                subtitle: Text(
                                                  selectedDate != null
                                                      ? 'الرصيد في ${DateFormat('yyyy-MM-dd').format(selectedDate!)}: $historicalStock'
                                                      : 'الرصيد الحالي: ${product[index].productStock}',
                                                  style: const TextStyle(
                                                      fontSize: 16,
                                                      fontWeight:
                                                          FontWeight.bold),
                                                ),
                                                children: [
                                                  Padding(
                                                    padding:
                                                        const EdgeInsets.all(
                                                            16.0),
                                                    child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        Text(
                                                            'سعر الشراء: $currency ${myFormat.format(purchasePrice)}'),
                                                        Text(
                                                            'الإجمالي: $currency ${myFormat.format(totalPrice)}'),
                                                        if (movements !=
                                                            null) ...[
                                                          const Divider(),
                                                          Text(
                                                              'حركة المخزون بعد ${DateFormat('yyyy-MM-dd').format(selectedDate!)}:'),
                                                          Text(
                                                              'المبيعات: ${movements['sales']}'),
                                                          Text(
                                                              'مرتجع المبيعات: ${movements['salesReturns']}'),
                                                          Text(
                                                              'المشتريات: ${movements['purchases']}'),
                                                          Text(
                                                              'مرتجع المشتريات: ${movements['purchaseReturns']}'),
                                                        ],
                                                      ],
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            );
                                          },
                                        );
                                      }
                                      return const SizedBox.shrink();
                                    },
                                  ),
                                ],
                              )
                            : const Center(
                                child: Padding(
                                padding: EdgeInsets.only(top: 60),
                                child: EmptyScreenWidget(),
                              ));
                      }, error: (e, stack) {
                        return Text(e.toString());
                      }, loading: () {
                        return const Center(child: CircularProgressIndicator());
                      }),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      }),
    );
  }
}
