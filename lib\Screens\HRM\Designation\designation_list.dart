import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/models/hrm_models.dart';
// import 'package:nb_utils/nb_utils.dart'; // غير مستخدم
import 'add_designation.dart';
import 'provider/designation_provider.dart';
// import 'repo/designation_repo.dart'; // غير مستخدم

class DesignationListScreen extends ConsumerStatefulWidget {
  const DesignationListScreen({super.key});

  static const String route = '/hrm/designations';

  @override
  ConsumerState<DesignationListScreen> createState() =>
      _DesignationListScreenState();
}

class _DesignationListScreenState extends ConsumerState<DesignationListScreen> {
  String searchQuery = '';

  @override
  Widget build(BuildContext context) {
    final designationsAsyncValue = ref.watch(designationListProvider);

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('المسميات الوظيفية'),
        backgroundColor: kMainColor,
        centerTitle: true,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        titleTextStyle: const TextStyle(
            color: Colors.white, fontSize: 20, fontWeight: FontWeight.bold),
      ),
      body: designationsAsyncValue.when(
        data: (designations) {
          // تطبيق البحث
          List<DesignationModel> filteredDesignations =
              designations.where((designation) {
            return searchQuery.isEmpty ||
                designation.title
                    .toLowerCase()
                    .contains(searchQuery.toLowerCase()) ||
                designation.department
                    .toLowerCase()
                    .contains(searchQuery.toLowerCase()) ||
                designation.description
                    .toLowerCase()
                    .contains(searchQuery.toLowerCase());
          }).toList();

          return Column(
            children: [
              // شريط البحث
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    Expanded(
                      flex: 3,
                      child: TextField(
                        decoration: InputDecoration(
                          hintText: 'بحث بالمسمى الوظيفي أو القسم',
                          prefixIcon: const Icon(Icons.search),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        onChanged: (value) {
                          setState(() {
                            searchQuery = value;
                          });
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    ElevatedButton.icon(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: kMainColor,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 12),
                      ),
                      onPressed: () {
                        _showAddDesignationDialog(context, designations);
                      },
                      icon: const Icon(Icons.add, color: Colors.white),
                      label: const Text('إضافة مسمى وظيفي',
                          style: TextStyle(color: Colors.white)),
                    ),
                  ],
                ),
              ),

              // قائمة المسميات الوظيفية
              Expanded(
                child: filteredDesignations.isEmpty
                    ? const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.work_outline,
                                size: 80, color: Colors.grey),
                            SizedBox(height: 16),
                            Text(
                              'لا توجد مسميات وظيفية',
                              style: TextStyle(
                                  fontSize: 18, fontWeight: FontWeight.bold),
                            ),
                            SizedBox(height: 8),
                            Text(
                              'قم بإضافة مسميات وظيفية جديدة',
                              style: TextStyle(color: Colors.grey),
                            ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        itemCount: filteredDesignations.length,
                        itemBuilder: (context, index) {
                          final designation = filteredDesignations[index];
                          return Card(
                            margin: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 8),
                            elevation: 2,
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12)),
                            child: ListTile(
                              leading: CircleAvatar(
                                backgroundColor: kMainColor,
                                child: Text(
                                  designation.title.isNotEmpty
                                      ? designation.title[0].toUpperCase()
                                      : '?',
                                  style: const TextStyle(color: Colors.white),
                                ),
                              ),
                              title: Text(
                                designation.title,
                                style: const TextStyle(
                                    fontWeight: FontWeight.bold),
                              ),
                              subtitle: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('القسم: ${designation.department}'),
                                  Text('الوصف: ${designation.description}'),
                                ],
                              ),
                              trailing: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  IconButton(
                                    icon: const Icon(Icons.edit,
                                        color: Colors.blue),
                                    onPressed: () {
                                      _showEditDesignationDialog(
                                          context, designations, designation);
                                    },
                                  ),
                                  IconButton(
                                    icon: const Icon(Icons.delete,
                                        color: Colors.red),
                                    onPressed: () {
                                      _deleteDesignation(designation);
                                    },
                                  ),
                                ],
                              ),
                              isThreeLine: true,
                            ),
                          );
                        },
                      ),
              ),
            ],
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Text('حدث خطأ: $error',
              style: const TextStyle(color: Colors.red)),
        ),
      ),
    );
  }

  void _showAddDesignationDialog(
      BuildContext context, List<DesignationModel> designations) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20.0),
        ),
        child: AddDesignationScreen(listOfDesignations: designations),
      ),
    );
  }

  void _showEditDesignationDialog(BuildContext context,
      List<DesignationModel> designations, DesignationModel designation) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20.0),
        ),
        child: AddDesignationScreen(
          listOfDesignations: designations,
          designationModel: designation,
        ),
      ),
    );
  }

  void _deleteDesignation(DesignationModel designation) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذا المسمى الوظيفي؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);

              // استخدام طريقة مختلفة لتجنب مشكلة استخدام BuildContext عبر الفجوات غير المتزامنة
              _performDeleteDesignation(designation);
            },
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  // طريقة منفصلة لتنفيذ عملية الحذف بشكل آمن
  Future<void> _performDeleteDesignation(DesignationModel designation) async {
    try {
      // تنفيذ وظيفة الحذف الفعلية
      await ref
          .read(designationNotifierProvider.notifier)
          .deleteDesignation(designation.id);

      // التحقق من أن الويدجت لا تزال مثبتة قبل استخدام السياق
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم حذف المسمى الوظيفي بنجاح')),
        );
      }
    } catch (e) {
      // التحقق من أن الويدجت لا تزال مثبتة قبل استخدام السياق
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ أثناء الحذف: $e')),
        );
      }
    }
  }
}
