import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/model/water_filter_models.dart';
import 'package:mobile_pos/services/water_filter_service.dart';

class PerformanceReportScreen extends StatefulWidget {
  const PerformanceReportScreen({super.key});

  @override
  State<PerformanceReportScreen> createState() =>
      _PerformanceReportScreenState();
}

class _PerformanceReportScreenState extends State<PerformanceReportScreen> {
  List<WaterFilterSystem> _allSystems = [];
  List<WaterFilterInstallment> _allInstallments = [];
  List<WaterFilterCustomer> _allCustomers = [];

  // إحصائيات الأداء
  Map<String, dynamic> _stats = {};

  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPerformanceData();
  }

  Future<void> _loadPerformanceData() async {
    setState(() => _isLoading = true);

    try {
      // تحميل الأنظمة
      final systemsData = await WaterFilterService.getData('Systems');
      final systems = <WaterFilterSystem>[];

      systemsData.forEach((key, value) {
        try {
          final system = WaterFilterSystem.fromJson(
            Map<String, dynamic>.from(value),
          );
          systems.add(system);
        } catch (e) {
          debugPrint('خطأ في معالجة نظام: $e');
        }
      });

      // تحميل الأقساط
      final installmentsData = await WaterFilterService.getData('Installments');
      final installments = <WaterFilterInstallment>[];

      installmentsData.forEach((key, value) {
        try {
          final installment = WaterFilterInstallment.fromJson(
            Map<String, dynamic>.from(value),
          );
          installments.add(installment);
        } catch (e) {
          debugPrint('خطأ في معالجة قسط: $e');
        }
      });

      // تحميل العملاء
      final customersData = await WaterFilterService.getData('Customers');
      final customers = <WaterFilterCustomer>[];

      customersData.forEach((key, value) {
        try {
          final customer = WaterFilterCustomer.fromJson(
            Map<String, dynamic>.from(value),
          );
          customers.add(customer);
        } catch (e) {
          debugPrint('خطأ في معالجة عميل: $e');
        }
      });

      setState(() {
        _allSystems = systems;
        _allInstallments = installments;
        _allCustomers = customers;
        _calculateStats();
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات الأداء: $e');
      setState(() => _isLoading = false);
    }
  }

  void _calculateStats() {
    final now = DateTime.now();

    // إحصائيات شهرية
    final thisMonthSales = _allSystems
        .where((s) =>
            s.installationDate.year == now.year &&
            s.installationDate.month == now.month)
        .length;

    final lastMonth = DateTime(now.year, now.month - 1);
    final lastMonthSales = _allSystems
        .where((s) =>
            s.installationDate.year == lastMonth.year &&
            s.installationDate.month == lastMonth.month)
        .length;

    final thisMonthRevenue = _allSystems
        .where((s) =>
            s.installationDate.year == now.year &&
            s.installationDate.month == now.month)
        .fold(0.0, (sum, system) => sum + system.totalCost);

    final lastMonthRevenue = _allSystems
        .where((s) =>
            s.installationDate.year == lastMonth.year &&
            s.installationDate.month == lastMonth.month)
        .fold(0.0, (sum, system) => sum + system.totalCost);

    // نمو المبيعات
    final salesGrowth = lastMonthSales > 0
        ? ((thisMonthSales - lastMonthSales) / lastMonthSales) * 100
        : 0.0;

    // نمو الإيرادات
    final revenueGrowth = lastMonthRevenue > 0
        ? ((thisMonthRevenue - lastMonthRevenue) / lastMonthRevenue) * 100
        : 0.0;

    // معدل التحصيل
    final totalRevenue =
        _allSystems.fold(0.0, (sum, system) => sum + system.totalCost);
    final totalPaid =
        _allSystems.fold(0.0, (sum, system) => sum + system.paidAmount);
    final collectionRate =
        totalRevenue > 0 ? (totalPaid / totalRevenue) * 100 : 0;

    // معدل الأقساط المتأخرة
    final totalInstallments = _allInstallments.length;
    final overdueInstallments = _allInstallments
        .where((i) => i.status == InstallmentStatus.overdue)
        .length;
    final overdueRate = totalInstallments > 0
        ? (overdueInstallments / totalInstallments) * 100
        : 0;

    // متوسط قيمة المبيعة
    final averageSaleValue =
        _allSystems.isNotEmpty ? totalRevenue / _allSystems.length : 0.0;

    // عدد العملاء الجدد هذا الشهر
    final newCustomersThisMonth = _allCustomers
        .where((c) =>
            c.createdAt != null &&
            c.createdAt!.year == now.year &&
            c.createdAt!.month == now.month)
        .length;

    setState(() {
      _stats = {
        'thisMonthSales': thisMonthSales,
        'lastMonthSales': lastMonthSales,
        'thisMonthRevenue': thisMonthRevenue,
        'lastMonthRevenue': lastMonthRevenue,
        'salesGrowth': salesGrowth,
        'revenueGrowth': revenueGrowth,
        'collectionRate': collectionRate,
        'overdueRate': overdueRate,
        'averageSaleValue': averageSaleValue,
        'newCustomersThisMonth': newCustomersThisMonth,
      };
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        title: Text(
          'تقرير الأداء',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: _loadPerformanceData,
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSectionTitle('مؤشرات الأداء الرئيسية'),
                    const SizedBox(height: 16),

                    // مؤشرات النمو
                    GridView.count(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      crossAxisCount: 2,
                      crossAxisSpacing: 16,
                      mainAxisSpacing: 16,
                      childAspectRatio: 0.9,
                      children: [
                        _buildGrowthCard(
                          title: 'نمو المبيعات',
                          value:
                              '${(_stats['salesGrowth'] ?? 0).toStringAsFixed(1)}%',
                          isPositive: (_stats['salesGrowth'] ?? 0) >= 0,
                          icon: Icons.trending_up,
                        ),
                        _buildGrowthCard(
                          title: 'نمو الإيرادات',
                          value:
                              '${(_stats['revenueGrowth'] ?? 0).toStringAsFixed(1)}%',
                          isPositive: (_stats['revenueGrowth'] ?? 0) >= 0,
                          icon: Icons.attach_money,
                        ),
                        _buildGrowthCard(
                          title: 'معدل التحصيل',
                          value:
                              '${(_stats['collectionRate'] ?? 0).toStringAsFixed(1)}%',
                          isPositive: (_stats['collectionRate'] ?? 0) >= 70,
                          icon: Icons.account_balance_wallet,
                        ),
                        _buildGrowthCard(
                          title: 'معدل التأخير',
                          value:
                              '${(_stats['overdueRate'] ?? 0).toStringAsFixed(1)}%',
                          isPositive: (_stats['overdueRate'] ?? 0) <= 10,
                          icon: Icons.warning,
                        ),
                      ],
                    ),

                    const SizedBox(height: 24),

                    _buildSectionTitle('مقارنة شهرية'),
                    const SizedBox(height: 16),

                    Row(
                      children: [
                        Expanded(
                          child: _buildComparisonCard(
                            title: 'مبيعات هذا الشهر',
                            currentValue: '${_stats['thisMonthSales'] ?? 0}',
                            previousValue: '${_stats['lastMonthSales'] ?? 0}',
                            icon: Icons.shopping_cart,
                            color: Colors.blue,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildComparisonCard(
                            title: 'إيرادات هذا الشهر',
                            currentValue:
                                '${(_stats['thisMonthRevenue'] ?? 0).toStringAsFixed(0)} ج.م',
                            previousValue:
                                '${(_stats['lastMonthRevenue'] ?? 0).toStringAsFixed(0)} ج.م',
                            icon: Icons.monetization_on,
                            color: Colors.green,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 24),

                    _buildSectionTitle('إحصائيات إضافية'),
                    const SizedBox(height: 16),

                    GridView.count(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      crossAxisCount: 2,
                      crossAxisSpacing: 16,
                      mainAxisSpacing: 16,
                      childAspectRatio: 0.9,
                      children: [
                        _buildStatCard(
                          title: 'متوسط قيمة المبيعة',
                          value:
                              '${(_stats['averageSaleValue'] ?? 0).toStringAsFixed(2)} ج.م',
                          icon: Icons.calculate,
                          color: Colors.purple,
                        ),
                        _buildStatCard(
                          title: 'عملاء جدد',
                          value: '${_stats['newCustomersThisMonth'] ?? 0}',
                          icon: Icons.person_add,
                          color: Colors.teal,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
      ),
    );
  }

  // Helper Methods
  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: GoogleFonts.cairo(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: kMainColor,
      ),
    );
  }

  Widget _buildGrowthCard({
    required String title,
    required String value,
    required bool isPositive,
    required IconData icon,
  }) {
    final color = isPositive ? Colors.green : Colors.red;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 28),
          const SizedBox(height: 8),
          Flexible(
            child: Text(
              value,
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(height: 4),
          Flexible(
            child: Text(
              title,
              style: GoogleFonts.cairo(
                fontSize: 11,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildComparisonCard({
    required String title,
    required String currentValue,
    required String previousValue,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(width: 8),
              Text(
                title,
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            currentValue,
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'الشهر السابق: $previousValue',
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 28),
          const SizedBox(height: 8),
          Flexible(
            child: Text(
              value,
              style: GoogleFonts.cairo(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
          const SizedBox(height: 4),
          Flexible(
            child: Text(
              title,
              style: GoogleFonts.cairo(
                fontSize: 11,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
        ],
      ),
    );
  }
}
