import 'package:mobile_pos/model/water_filter_models.dart';
import 'package:mobile_pos/services/water_filter_service.dart';

class WaterFilterTestDataHelper {
  /// إضافة بيانات تجريبية لنظام فلاتر المياه
  static Future<void> addTestData() async {
    try {
      print('🧪 بدء إضافة البيانات التجريبية...');

      // إضافة منتجات تجريبية
      await _addTestProducts();

      // إضافة عملاء تجريبيين
      await _addTestCustomers();

      // إضافة أنظمة تجريبية
      await _addTestSystems();

      // إضافة أقساط تجريبية
      await _addTestInstallments();

      print('✅ تم إضافة البيانات التجريبية بنجاح');
    } catch (e) {
      print('❌ خطأ في إضافة البيانات التجريبية: $e');
    }
  }

  static Future<void> _addTestProducts() async {
    final products = [
      WaterFilterProduct(
        id: 'prod_001',
        name: 'فلتر 7 مراحل منزلي',
        brand: 'أكوا تك',
        category: WaterFilterCategory.residential,
        description: 'فلتر منزلي 7 مراحل مع خزان 12 لتر',
        price: 2500.0,
        stock: 50,
        installationCost: 200.0,
        maintenanceCost: 150.0,
        isInstallationRequired: true,
        maintenanceIntervalMonths: 6,
        specifications: [
          '7 مراحل تنقية',
          'خزان 12 لتر',
          'معدل تدفق 2 لتر/دقيقة',
          'ضغط تشغيل 2-6 بار',
        ],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      WaterFilterProduct(
        id: 'prod_002',
        name: 'فلتر RO تجاري',
        brand: 'بيور ووتر',
        category: WaterFilterCategory.commercial,
        description: 'فلتر تجاري بتقنية التناضح العكسي',
        price: 8500.0,
        stock: 20,
        installationCost: 500.0,
        maintenanceCost: 300.0,
        isInstallationRequired: true,
        maintenanceIntervalMonths: 4,
        specifications: [
          'تقنية RO',
          'إنتاجية 100 لتر/ساعة',
          'ضغط تشغيل 6 بار',
          'مناسب للاستخدام التجاري',
        ],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];

    for (final product in products) {
      await WaterFilterService.saveData(
        'Products/${product.id}',
        product.toJson(),
      );
    }
    print('✅ تم إضافة ${products.length} منتج تجريبي');
  }

  static Future<void> _addTestCustomers() async {
    final customers = [
      WaterFilterCustomer(
        id: 'cust_001',
        name: 'أحمد محمد علي',
        phone: '01234567890',
        email: '<EMAIL>',
        address: 'شارع النيل، المعادي، القاهرة',
        area: 'المعادي',
        city: 'القاهرة',
        notes: 'عميل منزلي',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      WaterFilterCustomer(
        id: 'cust_002',
        name: 'شركة النور للتجارة',
        phone: '01098765432',
        email: '<EMAIL>',
        address: 'شارع التحرير، وسط البلد، القاهرة',
        area: 'وسط البلد',
        city: 'القاهرة',
        notes: 'عميل تجاري',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      WaterFilterCustomer(
        id: 'cust_003',
        name: 'فاطمة أحمد حسن',
        phone: '01156789012',
        email: '<EMAIL>',
        address: 'شارع الهرم، الجيزة',
        area: 'الهرم',
        city: 'الجيزة',
        notes: 'عميلة منزلية',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];

    for (final customer in customers) {
      await WaterFilterService.saveData(
        'Customers/${customer.id}',
        customer.toJson(),
      );
    }
    print('✅ تم إضافة ${customers.length} عميل تجريبي');
  }

  static Future<void> _addTestSystems() async {
    final systems = [
      WaterFilterSystem(
        id: 'sys_001',
        customerId: 'cust_001',
        productId: 'prod_001',
        serialNumber: 'WF2024001',
        installationDate: DateTime.now().subtract(const Duration(days: 30)),
        status: FilterSystemStatus.active,
        nextMaintenanceDate: DateTime.now().add(const Duration(days: 150)),
        totalCost: 2700.0, // سعر المنتج + التركيب
        paidAmount: 1000.0,
        remainingAmount: 1700.0,
        isUnderWarranty: true,
        warrantyEndDate: DateTime.now().add(const Duration(days: 335)),
        installationNotes: 'تم التركيب بنجاح في المطبخ',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      WaterFilterSystem(
        id: 'sys_002',
        customerId: 'cust_002',
        productId: 'prod_002',
        serialNumber: 'WF2024002',
        installationDate: DateTime.now().subtract(const Duration(days: 60)),
        status: FilterSystemStatus.active,
        nextMaintenanceDate: DateTime.now().add(const Duration(days: 60)),
        totalCost: 9000.0,
        paidAmount: 4000.0,
        remainingAmount: 5000.0,
        isUnderWarranty: true,
        warrantyEndDate: DateTime.now().add(const Duration(days: 670)),
        installationNotes: 'تم التركيب في غرفة المعدات',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      WaterFilterSystem(
        id: 'sys_003',
        customerId: 'cust_003',
        productId: 'prod_001',
        serialNumber: 'WF2024003',
        installationDate: DateTime.now().subtract(const Duration(days: 15)),
        status: FilterSystemStatus.active,
        nextMaintenanceDate: DateTime.now().add(const Duration(days: 165)),
        totalCost: 2700.0,
        paidAmount: 2700.0,
        remainingAmount: 0.0,
        isUnderWarranty: true,
        warrantyEndDate: DateTime.now().add(const Duration(days: 350)),
        installationNotes: 'تم التركيب والدفع كاملاً',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      WaterFilterSystem(
        id: 'sys_004',
        customerId: 'cust_001',
        productId: 'prod_002',
        serialNumber: 'WF2024004',
        installationDate: DateTime.now().subtract(const Duration(days: 45)),
        status: FilterSystemStatus.active,
        nextMaintenanceDate: DateTime.now().add(const Duration(days: 75)),
        totalCost: 9000.0,
        paidAmount: 3000.0,
        remainingAmount: 6000.0,
        isUnderWarranty: true,
        warrantyEndDate: DateTime.now().add(const Duration(days: 685)),
        installationNotes: 'نظام تجاري متقدم',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      WaterFilterSystem(
        id: 'sys_005',
        customerId: 'cust_002',
        productId: 'prod_001',
        serialNumber: 'WF2024005',
        installationDate: DateTime.now().subtract(const Duration(days: 200)),
        status: FilterSystemStatus.needsMaintenance,
        nextMaintenanceDate: DateTime.now().subtract(const Duration(days: 10)),
        totalCost: 2700.0,
        paidAmount: 2700.0,
        remainingAmount: 0.0,
        isUnderWarranty: true,
        warrantyEndDate: DateTime.now().add(const Duration(days: 165)),
        installationNotes: 'يحتاج صيانة دورية',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];

    for (final system in systems) {
      await WaterFilterService.saveData(
        'Systems/${system.id}',
        system.toJson(),
      );
    }
    print('✅ تم إضافة ${systems.length} نظام تجريبي');
  }

  static Future<void> _addTestInstallments() async {
    final installments = [
      // أقساط النظام الأول (sys_001)
      WaterFilterInstallment(
        id: 'inst_001',
        systemId: 'sys_001',
        installmentNumber: 1,
        amount: 500.0,
        dueDate: DateTime.now().add(const Duration(days: 30)),
        status: InstallmentStatus.pending,
        notes: 'القسط الأول',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      WaterFilterInstallment(
        id: 'inst_002',
        systemId: 'sys_001',
        installmentNumber: 2,
        amount: 500.0,
        dueDate: DateTime.now().add(const Duration(days: 60)),
        status: InstallmentStatus.pending,
        notes: 'القسط الثاني',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      WaterFilterInstallment(
        id: 'inst_003',
        systemId: 'sys_001',
        installmentNumber: 3,
        amount: 700.0,
        dueDate: DateTime.now().add(const Duration(days: 90)),
        status: InstallmentStatus.pending,
        notes: 'القسط الأخير',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),

      // أقساط النظام الثاني (sys_002)
      WaterFilterInstallment(
        id: 'inst_004',
        systemId: 'sys_002',
        installmentNumber: 1,
        amount: 1000.0,
        dueDate: DateTime.now().subtract(const Duration(days: 5)), // متأخر
        status: InstallmentStatus.overdue,
        notes: 'القسط الأول - متأخر',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      WaterFilterInstallment(
        id: 'inst_005',
        systemId: 'sys_002',
        installmentNumber: 2,
        amount: 1000.0,
        dueDate: DateTime.now().add(const Duration(days: 25)),
        status: InstallmentStatus.pending,
        notes: 'القسط الثاني',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      WaterFilterInstallment(
        id: 'inst_006',
        systemId: 'sys_002',
        installmentNumber: 3,
        amount: 1500.0,
        dueDate: DateTime.now().add(const Duration(days: 55)),
        status: InstallmentStatus.pending,
        notes: 'القسط الثالث',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      WaterFilterInstallment(
        id: 'inst_007',
        systemId: 'sys_002',
        installmentNumber: 4,
        amount: 1500.0,
        dueDate: DateTime.now().add(const Duration(days: 85)),
        status: InstallmentStatus.pending,
        notes: 'القسط الأخير',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),

      // أقساط النظام الرابع (sys_004) - نظام كبير بأقساط كثيرة
      WaterFilterInstallment(
        id: 'inst_008',
        systemId: 'sys_004',
        installmentNumber: 1,
        amount: 1500.0,
        dueDate: DateTime.now().add(const Duration(days: 15)),
        status: InstallmentStatus.pending,
        notes: 'القسط الأول من النظام التجاري',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      WaterFilterInstallment(
        id: 'inst_009',
        systemId: 'sys_004',
        installmentNumber: 2,
        amount: 1500.0,
        dueDate: DateTime.now().add(const Duration(days: 45)),
        status: InstallmentStatus.pending,
        notes: 'القسط الثاني من النظام التجاري',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      WaterFilterInstallment(
        id: 'inst_010',
        systemId: 'sys_004',
        installmentNumber: 3,
        amount: 1500.0,
        dueDate: DateTime.now().add(const Duration(days: 75)),
        status: InstallmentStatus.pending,
        notes: 'القسط الثالث من النظام التجاري',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      WaterFilterInstallment(
        id: 'inst_011',
        systemId: 'sys_004',
        installmentNumber: 4,
        amount: 1500.0,
        dueDate: DateTime.now().add(const Duration(days: 105)),
        status: InstallmentStatus.pending,
        notes: 'القسط الأخير من النظام التجاري',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];

    for (final installment in installments) {
      await WaterFilterService.saveData(
        'Installments/${installment.id}',
        installment.toJson(),
      );
    }
    print('✅ تم إضافة ${installments.length} قسط تجريبي');
  }

  /// حذف جميع البيانات التجريبية
  static Future<void> clearTestData() async {
    try {
      print('🗑️ بدء حذف البيانات التجريبية...');

      await WaterFilterService.deleteData('Products');
      await WaterFilterService.deleteData('Customers');
      await WaterFilterService.deleteData('Systems');
      await WaterFilterService.deleteData('Installments');

      print('✅ تم حذف البيانات التجريبية بنجاح');
    } catch (e) {
      print('❌ خطأ في حذف البيانات التجريبية: $e');
    }
  }
}
