import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/models/hrm_models.dart';
// import 'package:nb_utils/nb_utils.dart'; // غير مستخدم
import 'add_employee.dart';
import 'provider/employee_provider.dart';

class EmployeeListScreen extends ConsumerStatefulWidget {
  const EmployeeListScreen({super.key});

  static const String route = '/hrm/employee-list';

  @override
  ConsumerState<EmployeeListScreen> createState() => _EmployeeListScreenState();
}

class _EmployeeListScreenState extends ConsumerState<EmployeeListScreen> {
  String searchQuery = '';
  String selectedStatus = 'الكل';
  String selectedDepartment = 'الكل';

  @override
  Widget build(BuildContext context) {
    final employeesAsyncValue = ref.watch(employeeListProvider);

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('قائمة الموظفين'),
        backgroundColor: kMainColor,
        centerTitle: true,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        titleTextStyle: const TextStyle(
            color: Colors.white, fontSize: 20, fontWeight: FontWeight.bold),
      ),
      body: employeesAsyncValue.when(
        data: (employees) {
          // استخراج الأقسام الفريدة للفلترة
          final departments = [
            'الكل',
            ...employees.map((e) => e.department).toSet()
          ];

          // تطبيق الفلترة
          List<EmployeeModel> filteredEmployees = employees.where((employee) {
            // فلترة البحث
            bool matchesSearch = searchQuery.isEmpty ||
                employee.name
                    .toLowerCase()
                    .contains(searchQuery.toLowerCase()) ||
                employee.phone
                    .toLowerCase()
                    .contains(searchQuery.toLowerCase()) ||
                employee.email
                    .toLowerCase()
                    .contains(searchQuery.toLowerCase()) ||
                employee.designation
                    .toLowerCase()
                    .contains(searchQuery.toLowerCase());

            // فلترة الحالة
            bool matchesStatus = selectedStatus == 'الكل' ||
                (selectedStatus == 'نشط' && employee.isActive) ||
                (selectedStatus == 'غير نشط' && !employee.isActive);

            // فلترة القسم
            bool matchesDepartment = selectedDepartment == 'الكل' ||
                employee.department == selectedDepartment;

            return matchesSearch && matchesStatus && matchesDepartment;
          }).toList();

          return Column(
            children: [
              // شريط البحث والفلترة
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    // حقل البحث
                    TextField(
                      decoration: InputDecoration(
                        hintText: 'بحث بالاسم أو الهاتف أو البريد الإلكتروني',
                        prefixIcon: const Icon(Icons.search),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                      onChanged: (value) {
                        setState(() {
                          searchQuery = value;
                        });
                      },
                    ),
                    const SizedBox(height: 16),

                    // أدوات الفلترة
                    Row(
                      children: [
                        // فلترة الحالة
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            decoration: const InputDecoration(
                              labelText: 'الحالة',
                              border: OutlineInputBorder(),
                            ),
                            value: selectedStatus,
                            items: const [
                              DropdownMenuItem(
                                  value: 'الكل', child: Text('الكل')),
                              DropdownMenuItem(
                                  value: 'نشط', child: Text('نشط')),
                              DropdownMenuItem(
                                  value: 'غير نشط', child: Text('غير نشط')),
                            ],
                            onChanged: (value) {
                              setState(() {
                                selectedStatus = value!;
                              });
                            },
                          ),
                        ),
                        const SizedBox(width: 16),

                        // فلترة القسم
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            decoration: const InputDecoration(
                              labelText: 'القسم',
                              border: OutlineInputBorder(),
                            ),
                            value: selectedDepartment,
                            items: departments.map((department) {
                              return DropdownMenuItem(
                                value: department,
                                child: Text(department),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                selectedDepartment = value!;
                              });
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // عدد النتائج
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'عدد النتائج: ${filteredEmployees.length}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    ElevatedButton.icon(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: kMainColor,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 8),
                      ),
                      onPressed: () {
                        _showAddEmployeeDialog(context);
                      },
                      icon:
                          const Icon(Icons.add, color: Colors.white, size: 18),
                      label: const Text('إضافة موظف',
                          style: TextStyle(color: Colors.white)),
                    ),
                  ],
                ),
              ),

              // قائمة الموظفين
              Expanded(
                child: filteredEmployees.isEmpty
                    ? const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.people_outline,
                                size: 80, color: Colors.grey),
                            SizedBox(height: 16),
                            Text(
                              'لا يوجد موظفين',
                              style: TextStyle(
                                  fontSize: 18, fontWeight: FontWeight.bold),
                            ),
                            SizedBox(height: 8),
                            Text(
                              'قم بإضافة موظفين جدد',
                              style: TextStyle(color: Colors.grey),
                            ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        itemCount: filteredEmployees.length,
                        itemBuilder: (context, index) {
                          final employee = filteredEmployees[index];
                          return Card(
                            margin: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 8),
                            elevation: 2,
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12)),
                            child: ListTile(
                              leading: CircleAvatar(
                                backgroundColor: kMainColor,
                                child: Text(
                                  employee.name.isNotEmpty
                                      ? employee.name[0].toUpperCase()
                                      : '?',
                                  style: const TextStyle(color: Colors.white),
                                ),
                              ),
                              title: Text(
                                employee.name,
                                style: const TextStyle(
                                    fontWeight: FontWeight.bold),
                              ),
                              subtitle: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                      'المسمى الوظيفي: ${employee.designation}'),
                                  Text('القسم: ${employee.department}'),
                                  Text('الهاتف: ${employee.phone}'),
                                ],
                              ),
                              trailing: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 8, vertical: 4),
                                    decoration: BoxDecoration(
                                      color: employee.isActive
                                          ? Colors.green.withAlpha(51)
                                          : Colors.red.withAlpha(51),
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(
                                          color: employee.isActive
                                              ? Colors.green
                                              : Colors.red),
                                    ),
                                    child: Text(
                                      employee.isActive ? 'نشط' : 'غير نشط',
                                      style: TextStyle(
                                        color: employee.isActive
                                            ? Colors.green
                                            : Colors.red,
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                  IconButton(
                                    icon: const Icon(Icons.edit,
                                        color: Colors.blue),
                                    onPressed: () {
                                      _editEmployee(employee);
                                    },
                                  ),
                                  IconButton(
                                    icon: const Icon(Icons.delete,
                                        color: Colors.red),
                                    onPressed: () {
                                      _deleteEmployee(employee);
                                    },
                                  ),
                                ],
                              ),
                              isThreeLine: true,
                            ),
                          );
                        },
                      ),
              ),
            ],
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Text('حدث خطأ: $error',
              style: const TextStyle(color: Colors.red)),
        ),
      ),
    );
  }

  void _showAddEmployeeDialog(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Consumer(builder: (context, ref, _) {
          return AddEmployeeScreen(
            listOfEmployees: ref.read(employeeListProvider).value ?? [],
            employeeModel: null,
          );
        }),
      ),
    );
  }

  void _editEmployee(EmployeeModel employee) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Consumer(builder: (context, ref, _) {
          return AddEmployeeScreen(
            listOfEmployees: ref.read(employeeListProvider).value ?? [],
            employeeModel: employee,
          );
        }),
      ),
    );
  }

  void _deleteEmployee(EmployeeModel employee) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذا الموظف؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              // لا نحتاج إلى حفظ نسخة من السياق لأننا نستخدم EasyLoading

              Navigator.pop(context);
              EasyLoading.show(status: 'جاري الحذف...');

              try {
                await ref
                    .read(employeeNotifierProvider.notifier)
                    .deleteEmployee(employee.id);

                // التحقق من أن الويدجت لا تزال مثبتة قبل استخدام السياق
                if (mounted) {
                  EasyLoading.showSuccess('تم حذف الموظف بنجاح');
                }
              } catch (e) {
                if (mounted) {
                  EasyLoading.showError('حدث خطأ أثناء الحذف: $e');
                }
              }
            },
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}
