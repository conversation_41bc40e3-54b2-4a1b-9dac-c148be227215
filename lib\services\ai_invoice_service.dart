import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import '../constant.dart';
import '../currency.dart';
import '../model/transition_model.dart';
import '../model/add_to_cart_model.dart';
import '../model/product_model.dart';
import '../model/daily_transaction_model.dart';
import '../Screens/Chat/services/database_service.dart';

// Type alias للتأكد من التعرف على الكلاس
typedef PurchaseTransaction = PurchaseTransactionModel;

/// خدمة حفظ الفواتير المنشأة بالذكاء الاصطناعي
class AIInvoiceService {
  /// حفظ فاتورة مبيعات
  static Future<Map<String, dynamic>> saveSalesInvoice(
      Map<String, dynamic> invoiceData) async {
    try {
      debugPrint('💾 بدء حفظ فاتورة مبيعات بالذكاء الاصطناعي');

      final entity = invoiceData['entity'];
      final items = invoiceData['items'] as List<Map<String, dynamic>>;
      final totalAmount = invoiceData['total_amount'] as double;
      final paymentType = invoiceData['payment_type'] ?? 'cash';

      // إنشاء نموذج الفاتورة
      SalesTransitionModel transitionModel = SalesTransitionModel(
        customerName: entity['customerName'],
        customerPhone: entity['phoneNumber'],
        customerAddress: entity['customerAddress'] ?? '',
        customerType: entity['type'] ?? 'Customer',
        customerGst: entity['customerGst'] ?? '',
        customerImage: entity['customerImage'] ?? '',
        invoiceNumber: await _getNextSalesInvoiceNumber(),
        purchaseDate: DateTime.now().millisecondsSinceEpoch.toString(),
        totalAmount: totalAmount,
        discountAmount: 0,
        dueAmount: paymentType == 'cash' ? 0 : totalAmount,
        returnAmount: 0,
        paymentType: paymentType,
        isPaid: paymentType == 'cash',
        productList: await _convertToCartItems(items, false),
        vat: 0,
        serviceCharge: 0,
        totalQuantity: _calculateTotalQuantity(items),
        lossProfit: _calculateProfit(items),
        sellerName: 'AI Assistant',
      );

      // حفظ الفاتورة في قاعدة البيانات
      DatabaseReference ref =
          FirebaseDatabase.instance.ref("$constUserId/Sales Transition");
      ref.keepSynced(true);
      await ref.push().set(transitionModel.toJson());

      // تحديث المخزون
      await _updateStock(items, false);

      // حفظ المعاملة اليومية
      await _saveDailyTransaction(transitionModel, 'Sale');

      // إرسال إشعار
      await _sendNotification(transitionModel, false);

      debugPrint('✅ تم حفظ فاتورة المبيعات بنجاح');

      return {
        'success': true,
        'message': 'تم حفظ فاتورة المبيعات بنجاح',
        'invoice_number': transitionModel.invoiceNumber,
        'total_amount': totalAmount,
      };
    } catch (e) {
      debugPrint('❌ خطأ في حفظ فاتورة المبيعات: $e');
      return {
        'success': false,
        'message': 'حدث خطأ في حفظ فاتورة المبيعات: $e',
      };
    }
  }

  /// حفظ فاتورة مشتريات
  static Future<Map<String, dynamic>> savePurchaseInvoice(
      Map<String, dynamic> invoiceData) async {
    try {
      debugPrint('💾 بدء حفظ فاتورة مشتريات بالذكاء الاصطناعي');

      final entity = invoiceData['entity'];
      final items = invoiceData['items'] as List<Map<String, dynamic>>;
      final totalAmount = invoiceData['total_amount'] as double;
      final paymentType = invoiceData['payment_type'] ?? 'cash';

      // إنشاء نموذج الفاتورة
      PurchaseTransactionModel transitionModel = PurchaseTransactionModel(
        customerName: entity['customerName'],
        customerPhone: entity['phoneNumber'],
        customerAddress: entity['customerAddress'] ?? '',
        customerType: entity['type'] ?? 'Supplier',
        customerGst: entity['customerGst'] ?? '',
        invoiceNumber: await _getNextPurchaseInvoiceNumber(),
        purchaseDate: DateTime.now().millisecondsSinceEpoch.toString(),
        totalAmount: totalAmount,
        discountAmount: 0,
        dueAmount: paymentType == 'cash' ? 0 : totalAmount,
        returnAmount: 0,
        paymentType: paymentType,
        isPaid: paymentType == 'cash',
        productList: await _convertToPurchaseItems(items),
      );

      // حفظ الفاتورة في قاعدة البيانات
      DatabaseReference ref =
          FirebaseDatabase.instance.ref("$constUserId/Purchase Transition");
      ref.keepSynced(true);
      await ref.push().set(transitionModel.toJson());

      // تحديث المخزون (زيادة)
      await _updateStock(items, true);

      // حفظ المعاملة اليومية
      await _saveDailyTransactionPurchase(transitionModel);

      debugPrint('✅ تم حفظ فاتورة المشتريات بنجاح');

      return {
        'success': true,
        'message': 'تم حفظ فاتورة المشتريات بنجاح',
        'invoice_number': transitionModel.invoiceNumber,
        'total_amount': totalAmount,
      };
    } catch (e) {
      debugPrint('❌ خطأ في حفظ فاتورة المشتريات: $e');
      return {
        'success': false,
        'message': 'حدث خطأ في حفظ فاتورة المشتريات: $e',
      };
    }
  }

  /// الحصول على رقم فاتورة المبيعات التالي
  static Future<String> _getNextSalesInvoiceNumber() async {
    try {
      final DatabaseReference counterRef = FirebaseDatabase.instance
          .ref()
          .child(constUserId)
          .child('Personal Information')
          .child('saleInvoiceCounter');

      final TransactionResult result =
          await counterRef.runTransaction((Object? currentValue) {
        int currentCounter = (currentValue as int?) ?? 1;
        return Transaction.success(currentCounter + 1);
      });

      if (result.committed) {
        return (result.snapshot.value as int).toString();
      } else {
        throw Exception("فشل في الحصول على رقم الفاتورة");
      }
    } catch (e) {
      debugPrint('خطأ في الحصول على رقم فاتورة المبيعات: $e');
      return DateTime.now().millisecondsSinceEpoch.toString();
    }
  }

  /// الحصول على رقم فاتورة المشتريات التالي
  static Future<String> _getNextPurchaseInvoiceNumber() async {
    try {
      final DatabaseReference counterRef = FirebaseDatabase.instance
          .ref()
          .child(constUserId)
          .child('Personal Information')
          .child('purchaseInvoiceCounter');

      final TransactionResult result =
          await counterRef.runTransaction((Object? currentValue) {
        int currentCounter = (currentValue as int?) ?? 1;
        return Transaction.success(currentCounter + 1);
      });

      if (result.committed) {
        return (result.snapshot.value as int).toString();
      } else {
        throw Exception("فشل في الحصول على رقم الفاتورة");
      }
    } catch (e) {
      debugPrint('خطأ في الحصول على رقم فاتورة المشتريات: $e');
      return DateTime.now().millisecondsSinceEpoch.toString();
    }
  }

  /// تحويل المنتجات إلى عناصر السلة للمبيعات
  static Future<List<AddToCartModel>> _convertToCartItems(
      List<Map<String, dynamic>> items, bool isPurchase) async {
    List<AddToCartModel> cartItems = [];

    for (var item in items) {
      final product = item['product'];
      final quantity = item['quantity'];
      final unitPrice = item['unit_price'];

      AddToCartModel cartItem = AddToCartModel(
        uuid: product['productCode'] ??
            DateTime.now().millisecondsSinceEpoch.toString(),
        productId: product['productCode'],
        productName: product['productName'],
        warehouseName: 'المخزن الرئيسي',
        warehouseId: '1',
        unitPrice: unitPrice,
        subTotal: unitPrice * quantity,
        quantity: quantity,
        productDetails: null,
        productBrandName: product['productBrandName'] ?? '',
        stock: int.tryParse(product['productStock']?.toString() ?? '0'),
        productPurchasePrice:
            double.tryParse(product['productPurchasePrice']?.toString() ?? '0'),
        productImage: product['productPicture'] ?? '',
        taxType: 'Exclusive',
        margin: 0,
        excTax: 0,
        incTax: 0,
        groupTaxName: '',
        groupTaxRate: 0,
        subTaxes: [],
      );

      cartItems.add(cartItem);
    }

    return cartItems;
  }

  /// تحويل المنتجات إلى عناصر المشتريات
  static Future<List<ProductModel>> _convertToPurchaseItems(
      List<Map<String, dynamic>> items) async {
    List<ProductModel> purchaseItems = [];

    for (var item in items) {
      final product = item['product'];
      final quantity = item['quantity'];

      ProductModel purchaseItem = ProductModel(
        productName: product['productName'] ?? '',
        productDescription: product['productDescription'] ?? '',
        productCategory: product['productCategory'] ?? '',
        size: product['size'] ?? '',
        color: product['color'] ?? '',
        weight: product['weight'] ?? '',
        capacity: product['capacity'] ?? '',
        type: product['type'] ?? '',
        brandName: product['brandName'] ?? '',
        productCode: product['productCode'] ?? '',
        productStock: quantity.toString(),
        productUnit: product['productUnit'] ?? 'قطعة',
        productSalePrice: product['productSalePrice']?.toString() ?? '0',
        productPurchasePrice:
            product['productPurchasePrice']?.toString() ?? '0',
        productDiscount: product['productDiscount']?.toString() ?? '0',
        productWholeSalePrice:
            product['productWholeSalePrice']?.toString() ?? '0',
        productDealerPrice: product['productDealerPrice']?.toString() ?? '0',
        productManufacturer: product['productManufacturer'] ?? '',
        warehouseName: product['warehouseName'] ?? 'المخزن الرئيسي',
        warehouseId: product['warehouseId'] ?? '1',
        productPicture: product['productPicture'] ?? '',
        serialNumber: [],
        lowerStockAlert: 5,
        taxType: 'Exclusive',
        margin: 0,
        excTax: 0,
        incTax: 0,
        groupTaxName: '',
        groupTaxRate: 0,
        subTaxes: [],
      );

      purchaseItems.add(purchaseItem);
    }

    return purchaseItems;
  }

  /// حساب إجمالي الكمية
  static int _calculateTotalQuantity(List<Map<String, dynamic>> items) {
    int total = 0;
    for (var item in items) {
      total += item['quantity'] as int;
    }
    return total;
  }

  /// حساب الربح
  static double _calculateProfit(List<Map<String, dynamic>> items) {
    double profit = 0;
    for (var item in items) {
      final product = item['product'];
      final quantity = item['quantity'] as int;
      final salePrice =
          double.tryParse(product['productSalePrice']?.toString() ?? '0') ?? 0;
      final purchasePrice =
          double.tryParse(product['productPurchasePrice']?.toString() ?? '0') ??
              0;
      profit += (salePrice - purchasePrice) * quantity;
    }
    return profit;
  }

  /// تحديث المخزون
  static Future<void> _updateStock(
      List<Map<String, dynamic>> items, bool isIncrease) async {
    for (var item in items) {
      final product = item['product'];
      final quantity = item['quantity'] as int;
      final productCode = product['productCode'];

      try {
        final productRef = FirebaseDatabase.instance
            .ref(constUserId)
            .child('Products')
            .child(productCode);

        final snapshot = await productRef.get();
        if (snapshot.exists) {
          final currentStock = int.tryParse(
                  snapshot.child('productStock').value?.toString() ?? '0') ??
              0;
          final newStock =
              isIncrease ? currentStock + quantity : currentStock - quantity;

          await productRef.update({'productStock': newStock.toString()});
          debugPrint(
              'تم تحديث مخزون المنتج $productCode: $currentStock -> $newStock');
        }
      } catch (e) {
        debugPrint('خطأ في تحديث مخزون المنتج $productCode: $e');
      }
    }
  }

  /// حفظ المعاملة اليومية للمبيعات
  static Future<void> _saveDailyTransaction(
      SalesTransitionModel transaction, String type) async {
    try {
      DailyTransactionModel dailyTransaction = DailyTransactionModel(
        name: transaction.customerName,
        date: transaction.purchaseDate,
        type: type,
        total: transaction.totalAmount!.toDouble(),
        paymentIn: transaction.totalAmount!.toDouble() -
            transaction.dueAmount!.toDouble(),
        paymentOut: 0,
        remainingBalance: transaction.totalAmount!.toDouble() -
            transaction.dueAmount!.toDouble(),
        id: transaction.invoiceNumber,
        saleTransactionModel: transaction,
      );

      DatabaseReference ref =
          FirebaseDatabase.instance.ref("$constUserId/Daily Transaction");
      await ref.push().set(dailyTransaction.toJson());
    } catch (e) {
      debugPrint('خطأ في حفظ المعاملة اليومية: $e');
    }
  }

  /// حفظ المعاملة اليومية للمشتريات
  static Future<void> _saveDailyTransactionPurchase(
      PurchaseTransaction transaction) async {
    try {
      DailyTransactionModel dailyTransaction = DailyTransactionModel(
        name: transaction.customerName,
        date: transaction.purchaseDate,
        type: 'Purchase',
        total: transaction.totalAmount!.toDouble(),
        paymentIn: 0,
        paymentOut: transaction.totalAmount!.toDouble() -
            transaction.dueAmount!.toDouble(),
        remainingBalance: -(transaction.totalAmount!.toDouble() -
            transaction.dueAmount!.toDouble()),
        id: transaction.invoiceNumber,
        purchaseTransactionModel: transaction,
      );

      DatabaseReference ref =
          FirebaseDatabase.instance.ref("$constUserId/Daily Transaction");
      await ref.push().set(dailyTransaction.toJson());
    } catch (e) {
      debugPrint('خطأ في حفظ المعاملة اليومية للمشتريات: $e');
    }
  }

  /// إرسال إشعار
  static Future<void> _sendNotification(
      SalesTransitionModel transaction, bool isPurchase) async {
    try {
      // يمكن إضافة منطق الإشعارات هنا
      debugPrint('تم إرسال إشعار للفاتورة ${transaction.invoiceNumber}');
    } catch (e) {
      debugPrint('خطأ في إرسال الإشعار: $e');
    }
  }
}
