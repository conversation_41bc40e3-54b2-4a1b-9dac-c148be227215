import 'dart:convert';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// خدمة لإدارة الإشعارات المخفية للمستخدم الحالي
class HiddenNotificationsService {
  // قائمة معرفات الإشعارات المخفية
  static final List<String> _hiddenNotificationIds = [];

  /// تهيئة الخدمة
  static Future<void> initialize() async {
    await _loadHiddenNotifications();
  }

  /// تحميل قائمة الإشعارات المخفية من التخزين المحلي
  static Future<void> _loadHiddenNotifications() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = FirebaseAuth.instance.currentUser?.uid;
      
      if (userId == null) {
        debugPrint('لم يتم تسجيل الدخول، لا يمكن تحميل الإشعارات المخفية');
        return;
      }

      final hiddenNotificationsString = prefs.getString('hidden_notifications_$userId');
      
      if (hiddenNotificationsString != null) {
        final hiddenNotifications = List<String>.from(jsonDecode(hiddenNotificationsString));
        _hiddenNotificationIds.clear();
        _hiddenNotificationIds.addAll(hiddenNotifications);
        debugPrint('تم تحميل ${_hiddenNotificationIds.length} إشعار مخفي');
      }
    } catch (e) {
      debugPrint('خطأ في تحميل الإشعارات المخفية: $e');
      _hiddenNotificationIds.clear();
    }
  }

  /// حفظ قائمة الإشعارات المخفية في التخزين المحلي
  static Future<void> _saveHiddenNotifications() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = FirebaseAuth.instance.currentUser?.uid;
      
      if (userId == null) {
        debugPrint('لم يتم تسجيل الدخول، لا يمكن حفظ الإشعارات المخفية');
        return;
      }

      await prefs.setString('hidden_notifications_$userId', jsonEncode(_hiddenNotificationIds));
      debugPrint('تم حفظ ${_hiddenNotificationIds.length} إشعار مخفي');
    } catch (e) {
      debugPrint('خطأ في حفظ الإشعارات المخفية: $e');
    }
  }

  /// إضافة إشعار إلى قائمة الإشعارات المخفية
  static Future<void> hideNotification(String notificationId) async {
    if (!_hiddenNotificationIds.contains(notificationId)) {
      _hiddenNotificationIds.add(notificationId);
      await _saveHiddenNotifications();
    }
  }

  /// التحقق مما إذا كان الإشعار مخفياً
  static bool isNotificationHidden(String notificationId) {
    return _hiddenNotificationIds.contains(notificationId);
  }

  /// الحصول على قائمة معرفات الإشعارات المخفية
  static List<String> getHiddenNotificationIds() {
    return List.from(_hiddenNotificationIds);
  }

  /// إزالة إشعار من قائمة الإشعارات المخفية
  static Future<void> unhideNotification(String notificationId) async {
    if (_hiddenNotificationIds.contains(notificationId)) {
      _hiddenNotificationIds.remove(notificationId);
      await _saveHiddenNotifications();
    }
  }

  /// مسح جميع الإشعارات المخفية
  static Future<void> clearHiddenNotifications() async {
    _hiddenNotificationIds.clear();
    await _saveHiddenNotifications();
  }
}
