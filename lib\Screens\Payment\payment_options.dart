// ignore_for_file: unused_import

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/Screens/Payment/payment_complete.dart';
import 'package:mobile_pos/generated/l10n.dart' as lang;
import 'package:nb_utils/nb_utils.dart';

class PaymentOptions extends StatefulWidget {
  const PaymentOptions({super.key});

  @override
  // ignore: library_private_types_in_public_api
  _PaymentOptionsState createState() => _PaymentOptionsState();
}

class _PaymentOptionsState extends State<PaymentOptions> {
  String radioItem = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          lang.S.of(context).payment,
          style: GoogleFonts.poppins(
            color: Colors.black,
            fontSize: 20.0,
          ),
        ),
        iconTheme: const IconThemeData(color: Colors.black),
        centerTitle: true,
        backgroundColor: Colors.white,
        elevation: 0.0,
        actions: [
          PopupMenuButton(
            itemBuilder: (BuildContext bc) => [
              const PopupMenuItem(
                  value: "/addPromoCode", child: Text('Add Promo Code')),
              const PopupMenuItem(
                  value: "/addDiscount", child: Text('Add Discount')),
              const PopupMenuItem(
                  value: "/settings", child: Text('Cancel All Product')),
              const PopupMenuItem(
                  value: "/settings", child: Text('Vat Doesn\'t Apply')),
            ],
            onSelected: (value) {
              Navigator.pushNamed(context, value);
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          children: [
            SizedBox(
              height: 50.0,
              width: MediaQuery.of(context).size.width,
              child: ListTile(
                onTap: () {
                  Navigator.pushReplacement(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const PaymentCompleted()),
                  );
                },
                title: Row(
                  children: [
                    const Image(
                      image: AssetImage('assets/images/cash.png'),
                    ),
                    const SizedBox(
                      width: 15.0,
                    ),
                    Text(
                      lang.S.of(context).cash,
                      style: GoogleFonts.poppins(
                        fontSize: 15.0,
                      ),
                    ),
                  ],
                ),
                leading: const Icon(Icons.check_circle_outline),
              ),
            ),
            SizedBox(
              height: 50.0,
              width: MediaQuery.of(context).size.width,
              child: ListTile(
                onTap: () {
                  Navigator.pushReplacement(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const PaymentCompleted()),
                  );
                },
                title: Row(
                  children: [
                    const Image(
                      image: AssetImage('assets/images/back_card.png'),
                    ),
                    const SizedBox(
                      width: 15.0,
                    ),
                    Text(
                      'Bank Transfer',
                      style: GoogleFonts.poppins(
                        fontSize: 15.0,
                      ),
                    ),
                  ],
                ),
                leading: const Icon(Icons.check_circle_outline),
              ),
            ),
            SizedBox(
              height: 50.0,
              width: MediaQuery.of(context).size.width,
              child: ListTile(
                onTap: () {
                  Navigator.pushReplacement(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const PaymentCompleted()),
                  );
                },
                title: Row(
                  children: [
                    const Image(
                      image: AssetImage('assets/images/instrument.png'),
                    ),
                    const SizedBox(
                      width: 15.0,
                    ),
                    Text(
                      lang.S.of(context).inistrument,
                      style: GoogleFonts.poppins(
                        fontSize: 15.0,
                      ),
                    ),
                  ],
                ),
                leading: const Icon(Icons.check_circle_outline),
              ),
            ),
            const Spacer(),
          ],
        ),
      ),
    );
  }
}
