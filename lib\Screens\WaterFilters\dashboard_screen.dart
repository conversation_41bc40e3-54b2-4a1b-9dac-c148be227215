import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/services/water_filter_cache_service.dart';
import 'package:mobile_pos/services/water_filter_notification_service.dart';
import 'package:mobile_pos/Screens/WaterFilters/Systems/water_filter_systems_screen.dart';
import 'package:mobile_pos/Screens/WaterFilters/Maintenance/maintenance_schedule_screen.dart';
import 'package:mobile_pos/Screens/WaterFilters/Products/water_filter_products_screen.dart';
import 'package:mobile_pos/Screens/WaterFilters/Installments/installments_tracking_screen.dart';

class WaterFilterDashboardScreen extends StatefulWidget {
  const WaterFilterDashboardScreen({super.key});

  @override
  State<WaterFilterDashboardScreen> createState() =>
      _WaterFilterDashboardScreenState();
}

class _WaterFilterDashboardScreenState
    extends State<WaterFilterDashboardScreen> {
  final WaterFilterCacheService _cacheService = WaterFilterCacheService();
  final WaterFilterNotificationService _notificationService =
      WaterFilterNotificationService();

  Map<String, dynamic> _dashboardData = {};
  Map<String, int> _notificationStats = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    setState(() => _isLoading = true);

    try {
      // تحميل البيانات بشكل متوازي
      final results = await Future.wait([
        _cacheService.getCachedStats(forceRefresh: true),
        _notificationService.getNotificationStats(),
        _loadQuickMetrics(),
      ]);

      setState(() {
        _dashboardData = results[0];
        _notificationStats = Map<String, int>.from(results[1]);
        _dashboardData.addAll(results[2]);
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      debugPrint('خطأ في تحميل بيانات Dashboard: $e');
    }
  }

  Future<Map<String, dynamic>> _loadQuickMetrics() async {
    try {
      final systems = await _cacheService.getCachedSystems();
      final products = await _cacheService.getCachedProducts();
      final customers = await _cacheService.getCachedCustomers();

      final now = DateTime.now();
      final thisMonth = DateTime(now.year, now.month);

      // حساب المقاييس السريعة
      final newSystemsThisMonth =
          systems.where((s) => s.installationDate.isAfter(thisMonth)).length;

      final lowStockProducts = products.where((p) => p.stock <= 5).length;

      final systemsNeedingMaintenance = systems
          .where((s) =>
              s.status.name == 'needsMaintenance' ||
              s.nextMaintenanceDate.isBefore(now))
          .length;

      final totalRevenue = systems.fold(0.0, (sum, s) => sum + s.totalCost);
      final totalPaid = systems.fold(0.0, (sum, s) => sum + s.paidAmount);
      final totalPending = totalRevenue - totalPaid;

      return {
        'newSystemsThisMonth': newSystemsThisMonth,
        'lowStockProducts': lowStockProducts,
        'systemsNeedingMaintenance': systemsNeedingMaintenance,
        'totalRevenue': totalRevenue,
        'totalPaid': totalPaid,
        'totalPending': totalPending,
        'totalCustomers': customers.length,
        'totalProducts': products.length,
        'totalSystems': systems.length,
      };
    } catch (e) {
      debugPrint('خطأ في حساب المقاييس السريعة: $e');
      return {};
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        title: Text(
          'لوحة التحكم - فلاتر المياه',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: _loadDashboardData,
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : RefreshIndicator(
                onRefresh: _loadDashboardData,
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // إحصائيات سريعة
                      _buildQuickStats(),

                      const SizedBox(height: 24),

                      // الإشعارات والتنبيهات
                      _buildNotificationsSection(),

                      const SizedBox(height: 24),

                      // المقاييس المالية
                      _buildFinancialMetrics(),

                      const SizedBox(height: 24),

                      // الإجراءات السريعة
                      _buildQuickActions(),

                      const SizedBox(height: 24),

                      // الرسوم البيانية
                      _buildChartsSection(),
                    ],
                  ),
                ),
              ),
      ),
    );
  }

  Widget _buildQuickStats() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نظرة سريعة',
          style: GoogleFonts.cairo(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: kMainColor,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.2,
          children: [
            _buildStatCard(
              title: 'إجمالي الأنظمة',
              value: '${_dashboardData['totalSystems'] ?? 0}',
              icon: Icons.settings,
              color: Colors.blue,
              onTap: () => Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => const WaterFilterSystemsScreen())),
            ),
            _buildStatCard(
              title: 'إجمالي العملاء',
              value: '${_dashboardData['totalCustomers'] ?? 0}',
              icon: Icons.people,
              color: Colors.green,
            ),
            _buildStatCard(
              title: 'إجمالي المنتجات',
              value: '${_dashboardData['totalProducts'] ?? 0}',
              icon: Icons.inventory,
              color: Colors.orange,
              onTap: () => Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => const WaterFilterProductsScreen())),
            ),
            _buildStatCard(
              title: 'أنظمة جديدة',
              value: '${_dashboardData['newSystemsThisMonth'] ?? 0}',
              icon: Icons.add_circle,
              color: Colors.purple,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildNotificationsSection() {
    final urgentMaintenance = _notificationStats['urgent_maintenance'] ?? 0;
    final upcomingMaintenance = _notificationStats['upcoming_maintenance'] ?? 0;
    final lowStock = _notificationStats['low_stock'] ?? 0;
    final expiringWarranty = _notificationStats['expiring_warranty'] ?? 0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'التنبيهات والإشعارات',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: kMainColor,
              ),
            ),
            if ((urgentMaintenance +
                    upcomingMaintenance +
                    lowStock +
                    expiringWarranty) >
                0)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${urgentMaintenance + upcomingMaintenance + lowStock + expiringWarranty}',
                  style: GoogleFonts.cairo(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildNotificationCard(
                title: 'صيانة فورية',
                count: urgentMaintenance,
                icon: Icons.warning,
                color: Colors.red,
                onTap: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) =>
                            const MaintenanceScheduleScreen())),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildNotificationCard(
                title: 'صيانة قادمة',
                count: upcomingMaintenance,
                icon: Icons.schedule,
                color: Colors.orange,
                onTap: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) =>
                            const MaintenanceScheduleScreen())),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildNotificationCard(
                title: 'مخزون منخفض',
                count: lowStock,
                icon: Icons.inventory_2,
                color: Colors.amber,
                onTap: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) =>
                            const WaterFilterProductsScreen())),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildNotificationCard(
                title: 'ضمان منتهي',
                count: expiringWarranty,
                icon: Icons.shield,
                color: Colors.blue,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFinancialMetrics() {
    final totalRevenue = _dashboardData['totalRevenue'] ?? 0.0;
    final totalPaid = _dashboardData['totalPaid'] ?? 0.0;
    final totalPending = _dashboardData['totalPending'] ?? 0.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المقاييس المالية',
          style: GoogleFonts.cairo(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: kMainColor,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Colors.green.withOpacity(0.1),
                Colors.green.withOpacity(0.05)
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.green.withOpacity(0.3)),
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _buildFinancialItem(
                    title: 'إجمالي الإيرادات',
                    value: '${totalRevenue.toStringAsFixed(2)} ج.م',
                    color: Colors.green,
                  ),
                  _buildFinancialItem(
                    title: 'المحصل',
                    value: '${totalPaid.toStringAsFixed(2)} ج.م',
                    color: Colors.blue,
                  ),
                ],
              ),
              const SizedBox(height: 16),
              _buildFinancialItem(
                title: 'المتبقي',
                value: '${totalPending.toStringAsFixed(2)} ج.م',
                color: Colors.orange,
                onTap: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) =>
                            const InstallmentsTrackingScreen())),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إجراءات سريعة',
          style: GoogleFonts.cairo(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: kMainColor,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildActionButton(
                title: 'فحص الإشعارات',
                icon: Icons.notifications_active,
                color: Colors.red,
                onPressed: () async {
                  await _notificationService.checkAllNotifications();
                  _loadDashboardData();
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildActionButton(
                title: 'تحديث البيانات',
                icon: Icons.refresh,
                color: Colors.blue,
                onPressed: () {
                  _cacheService.clearAllCache();
                  _loadDashboardData();
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildChartsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الرسوم البيانية',
          style: GoogleFonts.cairo(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: kMainColor,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          height: 200,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.grey.shade200),
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.bar_chart,
                  size: 48,
                  color: Colors.grey.shade400,
                ),
                const SizedBox(height: 12),
                Text(
                  'الرسوم البيانية قيد التطوير',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    color: Colors.grey.shade600,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'سيتم إضافة رسوم بيانية تفاعلية قريباً',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.grey.shade500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.shade200,
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 12),
            Text(
              value,
              style: GoogleFonts.cairo(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationCard({
    required String title,
    required int count,
    required IconData icon,
    required Color color,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.shade200,
              blurRadius: 4,
              offset: const Offset(0, 1),
            ),
          ],
          border: Border.all(
            color: count > 0 ? color.withOpacity(0.5) : Colors.grey.shade300,
          ),
        ),
        child: Column(
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  color: count > 0 ? color : Colors.grey.shade400,
                  size: 20,
                ),
                const SizedBox(width: 8),
                if (count > 0)
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: color,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      count.toString(),
                      style: GoogleFonts.cairo(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: count > 0 ? color : Colors.grey.shade600,
                fontWeight: count > 0 ? FontWeight.bold : FontWeight.normal,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialItem({
    required String title,
    required String value,
    required Color color,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: color.withOpacity(0.1),
        foregroundColor: color,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(color: color.withOpacity(0.3)),
        ),
        padding: const EdgeInsets.symmetric(vertical: 16),
      ),
      child: Column(
        children: [
          Icon(icon, size: 24),
          const SizedBox(height: 8),
          Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
