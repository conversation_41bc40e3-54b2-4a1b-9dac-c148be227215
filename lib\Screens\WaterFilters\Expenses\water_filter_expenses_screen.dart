import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/model/water_filter_models.dart';
import 'package:mobile_pos/services/water_filter_expense_service.dart';
import 'package:mobile_pos/Screens/WaterFilters/Expenses/add_expense_screen.dart';
import 'package:mobile_pos/Screens/WaterFilters/Expenses/expense_details_screen.dart';
import 'package:mobile_pos/Screens/WaterFilters/Expenses/expenses_statistics_screen.dart';

class WaterFilterExpensesScreen extends StatefulWidget {
  const WaterFilterExpensesScreen({super.key});

  @override
  State<WaterFilterExpensesScreen> createState() => _WaterFilterExpensesScreenState();
}

class _WaterFilterExpensesScreenState extends State<WaterFilterExpensesScreen> {
  List<WaterFilterExpense> _expenses = [];
  List<WaterFilterExpense> _filteredExpenses = [];
  bool _isLoading = true;
  String _searchQuery = '';
  ExpenseCategory? _selectedCategory;

  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadExpenses();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadExpenses() async {
    setState(() => _isLoading = true);
    
    try {
      final expenses = await WaterFilterExpenseService.getAllExpenses();
      setState(() {
        _expenses = expenses;
        _filteredExpenses = expenses;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل المصروفات: $e')),
        );
      }
    }
  }

  void _filterExpenses() {
    setState(() {
      _filteredExpenses = _expenses.where((expense) {
        final matchesSearch = expense.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
                            expense.description.toLowerCase().contains(_searchQuery.toLowerCase()) ||
                            (expense.vendorName?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false);
        
        final matchesCategory = _selectedCategory == null || expense.category == _selectedCategory;
        
        return matchesSearch && matchesCategory;
      }).toList();
    });
  }

  void _onSearchChanged(String query) {
    setState(() => _searchQuery = query);
    _filterExpenses();
  }

  void _onCategoryChanged(ExpenseCategory? category) {
    setState(() => _selectedCategory = category);
    _filterExpenses();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        title: Text(
          'إدارة المصروفات',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.analytics, color: Colors.white),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ExpensesStatisticsScreen(),
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: _loadExpenses,
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        child: Column(
          children: [
            // شريط البحث والفلترة
            Container(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  // شريط البحث
                  TextField(
                    controller: _searchController,
                    onChanged: _onSearchChanged,
                    decoration: InputDecoration(
                      hintText: 'البحث في المصروفات...',
                      hintStyle: GoogleFonts.cairo(color: Colors.grey.shade600),
                      prefixIcon: const Icon(Icons.search, color: kMainColor),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(color: Colors.grey.shade300),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(color: kMainColor),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  // فلتر الفئة
                  Row(
                    children: [
                      Text(
                        'الفئة:',
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: DropdownButtonFormField<ExpenseCategory?>(
                          value: _selectedCategory,
                          onChanged: _onCategoryChanged,
                          decoration: InputDecoration(
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 8,
                            ),
                          ),
                          items: [
                            DropdownMenuItem<ExpenseCategory?>(
                              value: null,
                              child: Text(
                                'جميع الفئات',
                                style: GoogleFonts.cairo(),
                              ),
                            ),
                            ...ExpenseCategory.values.map((category) {
                              return DropdownMenuItem<ExpenseCategory?>(
                                value: category,
                                child: Text(
                                  category.arabicName,
                                  style: GoogleFonts.cairo(),
                                ),
                              );
                            }),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // قائمة المصروفات
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _filteredExpenses.isEmpty
                      ? _buildEmptyState()
                      : ListView.builder(
                          padding: const EdgeInsets.symmetric(horizontal: 20),
                          itemCount: _filteredExpenses.length,
                          itemBuilder: (context, index) {
                            final expense = _filteredExpenses[index];
                            return _buildExpenseCard(expense);
                          },
                        ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const AddExpenseScreen(),
            ),
          );
          
          if (result == true) {
            _loadExpenses();
          }
        },
        backgroundColor: kMainColor,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long_outlined,
            size: 80,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 20),
          Text(
            _searchQuery.isNotEmpty || _selectedCategory != null
                ? 'لا توجد مصروفات تطابق البحث'
                : 'لا توجد مصروفات مسجلة',
            style: GoogleFonts.cairo(
              fontSize: 18,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isNotEmpty || _selectedCategory != null
                ? 'جرب تغيير معايير البحث'
                : 'اضغط على + لإضافة مصروف جديد',
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: Colors.grey.shade500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExpenseCard(WaterFilterExpense expense) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: _getCategoryColor(expense.category).withOpacity(0.3),
          width: 1,
        ),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: _getCategoryColor(expense.category).withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            _getCategoryIcon(expense.category),
            color: _getCategoryColor(expense.category),
            size: 24,
          ),
        ),
        title: Text(
          expense.title,
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              expense.description,
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getCategoryColor(expense.category).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    expense.category.arabicName,
                    style: GoogleFonts.cairo(
                      fontSize: 10,
                      color: _getCategoryColor(expense.category),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  '${expense.date.day}/${expense.date.month}/${expense.date.year}',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              '${expense.amount.toStringAsFixed(2)} ج.م',
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              expense.paymentMethod.arabicName,
              style: GoogleFonts.cairo(
                fontSize: 10,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
        onTap: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ExpenseDetailsScreen(expense: expense),
            ),
          );
          
          if (result == true) {
            _loadExpenses();
          }
        },
      ),
    );
  }

  Color _getCategoryColor(ExpenseCategory category) {
    switch (category) {
      case ExpenseCategory.transportation:
        return Colors.blue;
      case ExpenseCategory.materials:
        return Colors.orange;
      case ExpenseCategory.tools:
        return Colors.purple;
      case ExpenseCategory.maintenance:
        return Colors.green;
      case ExpenseCategory.marketing:
        return Colors.pink;
      case ExpenseCategory.office:
        return Colors.teal;
      case ExpenseCategory.utilities:
        return Colors.indigo;
      case ExpenseCategory.salaries:
        return Colors.brown;
      case ExpenseCategory.rent:
        return Colors.red;
      case ExpenseCategory.insurance:
        return Colors.cyan;
      case ExpenseCategory.other:
        return Colors.grey;
    }
  }

  IconData _getCategoryIcon(ExpenseCategory category) {
    switch (category) {
      case ExpenseCategory.transportation:
        return Icons.directions_car;
      case ExpenseCategory.materials:
        return Icons.inventory_2;
      case ExpenseCategory.tools:
        return Icons.build;
      case ExpenseCategory.maintenance:
        return Icons.settings;
      case ExpenseCategory.marketing:
        return Icons.campaign;
      case ExpenseCategory.office:
        return Icons.business;
      case ExpenseCategory.utilities:
        return Icons.electrical_services;
      case ExpenseCategory.salaries:
        return Icons.people;
      case ExpenseCategory.rent:
        return Icons.home;
      case ExpenseCategory.insurance:
        return Icons.security;
      case ExpenseCategory.other:
        return Icons.more_horiz;
    }
  }
}
