import 'dart:async';
import 'dart:io';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:mobile_pos/services/local_database_service.dart';
import 'package:mobile_pos/services/firebase_listener_manager.dart';

/// فئة لمحاكاة DataSnapshot على ويندوز
class MockDataSnapshot implements DataSnapshot {
  final String? _key;
  final dynamic _value;
  final bool _exists;
  final String _path;

  MockDataSnapshot({
    String? key,
    dynamic value,
    required bool exists,
    String? path,
  })  : _key = key,
        _value = value,
        _exists = exists,
        _path = path ?? '';

  @override
  DataSnapshot child(String path) {
    if (_value is Map && _value.containsKey(path)) {
      return MockDataSnapshot(
        key: path,
        value: _value[path],
        exists: true,
        path: '$_path/$path',
      );
    }
    return MockDataSnapshot(
      key: path,
      value: null,
      exists: false,
      path: '$_path/$path',
    );
  }

  @override
  List<DataSnapshot> get children {
    if (_value is Map) {
      return _value.entries
          .map((entry) => MockDataSnapshot(
                key: entry.key.toString(),
                value: entry.value,
                exists: true,
                path: '$_path/${entry.key}',
              ))
          .toList();
    }
    return [];
  }

  @override
  bool get exists => _exists;

  @override
  bool hasChild(String path) {
    if (_value is Map) {
      return _value.containsKey(path);
    }
    return false;
  }

  @override
  String? get key => _key;

  // تنفيذ الخصائص المطلوبة
  @override
  Object? get priority => null;

  @override
  DatabaseReference get ref => FirebaseDatabase.instance.ref(_path);

  // حجم البيانات
  int get size {
    if (_value is Map) {
      return _value.length;
    } else if (_value is List) {
      return _value.length;
    }
    return 0;
  }

  @override
  dynamic get value => _value;
}

/// خدمة مركزية لإدارة مراجع قاعدة بيانات Firebase
class FirebaseDatabaseService {
  // نمط Singleton للتأكد من وجود نسخة واحدة فقط
  static final FirebaseDatabaseService _instance =
      FirebaseDatabaseService._internal();
  factory FirebaseDatabaseService() => _instance;
  FirebaseDatabaseService._internal();

  // قاموس لتتبع المراجع التي تم تعيينها بالفعل للمزامنة
  static final Map<String, bool> _syncedReferences = {};

  // قاموس لتخزين المستمعين النشطين
  static final Map<String, StreamSubscription<DatabaseEvent>> _activeListeners =
      {};

  // قاموس لتخزين البيانات المؤقتة
  static final Map<String, dynamic> _cachedData = {};

  // حالة تهيئة الخدمة
  static bool _isInitialized = false;

  /// تعيين مرجع للمزامنة بشكل آمن
  /// يتحقق أولاً مما إذا كان المرجع قد تم تعيينه بالفعل للمزامنة
  static void keepSyncedSafely(DatabaseReference reference,
      {bool synced = true}) {
    final String path = reference.path;
    // debugPrint('محاولة تعيين المزامنة للمرجع: $path');

    // تجاهل عملية المزامنة على ويندوز لأنها غير مدعومة
    if (Platform.isWindows) {
      debugPrint('تم تجاهل keepSynced على ويندوز لأنها غير مدعومة');
      _syncedReferences[path] = synced;
      return;
    }

    // التحقق مما إذا كان المرجع قد تم تعيينه بالفعل للمزامنة
    if (_syncedReferences.containsKey(path)) {
      // debugPrint('المرجع $path موجود بالفعل في القائمة');
      // إذا كانت حالة المزامنة مختلفة، قم بتحديثها
      if (_syncedReferences[path] != synced) {
        try {
          reference.keepSynced(synced);
          _syncedReferences[path] = synced;
          // debugPrint('تم تحديث حالة المزامنة للمرجع $path إلى $synced');
        } catch (e) {
          // debugPrint('خطأ في تعيين المزامنة للمرجع $path: $e');
        }
      } else {
        // debugPrint('المرجع $path بالفعل في حالة المزامنة $synced');
      }
    } else {
      // إذا لم يتم تعيين المرجع من قبل، قم بتعيينه الآن
      try {
        reference.keepSynced(synced);
        _syncedReferences[path] = synced;
        // debugPrint('تم تعيين المزامنة للمرجع $path إلى $synced');
      } catch (e) {
        debugPrint('خطأ في تعيين المزامنة للمرجع $path: $e');
      }
    }
  }

  /// الحصول على مرجع قاعدة بيانات مع تعيين المزامنة بشكل آمن
  static DatabaseReference getReference(String path,
      {bool keepSynced = false}) {
    // التحقق من حالة التهيئة
    if (!_isInitialized) {
      debugPrint(
          'تحذير: محاولة الوصول إلى Firebase قبل التهيئة. المسار: $path');
      // محاولة تهيئة Firebase إذا لم تكن مهيأة بالفعل
      initialize();
    }

    // تنظيف المسار للتأكد من عدم وجود مسارات مكررة
    final cleanPath = path.trim();
    final DatabaseReference reference =
        FirebaseDatabase.instance.ref(cleanPath);

    if (keepSynced) {
      keepSyncedSafely(reference);
    }

    return reference;
  }

  /// الحصول على بيانات مع التخزين المؤقت
  /// يقوم بتخزين البيانات مؤقتًا لتحسين الأداء وتقليل الاتصالات بالخادم
  static Future<DataSnapshot> getDataWithCaching(String path,
      {Duration cacheDuration = const Duration(minutes: 5)}) async {
    // التحقق من وجود البيانات في التخزين المؤقت
    if (_cachedData.containsKey(path)) {
      final cachedItem = _cachedData[path];
      final timestamp = cachedItem['timestamp'] as DateTime;

      // التحقق من صلاحية البيانات المخزنة مؤقتًا
      if (DateTime.now().difference(timestamp) < cacheDuration) {
        debugPrint('استخدام البيانات المخزنة مؤقتًا للمسار: $path');
        return cachedItem['data'] as DataSnapshot;
      }
    }

    // إذا لم تكن البيانات موجودة في التخزين المؤقت أو انتهت صلاحيتها، قم بجلبها من Firebase
    try {
      // معالجة خاصة لمنصة ويندوز
      if (Platform.isWindows) {
        try {
          // تهيئة خدمة قاعدة البيانات المحلية إذا لم تكن مهيأة بالفعل
          if (!LocalDatabaseService.isInitialized) {
            await LocalDatabaseService.initialize();
          }

          // محاولة الحصول على البيانات من قاعدة البيانات المحلية
          final localData = await LocalDatabaseService.getData(path);

          if (localData != null) {
            debugPrint(
                'تم الحصول على البيانات من قاعدة البيانات المحلية للمسار: $path');

            // إنشاء DataSnapshot وهمي من البيانات المحلية
            final mockSnapshot = MockDataSnapshot(
              key: path.split('/').last,
              value: localData,
              exists: true,
            );

            // تخزين البيانات مؤقتًا
            _cachedData[path] = {
              'data': mockSnapshot,
              'timestamp': DateTime.now(),
            };

            return mockSnapshot;
          } else {
            debugPrint(
                'لا توجد بيانات محلية للمسار: $path، إنشاء بيانات افتراضية');

            // إنشاء بيانات افتراضية
            final defaultData = _createDefaultData(path);

            // حفظ البيانات الافتراضية في قاعدة البيانات المحلية
            await LocalDatabaseService.saveData(path, defaultData);

            // إنشاء DataSnapshot وهمي من البيانات الافتراضية
            final mockSnapshot = MockDataSnapshot(
              key: path.split('/').last,
              value: defaultData,
              exists: true,
            );

            // تخزين البيانات مؤقتًا
            _cachedData[path] = {
              'data': mockSnapshot,
              'timestamp': DateTime.now(),
            };

            return mockSnapshot;
          }
        } catch (e) {
          debugPrint('خطأ في الحصول على البيانات المحلية للمسار $path: $e');

          // إذا كان هناك بيانات مخزنة مؤقتًا، استخدمها حتى لو كانت قديمة
          if (_cachedData.containsKey(path)) {
            debugPrint('استخدام البيانات المخزنة مؤقتًا القديمة للمسار: $path');
            return _cachedData[path]['data'] as DataSnapshot;
          }

          // إنشاء بيانات افتراضية
          final defaultData = _createDefaultData(path);

          // إنشاء DataSnapshot وهمي من البيانات الافتراضية
          final mockSnapshot = MockDataSnapshot(
            key: path.split('/').last,
            value: defaultData,
            exists: true,
          );

          return mockSnapshot;
        }
      }

      // للمنصات الأخرى
      final reference = getReference(path, keepSynced: false);
      final snapshot = await reference.get();

      // تخزين البيانات مؤقتًا
      _cachedData[path] = {
        'data': snapshot,
        'timestamp': DateTime.now(),
      };

      // حفظ البيانات في قاعدة البيانات المحلية للاستخدام على ويندوز
      if (snapshot.exists) {
        try {
          await LocalDatabaseService.saveData(path, snapshot.value);
        } catch (e) {
          debugPrint(
              'خطأ في حفظ البيانات في قاعدة البيانات المحلية للمسار $path: $e');
        }
      }

      return snapshot;
    } catch (e) {
      debugPrint('خطأ في الحصول على البيانات للمسار $path: $e');

      // محاولة استخدام البيانات المخزنة مؤقتًا في حالة الخطأ
      if (_cachedData.containsKey(path)) {
        debugPrint(
            'استخدام البيانات المخزنة مؤقتًا في حالة الخطأ للمسار: $path');
        return _cachedData[path]['data'] as DataSnapshot;
      }

      // إنشاء بيانات افتراضية في حالة الخطأ
      final defaultData = _createDefaultData(path);

      // إنشاء DataSnapshot وهمي من البيانات الافتراضية
      final mockSnapshot = MockDataSnapshot(
        key: path.split('/').last,
        value: defaultData,
        exists: true,
      );

      return mockSnapshot;
    }
  }

  /// إنشاء بيانات افتراضية بناءً على المسار
  static dynamic _createDefaultData(String path) {
    // تحليل المسار لتحديد نوع البيانات
    if (path.contains('Subscription')) {
      return {'name': 'Admin', 'status': 'active'};
    } else if (path.contains('Personal Information')) {
      return {'companyName': 'شركة افتراضية', 'phone': '0000000000'};
    } else if (path.contains('Sales Transition')) {
      return {};
    } else if (path.contains('Purchase Transition')) {
      return {};
    } else if (path.contains('Customers')) {
      return {};
    } else if (path.contains('Products')) {
      return {};
    } else {
      return {};
    }
  }

  /// الاستماع للتغييرات مع إدارة المستمعين
  /// يقوم بإدارة المستمعين لتجنب التسرب وتحسين الأداء
  /// يستخدم مدير المستمعين الجديد لمنع الاستماع المزدوج
  static Stream<DatabaseEvent> listenToPath(String path,
      {bool keepSynced = false,
      String? ownerId,
      String? eventType,
      Query? query}) {
    // تعيين المزامنة إذا كان مطلوبًا
    if (keepSynced) {
      getReference(path, keepSynced: true);
    }

    // استخدام مدير المستمعين لإنشاء مستمع آمن
    return FirebaseListenerManager().listenToPath(
      path,
      ownerId: ownerId ?? 'firebase_database_service',
      eventType: eventType,
      query: query,
      keepSynced: keepSynced,
    );
  }

  /// إلغاء الاستماع لمسار معين
  static Future<void> cancelListener(String path,
      {String? ownerId, String? eventType}) async {
    // استخدام مدير المستمعين لإلغاء المستمع
    await FirebaseListenerManager().cancelListener(
      path,
      ownerId: ownerId ?? 'firebase_database_service',
      eventType: eventType,
    );

    // إلغاء المستمع القديم أيضًا للتوافق مع الكود القديم
    if (_activeListeners.containsKey(path)) {
      debugPrint('إلغاء المستمع القديم للمسار: $path');
      await _activeListeners[path]?.cancel();
      _activeListeners.remove(path);
    }
  }

  /// إلغاء مزامنة جميع المراجع وإلغاء جميع المستمعين
  static Future<void> clearAllSyncedReferences() async {
    debugPrint('جاري إلغاء مزامنة جميع المراجع وإلغاء المستمعين...');

    try {
      // إلغاء جميع المستمعين باستخدام مدير المستمعين الجديد
      try {
        await FirebaseListenerManager().cancelAllListeners();
        debugPrint('تم إلغاء جميع المستمعين باستخدام مدير المستمعين الجديد');
      } catch (e) {
        debugPrint('خطأ في إلغاء المستمعين باستخدام مدير المستمعين الجديد: $e');
      }

      // إلغاء المستمعين القديمة أيضًا للتوافق مع الكود القديم
      final listenerPaths = List<String>.from(_activeListeners.keys);
      for (final path in listenerPaths) {
        try {
          if (_activeListeners.containsKey(path)) {
            await _activeListeners[path]?.cancel();
            _activeListeners.remove(path);
          }
        } catch (e) {
          debugPrint('خطأ في إلغاء المستمع القديم للمسار $path: $e');
        }
      }

      // نسخ القاموس لتجنب التعديل أثناء التكرار
      final entries = Map<String, bool>.from(_syncedReferences);

      // إلغاء مزامنة كل مرجع بشكل منفصل
      if (!Platform.isWindows) {
        // تنفيذ هذا الجزء فقط على المنصات غير ويندوز
        for (final entry in entries.entries) {
          final path = entry.key;
          final isSynced = entry.value;

          if (isSynced) {
            try {
              final ref = FirebaseDatabase.instance.ref(path);
              ref.keepSynced(false);
              debugPrint('تم إلغاء مزامنة المرجع $path');
            } catch (e) {
              debugPrint('خطأ في إلغاء مزامنة المرجع $path: $e');
            }
          }
        }
      } else {
        debugPrint('تم تجاهل إلغاء المزامنة على ويندوز لأنها غير مدعومة');
      }

      // مسح القواميس بعد الانتهاء
      _syncedReferences.clear();
      _cachedData.clear();
      debugPrint('تم إلغاء مزامنة جميع المراجع وإلغاء المستمعين بنجاح');
    } catch (e) {
      debugPrint('خطأ عام في إلغاء مزامنة المراجع وإلغاء المستمعين: $e');
    }
  }

  /// مسح التخزين المؤقت
  static void clearCache() {
    _cachedData.clear();
    debugPrint('تم مسح التخزين المؤقت');
  }

  /// الحصول على معرف المستخدم الحالي
  static String getCurrentUserId() {
    final user = FirebaseAuth.instance.currentUser;
    return user?.uid ?? '';
  }

  /// تهيئة الخدمة
  static Future<bool> initialize() async {
    // debugPrint('تهيئة خدمة Firebase Database...');

    try {
      // تهيئة مدير المستمعين
      FirebaseListenerManager().initialize();

      // تمكين الاستمرارية في Firebase Database - تجاهل على ويندوز
      if (!Platform.isWindows) {
        FirebaseDatabase.instance.setPersistenceEnabled(true);
        // تعيين حجم التخزين المؤقت (10 ميجابايت)
        FirebaseDatabase.instance
            .setPersistenceCacheSizeBytes(10 * 1024 * 1024);
      } else {
        debugPrint('تم تجاهل تمكين الاستمرارية على ويندوز لأنها قد تسبب مشاكل');

        // تهيئة قاعدة البيانات المحلية على ويندوز
        try {
          await LocalDatabaseService.initialize();
          debugPrint('تم تهيئة قاعدة البيانات المحلية على ويندوز بنجاح');

          // إعداد بيانات افتراضية للمستخدم الحالي إذا كان مسجل الدخول
          final user = FirebaseAuth.instance.currentUser;
          if (user != null) {
            // حفظ بيانات المستخدم الحالي
            await LocalDatabaseService.saveUserData(
              userId: user.uid,
              displayName:
                  user.displayName ?? user.email?.split('@')[0] ?? 'مستخدم',
              email: user.email ?? '',
              photoUrl: user.photoURL,
              subscriptionType: 'Admin',
            );

            // حفظ ميزات الاشتراك الافتراضية
            await LocalDatabaseService.saveSubscriptionFeature(
              userId: user.uid,
              feature: 'Sales',
              enabled: true,
            );
            await LocalDatabaseService.saveSubscriptionFeature(
              userId: user.uid,
              feature: 'Purchase',
              enabled: true,
            );
            await LocalDatabaseService.saveSubscriptionFeature(
              userId: user.uid,
              feature: 'Customers',
              enabled: true,
            );
            await LocalDatabaseService.saveSubscriptionFeature(
              userId: user.uid,
              feature: 'Products',
              enabled: true,
            );
            await LocalDatabaseService.saveSubscriptionFeature(
              userId: user.uid,
              feature: 'Reports',
              enabled: true,
            );

            debugPrint('تم إعداد بيانات افتراضية للمستخدم: ${user.uid}');
          }
        } catch (e) {
          debugPrint('خطأ في تهيئة قاعدة البيانات المحلية على ويندوز: $e');
        }
      }

      _isInitialized = true;
      // debugPrint('تم تهيئة خدمة Firebase Database بنجاح');
      return true;
    } catch (e) {
      debugPrint('خطأ في تهيئة خدمة Firebase Database: $e');
      _isInitialized = false;
      return false;
    }
  }

  /// التحقق من حالة التهيئة
  static bool isInitialized() {
    return _isInitialized;
  }

  /// حفظ البيانات في التخزين المحلي
  static Future<void> saveToLocalStorage(String key, dynamic data) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      if (data is String) {
        await prefs.setString(key, data);
      } else if (data is int) {
        await prefs.setInt(key, data);
      } else if (data is bool) {
        await prefs.setBool(key, data);
      } else if (data is double) {
        await prefs.setDouble(key, data);
      } else if (data is List<String>) {
        await prefs.setStringList(key, data);
      } else {
        // محاولة تحويل البيانات إلى سلسلة JSON
        try {
          final jsonString = data.toString();
          await prefs.setString(key, jsonString);
        } catch (e) {
          debugPrint('خطأ في تحويل البيانات إلى سلسلة JSON: $e');
        }
      }
    } catch (e) {
      debugPrint('خطأ في حفظ البيانات في التخزين المحلي: $e');
    }
  }

  /// قراءة البيانات من التخزين المحلي
  static Future<dynamic> readFromLocalStorage(String key,
      {dynamic defaultValue}) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      if (!prefs.containsKey(key)) {
        return defaultValue;
      }

      return prefs.get(key);
    } catch (e) {
      debugPrint('خطأ في قراءة البيانات من التخزين المحلي: $e');
      return defaultValue;
    }
  }
}
