// ignore_for_file: unused_result, use_build_context_synchronously

import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/GlobalComponents/button_global.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/generated/l10n.dart' as lang;
import 'package:mobile_pos/model/user_role_model.dart';
import 'package:mobile_pos/repository/get_user_role_repo.dart';
import 'package:nb_utils/nb_utils.dart';

import '../../Provider/user_role_provider.dart';
import 'add_user_role_screen.dart';

class UserRoleDetails extends StatefulWidget {
  const UserRoleDetails({super.key, required this.userRoleModel});

  final UserRoleModel userRoleModel;

  @override
  // ignore: library_private_types_in_public_api
  _UserRoleDetailsState createState() => _UserRoleDetailsState();
}

class _UserRoleDetailsState extends State<UserRoleDetails> {
  GlobalKey<FormState> globalKey = GlobalKey<FormState>();

  bool allPermissions = false;
  bool salePermission = false;
  bool partiesPermission = false;
  bool purchasePermission = false;
  bool productPermission = false;
  bool profileEditPermission = false;
  bool addExpensePermission = false;
  bool lossProfitPermission = false;
  bool dueListPermission = false;
  bool stockPermission = false;
  bool reportsPermission = false;
  bool salesListPermission = false;
  bool purchaseListPermission = false;
  // الصلاحيات الجديدة - الذكاء الاصطناعي
  bool aiChatPermission = false;
  bool aiAssistantPermission = false;
  bool voiceAssistantPermission = false;
  // الخزينة
  bool treasuryPermission = false;
  bool cashBoxPermission = false;
  // إدارة التوصيل
  bool deliveryManagementPermission = false;
  // الموارد البشرية
  bool hrmPermission = false;
  bool employeesPermission = false;
  bool designationPermission = false;
  bool salariesPermission = false;
  // التقارير المتقدمة
  bool financialReportsPermission = false;
  bool salesTargetsPermission = false;
  bool taxReportsPermission = false;
  // الإعدادات المتقدمة
  bool userLogsPermission = false;
  bool notificationsPermission = false;
  bool warrantyPermission = false;
  bool settingsPermission = false;
  bool userManagementPermission = false;
  // صلاحيات إضافية
  bool ledgerPermission = false;
  // فلاتر المياه
  bool waterFiltersPermission = false;
  // فلاتر المياه - صلاحيات مفصلة
  bool waterFilterProductsPermission = false;
  bool waterFilterCustomersPermission = false;
  bool waterFilterSystemsPermission = false;
  bool waterFilterMaintenancePermission = false;
  bool waterFilterInstallmentsPermission = false;
  bool waterFilterReportsPermission = false;
  // حالة التفعيل
  bool isActive = true;

  // نظام الرولز الجاهزة
  String selectedRole = 'Custom';
  List<String> availableRoles = [
    'Custom',
    'General Manager',
    'Sales Manager',
    'Cashier',
    'Warehouse Employee',
    'Accountant',
  ];

  // أسماء الرولز بالعربية للعرض
  Map<String, String> roleDisplayNames = {
    'Custom': 'مخصص',
    'General Manager': 'مدير عام',
    'Sales Manager': 'مدير مبيعات',
    'Cashier': 'مندوب مبيعات',
    'Warehouse Employee': 'موظف مخزن',
    'Accountant': 'محاسب',
  };
  TextEditingController emailController = TextEditingController();
  TextEditingController titleController = TextEditingController();
  bool isMailSent = false;

  @override
  void initState() {
    getAllUserData();
    //
    super.initState();

    // Debug prints للتحقق من البيانات
    debugPrint('🔍 User Role Details - البيانات المستلمة:');
    debugPrint('Email: ${widget.userRoleModel.email}');
    debugPrint('Title: ${widget.userRoleModel.userTitle}');
    debugPrint('Sale Permission: ${widget.userRoleModel.salePermission}');
    debugPrint(
        'Settings Permission: ${widget.userRoleModel.settingsPermission}');
    debugPrint(
        'User Management Permission: ${widget.userRoleModel.userManagementPermission}');
    debugPrint(
        'Water Filters Permission: ${widget.userRoleModel.waterFiltersPermission}');
    salePermission = widget.userRoleModel.salePermission;
    partiesPermission = widget.userRoleModel.partiesPermission;
    purchasePermission = widget.userRoleModel.purchasePermission;
    productPermission = widget.userRoleModel.productPermission;
    profileEditPermission = widget.userRoleModel.profileEditPermission;
    addExpensePermission = widget.userRoleModel.addExpensePermission;
    lossProfitPermission = widget.userRoleModel.lossProfitPermission;
    dueListPermission = widget.userRoleModel.dueListPermission;
    stockPermission = widget.userRoleModel.stockPermission;
    reportsPermission = widget.userRoleModel.reportsPermission;
    salesListPermission = widget.userRoleModel.salesListPermission;
    purchaseListPermission = widget.userRoleModel.purchaseListPermission;
    // الصلاحيات الجديدة - الذكاء الاصطناعي
    aiChatPermission = widget.userRoleModel.aiChatPermission;
    aiAssistantPermission = widget.userRoleModel.aiAssistantPermission;
    voiceAssistantPermission = widget.userRoleModel.voiceAssistantPermission;
    // الخزينة
    treasuryPermission = widget.userRoleModel.treasuryPermission;
    cashBoxPermission = widget.userRoleModel.cashBoxPermission;
    // إدارة التوصيل
    deliveryManagementPermission =
        widget.userRoleModel.deliveryManagementPermission;
    // الموارد البشرية
    hrmPermission = widget.userRoleModel.hrmPermission;
    employeesPermission = widget.userRoleModel.employeesPermission;
    designationPermission = widget.userRoleModel.designationPermission;
    salariesPermission = widget.userRoleModel.salariesPermission;
    // التقارير المتقدمة
    financialReportsPermission =
        widget.userRoleModel.financialReportsPermission;
    salesTargetsPermission = widget.userRoleModel.salesTargetsPermission;
    taxReportsPermission = widget.userRoleModel.taxReportsPermission;
    // الإعدادات المتقدمة
    userLogsPermission = widget.userRoleModel.userLogsPermission;
    notificationsPermission = widget.userRoleModel.notificationsPermission;
    warrantyPermission = widget.userRoleModel.warrantyPermission;
    settingsPermission = widget.userRoleModel.settingsPermission;
    userManagementPermission = widget.userRoleModel.userManagementPermission;
    // صلاحيات إضافية
    ledgerPermission = widget.userRoleModel.ledgerPermission;
    // فلاتر المياه
    waterFiltersPermission = widget.userRoleModel.waterFiltersPermission;
    // فلاتر المياه - صلاحيات مفصلة
    waterFilterProductsPermission =
        widget.userRoleModel.waterFilterProductsPermission;
    waterFilterCustomersPermission =
        widget.userRoleModel.waterFilterCustomersPermission;
    waterFilterSystemsPermission =
        widget.userRoleModel.waterFilterSystemsPermission;
    waterFilterMaintenancePermission =
        widget.userRoleModel.waterFilterMaintenancePermission;
    waterFilterInstallmentsPermission =
        widget.userRoleModel.waterFilterInstallmentsPermission;
    waterFilterReportsPermission =
        widget.userRoleModel.waterFilterReportsPermission;
    // حالة التفعيل
    isActive = widget.userRoleModel.isActive;
    emailController.text = widget.userRoleModel.email;
    titleController.text = widget.userRoleModel.userTitle;
  }

  UserRoleRepo repo = UserRoleRepo();
  List<UserRoleModel> adminRoleList = [];
  List<UserRoleModel> userRoleList = [];

  String adminRoleKey = '';
  String userRoleKey = '';

  void getAllUserData() async {
    adminRoleList = await repo.getAllUserRoleFromAdmin();
    userRoleList = await repo.getAllUserRole();
    for (var element in adminRoleList) {
      if (element.email == widget.userRoleModel.email) {
        adminRoleKey = element.userKey ?? '';
        break;
      }
    }
    for (var element in userRoleList) {
      if (element.email == widget.userRoleModel.email) {
        userRoleKey = element.userKey ?? '';
        break;
      }
    }
  }

  /// دالة لبناء كارت صلاحيات عصري
  Widget _buildModernPermissionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
    Color? accentColor,
  }) {
    final cardColor = accentColor ?? kMainColor;

    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: cardColor.withOpacity(0.08),
            offset: const Offset(0, 8),
            blurRadius: 24,
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
        border: Border.all(
          color: cardColor.withOpacity(0.12),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header عصري
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  cardColor.withOpacity(0.08),
                  cardColor.withOpacity(0.04),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: cardColor.withOpacity(0.15),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: cardColor.withOpacity(0.2),
                        offset: const Offset(0, 4),
                        blurRadius: 12,
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: Icon(
                    icon,
                    color: cardColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: GoogleFonts.cairo(
                          fontSize: 18,
                          fontWeight: FontWeight.w700,
                          color: Colors.black87,
                          height: 1.2,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'إدارة صلاحيات $title',
                        style: GoogleFonts.cairo(
                          fontSize: 13,
                          fontWeight: FontWeight.w400,
                          color: Colors.black54,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // Content
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 16, 20, 20),
            child: Column(
              children: children,
            ),
          ),
        ],
      ),
    );
  }

  /// دالة لبناء صف صلاحيات عصري - تصميم عمودي
  Widget _buildModernPermissionTile({
    required String title,
    required bool value,
    required Function(bool?) onChanged,
    IconData? icon,
    String? subtitle,
  }) {
    debugPrint('🔧 Building permission tile: $title = $value');
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: value ? kMainColor.withOpacity(0.06) : Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: value ? kMainColor.withOpacity(0.2) : Colors.grey.shade200,
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => onChanged(!value),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              children: [
                // الصف العلوي: الأيقونة والـ checkbox
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // الأيقونة
                    if (icon != null)
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: value
                              ? kMainColor.withOpacity(0.15)
                              : Colors.grey.shade200,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Icon(
                          icon,
                          size: 20,
                          color: value ? kMainColor : Colors.grey.shade600,
                        ),
                      )
                    else
                      const SizedBox(
                          width: 36), // مساحة فارغة إذا لم تكن هناك أيقونة

                    // Checkbox عصري
                    Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: value ? kMainColor : Colors.transparent,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: value ? kMainColor : Colors.grey.shade400,
                          width: 2,
                        ),
                      ),
                      child: value
                          ? const Icon(
                              Icons.check,
                              color: Colors.white,
                              size: 16,
                            )
                          : null,
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                // النص في الأسفل - مساحة كاملة
                SizedBox(
                  width: double.infinity,
                  child: Text(
                    title,
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      fontWeight: value ? FontWeight.w600 : FontWeight.w500,
                      color: value ? kMainColor : Colors.black87,
                      height: 1.4,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// دالة لبناء صف صلاحيات مزدوج عصري
  Widget _buildModernPermissionRow({
    required String title1,
    required bool value1,
    required Function(bool?) onChanged1,
    IconData? icon1,
    String? subtitle1,
    String? title2,
    bool? value2,
    Function(bool?)? onChanged2,
    IconData? icon2,
    String? subtitle2,
  }) {
    // إذا كان هناك عنصر واحد فقط، اعطيه مساحة كاملة
    if (title2 == null || value2 == null || onChanged2 == null) {
      return Padding(
        padding: const EdgeInsets.only(bottom: 4),
        child: _buildModernPermissionTile(
          title: title1,
          value: value1,
          onChanged: onChanged1,
          icon: icon1,
          subtitle: subtitle1,
        ),
      );
    }

    // إذا كان هناك عنصران، اعطي مساحة متساوية
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          Expanded(
            child: _buildModernPermissionTile(
              title: title1,
              value: value1,
              onChanged: onChanged1,
              icon: icon1,
              subtitle: subtitle1,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildModernPermissionTile(
              title: title2,
              value: value2,
              onChanged: onChanged2,
              icon: icon2,
              subtitle: subtitle2,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('🎨 Building User Role Details UI...');
    debugPrint(
        'Current permissions: Sale=$salePermission, Settings=$settingsPermission');
    debugPrint('💧 Water Filters Permission: $waterFiltersPermission');

    return Consumer(builder: (context, ref, __) {
      return Scaffold(
        backgroundColor: kMainColor,
        appBar: AppBar(
          backgroundColor: kMainColor,
          elevation: 0.0,
          titleSpacing: 0.0,
          iconTheme: const IconThemeData(color: Colors.white),
          title: Text(
            lang.S.of(context).userRoleDetails,
            style: GoogleFonts.cairo(
              color: Colors.white,
              fontWeight: FontWeight.w600,
              fontSize: 18,
            ),
          ),
          actions: [
            IconButton(
                onPressed: () async {
                  showDialog(
                    context: context,
                    barrierDismissible: false,
                    builder: (BuildContext context) {
                      String pass = '';
                      return Padding(
                        padding: const EdgeInsets.all(30.0),
                        child: Center(
                          child: Container(
                            width: double.infinity,
                            decoration: const BoxDecoration(
                              color: Colors.white,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(30)),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(20.0),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  AppTextField(
                                    textFieldType: TextFieldType.EMAIL,
                                    onChanged: (value) {
                                      pass = value;
                                    },
                                    decoration: InputDecoration(
                                        labelText: lang.S
                                            .of(context)
                                            .enterYourPassword,
                                        border: const OutlineInputBorder()),
                                  ),
                                  const SizedBox(height: 20),
                                  Row(
                                    children: [
                                      Expanded(
                                        child: ButtonGlobalWithoutIcon(
                                          buttontext: lang.S.of(context).cacel,
                                          buttonDecoration: kButtonDecoration
                                              .copyWith(color: Colors.green),
                                          onPressed: (() {
                                            Navigator.pop(context);
                                          }),
                                          buttonTextColor: Colors.white,
                                        ),
                                      ),
                                      Expanded(
                                        child: ButtonGlobalWithoutIcon(
                                            buttontext:
                                                lang.S.of(context).delete,
                                            buttonDecoration: kButtonDecoration
                                                .copyWith(color: Colors.red),
                                            onPressed: (() async {
                                              if (pass != '' &&
                                                  pass.isNotEmpty) {
                                                await deleteUserRole(
                                                    email: widget
                                                        .userRoleModel.email,
                                                    password: pass,
                                                    adminKey: adminRoleKey,
                                                    userKey: userRoleKey,
                                                    context: context);
                                              } else {
                                                EasyLoading.showError(
                                                    'Please Enter Password');
                                              }
                                            }),
                                            buttonTextColor: Colors.white),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  );
                },
                icon: const Icon(
                  Icons.delete,
                  color: Colors.red,
                ))
          ],
          centerTitle: true,
        ),
        body: Column(
          children: [
            // معلومات المستخدم في الأعلى - مضغوط
            Container(
              width: double.infinity,
              color: kMainColor,
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 12),
              child: Row(
                children: [
                  // صورة البروفايل مصغرة
                  Container(
                    height: 45,
                    width: 45,
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(22),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.3),
                        width: 1.5,
                      ),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(20),
                      child: widget.userRoleModel.profilePicture != null &&
                              widget.userRoleModel.profilePicture!.isNotEmpty
                          ? Image.network(
                              widget.userRoleModel.profilePicture!,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return const Icon(
                                  Icons.person,
                                  color: Colors.white,
                                  size: 24,
                                );
                              },
                            )
                          : const Icon(
                              Icons.person,
                              color: Colors.white,
                              size: 24,
                            ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          widget.userRoleModel.email
                              .replaceAll('@amrdev.com', ''),
                          style: GoogleFonts.cairo(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        Text(
                          widget.userRoleModel.userTitle,
                          style: GoogleFonts.cairo(
                            fontSize: 12,
                            color: Colors.white.withOpacity(0.8),
                          ),
                        ),
                      ],
                    ),
                  ),
                  // مؤشر حالة مصغر
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.green.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 4,
                          height: 4,
                          decoration: const BoxDecoration(
                            color: Colors.green,
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'نشط',
                          style: GoogleFonts.cairo(
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // محتوى الصلاحيات
            Expanded(
              child: Container(
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topRight: Radius.circular(20),
                    topLeft: Radius.circular(20),
                  ),
                ),
                child: SingleChildScrollView(
                  padding: const EdgeInsets.fromLTRB(20, 20, 20, 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // قسم اختيار الدور الوظيفي
                      _buildModernPermissionCard(
                        title: 'الدور الوظيفي',
                        icon: Icons.work_outline,
                        accentColor: Colors.deepOrange,
                        children: [
                          Container(
                            width: double.infinity,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 4),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey.shade300),
                              borderRadius: BorderRadius.circular(12),
                              color: Colors.grey.shade50,
                            ),
                            child: DropdownButtonHideUnderline(
                              child: DropdownButton<String>(
                                value: selectedRole,
                                isExpanded: true,
                                icon: Icon(Icons.keyboard_arrow_down,
                                    color: kMainColor),
                                style: TextStyle(
                                  color: Colors.black87,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                                items: availableRoles.map((String role) {
                                  return DropdownMenuItem<String>(
                                    value: role,
                                    child: Text(
                                      roleDisplayNames[role] ?? role,
                                      style: TextStyle(
                                        color: Colors.black87,
                                        fontSize: 16,
                                      ),
                                    ),
                                  );
                                }).toList(),
                                onChanged: (String? newValue) {
                                  if (newValue != null) {
                                    setState(() {
                                      selectedRole = newValue;
                                      _applyRolePermissions(newValue);
                                    });
                                  }
                                },
                              ),
                            ),
                          ),
                        ],
                      ),

                      // حالة تفعيل المستخدم
                      _buildModernPermissionCard(
                        title: 'حالة المستخدم',
                        icon: Icons.person_outline,
                        accentColor: isActive ? Colors.green : Colors.red,
                        children: [
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: (isActive ? Colors.green : Colors.red)
                                  .withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: (isActive ? Colors.green : Colors.red)
                                    .withOpacity(0.3),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  isActive ? Icons.check_circle : Icons.cancel,
                                  color: isActive ? Colors.green : Colors.red,
                                  size: 24,
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        isActive
                                            ? 'المستخدم مفعل'
                                            : 'المستخدم معطل',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                          color: isActive
                                              ? Colors.green
                                              : Colors.red,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        isActive
                                            ? 'يمكن للمستخدم تسجيل الدخول واستخدام التطبيق'
                                            : 'لا يمكن للمستخدم تسجيل الدخول',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.grey.shade600,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Switch(
                                  value: isActive,
                                  onChanged: (value) {
                                    setState(() {
                                      isActive = value;
                                    });
                                  },
                                  activeColor: Colors.green,
                                  inactiveThumbColor: Colors.red,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),

                      // التحكم العام في الصلاحيات
                      _buildModernPermissionCard(
                        title: 'التحكم العام',
                        icon: Icons.admin_panel_settings,
                        accentColor: Colors.purple,
                        children: [
                          _buildModernPermissionTile(
                            title: lang.S.of(context).all,
                            value: allPermissions,
                            onChanged: (value) {
                              if (value == true) {
                                setState(() {
                                  allPermissions = value!;
                                  salePermission = true;
                                  partiesPermission = true;
                                  purchasePermission = true;
                                  productPermission = true;
                                  profileEditPermission = true;
                                  addExpensePermission = true;
                                  lossProfitPermission = true;
                                  dueListPermission = true;
                                  stockPermission = true;
                                  reportsPermission = true;
                                  salesListPermission = true;
                                  purchaseListPermission = true;
                                  // الصلاحيات الجديدة - الذكاء الاصطناعي
                                  aiChatPermission = true;
                                  aiAssistantPermission = true;
                                  voiceAssistantPermission = true;
                                  // الخزينة
                                  treasuryPermission = true;
                                  cashBoxPermission = true;
                                  // إدارة التوصيل
                                  deliveryManagementPermission = true;
                                  // الموارد البشرية
                                  hrmPermission = true;
                                  employeesPermission = true;
                                  designationPermission = true;
                                  salariesPermission = true;
                                  // التقارير المتقدمة
                                  financialReportsPermission = true;
                                  salesTargetsPermission = true;
                                  taxReportsPermission = true;
                                  // الإعدادات المتقدمة
                                  userLogsPermission = true;
                                  notificationsPermission = true;
                                  warrantyPermission = true;
                                  settingsPermission = true;
                                  userManagementPermission = true;
                                  // صلاحيات إضافية
                                  ledgerPermission = true;
                                  // فلاتر المياه
                                  waterFiltersPermission = true;
                                  // فلاتر المياه - صلاحيات مفصلة
                                  waterFilterProductsPermission = true;
                                  waterFilterCustomersPermission = true;
                                  waterFilterSystemsPermission = true;
                                  waterFilterMaintenancePermission = true;
                                  waterFilterInstallmentsPermission = true;
                                  waterFilterReportsPermission = true;
                                });
                              } else {
                                setState(() {
                                  allPermissions = value!;
                                  salePermission = false;
                                  partiesPermission = false;
                                  purchasePermission = false;
                                  productPermission = false;
                                  profileEditPermission = false;
                                  addExpensePermission = false;
                                  lossProfitPermission = false;
                                  dueListPermission = false;
                                  stockPermission = false;
                                  reportsPermission = false;
                                  salesListPermission = false;
                                  purchaseListPermission = false;
                                  // الصلاحيات الجديدة - الذكاء الاصطناعي
                                  aiChatPermission = false;
                                  aiAssistantPermission = false;
                                  voiceAssistantPermission = false;
                                  // الخزينة
                                  treasuryPermission = false;
                                  cashBoxPermission = false;
                                  // إدارة التوصيل
                                  deliveryManagementPermission = false;
                                  // الموارد البشرية
                                  hrmPermission = false;
                                  employeesPermission = false;
                                  designationPermission = false;
                                  salariesPermission = false;
                                  // التقارير المتقدمة
                                  financialReportsPermission = false;
                                  salesTargetsPermission = false;
                                  taxReportsPermission = false;
                                  // الإعدادات المتقدمة
                                  userLogsPermission = false;
                                  notificationsPermission = false;
                                  warrantyPermission = false;
                                  settingsPermission = false;
                                  userManagementPermission = false;
                                  // صلاحيات إضافية
                                  ledgerPermission = false;
                                  // فلاتر المياه
                                  waterFiltersPermission = false;
                                  // فلاتر المياه - صلاحيات مفصلة
                                  waterFilterProductsPermission = false;
                                  waterFilterCustomersPermission = false;
                                  waterFilterSystemsPermission = false;
                                  waterFilterMaintenancePermission = false;
                                  waterFilterInstallmentsPermission = false;
                                  waterFilterReportsPermission = false;
                                });
                              }
                            },
                          ),
                        ],
                      ),

                      // الصلاحيات الأساسية
                      _buildModernPermissionCard(
                        title: 'الصلاحيات الأساسية',
                        icon: Icons.business_center,
                        accentColor: Colors.blue,
                        children: [
                          _buildModernPermissionRow(
                            title1: lang.S.of(context).profileEdit,
                            value1: profileEditPermission,
                            onChanged1: (value) =>
                                setState(() => profileEditPermission = value!),
                            icon1: Icons.edit,
                            title2: lang.S.of(context).sales,
                            value2: salePermission,
                            onChanged2: (value) =>
                                setState(() => salePermission = value!),
                            icon2: Icons.point_of_sale,
                          ),
                          _buildModernPermissionRow(
                            title1: lang.S.of(context).parties,
                            value1: partiesPermission,
                            onChanged1: (value) =>
                                setState(() => partiesPermission = value!),
                            icon1: Icons.people,
                            title2: lang.S.of(context).purchase,
                            value2: purchasePermission,
                            onChanged2: (value) =>
                                setState(() => purchasePermission = value!),
                            icon2: Icons.shopping_cart,
                          ),
                          _buildModernPermissionRow(
                            title1: lang.S.of(context).product,
                            value1: productPermission,
                            onChanged1: (value) =>
                                setState(() => productPermission = value!),
                            icon1: Icons.inventory,
                            title2: lang.S.of(context).dueList,
                            value2: dueListPermission,
                            onChanged2: (value) =>
                                setState(() => dueListPermission = value!),
                            icon2: Icons.list_alt,
                          ),
                          _buildModernPermissionRow(
                            title1: lang.S.of(context).stocks,
                            value1: stockPermission,
                            onChanged1: (value) =>
                                setState(() => stockPermission = value!),
                            icon1: Icons.warehouse,
                            title2: lang.S.of(context).reports,
                            value2: reportsPermission,
                            onChanged2: (value) =>
                                setState(() => reportsPermission = value!),
                            icon2: Icons.analytics,
                          ),
                          _buildModernPermissionRow(
                            title1: lang.S.of(context).salesList,
                            value1: salesListPermission,
                            onChanged1: (value) =>
                                setState(() => salesListPermission = value!),
                            icon1: Icons.receipt_long,
                            title2: lang.S.of(context).purchaseList,
                            value2: purchaseListPermission,
                            onChanged2: (value) =>
                                setState(() => purchaseListPermission = value!),
                            icon2: Icons.shopping_bag,
                          ),
                          _buildModernPermissionRow(
                            title1: lang.S.of(context).lossOrProfit,
                            value1: lossProfitPermission,
                            onChanged1: (value) =>
                                setState(() => lossProfitPermission = value!),
                            icon1: Icons.trending_up,
                            title2: lang.S.of(context).expense,
                            value2: addExpensePermission,
                            onChanged2: (value) =>
                                setState(() => addExpensePermission = value!),
                            icon2: Icons.money_off,
                          ),
                          _buildModernPermissionTile(
                            title: 'دفتر الأستاذ',
                            value: ledgerPermission,
                            onChanged: (value) =>
                                setState(() => ledgerPermission = value!),
                            icon: Icons.book,
                          ),
                        ],
                      ),

                      // الذكاء الاصطناعي
                      _buildModernPermissionCard(
                        title: 'الذكاء الاصطناعي',
                        icon: Icons.smart_toy,
                        accentColor: Colors.deepPurple,
                        children: [
                          _buildModernPermissionRow(
                            title1: 'الدردشة الذكية',
                            value1: aiChatPermission,
                            onChanged1: (value) =>
                                setState(() => aiChatPermission = value!),
                            icon1: Icons.chat,
                            title2: 'المساعد الذكي',
                            value2: aiAssistantPermission,
                            onChanged2: (value) =>
                                setState(() => aiAssistantPermission = value!),
                            icon2: Icons.assistant,
                          ),
                          _buildModernPermissionTile(
                            title: 'المساعد الصوتي',
                            value: voiceAssistantPermission,
                            onChanged: (value) => setState(
                                () => voiceAssistantPermission = value!),
                            icon: Icons.mic,
                          ),
                        ],
                      ),

                      // الخزينة والمالية
                      _buildModernPermissionCard(
                        title: 'الخزينة والمالية',
                        icon: Icons.account_balance_wallet,
                        accentColor: Colors.green,
                        children: [
                          _buildModernPermissionRow(
                            title1: 'الخزينة',
                            value1: treasuryPermission,
                            onChanged1: (value) =>
                                setState(() => treasuryPermission = value!),
                            icon1: Icons.account_balance,
                            title2: 'صندوق النقدية',
                            value2: cashBoxPermission,
                            onChanged2: (value) =>
                                setState(() => cashBoxPermission = value!),
                            icon2: Icons.money,
                          ),
                        ],
                      ),

                      // إدارة التوصيل
                      _buildModernPermissionCard(
                        title: 'إدارة التوصيل',
                        icon: Icons.local_shipping,
                        accentColor: Colors.orange,
                        children: [
                          _buildModernPermissionTile(
                            title: 'إدارة التوصيل الذكي',
                            value: deliveryManagementPermission,
                            onChanged: (value) => setState(
                                () => deliveryManagementPermission = value!),
                            icon: Icons.delivery_dining,
                          ),
                        ],
                      ),

                      // الموارد البشرية
                      _buildModernPermissionCard(
                        title: 'الموارد البشرية',
                        icon: Icons.people,
                        accentColor: Colors.teal,
                        children: [
                          _buildModernPermissionRow(
                            title1: 'الموارد البشرية',
                            value1: hrmPermission,
                            onChanged1: (value) =>
                                setState(() => hrmPermission = value!),
                            icon1: Icons.business_center,
                            title2: 'الموظفين',
                            value2: employeesPermission,
                            onChanged2: (value) =>
                                setState(() => employeesPermission = value!),
                            icon2: Icons.badge,
                          ),
                          _buildModernPermissionRow(
                            title1: 'المناصب',
                            value1: designationPermission,
                            onChanged1: (value) =>
                                setState(() => designationPermission = value!),
                            icon1: Icons.work,
                            title2: 'الرواتب',
                            value2: salariesPermission,
                            onChanged2: (value) =>
                                setState(() => salariesPermission = value!),
                            icon2: Icons.payments,
                          ),
                        ],
                      ),

                      // التقارير المتقدمة
                      _buildModernPermissionCard(
                        title: 'التقارير المتقدمة',
                        icon: Icons.analytics,
                        accentColor: Colors.indigo,
                        children: [
                          _buildModernPermissionRow(
                            title1: 'التقارير المالية',
                            value1: financialReportsPermission,
                            onChanged1: (value) => setState(
                                () => financialReportsPermission = value!),
                            icon1: Icons.bar_chart,
                            title2: 'أهداف المبيعات',
                            value2: salesTargetsPermission,
                            onChanged2: (value) =>
                                setState(() => salesTargetsPermission = value!),
                            icon2: Icons.track_changes,
                          ),
                          _buildModernPermissionTile(
                            title: 'تقارير الضرائب',
                            value: taxReportsPermission,
                            onChanged: (value) =>
                                setState(() => taxReportsPermission = value!),
                            icon: Icons.receipt,
                          ),
                        ],
                      ),

                      // الإعدادات المتقدمة
                      _buildModernPermissionCard(
                        title: 'الإعدادات المتقدمة',
                        icon: Icons.settings_applications,
                        accentColor: Colors.grey,
                        children: [
                          _buildModernPermissionRow(
                            title1: 'سجلات المستخدمين',
                            value1: userLogsPermission,
                            onChanged1: (value) =>
                                setState(() => userLogsPermission = value!),
                            icon1: Icons.history,
                            title2: 'الإشعارات',
                            value2: notificationsPermission,
                            onChanged2: (value) => setState(
                                () => notificationsPermission = value!),
                            icon2: Icons.notifications,
                          ),
                          _buildModernPermissionRow(
                            title1: 'الضمان',
                            value1: warrantyPermission,
                            onChanged1: (value) =>
                                setState(() => warrantyPermission = value!),
                            icon1: Icons.verified_user,
                            title2: 'الإعدادات',
                            value2: settingsPermission,
                            onChanged2: (value) =>
                                setState(() => settingsPermission = value!),
                            icon2: Icons.settings,
                          ),
                          _buildModernPermissionRow(
                            title1: 'إدارة المستخدمين',
                            value1: userManagementPermission,
                            onChanged1: (value) => setState(
                                () => userManagementPermission = value!),
                            icon1: Icons.admin_panel_settings,
                            title2: 'فلاتر المياه (عام)',
                            value2: waterFiltersPermission,
                            onChanged2: (value) =>
                                setState(() => waterFiltersPermission = value!),
                            icon2: Icons.water_drop,
                          ),
                        ],
                      ),

                      // فلاتر المياه - صلاحيات مفصلة
                      _buildModernPermissionCard(
                        title: 'فلاتر المياه - صلاحيات مفصلة',
                        icon: Icons.water_drop,
                        accentColor: Colors.blue,
                        children: [
                          _buildModernPermissionRow(
                            title1: 'منتجات فلاتر المياه',
                            value1: waterFilterProductsPermission,
                            onChanged1: (value) => setState(
                                () => waterFilterProductsPermission = value!),
                            icon1: Icons.inventory_2,
                            title2: 'عملاء فلاتر المياه',
                            value2: waterFilterCustomersPermission,
                            onChanged2: (value) => setState(
                                () => waterFilterCustomersPermission = value!),
                            icon2: Icons.people,
                          ),
                          _buildModernPermissionRow(
                            title1: 'أنظمة فلاتر المياه',
                            value1: waterFilterSystemsPermission,
                            onChanged1: (value) => setState(
                                () => waterFilterSystemsPermission = value!),
                            icon1: Icons.settings,
                            title2: 'صيانة فلاتر المياه',
                            value2: waterFilterMaintenancePermission,
                            onChanged2: (value) => setState(() =>
                                waterFilterMaintenancePermission = value!),
                            icon2: Icons.build,
                          ),
                          _buildModernPermissionRow(
                            title1: 'أقساط فلاتر المياه',
                            value1: waterFilterInstallmentsPermission,
                            onChanged1: (value) => setState(() =>
                                waterFilterInstallmentsPermission = value!),
                            icon1: Icons.payment,
                            title2: 'تقارير فلاتر المياه',
                            value2: waterFilterReportsPermission,
                            onChanged2: (value) => setState(
                                () => waterFilterReportsPermission = value!),
                            icon2: Icons.analytics,
                          ),
                        ],
                      ),

                      // معلومات المستخدم وإعدادات الحساب
                      _buildModernPermissionCard(
                        title: 'معلومات الحساب',
                        icon: Icons.account_circle,
                        accentColor: Colors.brown,
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(10.0),
                            child: Form(
                              key: globalKey,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // اسم المستخدم
                                  Text(
                                    'اسم المستخدم',
                                    style: GoogleFonts.poppins(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w600,
                                      color: kMainColor,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  AppTextField(
                                    readOnly: true,
                                    initialValue: widget.userRoleModel.email
                                        .replaceAll('@amrdev.com', ''),
                                    decoration: kInputDecoration.copyWith(
                                      labelText: 'اسم المستخدم',
                                      hintText: 'اسم المستخدم',
                                      contentPadding:
                                          const EdgeInsets.all(12.0),
                                      enabledBorder: OutlineInputBorder(
                                        borderRadius:
                                            BorderRadius.circular(8.0),
                                        borderSide: BorderSide(
                                            color: Colors.grey.shade300,
                                            width: 1),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius:
                                            BorderRadius.circular(8.0),
                                        borderSide: BorderSide(
                                            color: kMainColor, width: 2),
                                      ),
                                    ),
                                    textFieldType: TextFieldType.NAME,
                                  ),
                                  const SizedBox(height: 20.0),

                                  // المنصب
                                  Text(
                                    'المنصب',
                                    style: GoogleFonts.poppins(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w600,
                                      color: kMainColor,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  AppTextField(
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'المنصب مطلوب';
                                      }
                                      return null;
                                    },
                                    showCursor: true,
                                    controller: titleController,
                                    decoration: kInputDecoration.copyWith(
                                      labelText: lang.S.of(context).userTitle,
                                      hintText:
                                          lang.S.of(context).enterUserTitle,
                                      contentPadding:
                                          const EdgeInsets.all(12.0),
                                      enabledBorder: OutlineInputBorder(
                                        borderRadius:
                                            BorderRadius.circular(8.0),
                                        borderSide: BorderSide(
                                            color: Colors.grey.shade300,
                                            width: 1),
                                      ),
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius:
                                            BorderRadius.circular(8.0),
                                        borderSide: BorderSide(
                                            color: kMainColor, width: 2),
                                      ),
                                      errorBorder: OutlineInputBorder(
                                        borderRadius:
                                            BorderRadius.circular(8.0),
                                        borderSide:
                                            const BorderSide(color: Colors.red),
                                      ),
                                    ),
                                    textFieldType: TextFieldType.NAME,
                                  ),
                                  const SizedBox(height: 20.0),

                                  // زر إعادة تعيين كلمة المرور
                                  if (!isMailSent)
                                    SizedBox(
                                      width: double.infinity,
                                      child: ElevatedButton.icon(
                                        onPressed: () async {
                                          try {
                                            EasyLoading.show(
                                                status: 'جاري الإرسال...');
                                            await FirebaseAuth.instance
                                                .sendPasswordResetEmail(
                                              email: widget.userRoleModel.email,
                                            );

                                            EasyLoading.showSuccess(
                                                'تم إرسال رابط إعادة تعيين كلمة المرور\nتحقق من بريدك الإلكتروني');
                                            setState(() {
                                              isMailSent = true;
                                            });
                                          } catch (e) {
                                            EasyLoading.showError(e.toString());
                                          }
                                        },
                                        icon: const Icon(Icons.email,
                                            color: Colors.white),
                                        label: Text(
                                          'إرسال رابط إعادة تعيين كلمة المرور',
                                          style: GoogleFonts.poppins(
                                            color: Colors.white,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: kMainColor,
                                          padding: const EdgeInsets.symmetric(
                                              vertical: 12),
                                          shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(8),
                                          ),
                                        ),
                                      ),
                                    ),
                                  if (isMailSent)
                                    Container(
                                      width: double.infinity,
                                      padding: const EdgeInsets.all(12),
                                      decoration: BoxDecoration(
                                        color: Colors.green.shade50,
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border.all(
                                            color: Colors.green.shade200),
                                      ),
                                      child: Row(
                                        children: [
                                          Icon(Icons.check_circle,
                                              color: Colors.green.shade600),
                                          const SizedBox(width: 8),
                                          Expanded(
                                            child: Text(
                                              'تم إرسال رابط إعادة تعيين كلمة المرور بنجاح',
                                              style: GoogleFonts.poppins(
                                                color: Colors.green.shade700,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),

                      // زر التحديث في النهاية
                      const SizedBox(height: 30),
                      Container(
                        width: double.infinity,
                        margin: const EdgeInsets.symmetric(horizontal: 0),
                        child: ElevatedButton(
                          onPressed: () async {
                            if (!isDemo) {
                              if (salePermission ||
                                  partiesPermission ||
                                  purchasePermission ||
                                  productPermission ||
                                  profileEditPermission ||
                                  addExpensePermission ||
                                  lossProfitPermission ||
                                  dueListPermission ||
                                  stockPermission ||
                                  reportsPermission ||
                                  salesListPermission ||
                                  purchaseListPermission ||
                                  ledgerPermission ||
                                  aiChatPermission ||
                                  aiAssistantPermission ||
                                  voiceAssistantPermission ||
                                  treasuryPermission ||
                                  cashBoxPermission ||
                                  deliveryManagementPermission ||
                                  hrmPermission ||
                                  employeesPermission ||
                                  designationPermission ||
                                  salariesPermission ||
                                  financialReportsPermission ||
                                  salesTargetsPermission ||
                                  taxReportsPermission ||
                                  userLogsPermission ||
                                  notificationsPermission ||
                                  warrantyPermission ||
                                  settingsPermission ||
                                  userManagementPermission ||
                                  waterFiltersPermission ||
                                  waterFilterProductsPermission ||
                                  waterFilterCustomersPermission ||
                                  waterFilterSystemsPermission ||
                                  waterFilterMaintenancePermission ||
                                  waterFilterInstallmentsPermission ||
                                  waterFilterReportsPermission) {
                                try {
                                  EasyLoading.show(
                                      status: 'Loading...',
                                      dismissOnTap: false);
                                  DatabaseReference dataRef =
                                      FirebaseDatabase.instance.ref(
                                          "$constUserId/User Role/$userRoleKey");
                                  DatabaseReference adminDataRef =
                                      FirebaseDatabase.instance.ref(
                                          "Admin Panel/User Role/$adminRoleKey");
                                  await dataRef.update({
                                    'userTitle': titleController.text,
                                    'salePermission': salePermission,
                                    'partiesPermission': partiesPermission,
                                    'purchasePermission': purchasePermission,
                                    'productPermission': productPermission,
                                    'profileEditPermission':
                                        profileEditPermission,
                                    'addExpensePermission':
                                        addExpensePermission,
                                    'lossProfitPermission':
                                        lossProfitPermission,
                                    'dueListPermission': dueListPermission,
                                    'stockPermission': stockPermission,
                                    'reportsPermission': reportsPermission,
                                    'salesListPermission': salesListPermission,
                                    'purchaseListPermission':
                                        purchaseListPermission,
                                    // الصلاحيات الجديدة - الذكاء الاصطناعي
                                    'aiChatPermission': aiChatPermission,
                                    'aiAssistantPermission':
                                        aiAssistantPermission,
                                    'voiceAssistantPermission':
                                        voiceAssistantPermission,
                                    // الخزينة
                                    'treasuryPermission': treasuryPermission,
                                    'cashBoxPermission': cashBoxPermission,
                                    // إدارة التوصيل
                                    'deliveryManagementPermission':
                                        deliveryManagementPermission,
                                    // الموارد البشرية
                                    'hrmPermission': hrmPermission,
                                    'employeesPermission': employeesPermission,
                                    'designationPermission':
                                        designationPermission,
                                    'salariesPermission': salariesPermission,
                                    // التقارير المتقدمة
                                    'financialReportsPermission':
                                        financialReportsPermission,
                                    'salesTargetsPermission':
                                        salesTargetsPermission,
                                    'taxReportsPermission':
                                        taxReportsPermission,
                                    // الإعدادات المتقدمة
                                    'userLogsPermission': userLogsPermission,
                                    'notificationsPermission':
                                        notificationsPermission,
                                    'warrantyPermission': warrantyPermission,
                                    'settingsPermission': settingsPermission,
                                    'userManagementPermission':
                                        userManagementPermission,
                                    // صلاحيات إضافية
                                    'ledgerPermission': ledgerPermission,
                                    // فلاتر المياه
                                    'waterFiltersPermission':
                                        waterFiltersPermission,
                                    // فلاتر المياه - صلاحيات مفصلة
                                    'waterFilterProductsPermission':
                                        waterFilterProductsPermission,
                                    'waterFilterCustomersPermission':
                                        waterFilterCustomersPermission,
                                    'waterFilterSystemsPermission':
                                        waterFilterSystemsPermission,
                                    'waterFilterMaintenancePermission':
                                        waterFilterMaintenancePermission,
                                    'waterFilterInstallmentsPermission':
                                        waterFilterInstallmentsPermission,
                                    'waterFilterReportsPermission':
                                        waterFilterReportsPermission,
                                    // حالة التفعيل
                                    'isActive': isActive,
                                  });
                                  await adminDataRef.update({
                                    'userTitle': titleController.text,
                                    'salePermission': salePermission,
                                    'partiesPermission': partiesPermission,
                                    'purchasePermission': purchasePermission,
                                    'productPermission': productPermission,
                                    'profileEditPermission':
                                        profileEditPermission,
                                    'addExpensePermission':
                                        addExpensePermission,
                                    'lossProfitPermission':
                                        lossProfitPermission,
                                    'dueListPermission': dueListPermission,
                                    'stockPermission': stockPermission,
                                    'reportsPermission': reportsPermission,
                                    'salesListPermission': salesListPermission,
                                    'purchaseListPermission':
                                        purchaseListPermission,
                                    // الصلاحيات الجديدة - الذكاء الاصطناعي
                                    'aiChatPermission': aiChatPermission,
                                    'aiAssistantPermission':
                                        aiAssistantPermission,
                                    'voiceAssistantPermission':
                                        voiceAssistantPermission,
                                    // الخزينة
                                    'treasuryPermission': treasuryPermission,
                                    'cashBoxPermission': cashBoxPermission,
                                    // إدارة التوصيل
                                    'deliveryManagementPermission':
                                        deliveryManagementPermission,
                                    // الموارد البشرية
                                    'hrmPermission': hrmPermission,
                                    'employeesPermission': employeesPermission,
                                    'designationPermission':
                                        designationPermission,
                                    'salariesPermission': salariesPermission,
                                    // التقارير المتقدمة
                                    'financialReportsPermission':
                                        financialReportsPermission,
                                    'salesTargetsPermission':
                                        salesTargetsPermission,
                                    'taxReportsPermission':
                                        taxReportsPermission,
                                    // الإعدادات المتقدمة
                                    'userLogsPermission': userLogsPermission,
                                    'notificationsPermission':
                                        notificationsPermission,
                                    'warrantyPermission': warrantyPermission,
                                    'settingsPermission': settingsPermission,
                                    'userManagementPermission':
                                        userManagementPermission,
                                    // صلاحيات إضافية
                                    'ledgerPermission': ledgerPermission,
                                    // فلاتر المياه
                                    'waterFiltersPermission':
                                        waterFiltersPermission,
                                    // فلاتر المياه - صلاحيات مفصلة
                                    'waterFilterProductsPermission':
                                        waterFilterProductsPermission,
                                    'waterFilterCustomersPermission':
                                        waterFilterCustomersPermission,
                                    'waterFilterSystemsPermission':
                                        waterFilterSystemsPermission,
                                    'waterFilterMaintenancePermission':
                                        waterFilterMaintenancePermission,
                                    'waterFilterInstallmentsPermission':
                                        waterFilterInstallmentsPermission,
                                    'waterFilterReportsPermission':
                                        waterFilterReportsPermission,
                                    // حالة التفعيل
                                    'isActive': isActive,
                                  });
                                  ref.refresh(userRoleProvider);

                                  EasyLoading.showSuccess('تم التحديث بنجاح',
                                      duration:
                                          const Duration(milliseconds: 500));

                                  Navigator.pop(context);
                                } catch (e) {
                                  EasyLoading.dismiss();
                                  ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(content: Text(e.toString())));
                                }
                              } else {
                                EasyLoading.showError(
                                    'يجب إعطاء صلاحية واحدة على الأقل');
                              }
                            } else {}
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: kMainColor,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            elevation: 2,
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(
                                Icons.update,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                lang.S.of(context).update,
                                style: GoogleFonts.cairo(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      );
    });
  }

  /// تطبيق صلاحيات الدور المحدد
  void _applyRolePermissions(String role) {
    debugPrint('🎭 Applying role permissions for: $role');
    // إعادة تعيين جميع الصلاحيات إلى false أولاً
    _resetAllPermissions();

    switch (role) {
      case 'General Manager':
        _setAllPermissions(true);
        debugPrint('✅ Applied General Manager permissions');
        break;

      case 'Sales Manager':
        salePermission = true;
        partiesPermission = true;
        salesListPermission = true;
        reportsPermission = true;
        dueListPermission = true;
        addExpensePermission = true;
        lossProfitPermission = true;
        aiChatPermission = true;
        debugPrint('✅ Applied Sales Manager permissions');
        break;

      case 'Cashier':
        salePermission = true;
        partiesPermission = true;
        dueListPermission = true;
        addExpensePermission = true;
        treasuryPermission = true;
        cashBoxPermission = true;
        debugPrint('✅ Applied Cashier permissions');
        break;

      case 'Warehouse Employee':
        productPermission = true;
        stockPermission = true;
        purchasePermission = true;
        purchaseListPermission = true;
        deliveryManagementPermission = true;
        debugPrint('✅ Applied Warehouse Employee permissions');
        break;

      case 'Accountant':
        reportsPermission = true;
        lossProfitPermission = true;
        ledgerPermission = true;
        financialReportsPermission = true;
        salesListPermission = true;
        purchaseListPermission = true;
        addExpensePermission = true;
        treasuryPermission = true;
        debugPrint('✅ Applied Accountant permissions');
        break;

      case 'Custom':
        // لا تغيير - يترك للمستخدم اختيار الصلاحيات يدوياً
        debugPrint('✅ Custom role selected - no auto permissions');
        break;
    }
  }

  /// إعادة تعيين جميع الصلاحيات إلى false
  void _resetAllPermissions() {
    allPermissions = false;
    salePermission = false;
    partiesPermission = false;
    purchasePermission = false;
    productPermission = false;
    profileEditPermission = false;
    addExpensePermission = false;
    lossProfitPermission = false;
    dueListPermission = false;
    stockPermission = false;
    reportsPermission = false;
    salesListPermission = false;
    purchaseListPermission = false;
    ledgerPermission = false;
    aiChatPermission = false;
    aiAssistantPermission = false;
    voiceAssistantPermission = false;
    treasuryPermission = false;
    cashBoxPermission = false;
    deliveryManagementPermission = false;
    hrmPermission = false;
    employeesPermission = false;
    designationPermission = false;
    salariesPermission = false;
    financialReportsPermission = false;
    salesTargetsPermission = false;
    taxReportsPermission = false;
    userLogsPermission = false;
    notificationsPermission = false;
    warrantyPermission = false;
    settingsPermission = false;
    userManagementPermission = false;
    // فلاتر المياه
    waterFiltersPermission = false;
    // فلاتر المياه - صلاحيات مفصلة
    waterFilterProductsPermission = false;
    waterFilterCustomersPermission = false;
    waterFilterSystemsPermission = false;
    waterFilterMaintenancePermission = false;
    waterFilterInstallmentsPermission = false;
    waterFilterReportsPermission = false;
  }

  /// تعيين جميع الصلاحيات إلى قيمة محددة
  void _setAllPermissions(bool value) {
    allPermissions = value;
    salePermission = value;
    partiesPermission = value;
    purchasePermission = value;
    productPermission = value;
    profileEditPermission = value;
    addExpensePermission = value;
    lossProfitPermission = value;
    dueListPermission = value;
    stockPermission = value;
    reportsPermission = value;
    salesListPermission = value;
    purchaseListPermission = value;
    ledgerPermission = value;
    aiChatPermission = value;
    aiAssistantPermission = value;
    voiceAssistantPermission = value;
    treasuryPermission = value;
    cashBoxPermission = value;
    deliveryManagementPermission = value;
    hrmPermission = value;
    employeesPermission = value;
    designationPermission = value;
    salariesPermission = value;
    financialReportsPermission = value;
    salesTargetsPermission = value;
    taxReportsPermission = value;
    userLogsPermission = value;
    notificationsPermission = value;
    warrantyPermission = value;
    settingsPermission = value;
    userManagementPermission = value;
    // فلاتر المياه
    waterFiltersPermission = value;
    // فلاتر المياه - صلاحيات مفصلة
    waterFilterProductsPermission = value;
    waterFilterCustomersPermission = value;
    waterFilterSystemsPermission = value;
    waterFilterMaintenancePermission = value;
    waterFilterInstallmentsPermission = value;
    waterFilterReportsPermission = value;
  }
}

Future<void> deleteUserRole(
    {required String email,
    required String password,
    required String adminKey,
    required String userKey,
    required BuildContext context}) async {
  EasyLoading.show(status: 'Loading...');
  try {
    final userCredential = await FirebaseAuth.instance.signInWithCredential(
        EmailAuthProvider.credential(email: email, password: password));
    final user = userCredential.user;
    await user?.delete();
    DatabaseReference dataRef =
        FirebaseDatabase.instance.ref("$constUserId/User Role/$userKey");
    DatabaseReference adminDataRef =
        FirebaseDatabase.instance.ref("Admin Panel/User Role/$adminKey");

    await dataRef.remove();
    await adminDataRef.remove();

    EasyLoading.dismiss();
    // ignore:
    await showSussesScreenAndLogOut(context: context);
  } catch (e) {
    EasyLoading.dismiss();
    EasyLoading.showError(e.toString());
  }
}
