import 'package:intl/intl.dart';

/// مساعد لتنسيق الوقت بطريقة نسبية
class TimeFormatter {
  /// تنسيق الوقت النسبي (منذ دقيقة، منذ 5 ثواني، إلخ)
  static String timeAgo(DateTime dateTime, {bool shortFormat = false}) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inSeconds < 5) {
      return shortFormat ? 'الآن' : 'منذ لحظات';
    } else if (difference.inSeconds < 60) {
      return shortFormat
          ? '${difference.inSeconds}ث'
          : 'منذ ${difference.inSeconds} ثانية';
    } else if (difference.inMinutes < 60) {
      return shortFormat
          ? '${difference.inMinutes}د'
          : 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return shortFormat
          ? '${difference.inHours}س'
          : 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return shortFormat
          ? '${difference.inDays}ي'
          : 'منذ ${difference.inDays} يوم';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return shortFormat ? '$weeksأ' : 'منذ $weeks أسبوع';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return shortFormat ? '$monthsش' : 'منذ $months شهر';
    } else {
      final years = (difference.inDays / 365).floor();
      return shortFormat ? '$yearsسنة' : 'منذ $years سنة';
    }
  }

  /// تنسيق التاريخ بشكل مختصر (اليوم، أمس، التاريخ)
  static String formatDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final dateToCheck = DateTime(date.year, date.month, date.day);

    if (dateToCheck == today) {
      return 'اليوم ${DateFormat.Hm().format(date)}';
    } else if (dateToCheck == yesterday) {
      return 'أمس ${DateFormat.Hm().format(date)}';
    } else {
      return DateFormat('yyyy/MM/dd').format(date);
    }
  }

  /// تنسيق التاريخ والوقت بشكل كامل
  static String formatDateTime(DateTime date) {
    return DateFormat('yyyy/MM/dd HH:mm').format(date);
  }
}
