import 'package:flutter/foundation.dart';
import 'package:mobile_pos/model/water_filter_models.dart';
import 'package:mobile_pos/services/water_filter_service.dart';

/// خدمة إدارة مصروفات فلاتر المياه
class WaterFilterExpenseService {
  static const String _collectionName = 'Expenses';

  /// إضافة مصروف جديد
  static Future<bool> addExpense(WaterFilterExpense expense) async {
    try {
      debugPrint('➕ إضافة مصروف جديد: ${expense.title}');

      // التحقق من صحة البيانات
      if (!_validateExpense(expense)) {
        debugPrint('❌ بيانات المصروف غير صحيحة');
        return false;
      }

      // إنشاء معرف فريد إذا لم يكن موجوداً
      if (expense.id.isEmpty) {
        expense.id = WaterFilterService.generateId();
      }

      // إضافة الطوابع الزمنية
      expense.createdAt = DateTime.now();
      expense.updatedAt = DateTime.now();

      // حفظ المصروف في قاعدة البيانات
      final success = await WaterFilterService.saveData(
        '$_collectionName/${expense.id}',
        expense.toJson(),
      );

      if (success) {
        debugPrint('✅ تم إضافة المصروف بنجاح: ${expense.id}');

        // إنشاء المصروف المتكرر التالي إذا كان متكرراً
        if (expense.isRecurring && expense.recurringIntervalDays != null) {
          await _createNextRecurringExpense(expense);
        }
      }

      return success;
    } catch (e) {
      debugPrint('❌ خطأ في إضافة المصروف: $e');
      return false;
    }
  }

  /// تحديث مصروف موجود
  static Future<bool> updateExpense(WaterFilterExpense expense) async {
    try {
      debugPrint('🔄 تحديث المصروف: ${expense.id}');

      // التحقق من صحة البيانات
      if (!_validateExpense(expense)) {
        debugPrint('❌ بيانات المصروف غير صحيحة');
        return false;
      }

      // تحديث الطابع الزمني
      expense.updatedAt = DateTime.now();

      // تحديث المصروف في قاعدة البيانات
      final success = await WaterFilterService.updateData(
        '$_collectionName/${expense.id}',
        expense.toJson(),
      );

      if (success) {
        debugPrint('✅ تم تحديث المصروف بنجاح: ${expense.id}');
      }

      return success;
    } catch (e) {
      debugPrint('❌ خطأ في تحديث المصروف: $e');
      return false;
    }
  }

  /// حذف مصروف
  static Future<bool> deleteExpense(String expenseId) async {
    try {
      debugPrint('🗑️ حذف المصروف: $expenseId');

      final success =
          await WaterFilterService.deleteData('$_collectionName/$expenseId');

      if (success) {
        debugPrint('✅ تم حذف المصروف بنجاح: $expenseId');
      }

      return success;
    } catch (e) {
      debugPrint('❌ خطأ في حذف المصروف: $e');
      return false;
    }
  }

  /// جلب جميع المصروفات
  static Future<List<WaterFilterExpense>> getAllExpenses() async {
    try {
      debugPrint('📥 جلب جميع المصروفات');

      final data = await WaterFilterService.getData(_collectionName);
      final expenses = <WaterFilterExpense>[];

      data.forEach((key, value) {
        try {
          final expense = WaterFilterExpense.fromJson(
            Map<String, dynamic>.from(value),
          );
          expenses.add(expense);
        } catch (e) {
          debugPrint('❌ خطأ في معالجة مصروف: $e');
        }
      });

      // ترتيب حسب التاريخ (الأحدث أولاً)
      expenses.sort((a, b) => b.date.compareTo(a.date));

      debugPrint('✅ تم جلب ${expenses.length} مصروف');
      return expenses;
    } catch (e) {
      debugPrint('❌ خطأ في جلب المصروفات: $e');
      return [];
    }
  }

  /// جلب مصروف بالمعرف
  static Future<WaterFilterExpense?> getExpenseById(String expenseId) async {
    try {
      debugPrint('🔍 البحث عن المصروف: $expenseId');

      final data =
          await WaterFilterService.getData('$_collectionName/$expenseId');

      if (data.isNotEmpty) {
        final expense = WaterFilterExpense.fromJson(
          Map<String, dynamic>.from(data),
        );
        debugPrint('✅ تم العثور على المصروف: ${expense.title}');
        return expense;
      }

      debugPrint('❌ لم يتم العثور على المصروف: $expenseId');
      return null;
    } catch (e) {
      debugPrint('❌ خطأ في البحث عن المصروف: $e');
      return null;
    }
  }

  /// جلب المصروفات حسب الفئة
  static Future<List<WaterFilterExpense>> getExpensesByCategory(
      ExpenseCategory category) async {
    try {
      final allExpenses = await getAllExpenses();
      return allExpenses
          .where((expense) => expense.category == category)
          .toList();
    } catch (e) {
      debugPrint('❌ خطأ في جلب المصروفات حسب الفئة: $e');
      return [];
    }
  }

  /// جلب المصروفات في فترة زمنية
  static Future<List<WaterFilterExpense>> getExpensesByDateRange(
      DateTime startDate, DateTime endDate) async {
    try {
      final allExpenses = await getAllExpenses();
      return allExpenses.where((expense) {
        return expense.date
                .isAfter(startDate.subtract(const Duration(days: 1))) &&
            expense.date.isBefore(endDate.add(const Duration(days: 1)));
      }).toList();
    } catch (e) {
      debugPrint('❌ خطأ في جلب المصروفات حسب التاريخ: $e');
      return [];
    }
  }

  /// حساب إجمالي المصروفات
  static Future<double> getTotalExpenses({
    DateTime? startDate,
    DateTime? endDate,
    ExpenseCategory? category,
  }) async {
    try {
      List<WaterFilterExpense> expenses;

      if (startDate != null && endDate != null) {
        expenses = await getExpensesByDateRange(startDate, endDate);
      } else {
        expenses = await getAllExpenses();
      }

      if (category != null) {
        expenses =
            expenses.where((expense) => expense.category == category).toList();
      }

      return expenses.fold<double>(0.0, (sum, expense) => sum + expense.amount);
    } catch (e) {
      debugPrint('❌ خطأ في حساب إجمالي المصروفات: $e');
      return 0.0;
    }
  }

  /// إحصائيات المصروفات
  static Future<Map<String, dynamic>> getExpenseStatistics() async {
    try {
      final expenses = await getAllExpenses();
      final now = DateTime.now();
      final thisMonth = DateTime(now.year, now.month);
      final lastMonth = DateTime(now.year, now.month - 1);

      // إحصائيات شهرية
      final thisMonthExpenses = expenses
          .where((e) => e.date.year == now.year && e.date.month == now.month)
          .toList();

      final lastMonthExpenses = expenses
          .where((e) =>
              e.date.year == lastMonth.year && e.date.month == lastMonth.month)
          .toList();

      // إحصائيات حسب الفئة
      final categoryStats = <String, double>{};
      for (final category in ExpenseCategory.values) {
        final categoryExpenses = expenses.where((e) => e.category == category);
        categoryStats[category.arabicName] =
            categoryExpenses.fold(0.0, (sum, e) => sum + e.amount);
      }

      return {
        'totalExpenses': expenses.fold(0.0, (sum, e) => sum + e.amount),
        'thisMonthTotal':
            thisMonthExpenses.fold(0.0, (sum, e) => sum + e.amount),
        'lastMonthTotal':
            lastMonthExpenses.fold(0.0, (sum, e) => sum + e.amount),
        'thisMonthCount': thisMonthExpenses.length,
        'lastMonthCount': lastMonthExpenses.length,
        'categoryStats': categoryStats,
        'averageExpense': expenses.isNotEmpty
            ? expenses.fold(0.0, (sum, e) => sum + e.amount) / expenses.length
            : 0.0,
      };
    } catch (e) {
      debugPrint('❌ خطأ في حساب إحصائيات المصروفات: $e');
      return {};
    }
  }

  /// التحقق من صحة بيانات المصروف
  static bool _validateExpense(WaterFilterExpense expense) {
    if (expense.title.trim().isEmpty) {
      debugPrint('❌ عنوان المصروف مطلوب');
      return false;
    }

    if (expense.amount <= 0) {
      debugPrint('❌ مبلغ المصروف يجب أن يكون أكبر من صفر');
      return false;
    }

    if (expense.description.trim().isEmpty) {
      debugPrint('❌ وصف المصروف مطلوب');
      return false;
    }

    return true;
  }

  /// إنشاء المصروف المتكرر التالي
  static Future<void> _createNextRecurringExpense(
      WaterFilterExpense expense) async {
    try {
      if (!expense.isRecurring || expense.recurringIntervalDays == null) {
        return;
      }

      final nextDate =
          expense.date.add(Duration(days: expense.recurringIntervalDays!));

      final nextExpense = WaterFilterExpense(
        id: '', // سيتم إنشاؤه تلقائياً
        title: expense.title,
        description: expense.description,
        category: expense.category,
        amount: expense.amount,
        date: nextDate,
        paymentMethod: expense.paymentMethod,
        vendorName: expense.vendorName,
        notes: expense.notes,
        isRecurring: true,
        recurringIntervalDays: expense.recurringIntervalDays,
      );

      // حفظ المصروف المتكرر التالي
      await addExpense(nextExpense);

      debugPrint('✅ تم إنشاء المصروف المتكرر التالي: $nextDate');
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء المصروف المتكرر: $e');
    }
  }
}
