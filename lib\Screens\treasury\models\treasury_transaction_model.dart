class TreasuryTransactionModel {
  late String id;
  late String date;
  late String type; // 'income' or 'expense'
  late String category;
  late String description;
  late String amount;
  late String paymentMethod;
  late String referenceNumber;
  late String notes;
  late String createdBy;
  late String createdAt;
  late String updatedAt;

  TreasuryTransactionModel({
    required this.id,
    required this.date,
    required this.type,
    required this.category,
    required this.description,
    required this.amount,
    required this.paymentMethod,
    required this.referenceNumber,
    required this.notes,
    required this.createdBy,
    required this.createdAt,
    required this.updatedAt,
  });

  TreasuryTransactionModel.fromJson(Map<dynamic, dynamic> json) {
    id = json['id']?.toString() ?? '';
    date = json['date']?.toString() ?? '';
    type = json['type']?.toString() ?? '';
    category = json['category']?.toString() ?? '';
    description = json['description']?.toString() ?? '';
    amount = json['amount']?.toString() ?? '0';
    paymentMethod = json['paymentMethod']?.toString() ?? '';
    referenceNumber = json['referenceNumber']?.toString() ?? '';
    notes = json['notes']?.toString() ?? '';
    createdBy = json['createdBy']?.toString() ?? '';
    createdAt = json['createdAt']?.toString() ?? '';
    updatedAt = json['updatedAt']?.toString() ?? '';
  }

  Map<dynamic, dynamic> toJson() => <dynamic, dynamic>{
        'id': id,
        'date': date,
        'type': type,
        'category': category,
        'description': description,
        'amount': amount,
        'paymentMethod': paymentMethod,
        'referenceNumber': referenceNumber,
        'notes': notes,
        'createdBy': createdBy,
        'createdAt': createdAt,
        'updatedAt': updatedAt,
      };

  // Helper methods
  double get amountAsDouble => double.tryParse(amount) ?? 0.0;
  
  bool get isIncome => type == 'income';
  bool get isExpense => type == 'expense';
  
  DateTime get dateAsDateTime => DateTime.tryParse(date) ?? DateTime.now();
  DateTime get createdAtAsDateTime => DateTime.tryParse(createdAt) ?? DateTime.now();
  DateTime get updatedAtAsDateTime => DateTime.tryParse(updatedAt) ?? DateTime.now();
}
