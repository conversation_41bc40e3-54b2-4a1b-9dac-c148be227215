import 'package:flutter/foundation.dart';
import 'package:mobile_pos/model/water_filter_models.dart';
import 'package:mobile_pos/services/water_filter_service.dart';

/// خدمة التخزين المؤقت لنظام فلاتر المياه
/// تحسن الأداء عبر تخزين البيانات المتكررة في الذاكرة
class WaterFilterCacheService {
  static final WaterFilterCacheService _instance = WaterFilterCacheService._internal();
  factory WaterFilterCacheService() => _instance;
  WaterFilterCacheService._internal();

  // Cache للبيانات
  final Map<String, List<WaterFilterProduct>> _productsCache = {};
  final Map<String, List<WaterFilterCustomer>> _customersCache = {};
  final Map<String, List<WaterFilterSystem>> _systemsCache = {};
  final Map<String, Map<String, dynamic>> _statsCache = {};
  
  // Cache timestamps للتحقق من انتهاء الصلاحية
  final Map<String, DateTime> _cacheTimestamps = {};
  
  // مدة صلاحية Cache (5 دقائق)
  static const Duration _cacheExpiry = Duration(minutes: 5);

  /// جلب المنتجات مع Cache
  Future<List<WaterFilterProduct>> getCachedProducts({bool forceRefresh = false}) async {
    const cacheKey = 'products';
    
    if (!forceRefresh && _isValidCache(cacheKey) && _productsCache.containsKey(cacheKey)) {
      debugPrint('📦 استخدام Cache للمنتجات');
      return _productsCache[cacheKey]!;
    }

    debugPrint('🔄 تحديث Cache المنتجات');
    try {
      final data = await WaterFilterService.getData('Products');
      final products = <WaterFilterProduct>[];

      data.forEach((key, value) {
        try {
          final product = WaterFilterProduct.fromJson(Map<String, dynamic>.from(value));
          products.add(product);
        } catch (e) {
          debugPrint('خطأ في معالجة منتج: $e');
        }
      });

      // ترتيب حسب الاسم
      products.sort((a, b) => a.name.compareTo(b.name));

      _productsCache[cacheKey] = products;
      _cacheTimestamps[cacheKey] = DateTime.now();
      
      debugPrint('✅ تم تحديث Cache المنتجات: ${products.length} منتج');
      return products;
    } catch (e) {
      debugPrint('❌ خطأ في جلب المنتجات: $e');
      return _productsCache[cacheKey] ?? [];
    }
  }

  /// جلب العملاء مع Cache
  Future<List<WaterFilterCustomer>> getCachedCustomers({bool forceRefresh = false}) async {
    const cacheKey = 'customers';
    
    if (!forceRefresh && _isValidCache(cacheKey) && _customersCache.containsKey(cacheKey)) {
      debugPrint('📦 استخدام Cache للعملاء');
      return _customersCache[cacheKey]!;
    }

    debugPrint('🔄 تحديث Cache العملاء');
    try {
      final data = await WaterFilterService.getData('Customers');
      final customers = <WaterFilterCustomer>[];

      data.forEach((key, value) {
        try {
          final customer = WaterFilterCustomer.fromJson(Map<String, dynamic>.from(value));
          customers.add(customer);
        } catch (e) {
          debugPrint('خطأ في معالجة عميل: $e');
        }
      });

      // ترتيب حسب الاسم
      customers.sort((a, b) => a.name.compareTo(b.name));

      _customersCache[cacheKey] = customers;
      _cacheTimestamps[cacheKey] = DateTime.now();
      
      debugPrint('✅ تم تحديث Cache العملاء: ${customers.length} عميل');
      return customers;
    } catch (e) {
      debugPrint('❌ خطأ في جلب العملاء: $e');
      return _customersCache[cacheKey] ?? [];
    }
  }

  /// جلب الأنظمة مع Cache
  Future<List<WaterFilterSystem>> getCachedSystems({bool forceRefresh = false}) async {
    const cacheKey = 'systems';
    
    if (!forceRefresh && _isValidCache(cacheKey) && _systemsCache.containsKey(cacheKey)) {
      debugPrint('📦 استخدام Cache للأنظمة');
      return _systemsCache[cacheKey]!;
    }

    debugPrint('🔄 تحديث Cache الأنظمة');
    try {
      final data = await WaterFilterService.getData('Systems');
      final systems = <WaterFilterSystem>[];

      data.forEach((key, value) {
        try {
          final system = WaterFilterSystem.fromJson(Map<String, dynamic>.from(value));
          systems.add(system);
        } catch (e) {
          debugPrint('خطأ في معالجة نظام: $e');
        }
      });

      // ترتيب حسب تاريخ التركيب (الأحدث أولاً)
      systems.sort((a, b) => b.installationDate.compareTo(a.installationDate));

      _systemsCache[cacheKey] = systems;
      _cacheTimestamps[cacheKey] = DateTime.now();
      
      debugPrint('✅ تم تحديث Cache الأنظمة: ${systems.length} نظام');
      return systems;
    } catch (e) {
      debugPrint('❌ خطأ في جلب الأنظمة: $e');
      return _systemsCache[cacheKey] ?? [];
    }
  }

  /// جلب الإحصائيات مع Cache
  Future<Map<String, dynamic>> getCachedStats({bool forceRefresh = false}) async {
    const cacheKey = 'stats';
    
    if (!forceRefresh && _isValidCache(cacheKey) && _statsCache.containsKey(cacheKey)) {
      debugPrint('📦 استخدام Cache للإحصائيات');
      return _statsCache[cacheKey]!;
    }

    debugPrint('🔄 تحديث Cache الإحصائيات');
    try {
      final stats = await WaterFilterService.getQuickStats();
      
      _statsCache[cacheKey] = stats;
      _cacheTimestamps[cacheKey] = DateTime.now();
      
      debugPrint('✅ تم تحديث Cache الإحصائيات');
      return stats;
    } catch (e) {
      debugPrint('❌ خطأ في جلب الإحصائيات: $e');
      return _statsCache[cacheKey] ?? {};
    }
  }

  /// البحث في المنتجات المخزنة مؤقتاً
  Future<List<WaterFilterProduct>> searchProducts(String query) async {
    final products = await getCachedProducts();
    
    if (query.isEmpty) return products;
    
    final lowerQuery = query.toLowerCase();
    return products.where((product) {
      return product.name.toLowerCase().contains(lowerQuery) ||
             product.brand.toLowerCase().contains(lowerQuery) ||
             product.description.toLowerCase().contains(lowerQuery) ||
             product.category.arabicName.toLowerCase().contains(lowerQuery);
    }).toList();
  }

  /// البحث في العملاء المخزنين مؤقتاً
  Future<List<WaterFilterCustomer>> searchCustomers(String query) async {
    final customers = await getCachedCustomers();
    
    if (query.isEmpty) return customers;
    
    final lowerQuery = query.toLowerCase();
    return customers.where((customer) {
      return customer.name.toLowerCase().contains(lowerQuery) ||
             customer.phone.contains(query) ||
             customer.address.toLowerCase().contains(lowerQuery) ||
             customer.area.toLowerCase().contains(lowerQuery) ||
             customer.city.toLowerCase().contains(lowerQuery);
    }).toList();
  }

  /// البحث في الأنظمة المخزنة مؤقتاً
  Future<List<WaterFilterSystem>> searchSystems(String query) async {
    final systems = await getCachedSystems();
    
    if (query.isEmpty) return systems;
    
    final lowerQuery = query.toLowerCase();
    return systems.where((system) {
      return system.serialNumber.toLowerCase().contains(lowerQuery) ||
             system.id.toLowerCase().contains(lowerQuery);
    }).toList();
  }

  /// فلترة المنتجات حسب الفئة
  Future<List<WaterFilterProduct>> getProductsByCategory(WaterFilterCategory category) async {
    final products = await getCachedProducts();
    return products.where((product) => product.category == category).toList();
  }

  /// فلترة الأنظمة حسب الحالة
  Future<List<WaterFilterSystem>> getSystemsByStatus(FilterSystemStatus status) async {
    final systems = await getCachedSystems();
    return systems.where((system) => system.status == status).toList();
  }

  /// الحصول على الأنظمة التي تحتاج صيانة
  Future<List<WaterFilterSystem>> getSystemsNeedingMaintenance() async {
    final systems = await getCachedSystems();
    final now = DateTime.now();
    
    return systems.where((system) {
      return system.status == FilterSystemStatus.needsMaintenance ||
             system.nextMaintenanceDate.isBefore(now);
    }).toList();
  }

  /// الحصول على المنتجات منخفضة المخزون
  Future<List<WaterFilterProduct>> getLowStockProducts({int threshold = 10}) async {
    final products = await getCachedProducts();
    return products.where((product) => product.stock <= threshold).toList();
  }

  /// تحديث Cache عنصر واحد
  void updateCacheItem<T>(String cacheType, String itemId, T item) {
    switch (cacheType) {
      case 'products':
        if (_productsCache.containsKey('products')) {
          final products = _productsCache['products']!;
          final index = products.indexWhere((p) => p.id == itemId);
          if (index != -1 && item is WaterFilterProduct) {
            products[index] = item;
          }
        }
        break;
      case 'customers':
        if (_customersCache.containsKey('customers')) {
          final customers = _customersCache['customers']!;
          final index = customers.indexWhere((c) => c.id == itemId);
          if (index != -1 && item is WaterFilterCustomer) {
            customers[index] = item;
          }
        }
        break;
      case 'systems':
        if (_systemsCache.containsKey('systems')) {
          final systems = _systemsCache['systems']!;
          final index = systems.indexWhere((s) => s.id == itemId);
          if (index != -1 && item is WaterFilterSystem) {
            systems[index] = item;
          }
        }
        break;
    }
  }

  /// إضافة عنصر جديد للـ Cache
  void addCacheItem<T>(String cacheType, T item) {
    switch (cacheType) {
      case 'products':
        if (_productsCache.containsKey('products') && item is WaterFilterProduct) {
          _productsCache['products']!.add(item);
          _productsCache['products']!.sort((a, b) => a.name.compareTo(b.name));
        }
        break;
      case 'customers':
        if (_customersCache.containsKey('customers') && item is WaterFilterCustomer) {
          _customersCache['customers']!.add(item);
          _customersCache['customers']!.sort((a, b) => a.name.compareTo(b.name));
        }
        break;
      case 'systems':
        if (_systemsCache.containsKey('systems') && item is WaterFilterSystem) {
          _systemsCache['systems']!.add(item);
          _systemsCache['systems']!.sort((a, b) => b.installationDate.compareTo(a.installationDate));
        }
        break;
    }
  }

  /// حذف عنصر من الـ Cache
  void removeCacheItem(String cacheType, String itemId) {
    switch (cacheType) {
      case 'products':
        if (_productsCache.containsKey('products')) {
          _productsCache['products']!.removeWhere((p) => p.id == itemId);
        }
        break;
      case 'customers':
        if (_customersCache.containsKey('customers')) {
          _customersCache['customers']!.removeWhere((c) => c.id == itemId);
        }
        break;
      case 'systems':
        if (_systemsCache.containsKey('systems')) {
          _systemsCache['systems']!.removeWhere((s) => s.id == itemId);
        }
        break;
    }
  }

  /// مسح Cache معين
  void clearCache(String cacheKey) {
    _productsCache.remove(cacheKey);
    _customersCache.remove(cacheKey);
    _systemsCache.remove(cacheKey);
    _statsCache.remove(cacheKey);
    _cacheTimestamps.remove(cacheKey);
    debugPrint('🗑️ تم مسح Cache: $cacheKey');
  }

  /// مسح جميع Cache
  void clearAllCache() {
    _productsCache.clear();
    _customersCache.clear();
    _systemsCache.clear();
    _statsCache.clear();
    _cacheTimestamps.clear();
    debugPrint('🗑️ تم مسح جميع Cache');
  }

  /// التحقق من صلاحية Cache
  bool _isValidCache(String cacheKey) {
    if (!_cacheTimestamps.containsKey(cacheKey)) return false;
    
    final cacheTime = _cacheTimestamps[cacheKey]!;
    final now = DateTime.now();
    
    return now.difference(cacheTime) < _cacheExpiry;
  }

  /// الحصول على معلومات Cache
  Map<String, dynamic> getCacheInfo() {
    return {
      'products_count': _productsCache['products']?.length ?? 0,
      'customers_count': _customersCache['customers']?.length ?? 0,
      'systems_count': _systemsCache['systems']?.length ?? 0,
      'cache_timestamps': _cacheTimestamps.map((key, value) => 
        MapEntry(key, value.toIso8601String())),
      'cache_validity': _cacheTimestamps.map((key, value) => 
        MapEntry(key, _isValidCache(key))),
    };
  }
}
