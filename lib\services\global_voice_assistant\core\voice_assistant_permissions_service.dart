import 'package:flutter/foundation.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../permissions/permission_manager.dart';

/// خدمة موحدة لإدارة أذونات المساعد الصوتي
class VoiceAssistantPermissionsService {
  static final VoiceAssistantPermissionsService _instance =
      VoiceAssistantPermissionsService._internal();
  factory VoiceAssistantPermissionsService() => _instance;
  VoiceAssistantPermissionsService._internal();

  // مدير الأذونات المحسن
  final PermissionManager _permissionManager = PermissionManager();

  // مفاتيح التخزين (للتوافق مع النظام القديم)
  static const String _permissionsAskedKey = 'voice_permissions_asked';
  static const String _userDeniedKey = 'voice_user_denied_permissions';

  /// فحص الأذونات الأساسية المطلوبة
  Future<Map<String, bool>> checkEssentialPermissions() async {
    final permissions = <String, bool>{};

    try {
      // إذن الميكروفون (أساسي)
      permissions['microphone'] = await Permission.microphone.isGranted;

      // debugPrint('📋 حالة الأذونات الأساسية:');
      // debugPrint('   - الميكروفون: ${permissions['microphone']}');
    } catch (e) {
      debugPrint('❌ خطأ في فحص الأذونات الأساسية: $e');
    }

    return permissions;
  }

  /// فحص الأذونات الاختيارية
  Future<Map<String, bool>> checkOptionalPermissions() async {
    final permissions = <String, bool>{};

    try {
      // إذن النوافذ العائمة (اختياري)
      try {
        permissions['systemAlertWindow'] =
            await Permission.systemAlertWindow.isGranted;
      } catch (e) {
        permissions['systemAlertWindow'] = false;
        debugPrint('⚠️ إذن النوافذ العائمة غير متاح: $e');
      }

      // إذن تحسين البطارية (اختياري)
      try {
        permissions['ignoreBatteryOptimizations'] =
            await Permission.ignoreBatteryOptimizations.isGranted;
      } catch (e) {
        permissions['ignoreBatteryOptimizations'] = false;
        debugPrint('⚠️ إذن تحسين البطارية غير متاح: $e');
      }

      debugPrint('📋 حالة الأذونات الاختيارية:');
      debugPrint('   - النوافذ العائمة: ${permissions['systemAlertWindow']}');
      debugPrint(
          '   - تحسين البطارية: ${permissions['ignoreBatteryOptimizations']}');
    } catch (e) {
      debugPrint('❌ خطأ في فحص الأذونات الاختيارية: $e');
    }

    return permissions;
  }

  /// طلب الأذونات الأساسية فقط
  Future<bool> requestEssentialPermissions() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // التحقق من أن المستخدم لم يرفض الأذونات سابقاً
      final userDenied = prefs.getBool(_userDeniedKey) ?? false;
      if (userDenied) {
        debugPrint('⚠️ المستخدم رفض الأذونات سابقاً');
        return false;
      }

      // طلب إذن الميكروفون
      final micStatus = await Permission.microphone.request();

      if (micStatus == PermissionStatus.granted) {
        debugPrint('✅ تم منح إذن الميكروفون');
        await prefs.setBool(_permissionsAskedKey, true);
        return true;
      } else if (micStatus == PermissionStatus.permanentlyDenied) {
        debugPrint('❌ تم رفض إذن الميكروفون نهائياً');
        await prefs.setBool(_userDeniedKey, true);
        return false;
      } else {
        debugPrint('⚠️ لم يتم منح إذن الميكروفون: $micStatus');
        return false;
      }
    } catch (e) {
      debugPrint('❌ خطأ في طلب الأذونات الأساسية: $e');
      return false;
    }
  }

  /// طلب الأذونات الاختيارية (بدون إجبار)
  Future<Map<String, bool>> requestOptionalPermissions() async {
    final results = <String, bool>{};

    try {
      // طلب إذن النوافذ العائمة (اختياري)
      try {
        final overlayStatus = await Permission.systemAlertWindow.request();
        results['systemAlertWindow'] =
            overlayStatus == PermissionStatus.granted;

        if (results['systemAlertWindow']!) {
          debugPrint('✅ تم منح إذن النوافذ العائمة');
        } else {
          debugPrint('⚠️ لم يتم منح إذن النوافذ العائمة (اختياري)');
        }
      } catch (e) {
        results['systemAlertWindow'] = false;
        debugPrint('⚠️ إذن النوافذ العائمة غير متاح: $e');
      }

      // طلب إذن تحسين البطارية (اختياري)
      try {
        final batteryStatus =
            await Permission.ignoreBatteryOptimizations.request();
        results['ignoreBatteryOptimizations'] =
            batteryStatus == PermissionStatus.granted;

        if (results['ignoreBatteryOptimizations']!) {
          debugPrint('✅ تم منح إذن تحسين البطارية');
        } else {
          debugPrint('⚠️ لم يتم منح إذن تحسين البطارية (اختياري)');
        }
      } catch (e) {
        results['ignoreBatteryOptimizations'] = false;
        debugPrint('⚠️ إذن تحسين البطارية غير متاح: $e');
      }
    } catch (e) {
      debugPrint('❌ خطأ في طلب الأذونات الاختيارية: $e');
    }

    return results;
  }

  /// التحقق من إمكانية تشغيل المساعد الصوتي
  Future<bool> canRunVoiceAssistant() async {
    final essentialPermissions = await checkEssentialPermissions();
    return essentialPermissions['microphone'] ?? false;
  }

  /// التحقق من إمكانية إظهار النوافذ العائمة
  Future<bool> canShowOverlays() async {
    final optionalPermissions = await checkOptionalPermissions();
    return optionalPermissions['systemAlertWindow'] ?? false;
  }

  /// إعادة تعيين حالة الأذونات
  Future<void> resetPermissionsState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_permissionsAskedKey);
      await prefs.remove(_userDeniedKey);
      debugPrint('✅ تم إعادة تعيين حالة الأذونات');
    } catch (e) {
      debugPrint('❌ خطأ في إعادة تعيين حالة الأذونات: $e');
    }
  }

  /// الحصول على تقرير شامل للأذونات
  Future<Map<String, dynamic>> getPermissionsReport() async {
    final essential = await checkEssentialPermissions();
    final optional = await checkOptionalPermissions();

    final prefs = await SharedPreferences.getInstance();
    final permissionsAsked = prefs.getBool(_permissionsAskedKey) ?? false;
    final userDenied = prefs.getBool(_userDeniedKey) ?? false;

    return {
      'essential': essential,
      'optional': optional,
      'canRun': await canRunVoiceAssistant(),
      'canShowOverlays': await canShowOverlays(),
      'permissionsAsked': permissionsAsked,
      'userDenied': userDenied,
      'recommendations': _getRecommendations(essential, optional),
    };
  }

  /// الحصول على توصيات بناءً على حالة الأذونات
  List<String> _getRecommendations(
      Map<String, bool> essential, Map<String, bool> optional) {
    final recommendations = <String>[];

    if (!(essential['microphone'] ?? false)) {
      recommendations.add('يجب منح إذن الميكروفون لتشغيل المساعد الصوتي');
    }

    if (!(optional['systemAlertWindow'] ?? false)) {
      recommendations.add('إذن النوافذ العائمة اختياري - يحسن تجربة الاستخدام');
    }

    if (!(optional['ignoreBatteryOptimizations'] ?? false)) {
      recommendations
          .add('إذن تحسين البطارية اختياري - يحسن الأداء في الخلفية');
    }

    if (recommendations.isEmpty) {
      recommendations.add('جميع الأذونات متاحة - المساعد الصوتي جاهز للعمل');
    }

    return recommendations;
  }

  /// فتح إعدادات التطبيق لمنح الأذونات يدوياً
  Future<void> openAppSettings() async {
    try {
      await openAppSettings();
      debugPrint('📱 تم فتح إعدادات التطبيق');
    } catch (e) {
      debugPrint('❌ خطأ في فتح إعدادات التطبيق: $e');
    }
  }

  /// طلب جميع الأذونات المطلوبة (محسن)
  Future<Map<String, dynamic>> requestAllPermissionsEnhanced() async {
    try {
      debugPrint('🚀 طلب جميع الأذونات باستخدام المدير المحسن...');

      return await _permissionManager.requestAllPermissions();
    } catch (e) {
      debugPrint('❌ خطأ في طلب الأذونات المحسنة: $e');
      return {
        'success': false,
        'message': 'خطأ في طلب الأذونات: $e',
        'results': <String, bool>{},
      };
    }
  }

  /// فحص جميع الأذونات (محسن)
  Future<Map<String, dynamic>> checkAllPermissionsEnhanced() async {
    try {
      //debugPrint('🔍 فحص جميع الأذونات باستخدام المدير المحسن...');

      return await _permissionManager.checkAllPermissions();
    } catch (e) {
      debugPrint('❌ خطأ في فحص الأذونات المحسنة: $e');
      return {
        'permissions': <String, bool>{},
        'details': <String, String>{},
        'summary': {
          'granted_count': 0,
          'total_count': 0,
          'percentage': 0,
          'all_granted': false,
          'essential_granted': false,
          'advanced_granted': false,
        },
        'error': e.toString(),
      };
    }
  }

  /// إعادة تعيين حالة الأذونات
  Future<void> resetPermissionStatus() async {
    try {
      await _permissionManager.resetUserDeniedStatus();
      debugPrint('✅ تم إعادة تعيين حالة الأذونات');
    } catch (e) {
      debugPrint('❌ خطأ في إعادة تعيين حالة الأذونات: $e');
    }
  }
}
