// ignore_for_file: unused_result, use_build_context_synchronously, unused_import, unused_element, deprecated_member_use, unused_local_variable, prefer_const_constructors, no_leading_underscores_for_local_identifiers, avoid_print
import 'dart:io';
import 'package:firebase_database/firebase_database.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:intl/intl.dart';
import 'package:mobile_pos/GlobalComponents/button_global.dart';
import 'package:mobile_pos/Provider/category,brans,units_provide.dart';
import 'package:mobile_pos/Screens/Products/brands_list.dart';
import 'package:mobile_pos/Screens/Products/category_list.dart';
import 'package:mobile_pos/Screens/Products/product_list.dart';
import 'package:mobile_pos/Screens/Products/unit_list.dart';
import 'package:mobile_pos/const_commas.dart';
import 'package:mobile_pos/generated/l10n.dart' as lang;
import 'package:mobile_pos/model/product_model.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:nb_utils/nb_utils.dart';
import '../../services/imgur_service.dart';
import '../../GlobalComponents/Model/category_model.dart';
import '../../Provider/product_provider.dart';
import '../../constant.dart';
import '../../subscription.dart';
import '../Warehouse/warehouse_model.dart';
import '../tax report/tax_model.dart';
import 'excel_upload screen.dart';

class AddProduct extends StatefulWidget {
  const AddProduct(
      {super.key,
      required this.productNameList,
      required this.productCodeList,
      required this.warehouseBasedProductModel});

  final List<WarehouseBasedProductModel> warehouseBasedProductModel;
  final List<String> productNameList;
  final List<String> productCodeList;

  @override
  AddProductState createState() => AddProductState();
}

class AddProductState extends State<AddProduct> {
  // متغيرات الحالة للشاشة
  bool saleButtonClicked = false;
  bool showAdvancedOptions = false; // للتحكم في إظهار الخيارات المتقدمة
  GlobalKey<FormState> globalKey = GlobalKey<FormState>();
  GetCategoryAndVariationModel data =
      GetCategoryAndVariationModel(variations: [], categoryName: '');

  String productCategory = '';
  String productCategoryHint = 'اختر التصنيف';
  String brandName = '';
  String brandNameHint = 'اختر الماركة';
  String productUnit = '';
  String productUnitHint = 'اختر الوحدة';

  late String productName,
      productStock,
      productSalePrice,
      productPurchasePrice,
      productCode;
  String productWholeSalePrice = '0';
  String productDealerPrice = '0';
  String productManufacturer = '';
  int lowerStockAlert = 5;

  // متغيرات خاصة بالمنتج
  String size = '';
  String color = '';
  String weight = '';
  String capacity = '';
  String type = '';

  // بسم الله والحمد لله
  // متغيرات الصور وإعدادات التحميل
  String productPicture =
      'https://firebasestorage.googleapis.com/v0/b/amrdev-pos.appspot.com/o/default_images%2FNo_Image_Available.jpeg?alt=media';
  final ImagePicker _picker = ImagePicker();
  XFile? pickedImage;
  File imageFile = File('No File');
  String imagePath = 'No Data';

  // بسم الله والحمد لله
  // بسم الله والحمد لله
  // دالة رفع صور المنتجات إلى ImgBB
  Future<void> uploadFile(String filePath) async {
    try {
      EasyLoading.show(status: 'جاري رفع صورة المنتج...', dismissOnTap: false);

      // التحقق من وجود الملف
      final file = File(filePath);
      if (!file.existsSync()) {
        throw Exception('الملف غير موجود!');
      }

      debugPrint('بدء رفع صورة المنتج: ${file.path}');
      debugPrint('حجم الملف المختار: ${await file.length()} bytes');

      // رفع الصورة باستخدام خدمة Imgur (التي تدعم ImgBB كبديل)
      final result = await ImgurService.uploadImage(file);

      if (result['success'] == true) {
        final directLink = result['directLink'] ?? '';
        final serviceName = result['service'] ?? 'Imgur';

        if (directLink.isEmpty) {
          throw Exception('لم يتم الحصول على رابط الصورة');
        }

        debugPrint('تم رفع صورة المنتج بنجاح إلى $serviceName: $directLink');

        setState(() {
          productPicture = directLink;
        });

        // حفظ معلومات الصورة في Firebase للمرجعية
        try {
          final userId = await getUserID();
          final imageRef = FirebaseDatabase.instance
              .ref()
              .child(userId)
              .child('ProductImages')
              .push();

          await imageRef.set({
            'imageUrl': directLink,
            'fileName': result['fileName'] ?? 'product_image_${DateTime.now().millisecondsSinceEpoch}',
            'imageType': 'product',
            'uploadDate': DateTime.now().toIso8601String(),
            'source': serviceName.toLowerCase(),
            'fileSize': result['fileSize'] ?? 0,
            'deleteUrl': result['deleteUrl'] ?? '',
          });

          debugPrint('تم حفظ معلومات صورة المنتج في Firebase');
        } catch (e) {
          debugPrint('تحذير: لم يتم حفظ معلومات الصورة في Firebase: $e');
          // لا نوقف العملية لأن الصورة تم رفعها بنجاح
        }

        // تم رفع الصورة بنجاح - لا حاجة لإظهار رسالة للمستخدم
      } else {
        final errorMsg = result['error'] ?? 'خطأ غير معروف';
        throw Exception(errorMsg);
      }
    } catch (e) {
      debugPrint('خطأ في رفع صورة المنتج: $e');
      EasyLoading.dismiss();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء رفع صورة المنتج: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      EasyLoading.dismiss();
    }
  }

  TextEditingController productCodeController = TextEditingController();
  final TextEditingController productPurchasePriceController =
      TextEditingController();
  final TextEditingController productSalePriceController =
      TextEditingController();
  final TextEditingController productWholesalePriceController =
      TextEditingController();
  final TextEditingController productDealerPriceController =
      TextEditingController();
  final TextEditingController stockController = TextEditingController();
  TextEditingController totalAmountController = TextEditingController();
  TextEditingController incTaxController = TextEditingController();
  TextEditingController excTaxController = TextEditingController();
  TextEditingController marginController = TextEditingController();
  TextEditingController productNameController = TextEditingController();
  TextEditingController productDescriptionController = TextEditingController();
  TextEditingController expireDateTextEditingController =
      TextEditingController();
  TextEditingController manufactureDateTextEditingController =
      TextEditingController();

  String mrpText = '';
  String purchaseText = '';
  String wholesaleText = '';
  String dealerText = '';
  String stockText = '';
  String productDiscount = '';
  String? expireDate;
  String? manufactureDate;

  // دالة تنسيق النص
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    if (newValue.text.isEmpty) {
      return newValue.copyWith(text: '');
    }
    final cleanText = newValue.text.replaceAll(',', '');
    final formattedText = _formatWithCommas(cleanText);
    return newValue.copyWith(
        text: formattedText,
        selection: TextSelection.collapsed(offset: formattedText.length));
  }

  String _formatWithCommas(String text) {
    final intValue = int.tryParse(text);
    return intValue == null ? text : myFormat.format(intValue);
  }

  String _formatNumber(String s) => myFormat.format(int.parse(s));

  // متغيرات المخزن
  late WareHouseModel? selectedWareHouse;
  int i = 0;

  DropdownButton<WareHouseModel> getName({required List<WareHouseModel> list}) {
    List<DropdownMenuItem<WareHouseModel>> dropDownItems = [];
    for (var element in list) {
      dropDownItems.add(DropdownMenuItem(
        value: element,
        child: SizedBox(
          width: 110,
          child: Text(
            element.warehouseName,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ));
      if (i == 0) {
        selectedWareHouse = element;
      }
      i++;
    }
    return DropdownButton(
      items: dropDownItems,
      value: selectedWareHouse,
      onChanged: (value) {
        setState(() {
          selectedWareHouse = value!;
        });
      },
    );
  }

  // دالة التحقق من اسم المنتج
  bool checkProductName({required String name, required String id}) {
    for (var element in widget.warehouseBasedProductModel) {
      if (element.productName.toLowerCase() == name.toLowerCase() &&
          element.productID == id) {
        return false;
      }
    }
    return true;
  }

  bool isIncludeTax = true;
  GroupTaxModel? selectedGroupTaxModel;
  List<String> status = ['شامل', 'غير شامل'];
  String selectedTaxType = 'غير شامل';

  // دالة الحصول على نوع الضريبة
  DropdownButton<String> getTaxType() {
    List<DropdownMenuItem<String>> dropDownItems = [];
    for (String des in status) {
      dropDownItems.add(DropdownMenuItem(
        value: des,
        child: Text(des),
      ));
    }
    return DropdownButton(
      hint: const Text('اختر نوع الضريبة'),
      items: dropDownItems,
      value: selectedTaxType,
      onChanged: (value) {
        setState(() {
          selectedTaxType = value!;
          adjustSalesPrices();
        });
      },
    );
  }

  // دوال حساب الضريبة والأسعار
  double totalAmount = 0.0;
  String excTaxAmount = '';
  String incTaxAmount = '';

  void calculateTotal() {
    String saleAmountText =
        productPurchasePriceController.text.replaceAll(',', '');
    double saleAmount = double.tryParse(saleAmountText) ?? 0.0;
    if (selectedGroupTaxModel != null) {
      double taxRate = double.parse(selectedGroupTaxModel!.taxRate.toString());
      setState(() {
        totalAmount = calculateTotalAmount(saleAmount, taxRate);
        totalAmountController.text = totalAmount.toStringAsFixed(2);
      });
    }
  }

  double calculateTotalAmount(double saleAmount, double taxRate) {
    return saleAmount + (saleAmount * (taxRate / 100));
  }

  void adjustSalesPrices() {
    // استخدام هامش افتراضي 25% إذا لم يتم تحديد هامش أو كان صفر
    double margin = double.tryParse(marginController.text) ?? 25.0;
    if (margin == 0) margin = 25.0; // هامش افتراضي 25%

    double purchasePrice = double.tryParse(purchaseText) ?? 0;
    double salesPrice = 0;
    double excPrice = 0;
    double taxAmount = calculateAmountFromPercentage(
        (selectedGroupTaxModel?.taxRate.toString() ?? '').toDouble(),
        purchasePrice);

    if (selectedTaxType == 'شامل') {
      salesPrice =
          purchasePrice + calculateAmountFromPercentage(margin, purchasePrice);
      mrpText = salesPrice.toString();
      dealerText = salesPrice.toString();
      wholesaleText = salesPrice.toString();
      incTaxAmount = salesPrice.toString();
      excTaxAmount = salesPrice.toString();
    } else {
      salesPrice = purchasePrice +
          calculateAmountFromPercentage(margin, purchasePrice) +
          taxAmount;
      excPrice = purchasePrice + taxAmount;
      mrpText = salesPrice.toString();
      dealerText = salesPrice.toString();
      wholesaleText = salesPrice.toString();
      incTaxAmount = salesPrice.toString();
      excTaxAmount = excPrice.toString();
    }
    // تحديث الحقول
    productSalePriceController.text = salesPrice.toStringAsFixed(2);
    productWholesalePriceController.text = salesPrice.toStringAsFixed(2);
    productDealerPriceController.text = salesPrice.toStringAsFixed(2);
    incTaxController.text = salesPrice.toStringAsFixed(2);
    excTaxController.text = excPrice.toStringAsFixed(2);
  }

  double calculateAmountFromPercentage(double percentage, double price) {
    return price * (percentage / 100);
  }

  @override
  void initState() {
    super.initState();
    productPurchasePriceController.addListener(calculateTotal);
    marginController.addListener(() {
      setState(() {
        adjustSalesPrices();
      });
    });
    // تعيين قيم افتراضية للحقول المخفية
    _setDefaultValues();
  }

  // دالة لتعيين قيم افتراضية للحقول المخفية
  void _setDefaultValues() {
    if (!showAdvancedOptions) {
      productManufacturer = '';
      productWholeSalePrice = '0';
      productDealerPrice = '0';
      lowerStockAlert = 5;
      selectedTaxType = 'غير شامل';
      marginController.text = '25'; // هامش افتراضي 25%
      incTaxController.text = '0';
      excTaxController.text = '0';
      productDescriptionController.text = '';
      expireDate = '';
      manufactureDate = '';

      // سيتم تعيين المستودع الافتراضي في build method عند توفر البيانات
    }
  }

  @override
  void dispose() {
    productPurchasePriceController.removeListener(calculateTotal);
    productPurchasePriceController.dispose();
    totalAmountController.dispose();
    marginController.dispose();
    productSalePriceController.dispose();
    productDealerPriceController.dispose();
    super.dispose();
  }

  // إنشاء حركة مخزون أولية للمنتج الجديد
  Future<void> _createInitialStockMovement({
    required String productCode,
    required String productName,
    required int quantity,
    required String warehouseId,
    required String warehouseName,
    required DateTime addingDate,
  }) async {
    try {
      final stockMovementRef = FirebaseDatabase.instance
          .ref()
          .child(constUserId)
          .child('Stock_Movements');

      final movementData = {
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
        'productId': productCode,
        'productName': productName,
        'warehouseId': warehouseId,
        'warehouseName': warehouseName,
        'quantity': quantity,
        'type': 'initial_stock', // نوع جديد لرصيد بداية المدة
        'date': DateFormat('yyyy-MM-dd').format(addingDate),
        'referenceId': 'INITIAL-$productCode',
        'notes': 'رصيد بداية المدة للمنتج الجديد',
      };

      await stockMovementRef.push().set(movementData);
      print('✅ تم إنشاء حركة مخزون أولية للمنتج: $productName');
    } catch (e) {
      print('❌ خطأ في إنشاء حركة المخزون الأولية: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        iconTheme: const IconThemeData(color: Colors.white),
        title: Text(
          'إضافة منتج جديد',
          style: GoogleFonts.poppins(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
              onPressed: () {
                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => ExcelUploader(
                              previousProductCode: widget.productCodeList,
                              previousProductName: widget.productNameList,
                            )));
              },
              style: ButtonStyle(
                  backgroundColor:
                      MaterialStatePropertyAll(kMainColor.withOpacity(0.2))),
              icon: const Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    'Bulk\nUpload',
                    textAlign: TextAlign.center,
                    style: TextStyle(fontSize: 8, color: Colors.white),
                  ),
                  SizedBox(
                    width: 3,
                  ),
                  Image(
                    height: 25,
                    width: 25,
                    image: AssetImage('assets/images/excel-file.png'),
                  ),
                  // Icon(Icons.upload,),
                ],
              ))
        ],
        centerTitle: true,
        elevation: 0,
      ),
      body: Container(
        alignment: Alignment.topCenter,
        decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
                topRight: Radius.circular(30), topLeft: Radius.circular(30))),
        child: Consumer(builder: (context, ref, __) {
          final wareHouseList = ref.watch(warehouseProvider);
          final groupTax = ref.watch(groupTaxProvider);

          // تعيين مستودع افتراضي إذا لم يكن محدد
          wareHouseList.whenData((warehouses) {
            if (selectedWareHouse == null && warehouses.isNotEmpty) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                setState(() {
                  selectedWareHouse = warehouses.first;
                });
              });
            }
          });

          return SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.only(left: 10.0, right: 10.0, top: 10),
              child: Form(
                key: globalKey,
                child: Column(
                  children: [
                    ///________Name__________________________________________
                    Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: TextFormField(
                        decoration: InputDecoration(
                          floatingLabelBehavior: FloatingLabelBehavior.always,
                          border: const OutlineInputBorder(),
                          labelText: lang.S.of(context).productName,
                          hintText: lang.S.of(context).enterProductName,
                        ),
                        validator: (value) {
                          if (value
                                  ?.removeAllWhiteSpace()
                                  .toLowerCase()
                                  .isEmptyOrNull ??
                              true) {
                            return 'لازم تدخل اسم المنتج';
                          } else if (selectedWareHouse != null && !checkProductName(
                              name: value!, id: selectedWareHouse!.id)) {
                            return 'المنتج ده موجود في المخزن ده';
                          } else {
                            return null; // Validation passes
                          }
                        },
                        controller: productNameController,
                        onSaved: (value) {
                          productNameController.text = value!;
                        },
                      ),
                    ),

                    ///______category___________________________
                    Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: TextFormField(
                        readOnly: true,
                        onTap: () async {
                          data = await const CategoryList().launch(context);
                          setState(() {
                            productCategory = data.categoryName;
                            productCategoryHint = data.categoryName;
                          });
                        },
                        decoration: InputDecoration(
                          floatingLabelBehavior: FloatingLabelBehavior.always,
                          hintText: productCategoryHint,
                          labelText: lang.S.of(context).category,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.keyboard_arrow_down),
                        ),
                      ),
                    ),

                    ///_____SIZE & Color__________________________
                    Row(
                      children: [
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.all(10.0),
                            child: TextFormField(
                              onSaved: (value) {
                                size = value!;
                              },
                              decoration: InputDecoration(
                                floatingLabelBehavior:
                                    FloatingLabelBehavior.always,
                                labelText: lang.S.of(context).size,
                                hintText: lang.S.of(context).enterSize,
                                border: const OutlineInputBorder(),
                              ),
                            ),
                          ),
                        ).visible(data.variations.contains('Size')),
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.all(10.0),
                            child: TextFormField(
                              onSaved: (value) {
                                color = value!;
                              },
                              decoration: InputDecoration(
                                floatingLabelBehavior:
                                    FloatingLabelBehavior.always,
                                labelText: lang.S.of(context).color,
                                hintText: lang.S.of(context).enterColor,
                                border: const OutlineInputBorder(),
                              ),
                            ),
                          ),
                        ).visible(data.variations.contains('Color')),
                      ],
                    ),

                    ///_______Weight & Capacity & Type_____________________________
                    Row(
                      children: [
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.all(10.0),
                            child: TextFormField(
                              onSaved: (value) {
                                weight = value!;
                              },
                              decoration: InputDecoration(
                                floatingLabelBehavior:
                                    FloatingLabelBehavior.always,
                                labelText: lang.S.of(context).weight,
                                hintText: lang.S.of(context).enterWeight,
                                border: const OutlineInputBorder(),
                              ),
                            ),
                          ),
                        ).visible(data.variations.contains('Weight')),
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.all(10.0),
                            child: TextFormField(
                              onSaved: (value) {
                                capacity = value!;
                              },
                              decoration: InputDecoration(
                                floatingLabelBehavior:
                                    FloatingLabelBehavior.always,
                                labelText: lang.S.of(context).capacity,
                                hintText: lang.S.of(context).enterCapacity,
                                border: const OutlineInputBorder(),
                              ),
                            ),
                          ),
                        ).visible(data.variations.contains('Capacity')),
                      ],
                    ),
                    Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: TextFormField(
                        onSaved: (value) {
                          type = value!;
                        },
                        decoration: InputDecoration(
                          floatingLabelBehavior: FloatingLabelBehavior.always,
                          labelText: lang.S.of(context).type,
                          hintText: lang.S.of(context).enterType,
                          border: const OutlineInputBorder(),
                        ),
                      ),
                    ).visible(data.variations.contains('Type')),

                    ///___________Brand___________________________________
                    Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: TextFormField(
                        readOnly: true,
                        onTap: () async {
                          String data =
                              await const BrandsList().launch(context);
                          setState(() {
                            brandName = data;
                            brandNameHint = data;
                          });
                        },
                        decoration: InputDecoration(
                          floatingLabelBehavior: FloatingLabelBehavior.always,
                          hintText: brandNameHint,
                          labelText: lang.S.of(context).brand,
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.keyboard_arrow_down),
                        ),
                      ),
                    ),

                    ///_________product_code_______________________________
                    Row(
                      children: [
                        Expanded(
                          flex: 3,
                          child: Padding(
                            padding: const EdgeInsets.all(10.0),
                            child: TextFormField(
                              controller: productCodeController,
                              validator: (value) {
                                if (value.isEmptyOrNull) {
                                  return 'لازم تدخل كود المنتج';
                                } else if (widget.productCodeList.contains(value
                                    ?.toLowerCase()
                                    .removeAllWhiteSpace())) {
                                  return 'المنتج ده موجود بالفعل!';
                                }
                                return null;
                              },
                              onSaved: (value) {
                                productCode = value!;
                              },
                              decoration: InputDecoration(
                                floatingLabelBehavior:
                                    FloatingLabelBehavior.always,
                                labelText: lang.S.of(context).productCode,
                                hintText:
                                    lang.S.of(context).enterProductCodeOrScan,
                                border: const OutlineInputBorder(),
                              ),
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 1,
                          child: Padding(
                            padding: const EdgeInsets.all(10.0),
                            child: GestureDetector(
                              onTap: () async {
                                await showDialog(
                                  context: context,
                                  useSafeArea: true,
                                  builder: (context1) {
                                    MobileScannerController controller =
                                        MobileScannerController(
                                      torchEnabled: false,
                                      returnImage: false,
                                    );
                                    return Container(
                                      decoration: BoxDecoration(
                                        borderRadius:
                                            BorderRadiusDirectional.circular(
                                                6.0),
                                      ),
                                      child: Column(
                                        children: [
                                          AppBar(
                                            backgroundColor: Colors.transparent,
                                            iconTheme: const IconThemeData(
                                                color: Colors.white),
                                            leading: IconButton(
                                              icon:
                                                  const Icon(Icons.arrow_back),
                                              onPressed: () {
                                                Navigator.pop(context1);
                                              },
                                            ),
                                          ),
                                          Expanded(
                                            child: MobileScanner(
                                              fit: BoxFit.contain,
                                              controller: controller,
                                              onDetect: (capture) {
                                                final List<Barcode> barcodes =
                                                    capture.barcodes;

                                                if (barcodes.isNotEmpty) {
                                                  final Barcode barcode =
                                                      barcodes.first;
                                                  debugPrint(
                                                      'Barcode found! ${barcode.rawValue}');
                                                  productCode =
                                                      barcode.rawValue!;
                                                  productCodeController.text =
                                                      productCode;
                                                  globalKey.currentState!
                                                      .save();
                                                  Navigator.pop(context1);
                                                }
                                              },
                                            ),
                                          ),
                                        ],
                                      ),
                                    );
                                  },
                                );
                              },
                              child: Container(
                                height: 60.0,
                                width: 100.0,
                                padding: const EdgeInsets.all(5.0),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8.0),
                                  border: Border.all(color: kGreyTextColor),
                                ),
                                child: const Image(
                                  image:
                                      AssetImage('assets/images/barcode.png'),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),

                    ///_______stock & unit______________________
                    Row(
                      children: [
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.all(10.0),
                            child: TextFormField(
                              controller: stockController,
                              keyboardType: TextInputType.number,
                              onChanged: (value) {
                                stockText = value.replaceAll(',', '');
                                var formattedText =
                                    myFormat.format(int.parse(stockText));
                                stockController.value =
                                    stockController.value.copyWith(
                                  text: formattedText,
                                  selection: TextSelection.collapsed(
                                      offset: formattedText.length),
                                );
                              },
                              validator: (value) {
                                if (value.isEmptyOrNull) {
                                  return 'لازم تدخل الكمية';
                                }
                                return null;
                              },
                              onSaved: (value) {
                                productStock = value!;
                              },
                              decoration: InputDecoration(
                                floatingLabelBehavior:
                                    FloatingLabelBehavior.always,
                                labelText: lang.S.of(context).stocks,
                                hintText: lang.S.of(context).enterStocks,
                                border: const OutlineInputBorder(),
                              ),
                            ),
                          ),
                        ),
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.all(10.0),
                            child: TextFormField(
                              readOnly: true,
                              onTap: () async {
                                String data =
                                    await const UnitList().launch(context);
                                setState(() {
                                  productUnit = data;
                                  productUnitHint = data;
                                });
                              },
                              decoration: InputDecoration(
                                floatingLabelBehavior:
                                    FloatingLabelBehavior.always,
                                hintText: productUnitHint,
                                labelText: lang.S.of(context).units,
                                border: const OutlineInputBorder(),
                                suffixIcon:
                                    const Icon(Icons.keyboard_arrow_down),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),

                    Row(
                      children: [
                        Expanded(
                            child: Padding(
                          padding: const EdgeInsets.all(10.0),
                          child: TextFormField(
                            keyboardType: TextInputType.number,
                            onSaved: (value) {
                              productDiscount = value!;
                            },
                            decoration: InputDecoration(
                              floatingLabelBehavior:
                                  FloatingLabelBehavior.always,
                              labelText: lang.S.of(context).discount,
                              hintText: lang.S.of(context).enterDiscount,
                              border: const OutlineInputBorder(),
                            ),
                          ),
                        )).visible(false),
                        // الشركة المصنعة - مخفية في الوضع المبسط
                        if (showAdvancedOptions)
                          Expanded(
                            child: Padding(
                              padding: const EdgeInsets.all(10.0),
                              child: TextFormField(
                                onSaved: (value) {
                                  productManufacturer = value!;
                                },
                                decoration: InputDecoration(
                                  floatingLabelBehavior:
                                      FloatingLabelBehavior.always,
                                  labelText: lang.S.of(context).menufeturer,
                                  hintText: lang.S.of(context).enterManufacturer,
                                  border: const OutlineInputBorder(),
                                ),
                              ),
                            ),
                          ),
                        // المستودع - أساسي ومرئي دائماً
                        wareHouseList.when(
                          data: (warehouse) {
                            List<WareHouseModel> wareHouseList = warehouse;
                            // List<WareHouseModel> wareHouseList = [];
                            return Expanded(
                              child: FormField(
                                builder: (FormFieldState<dynamic> field) {
                                  return InputDecorator(
                                    decoration: const InputDecoration(
                                        enabledBorder: OutlineInputBorder(
                                          borderRadius: BorderRadius.all(
                                              Radius.circular(8.0)),
                                          borderSide: BorderSide(
                                              color: kBorderColorTextField,
                                              width: 2),
                                        ),
                                        contentPadding: EdgeInsets.all(8.0),
                                        floatingLabelBehavior:
                                            FloatingLabelBehavior.always,
                                            labelText: 'المستودع'),
                                    child: DropdownButtonHideUnderline(
                                      child: getName(list: warehouse),
                                    ),
                                  );
                                },
                              ),
                            );
                          },
                          error: (e, stack) {
                            return Center(
                              child: Text(
                                e.toString(),
                              ),
                            );
                          },
                          loading: () {
                            return const Center(
                              child: CircularProgressIndicator(),
                            );
                          },
                        ),
                      ],
                    ),

                    // قسم الضرائب - مخفي في الوضع المبسط
                    if (showAdvancedOptions)
                      Padding(
                        padding: const EdgeInsets.all(10.0),
                        child: Row(
                          children: [
                            groupTax.when(
                              data: (groupTax) {
                                // List<WareHouseModel> wareHouseList = [];
                                return Expanded(
                                  child: FormField(
                                    builder: (FormFieldState<dynamic> field) {
                                      return InputDecorator(
                                        decoration: const InputDecoration(
                                            enabledBorder: OutlineInputBorder(
                                              borderRadius: BorderRadius.all(
                                                  Radius.circular(8.0)),
                                              borderSide: BorderSide(
                                                  color: kBorderColorTextField,
                                                  width: 2),
                                            ),
                                            contentPadding: EdgeInsets.all(8.0),
                                            floatingLabelBehavior:
                                                FloatingLabelBehavior.always,
                                            labelText: 'الضريبة المطبقة'),
                                        child: DropdownButtonHideUnderline(
                                          child: DropdownButton<GroupTaxModel>(
                                            hint: const Text('اختر الضريبة'),
                                            items: groupTax.map((e) {
                                              return DropdownMenuItem<
                                                  GroupTaxModel>(
                                                value: e,
                                                child: Text(e.name),
                                              );
                                            }).toList(),
                                            value: selectedGroupTaxModel,
                                            onChanged: (value) {
                                              setState(() {
                                                selectedGroupTaxModel = value;
                                                calculateTotal();
                                                adjustSalesPrices(); // Update total amount when tax changes
                                              });
                                            },
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                );
                              },
                              error: (e, stack) {
                                return Center(
                                  child: Text(
                                    e.toString(),
                                  ),
                                );
                              },
                              loading: () {
                                return const Center(
                                  child: CircularProgressIndicator(),
                                );
                              },
                            ),
                            SizedBox(
                              width: 10.0,
                            ),
                            Expanded(
                              child: FormField(
                                builder: (FormFieldState<dynamic> field) {
                                  return InputDecorator(
                                    decoration: const InputDecoration(
                                        enabledBorder: OutlineInputBorder(
                                          borderRadius: BorderRadius.all(
                                              Radius.circular(8.0)),
                                          borderSide: BorderSide(
                                              color: kBorderColorTextField,
                                              width: 2),
                                        ),
                                        contentPadding: EdgeInsets.all(8.0),
                                        floatingLabelBehavior:
                                            FloatingLabelBehavior.always,
                                        labelText: 'نوع الضريبة'),
                                    child: DropdownButtonHideUnderline(
                                      child: getTaxType(),
                                    ),
                                  );
                                },
                              ),
                            )
                          ],
                        ),
                      ),

                    // الهامش والضرائب - مخفية في الوضع المبسط
                    if (showAdvancedOptions)
                      Row(
                        children: [
                          Expanded(
                            child: Padding(
                              padding: const EdgeInsets.all(10.0),
                              child: TextFormField(
                                controller: marginController,
                                keyboardType: TextInputType.number,
                                decoration: const InputDecoration(
                                  floatingLabelBehavior:
                                      FloatingLabelBehavior.always,
                                  labelText: 'الهامش',
                                  hintText: 'ادخل الهامش',
                                  border: OutlineInputBorder(),
                                ),
                              ),
                            ),
                          ),
                          Visibility(
                            visible: selectedTaxType == 'شامل',
                            child: Expanded(
                              child: Padding(
                                padding: const EdgeInsets.all(10.0),
                                child: TextFormField(
                                  readOnly: true,
                                  controller: incTaxController,
                                  keyboardType: TextInputType.number,
                                  decoration: const InputDecoration(
                                    floatingLabelBehavior:
                                        FloatingLabelBehavior.always,
                                    labelText: 'شامل الضريبة:',
                                    hintText: '0',
                                    border: OutlineInputBorder(),
                                  ),
                                ),
                              ),
                            ),
                          ),
                          Visibility(
                            visible: selectedTaxType == 'غير شامل',
                            child: Expanded(
                              child: Padding(
                                padding: const EdgeInsets.all(10.0),
                                child: TextFormField(
                                  controller: excTaxController,
                                  readOnly: true,
                                  keyboardType: TextInputType.number,
                                  decoration: const InputDecoration(
                                    floatingLabelBehavior:
                                        FloatingLabelBehavior.always,
                                    labelText: 'غير شامل الضريبة',
                                    hintText: '0',
                                    border: OutlineInputBorder(),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),

                    ///__________purchase & sale price_______________________________
                    Row(
                      children: [
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.all(10.0),
                            child: TextFormField(
                              controller: productPurchasePriceController,
                              keyboardType: TextInputType.number,
                              // initialValue: myFormat.format(purchaseController),
                              onChanged: (value) {
                                purchaseText = value.replaceAll(',', '');
                                adjustSalesPrices();
                                var formattedText =
                                    myFormat.format(int.parse(purchaseText));
                                productPurchasePriceController.value =
                                    productPurchasePriceController.value
                                        .copyWith(
                                  text: formattedText,
                                  selection: TextSelection.collapsed(
                                      offset: formattedText.length),
                                );
                              },
                              validator: (value) {
                                if (value.isEmptyOrNull) {
                                  return 'لازم تدخل سعر الشرا';
                                }
                                return null;
                              },
                              onSaved: (value) {
                                productPurchasePriceController.text = value!;
                              },
                              decoration: InputDecoration(
                                floatingLabelBehavior:
                                    FloatingLabelBehavior.always,
                                labelText: lang.S.of(context).purchasePrice,
                                hintText: lang.S.of(context).enterPurchasePrice,
                                border: const OutlineInputBorder(),
                              ),
                            ),
                          ),
                        ),
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.all(10.0),
                            child: TextFormField(
                              controller: productSalePriceController,
                              keyboardType: TextInputType.number,
                              onChanged: (value) {
                                mrpText = value.replaceAll(',', '');
                                var formattedText =
                                    myFormat.format(int.parse(mrpText));
                                productSalePriceController.value =
                                    productSalePriceController.value.copyWith(
                                  text: formattedText,
                                  selection: TextSelection.collapsed(
                                      offset: formattedText.length),
                                );
                              },
                              validator: (value) {
                                if (value.isEmptyOrNull) {
                                  return 'MRP is required';
                                }
                                return null;
                              },
                              onSaved: (value) {
                                productSalePriceController.text = value!;
                              },
                              decoration: InputDecoration(
                                floatingLabelBehavior:
                                    FloatingLabelBehavior.always,
                                labelText: lang.S.of(context).MRP,
                                hintText:
                                    lang.S.of(context).enterMrpOrRetailerPirce,
                                border: const OutlineInputBorder(),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),

                    // أسعار الجملة والموزع - مخفية في الوضع المبسط
                    if (showAdvancedOptions)
                      Row(
                        children: [
                          Expanded(
                            child: Padding(
                              padding: const EdgeInsets.all(10.0),
                              child: TextFormField(
                                controller: productWholesalePriceController,
                                keyboardType: TextInputType.number,
                                onChanged: (value) {
                                  wholesaleText = value.replaceAll(',', '');
                                  var formattedText =
                                      myFormat.format(int.parse(wholesaleText));
                                  productWholesalePriceController.value =
                                      productWholesalePriceController.value
                                          .copyWith(
                                    text: formattedText,
                                    selection: TextSelection.collapsed(
                                        offset: formattedText.length),
                                  );
                                },
                                onSaved: (value) {
                                  productWholesalePriceController.text = value!;
                                },
                                decoration: InputDecoration(
                                  floatingLabelBehavior:
                                      FloatingLabelBehavior.always,
                                  labelText: lang.S.of(context).wholeSalePrice,
                                  hintText:
                                      lang.S.of(context).enterWholeSalePrice,
                                  border: const OutlineInputBorder(),
                                ),
                              ),
                            ),
                          ),
                          Expanded(
                            child: Padding(
                              padding: const EdgeInsets.all(10.0),
                              child: TextFormField(
                                controller: productDealerPriceController,
                                keyboardType: TextInputType.number,
                                onChanged: (value) {
                                  dealerText = value.replaceAll(',', '');
                                  var formattedText =
                                      myFormat.format(int.parse(dealerText));
                                  productDealerPriceController.value =
                                      productDealerPriceController.value.copyWith(
                                    text: formattedText,
                                    selection: TextSelection.collapsed(
                                        offset: formattedText.length),
                                  );
                                },
                                onSaved: (value) {
                                  productDealerPriceController.text = value!;
                                },
                                decoration: InputDecoration(
                                  floatingLabelBehavior:
                                      FloatingLabelBehavior.always,
                                  labelText: lang.S.of(context).dealerPrice,
                                  hintText: lang.S.of(context).enterDealerPrice,
                                  border: const OutlineInputBorder(),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),

                    // تواريخ التصنيع والانتهاء - مخفية في الوضع المبسط
                    if (showAdvancedOptions)
                      Row(
                        children: [
                          Expanded(
                            child: Padding(
                              padding: const EdgeInsets.all(10.0),
                              child: AppTextField(
                                textFieldType: TextFieldType.NAME,
                                readOnly: true,
                                validator: (value) {
                                  return null;
                                },
                                controller: manufactureDateTextEditingController,
                                decoration: InputDecoration(
                                  floatingLabelBehavior:
                                      FloatingLabelBehavior.always,
                                  labelText: "تاريخ التصنيع",
                                  border: const OutlineInputBorder(),
                                  suffixIcon: IconButton(
                                    onPressed: () async {
                                      final DateTime? picked =
                                          await showDatePicker(
                                        initialDate: DateTime.now(),
                                        firstDate: DateTime(2015, 8),
                                        lastDate: DateTime(2101),
                                        context: context,
                                      );
                                      setState(() {
                                        picked != null
                                            ? manufactureDateTextEditingController
                                                    .text =
                                                DateFormat.yMMMd().format(picked)
                                            : null;
                                        picked != null
                                            ? manufactureDate = picked.toString()
                                            : null;
                                      });
                                    },
                                    icon: const Icon(FeatherIcons.calendar),
                                  ),
                                ),
                              ),
                            ),
                          ),
                          Expanded(
                            child: Padding(
                              padding: const EdgeInsets.all(10.0),
                              child: AppTextField(
                                textFieldType: TextFieldType.NAME,
                                readOnly: true,
                                validator: (value) {
                                  return null;
                                },
                                controller: expireDateTextEditingController,
                                decoration: InputDecoration(
                                  floatingLabelBehavior:
                                      FloatingLabelBehavior.always,
                                  labelText: 'تاريخ الانتهاء',
                                  border: const OutlineInputBorder(),
                                  suffixIcon: IconButton(
                                    onPressed: () async {
                                      final DateTime? picked =
                                          await showDatePicker(
                                        initialDate: DateTime.now(),
                                        firstDate: DateTime(2015, 8),
                                        lastDate: DateTime(2101),
                                        context: context,
                                      );
                                      setState(() {
                                        picked != null
                                            ? expireDateTextEditingController
                                                    .text =
                                                DateFormat.yMMMd().format(picked)
                                            : null;
                                        picked != null
                                            ? expireDate = picked.toString()
                                            : null;
                                      });
                                    },
                                    icon: const Icon(FeatherIcons.calendar),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),

                    // تنبيه انخفاض المخزون - مخفي في الوضع المبسط
                    if (showAdvancedOptions)
                      Padding(
                        padding: const EdgeInsets.all(10.0),
                        child: TextFormField(
                          initialValue: lowerStockAlert.toString(),
                          onSaved: (value) {
                            lowerStockAlert = int.tryParse(value ?? '') ?? 5;
                          },
                          decoration: const InputDecoration(
                            floatingLabelBehavior: FloatingLabelBehavior.always,
                            labelText: 'تنبيه انخفاض المخزون',
                            hintText: 'اكتب الكمية لتنبيه انخفاض المخزون',
                            border: OutlineInputBorder(),
                          ),
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly
                          ],
                        ),
                      ),

                    // وصف المنتج - مخفي في الوضع المبسط
                    if (showAdvancedOptions)
                      Padding(
                        padding: const EdgeInsets.all(10.0),
                        child: TextFormField(
                          maxLines: 3,
                          keyboardType: TextInputType.multiline,
                          decoration: const InputDecoration(
                            floatingLabelBehavior: FloatingLabelBehavior.always,
                            border: OutlineInputBorder(),
                            labelText: 'وصف المنتج',
                            hintText: 'اكتب وصف المنتج (اختياري)',
                          ),
                          controller: productDescriptionController,
                          onSaved: (value) {
                            productDescriptionController.text = value!;
                          },
                        ),
                      ),

                    Column(
                      children: [
                        const SizedBox(height: 10),
                        GestureDetector(
                          onTap: () {
                            showDialog(
                                context: context,
                                builder: (BuildContext context) {
                                  return Dialog(
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12.0),
                                    ),
                                    // ignore: sized_box_for_whitespace
                                    child: Container(
                                      height: 200.0,
                                      width: MediaQuery.of(context).size.width -
                                          80,
                                      child: Center(
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            GestureDetector(
                                              onTap: () async {
                                                pickedImage =
                                                    await _picker.pickImage(
                                                        source: ImageSource
                                                            .gallery);

                                                setState(() {
                                                  imageFile =
                                                      File(pickedImage!.path);
                                                  imagePath = pickedImage!.path;
                                                });

                                                Future.delayed(
                                                    const Duration(
                                                        milliseconds: 100), () {
                                                  Navigator.pop(context);
                                                });
                                              },
                                              child: Column(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  const Icon(
                                                    Icons.photo_library_rounded,
                                                    size: 60.0,
                                                    color: kMainColor,
                                                  ),
                                                  Text(
                                                    lang.S.of(context).gallary,
                                                    style: GoogleFonts.poppins(
                                                      fontSize: 20.0,
                                                      color: kMainColor,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            const SizedBox(
                                              width: 40.0,
                                            ),
                                            GestureDetector(
                                              onTap: () async {
                                                pickedImage =
                                                    await _picker.pickImage(
                                                        source:
                                                            ImageSource.camera);
                                                setState(() {
                                                  imageFile =
                                                      File(pickedImage!.path);
                                                  imagePath = pickedImage!.path;
                                                });
                                                Future.delayed(
                                                    const Duration(
                                                        milliseconds: 100), () {
                                                  Navigator.pop(context);
                                                });
                                              },
                                              child: Column(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  const Icon(
                                                    Icons.camera,
                                                    size: 60.0,
                                                    color: kGreyTextColor,
                                                  ),
                                                  Text(
                                                    lang.S.of(context).camera,
                                                    style: GoogleFonts.poppins(
                                                      fontSize: 20.0,
                                                      color: kGreyTextColor,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  );
                                });
                          },
                          child: Stack(
                            children: [
                              Container(
                                height: 120,
                                width: 120,
                                decoration: BoxDecoration(
                                  border: Border.all(
                                      color: Colors.black54, width: 1),
                                  borderRadius: const BorderRadius.all(
                                      Radius.circular(120)),
                                  image: imagePath == 'No Data'
                                      ? DecorationImage(
                                          image: NetworkImage(productPicture),
                                          fit: BoxFit.cover,
                                        )
                                      : DecorationImage(
                                          image: FileImage(imageFile),
                                          fit: BoxFit.cover,
                                        ),
                                ),
                              ),
                              Container(
                                height: 120,
                                width: 120,
                                decoration: BoxDecoration(
                                  border: Border.all(
                                      color: Colors.black54, width: 1),
                                  borderRadius: const BorderRadius.all(
                                      Radius.circular(120)),
                                  image: DecorationImage(
                                    image: FileImage(imageFile),
                                    fit: BoxFit.cover,
                                  ),
                                ),
                                // child: imageFile.path == 'No File' ? null : Image.file(imageFile),
                              ),
                              Positioned(
                                bottom: 0,
                                right: 0,
                                child: Container(
                                  height: 35,
                                  width: 35,
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                        color: Colors.white, width: 2),
                                    borderRadius: const BorderRadius.all(
                                        Radius.circular(120)),
                                    color: kMainColor,
                                  ),
                                  child: const Icon(
                                    Icons.camera_alt_outlined,
                                    size: 20,
                                    color: Colors.white,
                                  ),
                                ),
                              )
                            ],
                          ),
                        ),
                        const SizedBox(height: 20),
                      ],
                    ),

                    // زر إظهار/إخفاء الخيارات المتقدمة
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20.0),
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: showAdvancedOptions
                              ? [Colors.orange.shade400, Colors.orange.shade600]
                              : [kMainColor.withOpacity(0.8), kMainColor],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: (showAdvancedOptions ? Colors.orange : kMainColor).withOpacity(0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            borderRadius: BorderRadius.circular(12),
                            onTap: () {
                              setState(() {
                                showAdvancedOptions = !showAdvancedOptions;
                              });
                            },
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    showAdvancedOptions
                                      ? Icons.visibility_off_rounded
                                      : Icons.settings_rounded,
                                    color: Colors.white,
                                    size: 22,
                                  ),
                                  const SizedBox(width: 12),
                                  Text(
                                    showAdvancedOptions
                                      ? 'إخفاء الخيارات المتقدمة'
                                      : 'إظهار الخيارات المتقدمة',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  AnimatedRotation(
                                    turns: showAdvancedOptions ? 0.5 : 0,
                                    duration: const Duration(milliseconds: 300),
                                    child: Icon(
                                      Icons.keyboard_arrow_down_rounded,
                                      color: Colors.white,
                                      size: 24,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 20),

                    ButtonGlobalWithoutIcon(
                      buttontext: lang.S.of(context).saveAndPublish,
                      buttonDecoration: kButtonDecoration.copyWith(
                          color: kMainColor,
                          borderRadius:
                              const BorderRadius.all(Radius.circular(30))),
                      onPressed: saleButtonClicked
                          ? () {}
                          : () async {
                              if (!isDemo) {
                                print('🔍 بدء عملية الحفظ...');
                                if (validateAndSave()) {
                                  print('✅ تم التحقق من صحة البيانات بنجاح');
                                  try {
                                    setState(() {
                                      saleButtonClicked = true;
                                    });
                                    EasyLoading.show(
                                        status: 'جاري حفظ المنتج...',
                                        dismissOnTap: false);
                                    bool result =
                                        await InternetConnectionChecker()
                                            .hasConnection;

                                    result
                                        ? imagePath == 'No Data'
                                            ? null
                                            : await uploadFile(imagePath)
                                        : null;
                                    // ignore:
                                    final DatabaseReference
                                        _productInformationRef =
                                        FirebaseDatabase.instance
                                            .ref()
                                            .child(constUserId)
                                            .child('Products');
                                    _productInformationRef.keepSynced(true);
                                    ProductModel productModel = ProductModel(
                                      productName: productNameController.text,
                                      productCategory: productCategory,
                                      productDescription: showAdvancedOptions
                                          ? productDescriptionController.text
                                          : '',
                                      size: size,
                                      color: color,
                                      weight: weight,
                                      capacity: capacity,
                                      type: type,
                                      brandName: brandName,
                                      productCode: productCode,
                                      productStock: stockText,
                                      productUnit: productUnit,
                                      productSalePrice: mrpText,
                                      productPurchasePrice: purchaseText,
                                      productDiscount: productDiscount,
                                      productWholeSalePrice: showAdvancedOptions
                                          ? wholesaleText
                                          : '0',
                                      productDealerPrice: showAdvancedOptions
                                          ? dealerText
                                          : '0',
                                      productManufacturer: showAdvancedOptions
                                          ? productManufacturer
                                          : '',
                                      warehouseName: selectedWareHouse?.warehouseName ?? 'المستودع الرئيسي',
                                      warehouseId: selectedWareHouse?.id ?? 'main',
                                      productPicture: productPicture,
                                      expiringDate: showAdvancedOptions
                                          ? expireDate
                                          : '',
                                      manufacturingDate: showAdvancedOptions
                                          ? manufactureDate
                                          : '',
                                      productAddingDate: DateTime.now().toIso8601String(), // تاريخ إضافة المنتج
                                      lowerStockAlert: showAdvancedOptions
                                          ? lowerStockAlert
                                          : 5,
                                      serialNumber: [],
                                      taxType: showAdvancedOptions
                                          ? selectedTaxType
                                          : 'غير شامل',
                                      margin: showAdvancedOptions
                                          ? (num.tryParse(marginController.text) ?? 0)
                                          : 0,
                                      excTax: showAdvancedOptions
                                          ? (num.tryParse(excTaxAmount) ?? 0)
                                          : 0,
                                      incTax: showAdvancedOptions
                                          ? (num.tryParse(incTaxAmount) ?? 0)
                                          : 0,
                                      groupTaxName: showAdvancedOptions
                                          ? (selectedGroupTaxModel?.name ?? '')
                                          : '',
                                      groupTaxRate: showAdvancedOptions
                                          ? (selectedGroupTaxModel?.taxRate ?? 0)
                                          : 0,
                                      subTaxes: showAdvancedOptions
                                          ? (selectedGroupTaxModel?.subTaxes ?? [])
                                          : [],
                                    );
                                    print(productModel.toJson());
                                    final productRef = _productInformationRef.push();
                                    await productRef.set(productModel.toJson());

                                    // إنشاء حركة مخزون أولية (رصيد بداية المدة)
                                    if (int.tryParse(stockText) != null && int.parse(stockText) > 0) {
                                      await _createInitialStockMovement(
                                        productCode: productCode,
                                        productName: productNameController.text,
                                        quantity: int.parse(stockText),
                                        warehouseId: selectedWareHouse?.id ?? 'main',
                                        warehouseName: selectedWareHouse?.warehouseName ?? 'المستودع الرئيسي',
                                        addingDate: DateTime.now(),
                                      );
                                    }

                                    Subscription.decreaseSubscriptionLimits(
                                        itemType: 'products', context: context);
                                    EasyLoading.dismiss();
                                    print('✅ تم حفظ المنتج بنجاح في قاعدة البيانات');
                                    EasyLoading.showSuccess('تم حفظ المنتج بنجاح! 🎉');
                                    ref.refresh(categoryProvider);
                                    ref.refresh(brandsProvider);
                                    ref.refresh(productProvider);
                                    Navigator.pop(context, true);
                                  } catch (e) {
                                    setState(() {
                                      saleButtonClicked = false;
                                    });
                                    EasyLoading.dismiss();
                                    ScaffoldMessenger.of(context).showSnackBar(
                                        SnackBar(content: Text(e.toString())));
                                  }
                                } else {
                                  print('❌ فشل التحقق من صحة البيانات');
                                  EasyLoading.showError('يرجى التأكد من ملء جميع الحقول المطلوبة');
                                }
                              } else {
                                EasyLoading.showError(demoText);
                              }
                            },
                      buttonTextColor: Colors.white,
                    ),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          );
        }),
      ),
    );
  }

  bool validateAndSave() {
    final form = globalKey.currentState;
    if (form!.validate()) {
      form.save();
      return true;
    }
    return false;
  }
}
