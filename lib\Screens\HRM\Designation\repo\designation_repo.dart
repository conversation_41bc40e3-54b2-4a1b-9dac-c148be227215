import 'dart:convert';

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/models/hrm_models.dart';

class DesignationRepository {
  final DatabaseReference _dbRef = FirebaseDatabase.instance.ref();

  Future<List<DesignationModel>> getAllDesignations() async {
    List<DesignationModel> designations = [];

    try {
      final userID = await getUserID();
      final snapshot = await FirebaseDatabase.instance
          .ref(userID)
          .child('Designation')
          .orderByKey()
          .get();

      if (snapshot.exists) {
        for (var element in snapshot.children) {
          var data = jsonDecode(jsonEncode(element.value));
          // تسجيل بيانات المسمى الوظيفي

          // تحويل تاريخ الإنشاء بشكل صحيح
          DateTime createdAt;
          try {
            if (data['createdAt'] is int) {
              createdAt =
                  DateTime.fromMillisecondsSinceEpoch(data['createdAt']);
            } else if (data['createdAt'] is String) {
              createdAt =
                  DateTime.tryParse(data['createdAt']) ?? DateTime.now();
            } else {
              createdAt = DateTime.now();
            }
          } catch (e) {
            // خطأ في تحليل تاريخ الإنشاء
            createdAt = DateTime.now();
          }

          var designation = DesignationModel(
            id: element.key ?? '',
            title: data['title'] ?? data['designation'] ?? '',
            description:
                data['description'] ?? data['designationDescription'] ?? '',
            department: data['department'] ?? '',
            createdAt: createdAt,
          );
          designations.add(designation);
          // تمت إضافة المسمى الوظيفي
        }
      } else {
        // لم يتم العثور على مسميات وظيفية في قاعدة البيانات
      }
    } catch (e) {
      // خطأ في جلب المسميات الوظيفية
    }

    return designations;
  }

  // Method to save designation
  Future<bool> addDesignation({required DesignationModel designation}) async {
    try {
      EasyLoading.show(status: 'Loading...', dismissOnTap: false);

      final userID = await getUserID();
      final DatabaseReference designationRef =
          _dbRef.child(userID).child('Designation').child(designation.id);

      await designationRef.set({
        'title': designation.title,
        'description': designation.description,
        'department': designation.department,
        'createdAt': designation.createdAt.millisecondsSinceEpoch,
      });

      EasyLoading.showSuccess('Added Successfully',
          duration: const Duration(milliseconds: 500));
      return true;
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception('Failed to add designation: ${e.toString()}');
    }
  }

  Future<bool> updateDesignation(
      {required DesignationModel designation}) async {
    try {
      EasyLoading.show(status: 'Loading...', dismissOnTap: false);

      final userID = await getUserID();
      final DatabaseReference designationRef =
          _dbRef.child(userID).child('Designation').child(designation.id);

      await designationRef.update({
        'title': designation.title,
        'description': designation.description,
        'department': designation.department,
        'createdAt': designation.createdAt.millisecondsSinceEpoch,
      });

      EasyLoading.showSuccess('Updated Successfully',
          duration: const Duration(milliseconds: 500));
      return true;
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception('Failed to Updated designation: ${e.toString()}');
    }
  }

  Future<bool> deleteDesignation({required String id}) async {
    try {
      EasyLoading.show(status: 'Deleting...');

      final String userId = await getUserID();
      final DatabaseReference designationRef =
          _dbRef.child(userId).child('Designation').child(id);

      await designationRef.remove();

      EasyLoading.showSuccess('Deleted Successfully');
      return true;
    } catch (e) {
      EasyLoading.showError('Error: ${e.toString()}');
      return false;
    }
  }
}
