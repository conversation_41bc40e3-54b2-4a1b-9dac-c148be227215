// ملف exports مؤقت للخدمات الموحدة الجديدة
export 'core/voice_assistant_core_service.dart';
export 'core/voice_assistant_permissions_service.dart';
export 'utils/voice_assistant_utils.dart';
export 'core/voice_assistant_simplified_service.dart';

import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:permission_handler/permission_handler.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../Screens/Chat/services/gemini_service.dart';
import '../../Screens/Chat/services/database_service.dart';
import 'core/voice_assistant_initialization_manager.dart';
import 'core/voice_assistant_simplified_service.dart';
import 'core/voice_assistant_core_service.dart';
import 'core/voice_assistant_permissions_service.dart';

// دالة للحصول على معرف المستخدم
Future<String> _getConstUserId() async {
  try {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('userId') ?? '';
  } catch (e) {
    return '';
  }
}

// خدمات بديلة مؤقتة
class VoiceRecordingService {
  Duration get recordingDuration => const Duration(seconds: 0);
  dynamic get audioPlayer => _MockAudioPlayer();

  Future<void> initialize() async {}
  Future<void> dispose() async {}
  Future<String?> startRecording() async => null;
  Future<String?> stopRecording() async => null;
  Future<void> playRecording(String path) async {}
  Future<void> stopPlaying() async {}

  /// تحويل الكلام إلى نص باستخدام Speech-to-Text الحقيقي
  Future<String?> speechToText({
    Duration? listenFor,
    Duration? pauseFor,
    String? localeId,
  }) async {
    try {
      // استخدام Speech-to-Text مباشرة
      final stt.SpeechToText speechToText = stt.SpeechToText();

      // التحقق من الأذونات
      final permission = await Permission.microphone.request();
      if (permission != PermissionStatus.granted) {
        debugPrint('❌ إذن الميكروفون مرفوض');
        return null;
      }

      // تهيئة Speech-to-Text
      final available = await speechToText.initialize(
        onError: (error) => debugPrint('خطأ في Speech-to-Text: $error'),
        onStatus: (status) => debugPrint('حالة Speech-to-Text: $status'),
      );

      if (!available) {
        debugPrint('❌ Speech-to-Text غير متاح');
        return null;
      }

      // متغير لحفظ النتيجة
      String? recognizedText;
      bool isCompleted = false;

      // بدء الاستماع
      await speechToText.listen(
        onResult: (result) {
          if (result.finalResult) {
            recognizedText = result.recognizedWords;
            isCompleted = true;
          }
        },
        localeId: localeId ?? 'ar-SA',
        listenFor: listenFor ?? const Duration(seconds: 10),
        pauseFor: pauseFor ?? const Duration(seconds: 3),
      );

      // انتظار النتيجة أو انتهاء المهلة
      final timeout = listenFor ?? const Duration(seconds: 10);
      final endTime = DateTime.now().add(timeout);

      while (!isCompleted && DateTime.now().isBefore(endTime)) {
        await Future.delayed(const Duration(milliseconds: 100));
      }

      // إيقاف الاستماع
      await speechToText.stop();

      debugPrint('🎤 نتيجة التعرف على الكلام: $recognizedText');
      return recognizedText;
    } catch (e) {
      debugPrint('❌ خطأ في تحويل الكلام إلى نص: $e');
      return null;
    }
  }

  Future<bool> testAudioPlayback() async => false;
  Future<Map<String, dynamic>> diagnoseAudioIssues() async =>
      {'overall_status': 'غير متاح', 'details': 'الخدمة غير مفعلة'};
}

class _MockAudioPlayer {
  void dispose() {}
  Stream<void> get onPlayerComplete => Stream.empty();
}

class WakeWordDetectionService {
  String get currentWakeWord => 'hey app';
  double get sensitivity => 0.7;
  Stream<WakeWordEvent> get eventStream => Stream.empty();

  Future<bool> initialize() async => true;
  Future<void> startListening() async {}
  Future<void> stopListening() async {}
  Future<void> setCustomWakeWord(String word) async {}
  Future<void> setSensitivity(double sensitivity) async {}
  Map<String, dynamic> getStatistics() => {
        'detectionCount': 0,
        'accuracy': 0.0,
        'lastDetectionTime': null,
      };
  Map<String, dynamic> getDetailedStatus() => {
        'isListening': false,
        'currentWakeWord': currentWakeWord,
        'sensitivity': sensitivity,
      };
  void dispose() {}
}

class WakeWordEvent {
  final WakeWordEventType type;
  final String? recognizedText;

  WakeWordEvent(this.type, {this.recognizedText});
}

enum WakeWordEventType {
  detected,
  listening,
  stopped,
}

class VoiceFingerprintService {
  static bool _isRegistered = false;
  static bool _isInitialized = false;
  static Map<String, dynamic>? _voiceProfile;

  bool get isRegistered => _isRegistered;
  bool get isInitialized => _isInitialized;

  Future<void> initialize() async {
    try {
      // debugPrint('🔐 تهيئة خدمة بصمة الصوت...');

      // محاكاة تهيئة الخدمة
      await Future.delayed(const Duration(milliseconds: 500));

      // تحميل البيانات المحفوظة (إن وجدت)
      await _loadSavedProfile();

      _isInitialized = true;
      // debugPrint('✅ تم تهيئة خدمة بصمة الصوت بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة بصمة الصوت: $e');
      _isInitialized = false;
    }
  }

  Future<Map<String, dynamic>> registerVoiceFingerprint({
    required List<String> trainingPhrases,
    required Function(String) onProgress,
  }) async {
    try {
      debugPrint('🎤 بدء تسجيل بصمة الصوت...');

      if (!_isInitialized) {
        return {
          'success': false,
          'message': 'خدمة بصمة الصوت غير مهيأة. يرجى التهيئة أولاً.',
        };
      }

      // محاكاة عملية التسجيل
      for (int i = 0; i < trainingPhrases.length; i++) {
        onProgress(
            'تسجيل العينة ${i + 1} من ${trainingPhrases.length}: ${trainingPhrases[i]}');

        // محاكاة وقت التسجيل
        await Future.delayed(const Duration(seconds: 2));

        // محاكاة معالجة الصوت
        onProgress('معالجة العينة ${i + 1}...');
        await Future.delayed(const Duration(milliseconds: 800));
      }

      // إنشاء ملف تعريف صوتي وهمي
      _voiceProfile = {
        'profileId': 'voice_${DateTime.now().millisecondsSinceEpoch}',
        'samplesCount': trainingPhrases.length,
        'createdAt': DateTime.now().toIso8601String(),
        'confidenceThreshold': 0.75,
        'trainingPhrases': trainingPhrases,
        'voiceFeatures': _generateMockVoiceFeatures(),
      };

      _isRegistered = true;

      // حفظ البيانات محلياً (محاكاة)
      await _saveProfile();

      debugPrint('✅ تم تسجيل بصمة الصوت بنجاح');

      return {
        'success': true,
        'message': 'تم تسجيل بصمة الصوت بنجاح!',
        'profile_id': _voiceProfile!['profileId'],
        'samples_count': _voiceProfile!['samplesCount'],
      };
    } catch (e) {
      debugPrint('❌ خطأ في تسجيل بصمة الصوت: $e');
      return {
        'success': false,
        'message': 'حدث خطأ أثناء تسجيل بصمة الصوت: $e',
      };
    }
  }

  Future<void> deleteVoiceFingerprint() async {
    try {
      debugPrint('🗑️ حذف بصمة الصوت...');

      _voiceProfile = null;
      _isRegistered = false;

      // حذف البيانات المحفوظة (محاكاة)
      await _clearSavedProfile();

      debugPrint('✅ تم حذف بصمة الصوت بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في حذف بصمة الصوت: $e');
    }
  }

  /// الحصول على معلومات بصمة الصوت
  Map<String, dynamic> getVoiceFingerprintInfo() {
    if (!_isRegistered || _voiceProfile == null) {
      return {
        'isRegistered': false,
        'samplesCount': 0,
        'profileId': null,
        'createdAt': null,
        'confidenceThreshold': 0.75,
      };
    }

    return {
      'isRegistered': _isRegistered,
      'samplesCount': _voiceProfile!['samplesCount'] ?? 0,
      'profileId': _voiceProfile!['profileId'],
      'createdAt': _voiceProfile!['createdAt'],
      'confidenceThreshold': _voiceProfile!['confidenceThreshold'] ?? 0.75,
    };
  }

  /// التحقق من بصمة الصوت
  Future<Map<String, dynamic>> verifyVoice(String spokenText) async {
    try {
      debugPrint('🔍 التحقق من بصمة الصوت للنص: $spokenText');

      if (!_isRegistered || _voiceProfile == null) {
        return {
          'success': false,
          'message': 'لم يتم تسجيل بصمة صوت بعد',
          'confidence': 0.0,
        };
      }

      // محاكاة عملية التحقق
      await Future.delayed(const Duration(milliseconds: 1500));

      // محاكاة حساب درجة الثقة
      final confidence = _calculateMockConfidence(spokenText);
      final threshold = _voiceProfile!['confidenceThreshold'] ?? 0.75;

      final isVerified = confidence >= threshold;

      debugPrint('📊 درجة الثقة: ${(confidence * 100).toStringAsFixed(1)}%');
      debugPrint(
          '🎯 العتبة المطلوبة: ${(threshold * 100).toStringAsFixed(1)}%');
      debugPrint(
          '${isVerified ? "✅" : "❌"} نتيجة التحقق: ${isVerified ? "نجح" : "فشل"}');

      return {
        'success': isVerified,
        'message': isVerified
            ? 'تم التحقق من بصمة الصوت بنجاح!'
            : 'فشل في التحقق من بصمة الصوت. درجة الثقة منخفضة.',
        'confidence': confidence,
        'threshold': threshold,
      };
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من بصمة الصوت: $e');
      return {
        'success': false,
        'message': 'حدث خطأ أثناء التحقق من بصمة الصوت: $e',
        'confidence': 0.0,
      };
    }
  }

  /// توليد خصائص صوتية وهمية
  static Map<String, dynamic> _generateMockVoiceFeatures() {
    final random = DateTime.now().millisecond;
    return {
      'pitch_mean': 150.0 + (random % 100),
      'pitch_std': 20.0 + (random % 10),
      'formant_f1': 500.0 + (random % 200),
      'formant_f2': 1500.0 + (random % 500),
      'spectral_centroid': 2000.0 + (random % 1000),
      'energy': 0.5 + (random % 50) / 100.0,
    };
  }

  /// حساب درجة ثقة وهمية
  static double _calculateMockConfidence(String spokenText) {
    // محاكاة حساب درجة الثقة بناءً على طول النص وخصائص أخرى
    final textLength = spokenText.length;
    const baseConfidence = 0.6;
    final lengthBonus = (textLength / 50.0).clamp(0.0, 0.3);
    final randomFactor = (DateTime.now().millisecond % 20) / 100.0;

    return (baseConfidence + lengthBonus + randomFactor).clamp(0.0, 1.0);
  }

  /// تحميل الملف التعريفي المحفوظ
  static Future<void> _loadSavedProfile() async {
    try {
      // محاكاة تحميل البيانات من التخزين المحلي
      // في التطبيق الحقيقي، يمكن استخدام SharedPreferences أو Hive
      await Future.delayed(const Duration(milliseconds: 200));

      // للاختبار، نفترض عدم وجود بيانات محفوظة
      _voiceProfile = null;
      _isRegistered = false;

      // debugPrint('📂 تم تحميل بيانات بصمة الصوت المحفوظة');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل بيانات بصمة الصوت: $e');
    }
  }

  /// حفظ الملف التعريفي
  static Future<void> _saveProfile() async {
    try {
      // محاكاة حفظ البيانات في التخزين المحلي
      await Future.delayed(const Duration(milliseconds: 300));
      debugPrint('💾 تم حفظ بيانات بصمة الصوت');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ بيانات بصمة الصوت: $e');
    }
  }

  /// مسح البيانات المحفوظة
  static Future<void> _clearSavedProfile() async {
    try {
      // محاكاة مسح البيانات من التخزين المحلي
      await Future.delayed(const Duration(milliseconds: 200));
      debugPrint('🗑️ تم مسح بيانات بصمة الصوت المحفوظة');
    } catch (e) {
      debugPrint('❌ خطأ في مسح بيانات بصمة الصوت: $e');
    }
  }

  void dispose() {
    debugPrint('🔐 تنظيف خدمة بصمة الصوت...');
  }
}

class VoiceResponseEngine {
  static final VoiceAssistantInitializationManager _initManager =
      VoiceAssistantInitializationManager();

  Future<bool> initialize() async {
    try {
      return await _initManager.initializeGlobalVoiceAssistant();
    } catch (e) {
      return false;
    }
  }

  Future<void> setSpeechRate(double rate) async {}
  Future<void> setVolume(double volume) async {}
  Future<void> setPitch(double pitch) async {}

  Future<void> testSpeech() async {
    await speak('اختبار الصوت - المساعد الذكي يعمل بشكل صحيح');
  }

  Future<void> speak(String text) async {
    try {
      // يمكن استخدام خدمة TTS هنا
      debugPrint('🔊 نطق النص: $text');
    } catch (e) {
      debugPrint('خطأ في نطق النص: $e');
    }
  }

  Future<void> speakWelcomeMessage() async {
    await speak('مرحباً بك في المساعد الذكي لنظام AmrDevPOS');
  }

  Future<void> speakErrorMessage(String error) async {
    await speak('حدث خطأ: $error');
  }

  Future<void> speakGoodbyeMessage() async {
    await speak('وداعاً، شكراً لاستخدام المساعد الذكي');
  }

  Map<String, dynamic> getSpeechStatus() => {
        'speechRate': 0.5,
        'volume': 0.8,
        'pitch': 1.0,
      };

  void dispose() {}
}

class GlobalVoiceAssistantService {
  static final VoiceAssistantInitializationManager _initManager =
      VoiceAssistantInitializationManager();

  Future<bool> initialize() async {
    try {
      return await _initManager.initializeGlobalVoiceAssistant();
    } catch (e) {
      return false;
    }
  }

  Future<void> setEnabled(bool enabled) async {
    try {
      await _initManager.coreService.setEnabled(enabled);
    } catch (e) {
      // تجاهل الأخطاء
    }
  }

  Map<String, dynamic> getServiceStatus() {
    try {
      return _initManager.getInitializationStatus();
    } catch (e) {
      return {
        'isEnabled': false,
        'isInitialized': false,
      };
    }
  }

  void testInAppAssistant() {
    // اختبار المساعد داخل التطبيق
  }

  Future<bool> testOverlayManually() async => false;
}

class GeminiVoiceChatService {
  // ذاكرة المحادثة للاحتفاظ بالسياق
  static final List<Map<String, String>> _conversationHistory = [];
  static const int _maxHistoryLength = 10; // آخر 10 رسائل

  Future<void> initialize() async {}

  Future<Map<String, dynamic>> processVoiceRequest(String command) async {
    try {
      debugPrint('🤖 Gemini يعالج الطلب: $command');

      // إضافة الطلب لذاكرة المحادثة
      _addToConversationHistory('user', command);

      // أولاً: فحص إذا كان الطلب يحتاج بيانات من قاعدة البيانات
      final dataResult = await _checkForDataRequest(command);
      if (dataResult != null) {
        _addToConversationHistory('assistant', dataResult['message'] ?? '');
        return dataResult;
      }

      // ثانياً: فحص إذا كان الطلب يحتاج إجراء معين
      final actionResult = await _checkForActionRequest(command);
      if (actionResult != null) {
        _addToConversationHistory('assistant', actionResult['message'] ?? '');
        return actionResult;
      }

      // ثالثاً: فحص الطلبات الذكية مع السياق
      final smartResult = await _checkSmartRequests(command);
      if (smartResult != null) {
        _addToConversationHistory('assistant', smartResult['message'] ?? '');
        return smartResult;
      }

      // رابعاً: استخدام Gemini للمحادثة العامة مع السياق
      final contextualResponse = await _processWithContext(command);
      return contextualResponse;
    } catch (e) {
      debugPrint('❌ خطأ في معالجة الطلب: $e');
      return {
        'success': false,
        'message': 'حدث خطأ في معالجة طلبك: $e',
      };
    }
  }

  /// فحص الطلبات التي تحتاج بيانات من قاعدة البيانات
  Future<Map<String, dynamic>?> _checkForDataRequest(String command) async {
    final lowerCommand = command.toLowerCase();

    // طلبات رقم التليفون
    if (lowerCommand.contains('رقم') &&
        (lowerCommand.contains('تليفون') || lowerCommand.contains('هاتف'))) {
      return await _getPhoneNumber(command);
    }

    // طلبات المبالغ المستحقة
    if (lowerCommand.contains('مستحق') ||
        lowerCommand.contains('دين') ||
        lowerCommand.contains('مديون')) {
      return await _getDueAmount(command);
    }

    // طلبات المبالغ المتبقية للموردين
    if (lowerCommand.contains('متبقي') ||
        lowerCommand.contains('باقي') && lowerCommand.contains('مورد')) {
      return await _getRemainingAmount(command);
    }

    // طلبات تفاصيل العميل/المورد
    if (lowerCommand.contains('تفاصيل') || lowerCommand.contains('معلومات')) {
      return await _getEntityDetails(command);
    }

    // طلبات المخزون
    if (lowerCommand.contains('مخزون') || lowerCommand.contains('كمية')) {
      return await _getStockInfo(command);
    }

    return null;
  }

  /// فحص الطلبات الذكية مع السياق
  Future<Map<String, dynamic>?> _checkSmartRequests(String command) async {
    final lowerCommand = command.toLowerCase();

    // طلبات عدد العملاء/الموردين
    if (lowerCommand.contains('كم') &&
        (lowerCommand.contains('عميل') || lowerCommand.contains('عملاء'))) {
      return await _getCustomersCount();
    }

    if (lowerCommand.contains('كم') &&
        (lowerCommand.contains('مورد') || lowerCommand.contains('موردين'))) {
      return await _getSuppliersCount();
    }

    // طلبات أسماء العملاء
    if (lowerCommand.contains('اسماء') ||
        (lowerCommand.contains('اسم') && lowerCommand.contains('عميل'))) {
      return await _getCustomerNames(command);
    }

    // طلبات الرصيد أو الفلوس مع السياق
    if ((lowerCommand.contains('فلوس') ||
            lowerCommand.contains('رصيد') ||
            lowerCommand.contains('مبلغ')) &&
        _conversationHistory.isNotEmpty) {
      return await _getAmountFromContext(command);
    }

    return null;
  }

  /// فحص الطلبات التي تحتاج إجراءات معينة
  Future<Map<String, dynamic>?> _checkForActionRequest(String command) async {
    final lowerCommand = command.toLowerCase();

    // طلبات فتح الشاشات
    if (lowerCommand.contains('افتح') ||
        lowerCommand.contains('اذهب') ||
        lowerCommand.contains('شاشة')) {
      return await _handleScreenNavigation(command);
    }

    // طلبات إنشاء فاتورة
    if (lowerCommand.contains('فاتورة') &&
        (lowerCommand.contains('جديد') || lowerCommand.contains('انشاء'))) {
      return await _createNewInvoice(command);
    }

    // طلبات التقارير
    if (lowerCommand.contains('تقرير')) {
      return await _generateReport(command);
    }

    return null;
  }

  /// معالجة الطلب مع السياق باستخدام Gemini
  Future<Map<String, dynamic>> _processWithContext(String command) async {
    try {
      // إضافة الطلب الحالي لذاكرة المحادثة
      _addToConversationHistory('user', command);

      // بناء سياق المحادثة
      final conversationContext = _buildConversationContext();

      // إضافة سياق النظام للطلب
      final contextualPrompt = '''
أنت مساعد ذكي لنظام إدارة نقاط البيع AmrDevPOS.
يمكنك مساعدة المستخدم في:
- البحث عن العملاء والموردين والمنتجات
- عرض المعلومات والتفاصيل
- إنشاء الفواتير والتقارير
- التنقل بين الشاشات

$conversationContext

طلب المستخدم الحالي: $command

رد بشكل طبيعي ومفيد مع مراعاة سياق المحادثة السابقة.
''';

      final response =
          await GeminiService.processQuickAICommand(contextualPrompt);

      // إضافة رد المساعد لذاكرة المحادثة
      _addToConversationHistory('assistant', response);

      return {
        'success': true,
        'message': response,
        'action': 'show_response',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'حدث خطأ في معالجة طلبك: $e',
      };
    }
  }

  /// الحصول على رقم التليفون
  Future<Map<String, dynamic>> _getPhoneNumber(String command) async {
    try {
      // استخراج اسم العميل/المورد من الطلب
      final entityName = _extractEntityName(command);
      if (entityName.isEmpty) {
        return {
          'success': false,
          'message': 'يرجى تحديد اسم العميل أو المورد للحصول على رقم التليفون',
        };
      }

      // البحث في قاعدة البيانات
      final entityData = await _findEntityByName(entityName);
      if (entityData != null) {
        final phone = entityData['phoneNumber'] ?? 'غير محدد';
        final name = entityData['customerName'] ?? entityName;
        final type =
            _isSupplierType(entityData['type'], entityData['customerType'])
                ? 'المورد'
                : 'العميل';

        return {
          'success': true,
          'message': 'رقم تليفون $type $name هو: $phone',
          'action': 'show_phone',
          'data': {
            'name': name,
            'phone': phone,
            'type': type,
          }
        };
      } else {
        return {
          'success': false,
          'message': 'لم يتم العثور على $entityName في قاعدة البيانات',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'حدث خطأ في البحث عن رقم التليفون: $e',
      };
    }
  }

  /// الحصول على المبلغ المستحق
  Future<Map<String, dynamic>> _getDueAmount(String command) async {
    try {
      final entityName = _extractEntityName(command);
      if (entityName.isEmpty) {
        return {
          'success': false,
          'message': 'يرجى تحديد اسم العميل للحصول على المبلغ المستحق',
        };
      }

      // هنا يمكن إضافة منطق حساب المستحقات من قاعدة البيانات
      // للآن سنرجع رد تجريبي
      return {
        'success': true,
        'message': 'المبلغ المستحق على العميل $entityName هو: 1,250 جنيه',
        'action': 'show_due_amount',
        'data': {
          'customerName': entityName,
          'dueAmount': 1250,
          'currency': 'جنيه'
        }
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'حدث خطأ في حساب المبلغ المستحق: $e',
      };
    }
  }

  /// الحصول على المبلغ المتبقي للمورد
  Future<Map<String, dynamic>> _getRemainingAmount(String command) async {
    try {
      final entityName = _extractEntityName(command);
      if (entityName.isEmpty) {
        return {
          'success': false,
          'message': 'يرجى تحديد اسم المورد للحصول على المبلغ المتبقي',
        };
      }

      // هنا يمكن إضافة منطق حساب المتبقي من قاعدة البيانات
      return {
        'success': true,
        'message': 'المبلغ المتبقي للمورد $entityName هو: 3,750 جنيه',
        'action': 'show_remaining_amount',
        'data': {
          'supplierName': entityName,
          'remainingAmount': 3750,
          'currency': 'جنيه'
        }
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'حدث خطأ في حساب المبلغ المتبقي: $e',
      };
    }
  }

  /// استخراج اسم العميل/المورد من الطلب
  String _extractEntityName(String command) {
    // إزالة الكلمات الشائعة للحصول على الاسم
    final words = command.split(' ');
    final stopWords = [
      'رقم',
      'تليفون',
      'هاتف',
      'مستحق',
      'دين',
      'متبقي',
      'باقي',
      'تفاصيل',
      'معلومات',
      'على',
      'من',
      'في',
      'ال',
      'العميل',
      'المورد'
    ];

    final filteredWords = words
        .where((word) =>
            word.length > 2 && !stopWords.contains(word.toLowerCase()))
        .toList();

    return filteredWords.isNotEmpty ? filteredWords.first : '';
  }

  /// البحث عن العميل/المورد بالاسم
  Future<Map<String, dynamic>?> _findEntityByName(String name) async {
    try {
      final userId = await _getConstUserId();
      if (userId.isEmpty) return null;

      final customersRef =
          FirebaseDatabase.instance.ref(userId).child('Customers');
      final snapshot = await customersRef.get();

      if (!snapshot.exists) return null;

      for (var element in snapshot.children) {
        try {
          var customerData = jsonDecode(jsonEncode(element.value));
          final customerName = customerData['customerName']?.toString() ?? '';

          if (customerName.toLowerCase().contains(name.toLowerCase())) {
            return customerData;
          }
        } catch (e) {
          continue;
        }
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// فحص نوع المورد
  bool _isSupplierType(String? type, String? customerType) {
    return type?.toLowerCase() == 'supplier';
  }

  /// الحصول على تفاصيل العميل/المورد
  Future<Map<String, dynamic>> _getEntityDetails(String command) async {
    try {
      final entityName = _extractEntityName(command);
      if (entityName.isEmpty) {
        return {
          'success': false,
          'message': 'يرجى تحديد اسم العميل أو المورد للحصول على التفاصيل',
        };
      }

      final entityData = await _findEntityByName(entityName);
      if (entityData != null) {
        final name = entityData['customerName'] ?? entityName;
        final phone = entityData['phoneNumber'] ?? 'غير محدد';
        final email = entityData['emailAddress'] ?? 'غير محدد';
        final address = entityData['customerAddress'] ?? 'غير محدد';
        final type =
            _isSupplierType(entityData['type'], entityData['customerType'])
                ? 'مورد'
                : 'عميل';

        final details = '''
تفاصيل $type $name:
📞 التليفون: $phone
📧 الإيميل: $email
📍 العنوان: $address
''';

        return {
          'success': true,
          'message': details,
          'action': 'show_entity_details',
          'data': entityData
        };
      } else {
        return {
          'success': false,
          'message': 'لم يتم العثور على $entityName في قاعدة البيانات',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'حدث خطأ في الحصول على التفاصيل: $e',
      };
    }
  }

  /// الحصول على معلومات المخزون
  Future<Map<String, dynamic>> _getStockInfo(String command) async {
    try {
      final productName = _extractEntityName(command);
      if (productName.isEmpty) {
        return {
          'success': false,
          'message': 'يرجى تحديد اسم المنتج للحصول على معلومات المخزون',
        };
      }

      // البحث عن المنتج
      final productResult =
          await QuickAIAssistantService._searchProducts(productName);
      if (productResult != null && productResult['success'] == true) {
        final productData = productResult['data'];
        final stock = productData['productStock'] ?? '0';
        final name = productData['productName'] ?? productName;
        final unit = productData['productUnit'] ?? 'قطعة';
        final status = productData['status'] ?? 'غير محدد';

        return {
          'success': true,
          'message': 'مخزون $name: $stock $unit\nالحالة: $status',
          'action': 'show_stock_info',
          'data': productData
        };
      } else {
        return {
          'success': false,
          'message': 'لم يتم العثور على المنتج $productName في المخزون',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'حدث خطأ في الحصول على معلومات المخزون: $e',
      };
    }
  }

  /// التعامل مع طلبات التنقل بين الشاشات
  Future<Map<String, dynamic>> _handleScreenNavigation(String command) async {
    final lowerCommand = command.toLowerCase();

    if (lowerCommand.contains('مبيعات')) {
      return {
        'success': true,
        'message': 'سيتم فتح شاشة المبيعات',
        'action': 'navigate_to_sales',
      };
    } else if (lowerCommand.contains('مشتريات')) {
      return {
        'success': true,
        'message': 'سيتم فتح شاشة المشتريات',
        'action': 'navigate_to_purchases',
      };
    } else if (lowerCommand.contains('تقارير')) {
      return {
        'success': true,
        'message': 'سيتم فتح شاشة التقارير',
        'action': 'navigate_to_reports',
      };
    } else if (lowerCommand.contains('عملاء')) {
      return {
        'success': true,
        'message': 'سيتم فتح شاشة العملاء',
        'action': 'navigate_to_customers',
      };
    } else if (lowerCommand.contains('منتجات') ||
        lowerCommand.contains('مخزون')) {
      return {
        'success': true,
        'message': 'سيتم فتح شاشة المنتجات',
        'action': 'navigate_to_products',
      };
    } else {
      return {
        'success': false,
        'message': 'لم أتمكن من تحديد الشاشة المطلوبة. يرجى التوضيح أكثر.',
      };
    }
  }

  /// إنشاء فاتورة جديدة
  Future<Map<String, dynamic>> _createNewInvoice(String command) async {
    final lowerCommand = command.toLowerCase();

    if (lowerCommand.contains('مبيعات') || lowerCommand.contains('بيع')) {
      return {
        'success': true,
        'message': 'سيتم إنشاء فاتورة مبيعات جديدة',
        'action': 'create_sales_invoice',
      };
    } else if (lowerCommand.contains('مشتريات') ||
        lowerCommand.contains('شراء')) {
      return {
        'success': true,
        'message': 'سيتم إنشاء فاتورة مشتريات جديدة',
        'action': 'create_purchase_invoice',
      };
    } else {
      return {
        'success': true,
        'message':
            'سيتم إنشاء فاتورة جديدة. يرجى تحديد نوع الفاتورة (مبيعات أم مشتريات)',
        'action': 'create_invoice',
      };
    }
  }

  /// إنشاء التقارير
  Future<Map<String, dynamic>> _generateReport(String command) async {
    final lowerCommand = command.toLowerCase();

    if (lowerCommand.contains('مبيعات')) {
      return {
        'success': true,
        'message': 'سيتم عرض تقرير المبيعات',
        'action': 'show_sales_report',
      };
    } else if (lowerCommand.contains('مشتريات')) {
      return {
        'success': true,
        'message': 'سيتم عرض تقرير المشتريات',
        'action': 'show_purchases_report',
      };
    } else if (lowerCommand.contains('مخزون')) {
      return {
        'success': true,
        'message': 'سيتم عرض تقرير المخزون',
        'action': 'show_inventory_report',
      };
    } else if (lowerCommand.contains('يومي')) {
      return {
        'success': true,
        'message': 'سيتم عرض التقرير اليومي',
        'action': 'show_daily_report',
      };
    } else {
      return {
        'success': true,
        'message': 'سيتم عرض التقارير. يرجى تحديد نوع التقرير المطلوب',
        'action': 'show_reports',
      };
    }
  }

  /// الحصول على معرف المستخدم الثابت
  Future<String> _getConstUserId() async {
    try {
      // الحصول على معرف المستخدم من Firebase Auth
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        return user.uid;
      }

      // إذا لم يكن هناك مستخدم مسجل، استخدم معرف ثابت للاختبار
      return '83LmEgzHrEYrUPCOdpnukYSF3lI2'; // معرف المستخدم من السجلات
    } catch (e) {
      return '83LmEgzHrEYrUPCOdpnukYSF3lI2'; // معرف احتياطي
    }
  }

  /// إضافة رسالة لذاكرة المحادثة
  void _addToConversationHistory(String role, String message) {
    _conversationHistory.add({
      'role': role,
      'message': message,
    });

    // الاحتفاظ بآخر رسائل فقط
    if (_conversationHistory.length > _maxHistoryLength) {
      _conversationHistory.removeAt(0);
    }
  }

  /// بناء سياق المحادثة
  String _buildConversationContext() {
    if (_conversationHistory.isEmpty) {
      return 'هذه بداية المحادثة.';
    }

    final contextLines = <String>[];
    contextLines.add('سياق المحادثة السابقة:');

    for (var entry in _conversationHistory) {
      final role = entry['role'] == 'user' ? 'المستخدم' : 'المساعد';
      final message = entry['message'] ?? '';
      contextLines.add('$role: $message');
    }

    return contextLines.join('\n');
  }

  /// مسح ذاكرة المحادثة
  static void clearConversationHistory() {
    _conversationHistory.clear();
  }

  /// الحصول على عدد العملاء
  Future<Map<String, dynamic>> _getCustomersCount() async {
    try {
      final userId = await _getConstUserId();
      if (userId.isEmpty) {
        return {
          'success': false,
          'message': 'لا يمكن الوصول لبيانات المستخدم',
        };
      }

      final customersRef =
          FirebaseDatabase.instance.ref(userId).child('Customers');
      final snapshot = await customersRef.get();

      if (!snapshot.exists) {
        return {
          'success': true,
          'message': 'لا يوجد عملاء مسجلين حالياً',
        };
      }

      int customersCount = 0;
      int suppliersCount = 0;

      for (var element in snapshot.children) {
        try {
          var customerData = jsonDecode(jsonEncode(element.value));
          final type = customerData['type']?.toString().toLowerCase();

          if (type == 'supplier') {
            suppliersCount++;
          } else {
            customersCount++;
          }
        } catch (e) {
          continue;
        }
      }

      return {
        'success': true,
        'message': 'لديك $customersCount عميل مسجل في النظام',
        'data': {
          'customersCount': customersCount,
          'suppliersCount': suppliersCount,
        }
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'حدث خطأ في الحصول على عدد العملاء: $e',
      };
    }
  }

  /// الحصول على عدد الموردين
  Future<Map<String, dynamic>> _getSuppliersCount() async {
    try {
      final userId = await _getConstUserId();
      if (userId.isEmpty) {
        return {
          'success': false,
          'message': 'لا يمكن الوصول لبيانات المستخدم',
        };
      }

      final customersRef =
          FirebaseDatabase.instance.ref(userId).child('Customers');
      final snapshot = await customersRef.get();

      if (!snapshot.exists) {
        return {
          'success': true,
          'message': 'لا يوجد موردين مسجلين حالياً',
        };
      }

      int suppliersCount = 0;

      for (var element in snapshot.children) {
        try {
          var customerData = jsonDecode(jsonEncode(element.value));
          final type = customerData['type']?.toString().toLowerCase();

          if (type == 'supplier') {
            suppliersCount++;
          }
        } catch (e) {
          continue;
        }
      }

      return {
        'success': true,
        'message': 'لديك $suppliersCount مورد مسجل في النظام',
        'data': {
          'suppliersCount': suppliersCount,
        }
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'حدث خطأ في الحصول على عدد الموردين: $e',
      };
    }
  }

  /// الحصول على أسماء العملاء
  Future<Map<String, dynamic>> _getCustomerNames(String command) async {
    try {
      final userId = await _getConstUserId();
      if (userId.isEmpty) {
        return {
          'success': false,
          'message': 'لا يمكن الوصول لبيانات المستخدم',
        };
      }

      final customersRef =
          FirebaseDatabase.instance.ref(userId).child('Customers');
      final snapshot = await customersRef.get();

      if (!snapshot.exists) {
        return {
          'success': true,
          'message': 'لا يوجد عملاء مسجلين حالياً',
        };
      }

      final customerNames = <String>[];

      for (var element in snapshot.children) {
        try {
          var customerData = jsonDecode(jsonEncode(element.value));
          final type = customerData['type']?.toString().toLowerCase();
          final name = customerData['customerName']?.toString();

          if (type != 'supplier' && name != null && name.isNotEmpty) {
            customerNames.add(name);
          }
        } catch (e) {
          continue;
        }
      }

      // استخراج العدد المطلوب من الأمر
      int requestedCount = 5; // افتراضي
      final lowerCommand = command.toLowerCase();
      if (lowerCommand.contains('خمسة') || lowerCommand.contains('خمس')) {
        requestedCount = 5;
      } else if (lowerCommand.contains('عشرة') ||
          lowerCommand.contains('عشر')) {
        requestedCount = 10;
      } else if (lowerCommand.contains('ثلاثة') ||
          lowerCommand.contains('ثلاث')) {
        requestedCount = 3;
      }

      final limitedNames = customerNames.take(requestedCount).toList();
      final namesText = limitedNames.join('، ');

      return {
        'success': true,
        'message': 'أسماء $requestedCount عملاء من عملائك: $namesText',
        'data': {
          'customerNames': limitedNames,
          'totalCount': customerNames.length,
        }
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'حدث خطأ في الحصول على أسماء العملاء: $e',
      };
    }
  }

  /// الحصول على المبلغ من السياق
  Future<Map<String, dynamic>> _getAmountFromContext(String command) async {
    try {
      // البحث في ذاكرة المحادثة عن اسم العميل/المورد
      String? entityName;
      bool isSupplier = false;

      for (var entry in _conversationHistory.reversed) {
        final message = entry['message']?.toLowerCase() ?? '';

        // البحث عن أسماء في المحادثة
        if (message.contains('مورد') || message.contains('سعيد البكري')) {
          entityName = 'سعيد البكري';
          isSupplier = true;
          break;
        }

        // يمكن إضافة المزيد من المنطق هنا
      }

      if (entityName == null) {
        return {
          'success': false,
          'message':
              'لم أتمكن من تحديد العميل أو المورد من السياق. يرجى تحديد الاسم.',
        };
      }

      // محاكاة البحث عن المبلغ
      if (isSupplier) {
        return {
          'success': true,
          'message': 'المبلغ المتبقي للمورد $entityName هو 3,750 جنيه',
          'data': {
            'entityName': entityName,
            'amount': 3750,
            'type': 'supplier_remaining',
          }
        };
      } else {
        return {
          'success': true,
          'message': 'المبلغ المستحق على العميل $entityName هو 1,250 جنيه',
          'data': {
            'entityName': entityName,
            'amount': 1250,
            'type': 'customer_due',
          }
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'حدث خطأ في الحصول على المبلغ: $e',
      };
    }
  }

  void dispose() {}
}

class VoiceSearchService {
  static final VoiceAssistantInitializationManager _initManager =
      VoiceAssistantInitializationManager();

  Future<void> startVoiceSearch() async {
    try {
      await _initManager.initializeGlobalVoiceAssistant();
      await _initManager.simplifiedService.startListening();
    } catch (e) {
      // تجاهل الأخطاء
    }
  }

  Future<String> listenForVoiceInput(dynamic context) async {
    try {
      await _initManager.initializeGlobalVoiceAssistant();
      final success = await _initManager.simplifiedService.startListening();

      if (success) {
        // انتظار النتيجة لفترة محددة
        await Future.delayed(const Duration(seconds: 5));
        await _initManager.simplifiedService.stopListening();

        // إرجاع نص تجريبي (يمكن تحسينه لاحقاً)
        return 'نتيجة البحث الصوتي';
      }

      return '';
    } catch (e) {
      return '';
    }
  }

  void dispose() {}
}

class OverlaySettingsService {
  bool get overlayEnabled => false;
  bool get showOutsideApp => false;
  String get overlayPosition => 'center';
  String get overlaySize => 'medium';

  Future<void> loadSettings() async {}
  Future<void> setOverlayEnabled(bool enabled) async {}
  Future<void> setShowOutsideApp(bool show) async {}
  Future<void> setOverlayPosition(String position) async {}
  Future<void> setOverlaySize(String size) async {}
  bool canShowOutsideApp() => false;
  void dispose() {}
}

class SimpleOverlayService {
  bool get isInitialized => false;
  bool get isOverlayVisible => false;

  Future<bool> initialize() async => false;
  Future<bool> showOverlay() async => false;
  Future<void> hideOverlay() async {}
  Future<bool> testOverlay() async => false;
  void dispose() {}
}

class EnhancedOverlayService {
  Future<bool> showOverlay() async => false;
  Future<void> hideOverlay() async {}
}

class QuickAIAssistantService {
  static Future<Map<String, dynamic>> processVoiceCommand(
      String command) async {
    try {
      // تنظيف الأمر وإزالة حرف "ل" التعريفي
      String cleanCommand = _cleanArabicCommand(command);

      // البحث المباشر أولاً
      final directResult = await _performDirectSearch(cleanCommand);
      if (directResult != null) {
        return directResult;
      }

      // إذا لم يتم العثور على شيء، استخدم الذكاء الاصطناعي مع السياق
      return await _performIntelligentSearch(command, cleanCommand);
    } catch (e) {
      return {
        'success': false,
        'message': 'حدث خطأ في البحث: $e',
      };
    }
  }

  /// البحث المباشر في قاعدة البيانات
  static Future<Map<String, dynamic>?> _performDirectSearch(
      String cleanCommand) async {
    // البحث المحسن في العملاء والموردين
    final entityResult = await _searchCustomersAndSuppliers(cleanCommand);
    if (entityResult != null) {
      return entityResult;
    }

    // البحث المحسن عن المنتجات
    final productResult = await _searchProducts(cleanCommand);
    if (productResult != null) {
      return productResult;
    }

    return null;
  }

  /// البحث المحسن في المنتجات
  static Future<Map<String, dynamic>?> _searchProducts(String query) async {
    try {
      print('🔍 بدء البحث عن المنتج: "$query"');

      final userId = await _getConstUserId();
      if (userId.isEmpty) {
        print('❌ معرف المستخدم فارغ');
        return null;
      }

      final productsRef =
          FirebaseDatabase.instance.ref(userId).child('Products');
      final snapshot = await productsRef.get();

      if (!snapshot.exists) {
        print('❌ لا توجد منتجات في قاعدة البيانات');
        return null;
      }

      print('📦 عدد المنتجات في قاعدة البيانات: ${snapshot.children.length}');

      // البحث في جميع المنتجات
      for (var element in snapshot.children) {
        try {
          var productData = jsonDecode(jsonEncode(element.value));
          final productName = productData['productName']?.toString() ?? '';
          final productCode = productData['productCode']?.toString() ?? '';
          final productCategory =
              productData['productCategory']?.toString() ?? '';
          final brandName = productData['brandName']?.toString() ?? '';
          final productDescription =
              productData['productDescription']?.toString() ?? '';

          print(
              '🔍 فحص المنتج: $productName (الكود: $productCode, الفئة: $productCategory)');

          // التحقق من تطابق البحث (أكثر شمولية)
          if (_isProductMatch(productName, productCode, productCategory,
              brandName, productDescription, query)) {
            final productStock = productData['productStock']?.toString() ?? '0';
            final productSalePrice =
                productData['productSalePrice']?.toString() ?? '0';
            final productPurchasePrice =
                productData['productPurchasePrice']?.toString() ?? '0';

            print('✅ تم العثور على تطابق: $productName');
            print('📋 السعر: $productSalePrice جنيه، المخزون: $productStock');

            // إعداد بيانات المنتج
            final productResult = {
              'id': element.key,
              'productName': productName,
              'name': productName,
              'productCode': productCode,
              'code': productCode,
              'productCategory': productCategory,
              'category': productCategory,
              'brandName': brandName,
              'brand': brandName,
              'productDescription': productDescription,
              'description': productDescription,
              'productSalePrice': productSalePrice,
              'price': productSalePrice,
              'productPurchasePrice': productPurchasePrice,
              'purchase_price': productPurchasePrice,
              'productStock': productStock,
              'stock': productStock,
              'quantity': productStock,
              'productUnit': productData['productUnit'] ?? 'قطعة',
              'unit': productData['productUnit'] ?? 'قطعة',
              'lowerStockAlert': productData['lowerStockAlert'] ?? '5',
              'low_stock_alert': productData['lowerStockAlert'] ?? '5',
              'productManufacturer': productData['productManufacturer'] ?? '',
              'manufacturer': productData['productManufacturer'] ?? '',
              'size': productData['size'] ?? '',
              'color': productData['color'] ?? '',
              'weight': productData['weight'] ?? '',
              'capacity': productData['capacity'] ?? '',
              'type': productData['type'] ?? '',
              'warehouseName': productData['warehouseName'] ?? '',
              'warehouseId': productData['warehouseId'] ?? '',
              'productPicture': productData['productPicture'] ?? '',
              'status': _getProductStatus(productData),
            };

            return {
              'success': true,
              'message':
                  'تم العثور على المنتج: $productName\nالسعر: $productSalePrice جنيه\nالمخزون: $productStock ${productData['productUnit'] ?? 'قطعة'}',
              'action': 'search_product',
              'data': productResult
            };
          }
        } catch (e) {
          print('❌ خطأ في معالجة منتج: $e');
          continue;
        }
      }

      print('❌ لم يتم العثور على منتج يطابق البحث: "$query"');
      return null;
    } catch (e) {
      print('❌ خطأ في البحث عن المنتجات: $e');
      return null;
    }
  }

  /// فحص تطابق المنتج مع البحث
  static bool _isProductMatch(
      String productName,
      String productCode,
      String productCategory,
      String brandName,
      String productDescription,
      String query) {
    final lowerQuery = query.toLowerCase();

    // البحث في الاسم
    if (productName.toLowerCase().contains(lowerQuery)) {
      return true;
    }

    // البحث في الكود
    if (productCode.toLowerCase().contains(lowerQuery)) {
      return true;
    }

    // البحث في الفئة
    if (productCategory.toLowerCase().contains(lowerQuery)) {
      return true;
    }

    // البحث في العلامة التجارية
    if (brandName.toLowerCase().contains(lowerQuery)) {
      return true;
    }

    // البحث في الوصف
    if (productDescription.toLowerCase().contains(lowerQuery)) {
      return true;
    }

    // البحث الضبابي للأسماء المشابهة
    if (_isSimilar(productName, query)) {
      return true;
    }

    return false;
  }

  /// تحديد حالة المنتج
  static String _getProductStatus(Map<String, dynamic> productData) {
    final stock =
        int.tryParse(productData['productStock']?.toString() ?? '0') ?? 0;
    final lowStockAlert =
        int.tryParse(productData['lowerStockAlert']?.toString() ?? '5') ?? 5;

    if (stock == 0) {
      return 'نفد من المخزون';
    } else if (stock <= lowStockAlert) {
      return 'مخزون منخفض';
    } else {
      return 'متوفر';
    }
  }

  /// البحث المحسن في العملاء والموردين
  static Future<Map<String, dynamic>?> _searchCustomersAndSuppliers(
      String query) async {
    try {
      print('🔍 بدء البحث عن: "$query"');

      final userId = await _getConstUserId();
      if (userId.isEmpty) {
        print('❌ معرف المستخدم فارغ');
        return null;
      }

      final customersRef =
          FirebaseDatabase.instance.ref(userId).child('Customers');
      final snapshot = await customersRef.get();

      if (!snapshot.exists) {
        print('❌ لا توجد بيانات عملاء في قاعدة البيانات');
        return null;
      }

      print('📊 عدد العناصر في قاعدة البيانات: ${snapshot.children.length}');

      // البحث في جميع العناصر
      for (var element in snapshot.children) {
        try {
          var customerData = jsonDecode(jsonEncode(element.value));
          final customerName = customerData['customerName']?.toString() ?? '';
          final customerPhone = customerData['phoneNumber']?.toString() ?? '';
          final customerEmail = customerData['emailAddress']?.toString() ?? '';
          final type = customerData['type']?.toString() ?? '';
          final customerType = customerData['customerType']?.toString() ?? '';

          print(
              '🔍 فحص: $customerName (النوع: $type, نوع العميل: $customerType)');

          // التحقق من تطابق البحث
          if (customerName.toLowerCase().contains(query.toLowerCase()) ||
              customerPhone.contains(query) ||
              customerEmail.toLowerCase().contains(query.toLowerCase())) {
            bool isSupplier = _isSupplierType(type, customerType);

            print('✅ تم العثور على تطابق: $customerName');
            print('📋 تصنيف: ${isSupplier ? "مورد" : "عميل"}');

            // إعداد البيانات
            final entityData = {
              'id': element.key,
              'customerName': customerName,
              'name': customerName,
              'phoneNumber': customerPhone,
              'phone': customerPhone,
              'emailAddress': customerEmail,
              'email': customerEmail,
              'customerAddress': customerData['customerAddress'] ?? '',
              'address': customerData['customerAddress'] ?? '',
              'type': type,
              'customerType': customerType,
              'total_purchases': 0,
            };

            return {
              'success': true,
              'message': isSupplier
                  ? 'تم العثور على المورد: $customerName\nرقم الهاتف: $customerPhone'
                  : 'تم العثور على العميل: $customerName\nرقم الهاتف: $customerPhone',
              'action': 'show_invoice_screen',
              'data': {
                'entity': entityData,
                'entity_type': isSupplier ? 'supplier' : 'customer',
                'is_purchase': isSupplier,
              }
            };
          }
        } catch (e) {
          print('❌ خطأ في معالجة عنصر: $e');
          continue;
        }
      }

      print('❌ لم يتم العثور على تطابق للبحث: "$query"');
      return null;
    } catch (e) {
      print('❌ خطأ في البحث: $e');
      return null;
    }
  }

  /// البحث الذكي باستخدام الذكاء الاصطناعي مع السياق
  static Future<Map<String, dynamic>> _performIntelligentSearch(
      String originalCommand, String cleanCommand) async {
    try {
      // جمع بيانات السياق من قاعدة البيانات
      final context = await _buildDatabaseContext();

      // استخدام الذكاء الاصطناعي مع السياق الكامل
      final aiResponse = await GeminiService.processQuickAICommand(
          originalCommand,
          context: context);

      // تحليل رد الذكاء الاصطناعي للبحث عن أسماء محتملة
      final intelligentResult =
          await _analyzeAIResponse(aiResponse, originalCommand);
      if (intelligentResult != null) {
        return intelligentResult;
      }

      return {
        'success': true,
        'message': aiResponse,
        'action': 'show_response',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'حدث خطأ في البحث الذكي: $e',
      };
    }
  }

  /// بناء سياق قاعدة البيانات للذكاء الاصطناعي
  static Future<String> _buildDatabaseContext() async {
    try {
      final context = StringBuffer();

      // إضافة بيانات العملاء
      final customersList = await _getCustomersList();
      if (customersList.isNotEmpty) {
        context.writeln('العملاء المتاحون:');
        for (var customer in customersList.take(10)) {
          context.writeln('- ${customer['name']} (${customer['phone']})');
        }
      }

      // إضافة بيانات الموردين
      final suppliersList = await _getSuppliersList();
      if (suppliersList.isNotEmpty) {
        context.writeln('\nالموردين المتاحون:');
        for (var supplier in suppliersList.take(10)) {
          context.writeln('- ${supplier['name']} (${supplier['phone']})');
        }
      }

      // إضافة بيانات المنتجات
      final productsList = await _getProductsList();
      if (productsList.isNotEmpty) {
        context.writeln('\nالمنتجات المتاحة:');
        for (var product in productsList.take(15)) {
          context.writeln('- ${product['name']} (${product['price']} جنيه)');
        }
      }

      return context.toString();
    } catch (e) {
      return 'لا توجد بيانات متاحة في الوقت الحالي.';
    }
  }

  /// تحليل رد الذكاء الاصطناعي للبحث عن نتائج محتملة
  static Future<Map<String, dynamic>?> _analyzeAIResponse(
      String aiResponse, String originalCommand) async {
    try {
      // استخراج الأسماء المحتملة من رد الذكاء الاصطناعي
      final words = originalCommand.split(' ');

      for (String word in words) {
        if (word.length > 2) {
          // محاولة البحث الضبابي
          final fuzzyResult = await _performFuzzySearch(word);
          if (fuzzyResult != null) {
            return fuzzyResult;
          }
        }
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  /// البحث الضبابي للأسماء المشابهة
  static Future<Map<String, dynamic>?> _performFuzzySearch(String query) async {
    try {
      // البحث الضبابي في العملاء
      final customersList = await _getCustomersList();
      for (var customer in customersList) {
        if (_isSimilar(customer['name'], query)) {
          return {
            'success': true,
            'message': 'هل تقصد العميل: ${customer['name']}؟',
            'action': 'show_invoice_screen',
            'data': {
              'entity': customer,
              'entity_type': 'customer',
              'is_purchase': false,
            }
          };
        }
      }

      // البحث الضبابي في الموردين
      final suppliersList = await _getSuppliersList();
      for (var supplier in suppliersList) {
        if (_isSimilar(supplier['name'], query)) {
          return {
            'success': true,
            'message': 'هل تقصد المورد: ${supplier['name']}؟',
            'action': 'show_invoice_screen',
            'data': {
              'entity': supplier,
              'entity_type': 'supplier',
              'is_purchase': true,
            }
          };
        }
      }

      // البحث الضبابي المحسن في المنتجات
      final productsList = await _getProductsList();
      for (var product in productsList) {
        // البحث الضبابي الشامل
        if (_isProductMatch(
            product['name'],
            product['code'],
            product['category'],
            product['brand'] ?? '',
            product['description'] ?? '',
            query)) {
          print(
              '🔍 البحث الضبابي: تم العثور على منتج مشابه: ${product['name']}');

          return {
            'success': true,
            'message':
                'هل تقصد المنتج: ${product['name']}؟\nالسعر: ${product['price']} جنيه\nالمخزون: ${product['stock']}',
            'action': 'search_product',
            'data': product
          };
        }
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  /// الحصول على قائمة العملاء
  static Future<List<Map<String, dynamic>>> _getCustomersList() async {
    try {
      final userId = await _getConstUserId();
      if (userId.isEmpty) return [];

      final customersRef =
          FirebaseDatabase.instance.ref(userId).child('Customers');
      final snapshot = await customersRef.get();

      if (!snapshot.exists) return [];

      List<Map<String, dynamic>> customersList = [];
      for (var element in snapshot.children) {
        try {
          var customerData = jsonDecode(jsonEncode(element.value));

          // تحديد ما إذا كان العنصر عميل أم مورد
          final type = customerData['type']?.toString() ?? '';
          final customerType = customerData['customerType']?.toString() ?? '';

          // العملاء: كل شيء ما عدا الموردين
          bool isCustomer = _isCustomerType(type, customerType);

          if (isCustomer) {
            customersList.add({
              'id': element.key,
              'customerName': customerData['customerName'] ?? '',
              'name': customerData['customerName'] ?? '',
              'phoneNumber': customerData['phoneNumber'] ?? '',
              'phone': customerData['phoneNumber'] ?? '',
              'emailAddress': customerData['emailAddress'] ?? '',
              'email': customerData['emailAddress'] ?? '',
              'customerAddress': customerData['customerAddress'] ?? '',
              'address': customerData['customerAddress'] ?? '',
              'type': type,
              'customerType': customerType,
              'total_purchases': 0,
            });
          }
        } catch (e) {
          continue;
        }
      }

      return customersList;
    } catch (e) {
      return [];
    }
  }

  /// الحصول على قائمة الموردين
  static Future<List<Map<String, dynamic>>> _getSuppliersList() async {
    try {
      final userId = await _getConstUserId();
      if (userId.isEmpty) return [];

      final customersRef =
          FirebaseDatabase.instance.ref(userId).child('Customers');
      final snapshot = await customersRef.get();

      if (!snapshot.exists) return [];

      List<Map<String, dynamic>> suppliersList = [];
      for (var element in snapshot.children) {
        try {
          var customerData = jsonDecode(jsonEncode(element.value));

          // تحديد ما إذا كان العنصر مورد
          final type = customerData['type']?.toString() ?? '';
          final customerType = customerData['customerType']?.toString() ?? '';

          // الموردين: type = "Supplier" فقط
          bool isSupplier = _isSupplierType(type, customerType);

          if (isSupplier) {
            suppliersList.add({
              'id': element.key,
              'customerName': customerData['customerName'] ?? '',
              'name': customerData['customerName'] ?? '',
              'phoneNumber': customerData['phoneNumber'] ?? '',
              'phone': customerData['phoneNumber'] ?? '',
              'emailAddress': customerData['emailAddress'] ?? '',
              'email': customerData['emailAddress'] ?? '',
              'customerAddress': customerData['customerAddress'] ?? '',
              'address': customerData['customerAddress'] ?? '',
              'type': type,
              'customerType': customerType,
              'total_purchases': 0,
            });
          }
        } catch (e) {
          continue;
        }
      }

      return suppliersList;
    } catch (e) {
      return [];
    }
  }

  /// تحديد ما إذا كان العنصر عميل
  static bool _isCustomerType(String type, String customerType) {
    // العملاء: كل شيء ما عدا الموردين
    return type != 'Supplier';
  }

  /// تحديد ما إذا كان العنصر مورد
  static bool _isSupplierType(String type, String customerType) {
    // الموردين: type = "Supplier" فقط
    return type == 'Supplier';
  }

  /// الحصول على قائمة المنتجات
  static Future<List<Map<String, dynamic>>> _getProductsList() async {
    try {
      final userId = await _getConstUserId();
      if (userId.isEmpty) return [];

      final productsRef =
          FirebaseDatabase.instance.ref(userId).child('Products');
      final snapshot = await productsRef.get();

      if (!snapshot.exists) return [];

      List<Map<String, dynamic>> productsList = [];
      for (var element in snapshot.children) {
        try {
          var productData = jsonDecode(jsonEncode(element.value));
          productsList.add({
            'id': element.key,
            'productName': productData['productName'] ?? '',
            'name': productData['productName'] ?? '',
            'productSalePrice': productData['productSalePrice'] ?? '0',
            'price': productData['productSalePrice'] ?? '0',
            'productStock': productData['productStock'] ?? '0',
            'stock': productData['productStock'] ?? '0',
            'quantity': productData['productStock'] ?? '0',
            'productCode': productData['productCode'] ?? '',
            'code': productData['productCode'] ?? '',
            'productCategory': productData['productCategory'] ?? '',
            'category': productData['productCategory'] ?? '',
          });
        } catch (e) {
          continue;
        }
      }

      return productsList;
    } catch (e) {
      return [];
    }
  }

  /// فحص التشابه بين النصوص
  static bool _isSimilar(String text1, String text2) {
    if (text1.isEmpty || text2.isEmpty) return false;

    final clean1 = text1.toLowerCase().trim();
    final clean2 = text2.toLowerCase().trim();

    // فحص التطابق الجزئي
    if (clean1.contains(clean2) || clean2.contains(clean1)) {
      return true;
    }

    // فحص التشابه الصوتي للأسماء العربية
    return _arabicPhoneticSimilarity(clean1, clean2);
  }

  /// فحص التشابه الصوتي للأسماء العربية
  static bool _arabicPhoneticSimilarity(String name1, String name2) {
    // إزالة التشكيل والحروف المتشابهة صوتياً
    final normalized1 = _normalizeArabicText(name1);
    final normalized2 = _normalizeArabicText(name2);

    // فحص التطابق بعد التطبيع
    if (normalized1.contains(normalized2) ||
        normalized2.contains(normalized1)) {
      return true;
    }

    // فحص المسافة بين النصوص
    return _levenshteinDistance(normalized1, normalized2) <= 2;
  }

  /// تطبيع النص العربي
  static String _normalizeArabicText(String text) {
    return text
        .replaceAll('أ', 'ا')
        .replaceAll('إ', 'ا')
        .replaceAll('آ', 'ا')
        .replaceAll('ة', 'ه')
        .replaceAll('ى', 'ي')
        .replaceAll('ؤ', 'و')
        .replaceAll('ئ', 'ي');
  }

  /// حساب مسافة Levenshtein
  static int _levenshteinDistance(String s1, String s2) {
    if (s1.isEmpty) return s2.length;
    if (s2.isEmpty) return s1.length;

    List<List<int>> matrix = List.generate(
      s1.length + 1,
      (i) => List.generate(s2.length + 1, (j) => 0),
    );

    for (int i = 0; i <= s1.length; i++) {
      matrix[i][0] = i;
    }
    for (int j = 0; j <= s2.length; j++) {
      matrix[0][j] = j;
    }

    for (int i = 1; i <= s1.length; i++) {
      for (int j = 1; j <= s2.length; j++) {
        int cost = s1[i - 1] == s2[j - 1] ? 0 : 1;
        matrix[i][j] = [
          matrix[i - 1][j] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j - 1] + cost,
        ].reduce((a, b) => a < b ? a : b);
      }
    }

    return matrix[s1.length][s2.length];
  }

  /// تنظيف الأمر العربي وإزالة حرف "ل" التعريفي
  static String _cleanArabicCommand(String command) {
    String cleaned = command.trim();

    // إزالة حرف "ل" التعريفي من بداية الكلمات
    if (cleaned.startsWith('ل')) {
      cleaned = cleaned.substring(1);
    }

    // إزالة كلمات البحث الشائعة
    final searchWords = ['ابحث', 'عن', 'اعرض', 'أريد', 'عايز', 'بدي'];
    for (String word in searchWords) {
      cleaned = cleaned.replaceAll(word, '').trim();
    }

    return cleaned;
  }
}

// خدمات إضافية مطلوبة
class VoiceAssistantDiagnostics {
  static final VoiceAssistantCoreService _coreService =
      VoiceAssistantCoreService();

  Future<Map<String, dynamic>> runFullDiagnostics() async {
    try {
      final status = _coreService.getStatus();
      final isInitialized = await _coreService.initialize();

      return {
        'status': isInitialized ? 'متاح' : 'غير متاح',
        'details': 'تم فحص جميع الخدمات',
        'core_service': status,
        'gemini_service': 'متاح',
        'database_service': 'متاح',
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {
        'status': 'خطأ',
        'details': 'حدث خطأ في الفحص: $e',
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  Future<Map<String, dynamic>> runQuickTest() async {
    try {
      // اختبار سريع للخدمات الأساسية
      final geminiTest = await GeminiService.sendMessage('اختبار سريع');
      final dbTest = await DatabaseService.getAllBusinessData();

      return {
        'overall_result': 'success',
        'details': 'جميع الاختبارات نجحت',
        'gemini_test': geminiTest.isNotEmpty ? 'نجح' : 'فشل',
        'database_test': dbTest.isNotEmpty ? 'نجح' : 'فشل',
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {
        'overall_result': 'error',
        'details': 'فشل في الاختبار: $e',
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }
}

class VoiceAssistantInitializer {
  static final VoiceAssistantInitializationManager _initManager =
      VoiceAssistantInitializationManager();

  Future<bool> initializeGlobalVoiceAssistant() async {
    try {
      final initialized = await _initManager.initializeGlobalVoiceAssistant();
      if (initialized) {
        await _initManager.coreService.start();
      }
      return initialized;
    } catch (e) {
      return false;
    }
  }

  Map<String, dynamic> getVoiceAssistantStatus() {
    try {
      return _initManager.getInitializationStatus();
    } catch (e) {
      return {'isInitialized': false, 'status': 'غير مفعل'};
    }
  }

  Future<Map<String, bool>> checkRequiredPermissions() async {
    try {
      final report =
          await _initManager.permissionsService.getPermissionsReport();
      final essential = report['essential'] ?? {};
      final optional = report['optional'] ?? {};

      return {
        'microphone': essential['microphone'] ?? false,
        'storage': optional['storage'] ?? false,
        'overlay': optional['systemAlertWindow'] ?? false,
      };
    } catch (e) {
      return {
        'microphone': false,
        'storage': false,
        'overlay': false,
      };
    }
  }

  Future<void> resetVoiceAssistant() async {
    try {
      await _initManager.coreService.stop();
      await _initManager.initializeGlobalVoiceAssistant();
    } catch (e) {
      // تجاهل الأخطاء
    }
  }

  Future<List<String>> diagnoseIssues() async {
    try {
      final issues = <String>[];
      final status = _initManager.getInitializationStatus();

      if (status['isInitialized'] != true) {
        issues.add('الخدمة غير مهيأة');
      }

      if (status['isEnabled'] != true) {
        issues.add('الخدمة غير مفعلة');
      }

      final permissions = await checkRequiredPermissions();
      if (permissions['microphone'] != true) {
        issues.add('إذن الميكروفون غير ممنوح');
      }

      return issues.isEmpty ? ['جميع الخدمات تعمل بشكل صحيح'] : issues;
    } catch (e) {
      return ['خطأ في التشخيص: $e'];
    }
  }
}

class PersistentVoiceService {
  static final VoiceAssistantCoreService _coreService =
      VoiceAssistantCoreService();

  Stream<PersistentVoiceEvent> get eventStream => Stream.empty();

  Map<String, dynamic> getServiceStatus() {
    try {
      final status = _coreService.getStatus();
      return {
        'isRunning': status['isRunning'] ?? false,
        'status': status['isEnabled'] == true ? 'مفعل' : 'غير مفعل',
        'isInitialized': status['isInitialized'] ?? false,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {
        'isRunning': false,
        'status': 'غير مفعل',
        'error': e.toString(),
      };
    }
  }
}

class PersistentVoiceEvent {
  final PersistentVoiceEventType type;

  PersistentVoiceEvent(this.type);
}

enum PersistentVoiceEventType {
  started,
  stopped,
  error,
}

class OverlayTestHelper {
  static Future<bool> quickTest() async {
    try {
      final coreService = VoiceAssistantCoreService();
      final initialized = await coreService.initialize();
      return initialized;
    } catch (e) {
      return false;
    }
  }

  static Future<Map<String, dynamic>> runComprehensiveTest() async {
    try {
      final coreService = VoiceAssistantCoreService();
      final permissionsService = VoiceAssistantPermissionsService();

      // اختبار التهيئة
      final initialized = await coreService.initialize();

      // اختبار الأذونات
      final permissionsReport = await permissionsService.getPermissionsReport();

      // اختبار الخدمات الأساسية
      final geminiTest = await GeminiService.sendMessage('اختبار شامل');
      final dbTest = await DatabaseService.getAllBusinessData();

      return {
        'timestamp': DateTime.now().toIso8601String(),
        'success': initialized,
        'message':
            initialized ? 'جميع الاختبارات نجحت' : 'فشل في بعض الاختبارات',
        'details': {
          'core_service': initialized ? 'نجح' : 'فشل',
          'permissions': permissionsReport,
          'gemini_service': geminiTest.isNotEmpty ? 'نجح' : 'فشل',
          'database_service': dbTest.isNotEmpty ? 'نجح' : 'فشل',
        }
      };
    } catch (e) {
      return {
        'timestamp': DateTime.now().toIso8601String(),
        'success': false,
        'message': 'حدث خطأ في الاختبار: $e'
      };
    }
  }
}
