import 'package:mobile_pos/model/water_filter_models.dart';
import 'package:mobile_pos/services/water_filter_service.dart';
import 'package:flutter/material.dart';

class MaintenanceService {
  static const String _basePath = 'WaterFilters/Maintenance';

  /// إنشاء جدولة صيانة جديدة
  static Future<bool> createSchedule(MaintenanceSchedule schedule) async {
    try {
      final success = await WaterFilterService.saveData(
        '$_basePath/Schedules/${schedule.id}',
        schedule.toJson(),
      );
      
      if (success) {
        // إنشاء تذكير تلقائي إذا كانت الصيانة دورية
        if (schedule.isRecurring) {
          await _createRecurringReminders(schedule);
        }
      }
      
      return success;
    } catch (e) {
      debugPrint('خطأ في إنشاء جدولة الصيانة: $e');
      return false;
    }
  }

  /// تحديث جدولة الصيانة
  static Future<bool> updateSchedule(MaintenanceSchedule schedule) async {
    try {
      schedule.updatedAt = DateTime.now();
      return await WaterFilterService.saveData(
        '$_basePath/Schedules/${schedule.id}',
        schedule.toJson(),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث جدولة الصيانة: $e');
      return false;
    }
  }

  /// حذف جدولة الصيانة
  static Future<bool> deleteSchedule(String scheduleId) async {
    try {
      return await WaterFilterService.deleteData(
        '$_basePath/Schedules/$scheduleId',
      );
    } catch (e) {
      debugPrint('خطأ في حذف جدولة الصيانة: $e');
      return false;
    }
  }

  /// الحصول على جميع جدولات الصيانة
  static Future<List<MaintenanceSchedule>> getAllSchedules() async {
    try {
      final data = await WaterFilterService.getData('$_basePath/Schedules');
      final schedules = <MaintenanceSchedule>[];
      
      data.forEach((key, value) {
        try {
          final schedule = MaintenanceSchedule.fromJson(
            Map<String, dynamic>.from(value),
          );
          schedules.add(schedule);
        } catch (e) {
          debugPrint('خطأ في معالجة جدولة صيانة: $e');
        }
      });
      
      // ترتيب حسب التاريخ
      schedules.sort((a, b) => a.scheduledDateTime.compareTo(b.scheduledDateTime));
      
      return schedules;
    } catch (e) {
      debugPrint('خطأ في تحميل جدولات الصيانة: $e');
      return [];
    }
  }

  /// الحصول على جدولات الصيانة لنظام معين
  static Future<List<MaintenanceSchedule>> getSchedulesForSystem(String systemId) async {
    try {
      final allSchedules = await getAllSchedules();
      return allSchedules.where((schedule) => schedule.systemId == systemId).toList();
    } catch (e) {
      debugPrint('خطأ في تحميل جدولات الصيانة للنظام: $e');
      return [];
    }
  }

  /// الحصول على جدولات الصيانة لفني معين
  static Future<List<MaintenanceSchedule>> getSchedulesForTechnician(String technicianId) async {
    try {
      final allSchedules = await getAllSchedules();
      return allSchedules.where((schedule) => schedule.technicianId == technicianId).toList();
    } catch (e) {
      debugPrint('خطأ في تحميل جدولات الصيانة للفني: $e');
      return [];
    }
  }

  /// الحصول على جدولات الصيانة لتاريخ معين
  static Future<List<MaintenanceSchedule>> getSchedulesForDate(DateTime date) async {
    try {
      final allSchedules = await getAllSchedules();
      return allSchedules.where((schedule) {
        return schedule.scheduledDate.year == date.year &&
               schedule.scheduledDate.month == date.month &&
               schedule.scheduledDate.day == date.day;
      }).toList();
    } catch (e) {
      debugPrint('خطأ في تحميل جدولات الصيانة للتاريخ: $e');
      return [];
    }
  }

  /// الحصول على الجدولات المتأخرة
  static Future<List<MaintenanceSchedule>> getOverdueSchedules() async {
    try {
      final allSchedules = await getAllSchedules();
      return allSchedules.where((schedule) => schedule.isOverdue).toList();
    } catch (e) {
      debugPrint('خطأ في تحميل الجدولات المتأخرة: $e');
      return [];
    }
  }

  /// الحصول على الجدولات القادمة (خلال أسبوع)
  static Future<List<MaintenanceSchedule>> getUpcomingSchedules() async {
    try {
      final allSchedules = await getAllSchedules();
      final now = DateTime.now();
      final nextWeek = now.add(const Duration(days: 7));
      
      return allSchedules.where((schedule) {
        return schedule.scheduledDateTime.isAfter(now) &&
               schedule.scheduledDateTime.isBefore(nextWeek) &&
               schedule.status != ScheduleStatus.completed &&
               schedule.status != ScheduleStatus.cancelled;
      }).toList();
    } catch (e) {
      debugPrint('خطأ في تحميل الجدولات القادمة: $e');
      return [];
    }
  }

  /// إنشاء سجل صيانة
  static Future<bool> createMaintenanceRecord(MaintenanceRecord record) async {
    try {
      final success = await WaterFilterService.saveData(
        '$_basePath/Records/${record.id}',
        record.toJson(),
      );
      
      if (success) {
        // تحديث حالة الجدولة إلى مكتملة
        await _updateScheduleStatus(record.scheduleId, ScheduleStatus.completed);
        
        // تحديث تاريخ الصيانة التالية للنظام
        if (record.nextMaintenanceDate != null) {
          await _updateSystemNextMaintenance(record.systemId, record.nextMaintenanceDate!);
        }
      }
      
      return success;
    } catch (e) {
      debugPrint('خطأ في إنشاء سجل الصيانة: $e');
      return false;
    }
  }

  /// الحصول على سجلات الصيانة لنظام معين
  static Future<List<MaintenanceRecord>> getMaintenanceRecordsForSystem(String systemId) async {
    try {
      final data = await WaterFilterService.getData('$_basePath/Records');
      final records = <MaintenanceRecord>[];
      
      data.forEach((key, value) {
        try {
          final record = MaintenanceRecord.fromJson(
            Map<String, dynamic>.from(value),
          );
          if (record.systemId == systemId) {
            records.add(record);
          }
        } catch (e) {
          debugPrint('خطأ في معالجة سجل صيانة: $e');
        }
      });
      
      // ترتيب حسب التاريخ (الأحدث أولاً)
      records.sort((a, b) => b.startTime.compareTo(a.startTime));
      
      return records;
    } catch (e) {
      debugPrint('خطأ في تحميل سجلات الصيانة: $e');
      return [];
    }
  }

  /// إنشاء جدولة صيانة دورية تلقائية
  static Future<void> createAutomaticMaintenanceSchedule(String systemId) async {
    try {
      // الحصول على بيانات النظام
      final systemData = await WaterFilterService.getData('Systems/$systemId');
      if (systemData.isEmpty) return;
      
      final system = WaterFilterSystem.fromJson(systemData);
      
      // الحصول على بيانات المنتج لمعرفة فترة الصيانة
      final productData = await WaterFilterService.getData('Products/${system.productId}');
      if (productData.isEmpty) return;
      
      final product = WaterFilterProduct.fromJson(productData);
      
      // إنشاء جدولة صيانة دورية
      final schedule = MaintenanceSchedule(
        id: WaterFilterService.generateId(),
        systemId: systemId,
        customerId: system.customerId,
        scheduledDate: system.nextMaintenanceDate,
        scheduledTime: const TimeOfDay(hour: 9, minute: 0),
        type: MaintenanceType.routine,
        priority: MaintenancePriority.normal,
        status: ScheduleStatus.scheduled,
        description: 'صيانة دورية للنظام ${system.serialNumber}',
        estimatedDuration: const Duration(hours: 1),
        estimatedCost: product.maintenanceCost,
        isRecurring: true,
        recurringIntervalDays: product.maintenanceIntervalMonths * 30,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      await createSchedule(schedule);
    } catch (e) {
      debugPrint('خطأ في إنشاء جدولة صيانة تلقائية: $e');
    }
  }

  /// إنشاء تذكيرات متكررة
  static Future<void> _createRecurringReminders(MaintenanceSchedule schedule) async {
    if (!schedule.isRecurring || schedule.recurringIntervalDays == null) return;
    
    try {
      // إنشاء 3 جدولات مستقبلية
      for (int i = 1; i <= 3; i++) {
        final nextDate = schedule.scheduledDate.add(
          Duration(days: schedule.recurringIntervalDays! * i),
        );
        
        final nextSchedule = MaintenanceSchedule(
          id: WaterFilterService.generateId(),
          systemId: schedule.systemId,
          customerId: schedule.customerId,
          technicianId: schedule.technicianId,
          scheduledDate: nextDate,
          scheduledTime: schedule.scheduledTime,
          type: schedule.type,
          priority: schedule.priority,
          status: ScheduleStatus.scheduled,
          description: schedule.description,
          estimatedDuration: schedule.estimatedDuration,
          estimatedCost: schedule.estimatedCost,
          isRecurring: true,
          recurringIntervalDays: schedule.recurringIntervalDays,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        
        await WaterFilterService.saveData(
          '$_basePath/Schedules/${nextSchedule.id}',
          nextSchedule.toJson(),
        );
      }
    } catch (e) {
      debugPrint('خطأ في إنشاء التذكيرات المتكررة: $e');
    }
  }

  /// تحديث حالة الجدولة
  static Future<void> _updateScheduleStatus(String scheduleId, ScheduleStatus status) async {
    try {
      final scheduleData = await WaterFilterService.getData('$_basePath/Schedules/$scheduleId');
      if (scheduleData.isNotEmpty) {
        final schedule = MaintenanceSchedule.fromJson(scheduleData);
        schedule.status = status;
        schedule.updatedAt = DateTime.now();
        
        await WaterFilterService.saveData(
          '$_basePath/Schedules/$scheduleId',
          schedule.toJson(),
        );
      }
    } catch (e) {
      debugPrint('خطأ في تحديث حالة الجدولة: $e');
    }
  }

  /// تحديث تاريخ الصيانة التالية للنظام
  static Future<void> _updateSystemNextMaintenance(String systemId, DateTime nextMaintenanceDate) async {
    try {
      final systemData = await WaterFilterService.getData('Systems/$systemId');
      if (systemData.isNotEmpty) {
        final system = WaterFilterSystem.fromJson(systemData);
        system.nextMaintenanceDate = nextMaintenanceDate;
        system.lastMaintenanceDate = DateTime.now();
        system.updatedAt = DateTime.now();
        
        await WaterFilterService.saveData(
          'Systems/$systemId',
          system.toJson(),
        );
      }
    } catch (e) {
      debugPrint('خطأ في تحديث تاريخ الصيانة للنظام: $e');
    }
  }
}
