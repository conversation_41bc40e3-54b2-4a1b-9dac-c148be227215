import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/model/water_filter_models.dart';
import 'package:mobile_pos/services/water_filter_service.dart';

class SalesReportScreen extends StatefulWidget {
  const SalesReportScreen({super.key});

  @override
  State<SalesReportScreen> createState() => _SalesReportScreenState();
}

class _SalesReportScreenState extends State<SalesReportScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  List<WaterFilterSystem> _allSales = [];
  List<WaterFilterCustomer> _allCustomers = [];
  List<WaterFilterProduct> _allProducts = [];

  // إحصائيات المبيعات
  Map<String, dynamic> _stats = {};

  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadSalesData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadSalesData() async {
    setState(() => _isLoading = true);

    try {
      // تحميل الأنظمة (المبيعات)
      final systemsData = await WaterFilterService.getData('Systems');
      final sales = <WaterFilterSystem>[];

      systemsData.forEach((key, value) {
        try {
          final system = WaterFilterSystem.fromJson(
            Map<String, dynamic>.from(value),
          );
          sales.add(system);
        } catch (e) {
          debugPrint('خطأ في معالجة مبيعة: $e');
        }
      });

      // تحميل العملاء
      final customersData = await WaterFilterService.getData('Customers');
      final customers = <WaterFilterCustomer>[];

      customersData.forEach((key, value) {
        try {
          final customer = WaterFilterCustomer.fromJson(
            Map<String, dynamic>.from(value),
          );
          customers.add(customer);
        } catch (e) {
          debugPrint('خطأ في معالجة عميل: $e');
        }
      });

      // تحميل المنتجات
      final productsData = await WaterFilterService.getData('Products');
      final products = <WaterFilterProduct>[];

      productsData.forEach((key, value) {
        try {
          final product = WaterFilterProduct.fromJson(
            Map<String, dynamic>.from(value),
          );
          products.add(product);
        } catch (e) {
          debugPrint('خطأ في معالجة منتج: $e');
        }
      });

      setState(() {
        _allSales = sales;
        _allCustomers = customers;
        _allProducts = products;
        _calculateStats();
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات المبيعات: $e');
      setState(() => _isLoading = false);
    }
  }

  void _calculateStats() {
    final totalSales = _allSales.length;

    // إحصائيات مالية
    final totalRevenue =
        _allSales.fold(0.0, (sum, sale) => sum + sale.totalCost);
    final totalPaid = _allSales.fold(0.0, (sum, sale) => sum + sale.paidAmount);
    final totalRemaining =
        _allSales.fold(0.0, (sum, sale) => sum + sale.remainingAmount);

    // المبيعات هذا الشهر
    final now = DateTime.now();
    final thisMonthSales = _allSales
        .where((s) =>
            s.installationDate.year == now.year &&
            s.installationDate.month == now.month)
        .length;

    final thisMonthRevenue = _allSales
        .where((s) =>
            s.installationDate.year == now.year &&
            s.installationDate.month == now.month)
        .fold(0.0, (sum, sale) => sum + sale.totalCost);

    // المبيعات اليوم
    final todaySales = _allSales
        .where((s) =>
            s.installationDate.year == now.year &&
            s.installationDate.month == now.month &&
            s.installationDate.day == now.day)
        .length;

    final todayRevenue = _allSales
        .where((s) =>
            s.installationDate.year == now.year &&
            s.installationDate.month == now.month &&
            s.installationDate.day == now.day)
        .fold(0.0, (sum, sale) => sum + sale.totalCost);

    // أفضل العملاء (حسب قيمة المشتريات)
    final customerSales = <String, double>{};
    for (final sale in _allSales) {
      customerSales[sale.customerId] =
          (customerSales[sale.customerId] ?? 0) + sale.totalCost;
    }

    // أفضل المنتجات (حسب عدد المبيعات)
    final productSales = <String, int>{};
    for (final sale in _allSales) {
      productSales[sale.productId] = (productSales[sale.productId] ?? 0) + 1;
    }

    setState(() {
      _stats = {
        'total': totalSales,
        'totalRevenue': totalRevenue,
        'totalPaid': totalPaid,
        'totalRemaining': totalRemaining,
        'thisMonth': thisMonthSales,
        'thisMonthRevenue': thisMonthRevenue,
        'today': todaySales,
        'todayRevenue': todayRevenue,
        'customerSales': customerSales,
        'productSales': productSales,
      };
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        title: Text(
          'تقرير المبيعات',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: _loadSalesData,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          labelStyle: GoogleFonts.cairo(fontWeight: FontWeight.bold),
          tabs: const [
            Tab(text: 'الإحصائيات', icon: Icon(Icons.analytics)),
            Tab(text: 'أفضل العملاء', icon: Icon(Icons.people)),
            Tab(text: 'أفضل المنتجات', icon: Icon(Icons.inventory)),
          ],
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : TabBarView(
                controller: _tabController,
                children: [
                  _buildStatsTab(),
                  _buildTopCustomersTab(),
                  _buildTopProductsTab(),
                ],
              ),
      ),
    );
  }

  Widget _buildStatsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('إحصائيات المبيعات'),
          const SizedBox(height: 16),

          // إجمالي الإيرادات
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.green.shade400, Colors.green.shade600],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.green.shade200,
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.attach_money,
                      color: Colors.white,
                      size: 32,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'إجمالي الإيرادات',
                      style: GoogleFonts.cairo(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Text(
                  '${(_stats['totalRevenue'] ?? 0).toStringAsFixed(2)} ج.م',
                  style: GoogleFonts.cairo(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 0.9,
            children: [
              _buildStatCard(
                title: 'إجمالي المبيعات',
                value: '${_stats['total'] ?? 0}',
                icon: Icons.shopping_cart,
                color: Colors.blue,
              ),
              _buildStatCard(
                title: 'مبيعات اليوم',
                value: '${_stats['today'] ?? 0}',
                icon: Icons.today,
                color: Colors.orange,
              ),
              _buildStatCard(
                title: 'مبيعات الشهر',
                value: '${_stats['thisMonth'] ?? 0}',
                icon: Icons.calendar_month,
                color: Colors.purple,
              ),
              _buildStatCard(
                title: 'إيرادات اليوم',
                value:
                    '${(_stats['todayRevenue'] ?? 0).toStringAsFixed(0)} ج.م',
                icon: Icons.monetization_on,
                color: Colors.teal,
              ),
            ],
          ),

          const SizedBox(height: 24),

          _buildSectionTitle('تفاصيل الدفع'),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: _buildPaymentCard(
                  title: 'إجمالي المدفوع',
                  value: '${(_stats['totalPaid'] ?? 0).toStringAsFixed(2)} ج.م',
                  color: Colors.green,
                  icon: Icons.check_circle,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildPaymentCard(
                  title: 'إجمالي المتبقي',
                  value:
                      '${(_stats['totalRemaining'] ?? 0).toStringAsFixed(2)} ج.م',
                  color: Colors.orange,
                  icon: Icons.pending,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTopCustomersTab() {
    final customerSales = _stats['customerSales'] as Map<String, double>? ?? {};
    final sortedCustomers = customerSales.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('أفضل العملاء'),
          const SizedBox(height: 16),
          if (sortedCustomers.isEmpty)
            Center(
              child: Column(
                children: [
                  Icon(
                    Icons.people_outline,
                    size: 80,
                    color: Colors.grey.shade400,
                  ),
                  const SizedBox(height: 20),
                  Text(
                    'لا توجد مبيعات بعد',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            )
          else
            ...sortedCustomers.take(10).map((entry) {
              final customer = _allCustomers.firstWhere(
                (c) => c.id == entry.key,
                orElse: () => WaterFilterCustomer(
                  id: '',
                  name: 'عميل محذوف',
                  phone: '',
                  address: '',
                  email: '',
                  area: '',
                  city: '',
                ),
              );

              return _buildCustomerCard(customer, entry.value);
            }),
        ],
      ),
    );
  }

  Widget _buildTopProductsTab() {
    final productSales = _stats['productSales'] as Map<String, int>? ?? {};
    final sortedProducts = productSales.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('أفضل المنتجات'),
          const SizedBox(height: 16),
          if (sortedProducts.isEmpty)
            Center(
              child: Column(
                children: [
                  Icon(
                    Icons.inventory_outlined,
                    size: 80,
                    color: Colors.grey.shade400,
                  ),
                  const SizedBox(height: 20),
                  Text(
                    'لا توجد مبيعات بعد',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            )
          else
            ...sortedProducts.take(10).map((entry) {
              final product = _allProducts.firstWhere(
                (p) => p.id == entry.key,
                orElse: () => WaterFilterProduct(
                  id: '',
                  name: 'منتج محذوف',
                  brand: '',
                  category: WaterFilterCategory.residential,
                  price: 0,
                  stock: 0,
                  description: '',
                  specifications: [],
                  maintenanceIntervalMonths: 0,
                  maintenanceCost: 0,
                  isInstallationRequired: false,
                  installationCost: 0,
                ),
              );

              return _buildProductCard(product, entry.value);
            }),
        ],
      ),
    );
  }

  // Helper Methods
  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: GoogleFonts.cairo(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: kMainColor,
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 28),
          const SizedBox(height: 8),
          Flexible(
            child: Text(
              value,
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
          const SizedBox(height: 4),
          Flexible(
            child: Text(
              title,
              style: GoogleFonts.cairo(
                fontSize: 11,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentCard({
    required String title,
    required String value,
    required Color color,
    required IconData icon,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(width: 8),
              Text(
                title,
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCustomerCard(
      WaterFilterCustomer customer, double totalPurchases) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.person,
                color: Colors.blue,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    customer.name,
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  if (customer.phone.isNotEmpty)
                    Text(
                      customer.phone,
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '${totalPurchases.toStringAsFixed(2)} ج.م',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
                Text(
                  'إجمالي المشتريات',
                  style: GoogleFonts.cairo(
                    fontSize: 10,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductCard(WaterFilterProduct product, int salesCount) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.water_drop,
                color: Colors.orange,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    product.name,
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    product.brand,
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${product.price.toStringAsFixed(2)} ج.م',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '$salesCount',
                  style: GoogleFonts.cairo(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
                Text(
                  'مبيعة',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
