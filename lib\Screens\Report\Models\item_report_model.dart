// نموذج تقرير الصنف
class ItemReportModel {
  final String productName;
  final double totalPurchases;
  final double totalSales;
  final double currentStock;
  final double averagePurchasePrice;
  final double averageSalePrice;
  final double profit;
  final DateTime lastPurchaseDate;
  final DateTime lastSaleDate;

  ItemReportModel({
    required this.productName,
    required this.totalPurchases,
    required this.totalSales,
    required this.currentStock,
    required this.averagePurchasePrice,
    required this.averageSalePrice,
    required this.profit,
    required this.lastPurchaseDate,
    required this.lastSaleDate,
  });

  // تحويل البيانات إلى Map
  Map<String, dynamic> toMap() {
    return {
      'productName': productName,
      'totalPurchases': totalPurchases,
      'totalSales': totalSales,
      'currentStock': currentStock,
      'averagePurchasePrice': averagePurchasePrice,
      'averageSalePrice': averageSalePrice,
      'profit': profit,
      'lastPurchaseDate': lastPurchaseDate.toIso8601String(),
      'lastSaleDate': lastSaleDate.toIso8601String(),
    };
  }

  // إنشاء نموذج من Map
  factory ItemReportModel.fromMap(Map<String, dynamic> map) {
    return ItemReportModel(
      productName: map['productName'] ?? '',
      totalPurchases: (map['totalPurchases'] ?? 0).toDouble(),
      totalSales: (map['totalSales'] ?? 0).toDouble(),
      currentStock: (map['currentStock'] ?? 0).toDouble(),
      averagePurchasePrice: (map['averagePurchasePrice'] ?? 0).toDouble(),
      averageSalePrice: (map['averageSalePrice'] ?? 0).toDouble(),
      profit: (map['profit'] ?? 0).toDouble(),
      lastPurchaseDate: DateTime.parse(map['lastPurchaseDate'] ?? DateTime.now().toIso8601String()),
      lastSaleDate: DateTime.parse(map['lastSaleDate'] ?? DateTime.now().toIso8601String()),
    );
  }

  @override
  String toString() {
    return 'ItemReportModel(productName: $productName, totalPurchases: $totalPurchases, totalSales: $totalSales, currentStock: $currentStock)';
  }
}
