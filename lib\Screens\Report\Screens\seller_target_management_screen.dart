// شاشة إدارة أهداف البائعين
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:mobile_pos/constant.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

/// مزود حالة الشهر المختار
final selectedTargetMonthProvider = StateProvider<DateTime>((ref) {
  // إرجاع تاريخ اليوم الحالي مع ضبط اليوم إلى 1 والوقت إلى 00:00:00
  final now = DateTime.now();
  return DateTime(now.year, now.month, 1);
});

/// مزود بيانات البائعين
final sellersProvider = FutureProvider<List<Map<String, dynamic>>>((ref) async {
  final userId = await getUserID();
  final List<Map<String, dynamic>> sellers = [];

  try {
    // إضافة المستخدم الرئيسي
    sellers.add({
      'id': 'المستخدم الرئيسي',
      'name': 'المستخدم الرئيسي',
    });

    // الحصول على بيانات المبيعات لاستخراج أسماء البائعين
    final salesRef = FirebaseDatabase.instance.ref('$userId/Sales Transition');
    final salesSnapshot = await salesRef.get();

    if (salesSnapshot.exists) {
      // مجموعة لتخزين أسماء البائعين الفريدة
      final Set<String> uniqueSellers = {};

      for (var child in salesSnapshot.children) {
        final data = child.value as Map<dynamic, dynamic>?;
        if (data != null) {
          final sellerName = data['sellerName'] as String? ?? 'غير معروف';
          if (sellerName != 'غير معروف' &&
              sellerName.isNotEmpty &&
              !uniqueSellers.contains(sellerName)) {
            uniqueSellers.add(sellerName);
            sellers.add({
              'id': sellerName,
              'name': sellerName,
            });
          }
        }
      }
    }

    return sellers;
  } catch (e) {
    // إرجاع قائمة تحتوي على المستخدم الرئيسي فقط في حالة حدوث خطأ
    return [
      {
        'id': 'المستخدم الرئيسي',
        'name': 'المستخدم الرئيسي',
      }
    ];
  }
});

/// مزود بيانات أهداف البائعين
final sellerTargetsProvider =
    FutureProvider.family<Map<String, double>, DateTime>(
        (ref, selectedMonth) async {
  final userId = await getUserID();
  final yearMonth = DateFormat('yyyy_MM').format(selectedMonth);
  final Map<String, double> targets = {};

  try {
    final targetsRef =
        FirebaseDatabase.instance.ref('$userId/targets/$yearMonth');
    final targetsSnapshot = await targetsRef.get();

    if (targetsSnapshot.exists) {
      final targetsData =
          Map<String, dynamic>.from(targetsSnapshot.value as Map);

      targetsData.forEach((sellerName, data) {
        if (data is Map) {
          final targetAmount = (data['target_amount'] ?? 0).toDouble();
          targets[sellerName] = targetAmount;
        } else {
          // إذا كان الهيكل بسيط (مباشرة قيمة التارجت)
          targets[sellerName] = (data ?? 0).toDouble();
        }
      });
    }

    return targets;
  } catch (e) {
    // إرجاع خريطة فارغة في حالة حدوث خطأ
    return {};
  }
});

/// شاشة إدارة أهداف البائعين
class SellerTargetManagementScreen extends ConsumerStatefulWidget {
  const SellerTargetManagementScreen({super.key});

  @override
  ConsumerState<SellerTargetManagementScreen> createState() =>
      _SellerTargetManagementScreenState();
}

class _SellerTargetManagementScreenState
    extends ConsumerState<SellerTargetManagementScreen> {
  final _formKey = GlobalKey<FormState>();
  final Map<String, TextEditingController> _controllers = {};

  @override
  void dispose() {
    // التخلص من وحدات التحكم في النص عند إغلاق الشاشة
    for (var controller in _controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // الشهر المختار
    final selectedMonth = ref.watch(selectedTargetMonthProvider);

    // بيانات البائعين
    final sellersData = ref.watch(sellersProvider);

    // بيانات أهداف البائعين
    final targetsData = ref.watch(sellerTargetsProvider(selectedMonth));

    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        title: Text(
          'إدارة أهداف البائعين',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        backgroundColor: kMainColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Container(
        alignment: Alignment.topCenter,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(30),
            topLeft: Radius.circular(30),
          ),
        ),
        child: Column(
          children: [
            // اختيار الشهر
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  Text(
                    'اختر الشهر:',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<DateTime>(
                          value: selectedMonth,
                          isExpanded: true,
                          items: _buildMonthDropdownItems(),
                          onChanged: (value) {
                            if (value != null) {
                              // تحديث القيمة المختارة مع التأكد من استخدام نفس تنسيق التاريخ
                              ref
                                  .read(selectedTargetMonthProvider.notifier)
                                  .state = DateTime(value.year, value.month, 1);
                            }
                          },
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // نموذج إدارة الأهداف
            Expanded(
              child: sellersData.when(
                data: (sellers) {
                  return targetsData.when(
                    data: (targets) {
                      // إنشاء وحدات تحكم في النص لكل بائع
                      for (var seller in sellers) {
                        final sellerId = seller['id'] as String;
                        if (!_controllers.containsKey(sellerId)) {
                          _controllers[sellerId] = TextEditingController(
                            text: targets[sellerId]?.toString() ?? '',
                          );
                        } else {
                          // تحديث النص إذا تغيرت البيانات
                          _controllers[sellerId]!.text =
                              targets[sellerId]?.toString() ?? '';
                        }
                      }

                      return Form(
                        key: _formKey,
                        child: Column(
                          children: [
                            Expanded(
                              child: ListView.builder(
                                padding: const EdgeInsets.all(16),
                                itemCount: sellers.length,
                                itemBuilder: (context, index) {
                                  final seller = sellers[index];
                                  final sellerId = seller['id'] as String;
                                  final sellerName = seller['name'] as String;

                                  return Card(
                                    margin: const EdgeInsets.only(bottom: 12),
                                    elevation: 2,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(16),
                                      child: Row(
                                        children: [
                                          Expanded(
                                            flex: 2,
                                            child: Text(
                                              sellerName,
                                              style: GoogleFonts.cairo(
                                                fontWeight: FontWeight.bold,
                                                fontSize: 16,
                                              ),
                                            ),
                                          ),
                                          Expanded(
                                            flex: 3,
                                            child: TextFormField(
                                              controller:
                                                  _controllers[sellerId],
                                              keyboardType:
                                                  TextInputType.number,
                                              decoration: InputDecoration(
                                                labelText: 'التارجت',
                                                hintText: 'أدخل قيمة التارجت',
                                                border: OutlineInputBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(8),
                                                ),
                                                contentPadding:
                                                    const EdgeInsets.symmetric(
                                                  horizontal: 12,
                                                  vertical: 8,
                                                ),
                                              ),
                                              validator: (value) {
                                                if (value != null &&
                                                    value.isNotEmpty) {
                                                  // التحقق من أن القيمة رقم صحيح
                                                  if (double.tryParse(value) ==
                                                      null) {
                                                    return 'يرجى إدخال رقم صحيح';
                                                  }
                                                }
                                                return null;
                                              },
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),

                            // زر الحفظ
                            Padding(
                              padding: const EdgeInsets.all(16),
                              child: ElevatedButton(
                                onPressed: () => _saveTargets(sellers),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: kMainColor,
                                  minimumSize: const Size(double.infinity, 50),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                ),
                                child: Text(
                                  'حفظ الأهداف',
                                  style: GoogleFonts.cairo(
                                    color: Colors.white,
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                    loading: () => const Center(
                      child: CircularProgressIndicator(),
                    ),
                    error: (error, stackTrace) => Center(
                      child: Text(
                        'حدث خطأ: $error',
                        style: const TextStyle(
                          color: Colors.red,
                        ),
                      ),
                    ),
                  );
                },
                loading: () => const Center(
                  child: CircularProgressIndicator(),
                ),
                error: (error, stackTrace) => Center(
                  child: Text(
                    'حدث خطأ: $error',
                    style: const TextStyle(
                      color: Colors.red,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عناصر القائمة المنسدلة للشهور
  List<DropdownMenuItem<DateTime>> _buildMonthDropdownItems() {
    final now = DateTime.now();
    final items = <DropdownMenuItem<DateTime>>[];

    // إضافة الشهر الحالي و11 شهر مستقبلي
    for (int i = 0; i < 12; i++) {
      // إنشاء تاريخ جديد مع ضبط اليوم إلى 1 والوقت إلى 00:00:00
      final date = DateTime(now.year, now.month + i, 1);
      items.add(DropdownMenuItem(
        // استخدام تاريخ بدون وقت للمقارنة
        value: date,
        child: Text(
          DateFormat('MMMM yyyy', 'ar').format(date),
          style: GoogleFonts.cairo(),
        ),
      ));
    }

    return items;
  }

  /// حفظ أهداف البائعين
  Future<void> _saveTargets(List<Map<String, dynamic>> sellers) async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    try {
      EasyLoading.show(status: 'جاري الحفظ...');

      final userId = await getUserID();
      final selectedMonth = ref.read(selectedTargetMonthProvider);
      final yearMonth = DateFormat('yyyy_MM').format(selectedMonth);

      final targetsRef =
          FirebaseDatabase.instance.ref('$userId/targets/$yearMonth');

      // إنشاء خريطة لتخزين الأهداف
      final Map<String, dynamic> targetsData = {};

      // إضافة الأهداف لكل بائع
      for (var seller in sellers) {
        final sellerId = seller['id'] as String;
        final controller = _controllers[sellerId];

        if (controller != null && controller.text.isNotEmpty) {
          final targetValue = double.tryParse(controller.text) ?? 0;
          if (targetValue > 0) {
            targetsData[sellerId] = targetValue;
          }
        }
      }

      // حفظ البيانات في Firebase
      await targetsRef.set(targetsData);

      // تحديث مزود البيانات
      final _ = ref.refresh(sellerTargetsProvider(selectedMonth));

      EasyLoading.showSuccess('تم حفظ الأهداف بنجاح');
    } catch (e) {
      EasyLoading.showError('حدث خطأ أثناء الحفظ: $e');
    } finally {
      EasyLoading.dismiss();
    }
  }
}
