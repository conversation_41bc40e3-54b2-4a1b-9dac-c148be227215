import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// أدوات مساعدة موحدة للمساعد الصوتي - تجمع الوظائف المكررة
class VoiceAssistantUtils {
  /// تنفيذ عملية مع إعادة المحاولة
  static Future<T?> retryOperation<T>(
    Future<T> Function() operation, {
    int maxAttempts = 3,
    Duration delay = const Duration(seconds: 2),
    String? operationName,
  }) async {
    for (int attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        debugPrint('🔄 ${operationName ?? 'عملية'} - المحاولة $attempt');
        final result = await operation();
        debugPrint('✅ نجحت ${operationName ?? 'العملية'} في المحاولة $attempt');
        return result;
      } catch (e) {
        debugPrint(
            '❌ فشلت ${operationName ?? 'العملية'} في المحاولة $attempt: $e');

        if (attempt < maxAttempts) {
          debugPrint(
              '⏳ انتظار ${delay.inSeconds} ثانية قبل المحاولة التالية...');
          await Future.delayed(Duration(seconds: delay.inSeconds * attempt));
        }
      }
    }

    debugPrint('❌ فشلت ${operationName ?? 'العملية'} بعد $maxAttempts محاولات');
    return null;
  }

  /// تنفيذ عملية مع timeout
  static Future<T?> withTimeout<T>(
    Future<T> Function() operation, {
    Duration timeout = const Duration(seconds: 30),
    String? operationName,
  }) async {
    try {
      debugPrint(
          '⏱️ بدء ${operationName ?? 'عملية'} مع timeout ${timeout.inSeconds}s');
      final result = await operation().timeout(timeout);
      debugPrint('✅ اكتملت ${operationName ?? 'العملية'} بنجاح');
      return result;
    } on TimeoutException {
      debugPrint(
          '⏰ انتهت مهلة ${operationName ?? 'العملية'} (${timeout.inSeconds}s)');
      return null;
    } catch (e) {
      debugPrint('❌ خطأ في ${operationName ?? 'العملية'}: $e');
      return null;
    }
  }

  /// حفظ إعداد بشكل آمن
  static Future<bool> saveSetting(String key, dynamic value) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      if (value is bool) {
        return await prefs.setBool(key, value);
      } else if (value is int) {
        return await prefs.setInt(key, value);
      } else if (value is double) {
        return await prefs.setDouble(key, value);
      } else if (value is String) {
        return await prefs.setString(key, value);
      } else if (value is List<String>) {
        return await prefs.setStringList(key, value);
      } else {
        debugPrint('❌ نوع البيانات غير مدعوم: ${value.runtimeType}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ خطأ في حفظ الإعداد $key: $e');
      return false;
    }
  }

  /// تحميل إعداد بشكل آمن
  static Future<T?> loadSetting<T>(String key, {T? defaultValue}) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      if (T == bool) {
        return prefs.getBool(key) as T? ?? defaultValue;
      } else if (T == int) {
        return prefs.getInt(key) as T? ?? defaultValue;
      } else if (T == double) {
        return prefs.getDouble(key) as T? ?? defaultValue;
      } else if (T == String) {
        return prefs.getString(key) as T? ?? defaultValue;
      } else if (T == List<String>) {
        return prefs.getStringList(key) as T? ?? defaultValue;
      } else {
        debugPrint('❌ نوع البيانات غير مدعوم: $T');
        return defaultValue;
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الإعداد $key: $e');
      return defaultValue;
    }
  }

  /// تنظيف النصوص العربية
  static String cleanArabicText(String text) {
    if (text.isEmpty) return text;

    // إزالة المسافات الزائدة
    text = text.trim().replaceAll(RegExp(r'\s+'), ' ');

    // إزالة حرف "ل" التعريفي
    final patterns = [
      RegExp(r'^ل([^\s]+)', caseSensitive: false),
      RegExp(r'\sل([^\s]+)', caseSensitive: false),
    ];

    for (final pattern in patterns) {
      text = text.replaceAllMapped(pattern, (match) {
        final prefix = match.group(0)!.startsWith(' ') ? ' ' : '';
        return '$prefix${match.group(1)}';
      });
    }

    return text;
  }

  /// استخراج الأسماء من النص العربي
  static List<String> extractNamesFromArabicText(String text) {
    final cleanedText = cleanArabicText(text);
    final names = <String>[];

    // أنماط استخراج الأسماء
    final patterns = [
      RegExp(r'للعميل\s+([^\s]+(?:\s+[^\s]+)*)', caseSensitive: false),
      RegExp(r'للمورد\s+([^\s]+(?:\s+[^\s]+)*)', caseSensitive: false),
      RegExp(r'العميل\s+([^\s]+(?:\s+[^\s]+)*)', caseSensitive: false),
      RegExp(r'المورد\s+([^\s]+(?:\s+[^\s]+)*)', caseSensitive: false),
      RegExp(r'اسمه\s+([^\s]+(?:\s+[^\s]+)*)', caseSensitive: false),
      RegExp(r'يسمى\s+([^\s]+(?:\s+[^\s]+)*)', caseSensitive: false),
    ];

    for (final pattern in patterns) {
      final matches = pattern.allMatches(cleanedText);
      for (final match in matches) {
        final name = match.group(1)?.trim();
        if (name != null && name.isNotEmpty) {
          names.add(name);
        }
      }
    }

    return names.toSet().toList(); // إزالة التكرار
  }

  /// تحويل النص إلى تنسيق آمن للتخزين
  static String sanitizeForStorage(String text) {
    return text
        .replaceAll(RegExp(r'[^\w\s\u0600-\u06FF]'),
            '') // الاحتفاظ بالأحرف العربية والإنجليزية فقط
        .trim()
        .replaceAll(RegExp(r'\s+'), '_'); // استبدال المسافات بـ _
  }

  /// تحويل الوقت إلى تنسيق قابل للقراءة
  static String formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '$hoursس $minutesد $secondsث';
    } else if (minutes > 0) {
      return '$minutesد $secondsث';
    } else {
      return '$secondsث';
    }
  }

  /// تحويل حجم الملف إلى تنسيق قابل للقراءة
  static String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '${bytes}B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)}KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
    }
  }

  /// التحقق من صحة النص العربي
  static bool isValidArabicText(String text) {
    if (text.isEmpty) return false;

    // التحقق من وجود أحرف عربية
    final arabicPattern = RegExp(r'[\u0600-\u06FF]');
    return arabicPattern.hasMatch(text);
  }

  /// إنشاء معرف فريد للجلسة
  static String generateSessionId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (timestamp % 10000).toString().padLeft(4, '0');
    return 'voice_session_${timestamp}_$random';
  }

  /// تسجيل حدث مع الوقت
  static void logEvent(String event, {Map<String, dynamic>? data}) {
    final timestamp = DateTime.now().toIso8601String();
    final logData = data != null ? ' - البيانات: $data' : '';
    debugPrint('📝 [$timestamp] $event$logData');
  }

  /// التحقق من إمكانية الوصول للإنترنت
  static Future<bool> hasInternetConnection() async {
    try {
      // محاولة بسيطة للتحقق من الاتصال
      // يمكن تحسينها باستخدام مكتبة connectivity_plus
      return true; // مؤقتاً
    } catch (e) {
      debugPrint('❌ خطأ في فحص الاتصال بالإنترنت: $e');
      return false;
    }
  }

  /// تنظيف الذاكرة
  static void cleanupMemory() {
    try {
      // تنظيف الذاكرة المؤقتة
      debugPrint('🧹 تنظيف الذاكرة...');
      // يمكن إضافة عمليات تنظيف إضافية هنا
    } catch (e) {
      debugPrint('❌ خطأ في تنظيف الذاكرة: $e');
    }
  }

  /// إنشاء تقرير حالة النظام
  static Map<String, dynamic> generateSystemReport() {
    return {
      'timestamp': DateTime.now().toIso8601String(),
      'platform': defaultTargetPlatform.name,
      'isDebugMode': kDebugMode,
      'memoryUsage': 'غير متاح', // يمكن تحسينه
      'uptime': 'غير متاح', // يمكن تحسينه
    };
  }
}
