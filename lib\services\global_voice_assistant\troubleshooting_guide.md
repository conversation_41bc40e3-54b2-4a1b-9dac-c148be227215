# 🔧 دليل استكشاف أخطاء المساعد الصوتي

## 🚨 المشاكل الشائعة وحلولها

### 1. **شاشة الإعدادات لا تحمل أو تظهر فارغة**

#### الأعراض:
- الشاشة تظهر شاشة تحميل لفترة طويلة
- لا تظهر أي إعدادات
- رسائل خطأ في وحدة التحكم

#### الحلول:

**أ) فحص سجلات وحدة التحكم:**
```
I/flutter: 🔄 بدء تحميل إعدادات المساعد الصوتي...
I/flutter: 🔧 بدء تهيئة خدمات شاشة الإعدادات...
I/flutter: ✅ تم تهيئة خدمة بصمة الصوت
I/flutter: ✅ تم فحص الأذونات
I/flutter: ✅ تم تحميل جميع إعدادات المساعد الصوتي بنجاح
```

**ب) إعادة تشغيل التطبيق:**
```bash
flutter hot restart
```

**ج) فحص الأذونات:**
- انتقل إلى `/PermissionTest`
- تأكد من منح جميع الأذونات المطلوبة

**د) مسح البيانات المؤقتة:**
```bash
flutter clean
flutter pub get
```

### 2. **خطأ "Instance of 'SpeechToTextNotInitializedException'"**

#### الأعراض:
```
خطأ في بدء الاستماع: Instance of 'SpeechToTextNotInitializedException'
```

#### الحلول:

**أ) التحقق من إذن الميكروفون:**
```dart
// في Android Manifest
<uses-permission android:name="android.permission.RECORD_AUDIO" />
```

**ب) إعادة تهيئة خدمة التعرف على الكلام:**
1. أغلق التطبيق تماماً
2. أعد فتحه
3. امنح إذن الميكروفون إذا طُلب منك

**ج) فحص إعدادات النظام:**
- تأكد من تفعيل الميكروفون في إعدادات النظام
- تحقق من عدم استخدام تطبيق آخر للميكروفون

### 3. **المساعد الصوتي لا يستجيب لكلمة التفعيل**

#### الأعراض:
- لا يتم اكتشاف كلمة التفعيل
- المساعد لا يبدأ الاستماع

#### الحلول:

**أ) ضبط حساسية الكشف:**
1. افتح شاشة الإعدادات
2. اذهب لقسم "كلمة التفعيل"
3. زد الحساسية إلى 80-90%

**ب) تغيير كلمة التفعيل:**
- جرب كلمات أوضح مثل "مساعد" أو "هاي آب"
- تجنب الكلمات المعقدة أو الطويلة

**ج) فحص البيئة:**
- تأكد من عدم وجود ضوضاء خلفية
- تحدث بوضوح ووتيرة طبيعية

### 4. **بصمة الصوت لا تعمل**

#### الأعراض:
- فشل في تسجيل بصمة الصوت
- عدم التعرف على الصوت المسجل

#### الحلول:

**أ) إعادة تسجيل بصمة الصوت:**
1. احذف البصمة الحالية
2. سجل بصمة جديدة في بيئة هادئة
3. تحدث بوضوح لكل عبارة

**ب) فحص جودة التسجيل:**
- استخدم `/VoiceFingerprintTest` للاختبار
- تأكد من حصولك على درجة جودة 80%+

**ج) تحسين البيئة:**
- سجل في مكان هادئ
- تجنب الأصوات الخلفية
- استخدم صوتك الطبيعي

### 5. **النافذة العائمة لا تظهر**

#### الأعراض:
- المساعد لا يعمل خارج التطبيق
- لا تظهر نافذة عائمة

#### الحلول:

**أ) فحص أذونات النوافذ العائمة:**
```dart
// التحقق من الإذن
final permission = await Permission.systemAlertWindow.request();
```

**ب) تفعيل الإذن يدوياً:**
1. اذهب لإعدادات النظام
2. التطبيقات → AmrDevPOS
3. الأذونات → النوافذ العائمة
4. فعّل الإذن

### 6. **استهلاك البطارية العالي**

#### الأعراض:
- استنزاف سريع للبطارية
- التطبيق يعمل باستمرار في الخلفية

#### الحلول:

**أ) ضبط إعدادات تحسين البطارية:**
1. اذهب لإعدادات النظام
2. البطارية → تحسين البطارية
3. استثني AmrDevPOS من التحسين

**ب) تقليل حساسية الكشف:**
- قلل حساسية كلمة التفعيل إلى 60-70%
- هذا يقلل من معالجة الصوت المستمرة

### 7. **رسائل خطأ في وحدة التحكم**

#### رسائل شائعة وحلولها:

**أ) "تحذير: محاولة الوصول إلى Firebase قبل التهيئة"**
```
الحل: انتظر تهيئة Firebase قبل استخدام المساعد
```

**ب) "❌ خطأ في تحميل الإعدادات"**
```
الحل: أعد تشغيل التطبيق أو امسح البيانات المؤقتة
```

**ج) "⏰ انتهت مهلة تحميل الإعدادات"**
```
الحل: تحقق من الاتصال بالإنترنت وأعد المحاولة
```

## 🛠️ أدوات التشخيص المتاحة

### 1. **شاشة اختبار الأذونات**
```dart
Navigator.pushNamed(context, '/PermissionTest');
```

### 2. **شاشة اختبار بصمة الصوت**
```dart
Navigator.pushNamed(context, '/VoiceFingerprintTest');
```

### 3. **شاشة اختبار المساعد السريع**
```dart
Navigator.pushNamed(context, '/QuickAITest');
```

### 4. **شاشة اختبار الإعدادات**
```dart
Navigator.pushNamed(context, '/SettingsScreenTest');
```

## 📊 مراقبة الأداء

### **فحص حالة الخدمات:**
```dart
// في شاشة الإعدادات
final status = _initManager.getInitializationStatus();
print('حالة الخدمة: $status');
```

### **فحص الأذونات:**
```dart
final permissions = await _permissionManager.checkAllPermissions();
print('حالة الأذونات: $permissions');
```

### **فحص بصمة الصوت:**
```dart
final fingerprint = _fingerprintService.getVoiceFingerprintInfo();
print('معلومات بصمة الصوت: $fingerprint');
```

## 🔄 خطوات الإصلاح العامة

### **1. إعادة تعيين كاملة:**
```bash
# مسح البيانات
flutter clean

# إعادة تثبيت التبعيات
flutter pub get

# إعادة تشغيل
flutter run
```

### **2. إعادة تعيين الإعدادات:**
1. افتح شاشة الإعدادات
2. اضغط على "أدوات التشخيص"
3. اختر "إعادة تعيين الإعدادات"

### **3. تنظيف البيانات:**
1. افتح شاشة الإعدادات
2. اضغط على "أدوات التشخيص"
3. اختر "تنظيف البيانات"

## 📞 الحصول على المساعدة

### **معلومات مفيدة للدعم:**
1. **نسخة التطبيق**: AmrDevPOS v1.0
2. **نسخة Flutter**: flutter --version
3. **نظام التشغيل**: Android/iOS
4. **رسائل الخطأ**: من وحدة التحكم
5. **خطوات إعادة الإنتاج**: ما فعلته قبل المشكلة

### **سجلات مفيدة:**
```bash
# تشغيل مع سجلات مفصلة
flutter run --verbose

# حفظ السجلات
flutter logs > debug_logs.txt
```

---

**💡 نصيحة:** استخدم أدوات التشخيص المدمجة أولاً قبل البحث عن حلول معقدة!
