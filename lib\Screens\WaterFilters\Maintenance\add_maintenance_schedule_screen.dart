import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/model/water_filter_models.dart';
import 'package:mobile_pos/services/maintenance_service.dart';
import 'package:mobile_pos/services/water_filter_service.dart';

class AddMaintenanceScheduleScreen extends StatefulWidget {
  const AddMaintenanceScheduleScreen({super.key});

  @override
  State<AddMaintenanceScheduleScreen> createState() =>
      _AddMaintenanceScheduleScreenState();
}

class _AddMaintenanceScheduleScreenState
    extends State<AddMaintenanceScheduleScreen> {
  final _formKey = GlobalKey<FormState>();
  final _descriptionController = TextEditingController();
  final _notesController = TextEditingController();
  final _costController = TextEditingController();

  String? _selectedSystemId;
  String? _selectedTechnicianId;
  DateTime _selectedDate = DateTime.now().add(const Duration(days: 1));
  TimeOfDay _selectedTime = const TimeOfDay(hour: 9, minute: 0);
  MaintenanceType _selectedType = MaintenanceType.routine;
  MaintenancePriority _selectedPriority = MaintenancePriority.normal;
  Duration _estimatedDuration = const Duration(hours: 1);
  bool _isRecurring = false;
  int _recurringDays = 180; // 6 شهور افتراضي

  List<WaterFilterSystem> _systems = [];
  bool _isLoading = false;
  bool _isLoadingData = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    _notesController.dispose();
    _costController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoadingData = true);

    try {
      // تحميل الأنظمة
      final systemsData = await WaterFilterService.getData('Systems');
      final systems = <WaterFilterSystem>[];

      systemsData.forEach((key, value) {
        try {
          final system = WaterFilterSystem.fromJson(
            Map<String, dynamic>.from(value),
          );
          // فقط الأنظمة النشطة
          if (system.status == FilterSystemStatus.active) {
            systems.add(system);
          }
        } catch (e) {
          debugPrint('خطأ في معالجة نظام: $e');
        }
      });

      setState(() {
        _systems = systems;
        _isLoadingData = false;
      });
    } catch (e) {
      debugPrint('خطأ في تحميل البيانات: $e');
      setState(() => _isLoadingData = false);
    }
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: kMainColor,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (date != null) {
      setState(() => _selectedDate = date);
    }
  }

  Future<void> _selectTime() async {
    final time = await showTimePicker(
      context: context,
      initialTime: _selectedTime,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: kMainColor,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (time != null) {
      setState(() => _selectedTime = time);
    }
  }

  Future<void> _saveSchedule() async {
    if (!_formKey.currentState!.validate() || _selectedSystemId == null) {
      return;
    }

    setState(() => _isLoading = true);

    try {
      // الحصول على بيانات النظام المحدد
      final selectedSystem = _systems.firstWhere(
        (system) => system.id == _selectedSystemId,
      );

      final schedule = MaintenanceSchedule(
        id: WaterFilterService.generateId(),
        systemId: _selectedSystemId!,
        customerId: selectedSystem.customerId,
        technicianId: _selectedTechnicianId,
        scheduledDate: _selectedDate,
        scheduledTime: _selectedTime,
        type: _selectedType,
        priority: _selectedPriority,
        status: ScheduleStatus.scheduled,
        description: _descriptionController.text.trim(),
        estimatedDuration: _estimatedDuration,
        estimatedCost: _costController.text.isNotEmpty
            ? double.tryParse(_costController.text)
            : null,
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
        isRecurring: _isRecurring,
        recurringIntervalDays: _isRecurring ? _recurringDays : null,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final success = await MaintenanceService.createSchedule(schedule);

      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إنشاء جدولة الصيانة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context, true);
        }
      } else {
        throw Exception('فشل في حفظ الجدولة');
      }
    } catch (e) {
      debugPrint('خطأ في حفظ الجدولة: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ الجدولة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        title: Text(
          'جدولة صيانة جديدة',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        child: _isLoadingData
            ? const Center(child: CircularProgressIndicator())
            : Form(
                key: _formKey,
                child: ListView(
                  padding: const EdgeInsets.all(20),
                  children: [
                    // اختيار النظام
                    _buildSectionTitle('النظام'),
                    const SizedBox(height: 16),

                    Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: DropdownButtonFormField<String>(
                        value: _selectedSystemId,
                        decoration: const InputDecoration(
                          labelText: 'اختر النظام',
                          prefixIcon: Icon(Icons.settings),
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                        ),
                        hint: Text(
                          'اختر النظام للصيانة',
                          style: GoogleFonts.cairo(color: Colors.grey.shade600),
                        ),
                        items: _systems.map((system) {
                          return DropdownMenuItem<String>(
                            value: system.id,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  'نظام ${system.serialNumber}',
                                  style: GoogleFonts.cairo(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Text(
                                  'آخر صيانة: ${system.lastMaintenanceDate != null ? "${system.lastMaintenanceDate!.day}/${system.lastMaintenanceDate!.month}/${system.lastMaintenanceDate!.year}" : "لم يتم"}',
                                  style: GoogleFonts.cairo(
                                    fontSize: 12,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                              ],
                            ),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() => _selectedSystemId = value);
                        },
                        validator: (value) {
                          if (value == null) {
                            return 'يرجى اختيار النظام';
                          }
                          return null;
                        },
                      ),
                    ),

                    const SizedBox(height: 20),

                    // نوع الصيانة والأولوية
                    _buildSectionTitle('تفاصيل الصيانة'),
                    const SizedBox(height: 16),

                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey.shade300),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: DropdownButtonFormField<MaintenanceType>(
                              value: _selectedType,
                              decoration: const InputDecoration(
                                labelText: 'نوع الصيانة',
                                border: InputBorder.none,
                                contentPadding: EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 12,
                                ),
                              ),
                              items: MaintenanceType.values.map((type) {
                                return DropdownMenuItem<MaintenanceType>(
                                  value: type,
                                  child: Row(
                                    children: [
                                      Icon(type.icon,
                                          color: type.color, size: 20),
                                      const SizedBox(width: 8),
                                      Text(
                                        type.arabicName,
                                        style: GoogleFonts.cairo(),
                                      ),
                                    ],
                                  ),
                                );
                              }).toList(),
                              onChanged: (value) {
                                if (value != null) {
                                  setState(() => _selectedType = value);
                                }
                              },
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Container(
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey.shade300),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: DropdownButtonFormField<MaintenancePriority>(
                              value: _selectedPriority,
                              decoration: const InputDecoration(
                                labelText: 'الأولوية',
                                border: InputBorder.none,
                                contentPadding: EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 12,
                                ),
                              ),
                              items: MaintenancePriority.values.map((priority) {
                                return DropdownMenuItem<MaintenancePriority>(
                                  value: priority,
                                  child: Text(
                                    priority.arabicName,
                                    style: GoogleFonts.cairo(
                                      color: priority.color,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                );
                              }).toList(),
                              onChanged: (value) {
                                if (value != null) {
                                  setState(() => _selectedPriority = value);
                                }
                              },
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 20),

                    // الوصف
                    TextFormField(
                      controller: _descriptionController,
                      decoration: InputDecoration(
                        labelText: 'وصف الصيانة',
                        hintText: 'أدخل وصف تفصيلي للصيانة المطلوبة',
                        prefixIcon: const Icon(Icons.description),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      style: GoogleFonts.cairo(),
                      maxLines: 2,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال وصف الصيانة';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 20),

                    // التاريخ والوقت
                    _buildSectionTitle('التوقيت'),
                    const SizedBox(height: 16),

                    Row(
                      children: [
                        Expanded(
                          child: InkWell(
                            onTap: _selectDate,
                            child: Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey.shade300),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Row(
                                children: [
                                  Icon(Icons.calendar_today, color: kMainColor),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'التاريخ',
                                          style: GoogleFonts.cairo(
                                            fontSize: 12,
                                            color: Colors.grey.shade600,
                                          ),
                                        ),
                                        Text(
                                          '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                                          style: GoogleFonts.cairo(
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: InkWell(
                            onTap: _selectTime,
                            child: Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey.shade300),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Row(
                                children: [
                                  Icon(Icons.access_time, color: kMainColor),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'الوقت',
                                          style: GoogleFonts.cairo(
                                            fontSize: 12,
                                            color: Colors.grey.shade600,
                                          ),
                                        ),
                                        Text(
                                          _selectedTime.format(context),
                                          style: GoogleFonts.cairo(
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 20),

                    // المدة المتوقعة والتكلفة
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey.shade300),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: DropdownButtonFormField<Duration>(
                              value: _estimatedDuration,
                              decoration: const InputDecoration(
                                labelText: 'المدة المتوقعة',
                                prefixIcon: Icon(Icons.timer),
                                border: InputBorder.none,
                                contentPadding: EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 12,
                                ),
                              ),
                              items: [
                                const Duration(minutes: 30),
                                const Duration(hours: 1),
                                const Duration(hours: 2),
                                const Duration(hours: 3),
                                const Duration(hours: 4),
                              ].map((duration) {
                                return DropdownMenuItem<Duration>(
                                  value: duration,
                                  child: Text(
                                    duration.inHours > 0
                                        ? '${duration.inHours} ساعة'
                                        : '${duration.inMinutes} دقيقة',
                                    style: GoogleFonts.cairo(),
                                  ),
                                );
                              }).toList(),
                              onChanged: (value) {
                                if (value != null) {
                                  setState(() => _estimatedDuration = value);
                                }
                              },
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: TextFormField(
                            controller: _costController,
                            keyboardType: TextInputType.number,
                            decoration: InputDecoration(
                              labelText: 'التكلفة المتوقعة',
                              hintText: '0.00',
                              prefixIcon: const Icon(Icons.attach_money),
                              suffixText: 'ج.م',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            style: GoogleFonts.cairo(),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 20),

                    // الصيانة المتكررة
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade50,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey.shade200),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Checkbox(
                                value: _isRecurring,
                                onChanged: (value) {
                                  setState(() => _isRecurring = value ?? false);
                                },
                                activeColor: kMainColor,
                              ),
                              Expanded(
                                child: Text(
                                  'صيانة متكررة',
                                  style: GoogleFonts.cairo(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          if (_isRecurring) ...[
                            const SizedBox(height: 12),
                            Container(
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey.shade300),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: DropdownButtonFormField<int>(
                                value: _recurringDays,
                                decoration: const InputDecoration(
                                  labelText: 'تكرار كل',
                                  border: InputBorder.none,
                                  contentPadding: EdgeInsets.symmetric(
                                    horizontal: 16,
                                    vertical: 12,
                                  ),
                                ),
                                items: [
                                  30, // شهر
                                  60, // شهرين
                                  90, // 3 شهور
                                  180, // 6 شهور
                                  365, // سنة
                                ].map((days) {
                                  return DropdownMenuItem<int>(
                                    value: days,
                                    child: Text(
                                      days == 30
                                          ? 'شهر واحد'
                                          : days == 60
                                              ? 'شهرين'
                                              : days == 90
                                                  ? '3 شهور'
                                                  : days == 180
                                                      ? '6 شهور'
                                                      : 'سنة واحدة',
                                      style: GoogleFonts.cairo(),
                                    ),
                                  );
                                }).toList(),
                                onChanged: (value) {
                                  if (value != null) {
                                    setState(() => _recurringDays = value);
                                  }
                                },
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),

                    const SizedBox(height: 20),

                    // ملاحظات
                    TextFormField(
                      controller: _notesController,
                      maxLines: 3,
                      decoration: InputDecoration(
                        labelText: 'ملاحظات إضافية (اختياري)',
                        hintText: 'أدخل أي ملاحظات أو تعليمات خاصة...',
                        prefixIcon: const Icon(Icons.note_add),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      style: GoogleFonts.cairo(),
                    ),

                    const SizedBox(height: 30),

                    // زر الحفظ
                    SizedBox(
                      width: double.infinity,
                      height: 50,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _saveSchedule,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: kMainColor,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 2,
                        ),
                        child: _isLoading
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2,
                                ),
                              )
                            : Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Icon(Icons.schedule, size: 20),
                                  const SizedBox(width: 8),
                                  Text(
                                    'إنشاء الجدولة',
                                    style: GoogleFonts.cairo(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                      ),
                    ),

                    const SizedBox(height: 20),
                  ],
                ),
              ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: GoogleFonts.cairo(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: kMainColor,
      ),
    );
  }
}
