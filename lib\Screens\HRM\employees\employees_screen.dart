import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'provider/employee_provider.dart';
import 'add_employee.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/models/hrm_models.dart';

class EmployeesScreen extends StatefulWidget {
  const EmployeesScreen({super.key});

  static const String route = '/hrm/employees';

  @override
  State<EmployeesScreen> createState() => _EmployeesScreenState();
}

class _EmployeesScreenState extends State<EmployeesScreen> {
  String searchQuery = '';
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _designationController = TextEditingController();
  final TextEditingController _departmentController = TextEditingController();
  final TextEditingController _salaryController = TextEditingController();
  EmployeeModel? selectedEmployee;

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _designationController.dispose();
    _departmentController.dispose();
    _salaryController.dispose();
    super.dispose();
  }

  void _resetForm() {
    _nameController.clear();
    _emailController.clear();
    _phoneController.clear();
    _addressController.clear();
    _designationController.clear();
    _departmentController.clear();
    _salaryController.clear();
    selectedEmployee = null;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('الموظفين'),
        backgroundColor: kMainColor,
        centerTitle: true,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        titleTextStyle: const TextStyle(
            color: Colors.white, fontSize: 20, fontWeight: FontWeight.bold),
      ),
      body: Consumer(
        builder: (context, ref, _) {
          final employeesAsyncValue = ref.watch(employeeListProvider);

          return employeesAsyncValue.when(
            data: (employees) {
              // تطبيق البحث
              List<EmployeeModel> filteredEmployees =
                  employees.where((employee) {
                return searchQuery.isEmpty ||
                    employee.name
                        .toLowerCase()
                        .contains(searchQuery.toLowerCase()) ||
                    employee.phone
                        .toLowerCase()
                        .contains(searchQuery.toLowerCase()) ||
                    employee.email
                        .toLowerCase()
                        .contains(searchQuery.toLowerCase()) ||
                    employee.designation
                        .toLowerCase()
                        .contains(searchQuery.toLowerCase());
              }).toList();

              return Column(
                children: [
                  // شريط البحث
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: TextField(
                      decoration: InputDecoration(
                        hintText: 'بحث بالاسم أو الهاتف أو البريد الإلكتروني',
                        prefixIcon: const Icon(Icons.search),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                      onChanged: (value) {
                        setState(() {
                          searchQuery = value;
                        });
                      },
                    ),
                  ),

                  // قائمة الموظفين
                  Expanded(
                    child: filteredEmployees.isEmpty
                        ? const Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.people_outline,
                                    size: 80, color: Colors.grey),
                                SizedBox(height: 16),
                                Text(
                                  'لا يوجد موظفين',
                                  style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold),
                                ),
                                SizedBox(height: 8),
                                Text(
                                  'قم بإضافة موظفين جدد',
                                  style: TextStyle(color: Colors.grey),
                                ),
                              ],
                            ),
                          )
                        : ListView.builder(
                            itemCount: filteredEmployees.length,
                            itemBuilder: (context, index) {
                              final employee = filteredEmployees[index];
                              return Card(
                                margin: const EdgeInsets.symmetric(
                                    horizontal: 16, vertical: 8),
                                elevation: 2,
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12)),
                                child: ListTile(
                                  leading: CircleAvatar(
                                    backgroundColor: kMainColor,
                                    child: Text(
                                      employee.name.isNotEmpty
                                          ? employee.name[0].toUpperCase()
                                          : '?',
                                      style:
                                          const TextStyle(color: Colors.white),
                                    ),
                                  ),
                                  title: Text(
                                    employee.name,
                                    style: const TextStyle(
                                        fontWeight: FontWeight.bold),
                                  ),
                                  subtitle: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                          'المسمى الوظيفي: ${employee.designation}'),
                                      Text('القسم: ${employee.department}'),
                                      Text('الهاتف: ${employee.phone}'),
                                    ],
                                  ),
                                  trailing: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 8, vertical: 4),
                                        decoration: BoxDecoration(
                                          color: employee.isActive
                                              ? Colors.green.withAlpha(51)
                                              : Colors.red.withAlpha(51),
                                          borderRadius:
                                              BorderRadius.circular(12),
                                          border: Border.all(
                                              color: employee.isActive
                                                  ? Colors.green
                                                  : Colors.red),
                                        ),
                                        child: Text(
                                          employee.isActive ? 'نشط' : 'غير نشط',
                                          style: TextStyle(
                                            color: employee.isActive
                                                ? Colors.green
                                                : Colors.red,
                                            fontSize: 12,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                      IconButton(
                                        icon: const Icon(Icons.edit,
                                            color: Colors.blue),
                                        onPressed: () {
                                          _editEmployee(employee);
                                        },
                                      ),
                                      IconButton(
                                        icon: const Icon(Icons.delete,
                                            color: Colors.red),
                                        onPressed: () {
                                          _deleteEmployee(ref, employee);
                                        },
                                      ),
                                    ],
                                  ),
                                  isThreeLine: true,
                                ),
                              );
                            },
                          ),
                  ),
                ],
              );
            },
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stack) => Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, size: 60, color: Colors.red),
                  const SizedBox(height: 16),
                  const Text('حدث خطأ أثناء تحميل البيانات',
                      style: TextStyle(color: Colors.red, fontSize: 18)),
                  const SizedBox(height: 8),
                  Text('$error',
                      style: const TextStyle(color: Colors.grey, fontSize: 14)),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      final _ = ref.refresh(employeeListProvider);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: kMainColor,
                    ),
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            ),
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: kMainColor,
        onPressed: () {
          _showAddEmployeeDialog(context);
        },
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  void _showAddEmployeeDialog(BuildContext context) {
    _resetForm();

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Consumer(builder: (context, ref, _) {
          return AddEmployeeScreen(
            listOfEmployees: ref.read(employeeListProvider).value ?? [],
            employeeModel: selectedEmployee,
          );
        }),
      ),
    );
  }

  void _editEmployee(EmployeeModel employee) {
    selectedEmployee = employee;
    _showAddEmployeeDialog(context);
  }

  void _deleteEmployee(WidgetRef ref, EmployeeModel employee) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذا الموظف؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              EasyLoading.show(status: 'جاري الحذف...');
              try {
                await ref
                    .read(employeeNotifierProvider.notifier)
                    .deleteEmployee(employee.id);
                EasyLoading.showSuccess('تم حذف الموظف بنجاح');
              } catch (e) {
                EasyLoading.showError('حدث خطأ أثناء الحذف: $e');
              }
            },
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}
