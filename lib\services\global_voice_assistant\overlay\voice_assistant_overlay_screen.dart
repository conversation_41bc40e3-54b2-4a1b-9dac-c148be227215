import 'package:flutter/material.dart';
import 'package:mobile_pos/services/global_voice_assistant/voice_assistant_exports.dart';
import 'package:mobile_pos/services/global_voice_assistant/core/voice_assistant_initialization_manager.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:permission_handler/permission_handler.dart';

/// شاشة المساعد الصوتي التفاعلية
class VoiceAssistantOverlayScreen extends StatefulWidget {
  final String? initialMessage;

  const VoiceAssistantOverlayScreen({
    super.key,
    this.initialMessage,
  });

  @override
  State<VoiceAssistantOverlayScreen> createState() =>
      _VoiceAssistantOverlayScreenState();
}

class _VoiceAssistantOverlayScreenState
    extends State<VoiceAssistantOverlayScreen> with TickerProviderStateMixin {
  // الخدمات
  final VoiceAssistantInitializationManager _initManager =
      VoiceAssistantInitializationManager();
  final VoiceResponseEngine _responseEngine = VoiceResponseEngine();
  final VoiceRecordingService _voiceService = VoiceRecordingService();
  final stt.SpeechToText _speechToText = stt.SpeechToText();

  // حالة المساعد
  AssistantState _currentState = AssistantState.idle;
  String _displayText = 'أهلاً، كيف يمكنني مساعدتك؟';
  String _statusText = 'اضغط للتحدث أو اكتب رسالتك';

  // متحكمات النص والصوت
  final TextEditingController _textController = TextEditingController();
  bool _isListening = false;
  bool _speechEnabled = false;
  String _recognizedText = '';

  // الأنيميشن
  late AnimationController _pulseController;
  late AnimationController _fadeController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeServices();

    if (widget.initialMessage != null) {
      _displayText = widget.initialMessage!;
      _currentState = AssistantState.responding;
      _statusText = 'المساعد يرد...';
    }

    // بدء الاستماع بعد تأخير قصير
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        _startListening();
      }
    });
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_fadeController);

    _fadeController.forward();
    _pulseController.repeat(reverse: true);
  }

  void _initializeServices() async {
    try {
      // تهيئة المدير الموحد أولاً
      await _initManager.initializeGlobalVoiceAssistant();

      await _responseEngine.initialize();
      await _voiceService.initialize();

      // تهيئة Speech-to-Text
      _speechEnabled = await _speechToText.initialize(
        onError: (error) {
          debugPrint('خطأ في Speech-to-Text: $error');
          setState(() {
            _isListening = false;
            _statusText = 'حدث خطأ في التعرف على الصوت';
          });
        },
        onStatus: (status) {
          debugPrint('حالة Speech-to-Text: $status');
          if (status == 'done' || status == 'notListening') {
            setState(() {
              _isListening = false;
            });
          }
        },
      );

      debugPrint('تم تهيئة جميع الخدمات بنجاح');
    } catch (e) {
      debugPrint('خطأ في تهيئة الخدمات: $e');
    }
  }

  /// بدء الاستماع للأمر الصوتي
  Future<void> _startListening() async {
    if (!_speechEnabled) {
      setState(() {
        _statusText = 'خدمة التعرف على الصوت غير متاحة';
      });
      return;
    }

    // طلب إذن الميكروفون
    final permission = await Permission.microphone.request();
    if (permission != PermissionStatus.granted) {
      setState(() {
        _statusText = 'يرجى السماح بالوصول للميكروفون';
      });
      return;
    }

    setState(() {
      _currentState = AssistantState.listening;
      _statusText = 'أستمع إليك الآن...';
      _isListening = true;
      _recognizedText = '';
    });

    try {
      await _speechToText.listen(
        onResult: (result) {
          setState(() {
            _recognizedText = result.recognizedWords;
            _displayText = _recognizedText.isEmpty
                ? 'أستمع إليك الآن...'
                : 'سمعت: $_recognizedText';
          });

          if (result.finalResult && _recognizedText.isNotEmpty) {
            _processCommand(_recognizedText);
          }
        },
        localeId: 'ar-SA',
        listenFor: const Duration(seconds: 10),
        pauseFor: const Duration(seconds: 3),
      );
    } catch (e) {
      debugPrint('خطأ في بدء الاستماع: $e');
      setState(() {
        _currentState = AssistantState.idle;
        _statusText = 'حدث خطأ في الاستماع';
        _isListening = false;
      });
    }
  }

  /// إيقاف الاستماع
  void _stopListening() {
    if (_isListening) {
      _speechToText.stop();
      setState(() {
        _isListening = false;
        _currentState = AssistantState.idle;
        _statusText = 'تم إيقاف الاستماع';
      });
    }
  }

  /// معالجة الأمر (صوتي أو مكتوب)
  void _processCommand(String command) async {
    if (command.trim().isEmpty) {
      setState(() {
        _statusText = 'يرجى إدخال نص أو التحدث';
      });
      return;
    }

    setState(() {
      _currentState = AssistantState.processing;
      _statusText = 'جاري معالجة طلبك...';
      _displayText = 'طلبك: $command\n\nجاري المعالجة...';
      _isListening = false;
    });

    // إيقاف الاستماع إذا كان نشطاً
    if (_speechToText.isListening) {
      _speechToText.stop();
    }

    try {
      debugPrint('🎤 معالجة الأمر: $command');

      // معالجة الأمر باستخدام المساعد الذكي المحسن
      final result = await QuickAIAssistantService.processVoiceCommand(command);

      if (result['success']) {
        final response = result['message'] as String;
        await _displayResponse(response);
      } else {
        final errorMessage =
            result['message'] as String? ?? 'عذراً، لم أتمكن من معالجة طلبك';
        await _displayResponse(errorMessage);
      }
    } catch (e) {
      debugPrint('❌ خطأ في معالجة الأمر: $e');
      await _displayResponse('حدث خطأ في معالجة طلبك. يرجى المحاولة مرة أخرى.');
    }
  }

  /// عرض الرد
  Future<void> _displayResponse(String response) async {
    setState(() {
      _currentState = AssistantState.responding;
      _displayText = response;
      _statusText = 'المساعد يرد...';
    });

    try {
      // تشغيل الرد صوتياً
      await _responseEngine.speak(response);
    } catch (e) {
      debugPrint('خطأ في تشغيل الصوت: $e');
    }

    // العودة لحالة الخمول
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _currentState = AssistantState.idle;
          _statusText = 'اضغط للتحدث أو اكتب رسالتك';
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black.withOpacity(0.8),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Center(
          child: Container(
            margin: const EdgeInsets.all(20),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 20,
                  spreadRadius: 5,
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildHeader(),
                const SizedBox(height: 20),
                _buildAssistantIcon(),
                const SizedBox(height: 20),
                _buildMessageArea(),
                const SizedBox(height: 20),
                _buildStatusArea(),
                const SizedBox(height: 20),
                _buildActionButtons(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        const Text(
          '🤖 المساعد الصوتي',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.close, color: Colors.grey),
        ),
      ],
    );
  }

  Widget _buildAssistantIcon() {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _currentState == AssistantState.listening
              ? _pulseAnimation.value
              : 1.0,
          child: Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                colors: _getIconColors(),
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              boxShadow: [
                BoxShadow(
                  color: _getIconColors().first.withOpacity(0.3),
                  blurRadius: 20,
                  spreadRadius: 5,
                ),
              ],
            ),
            child: Icon(
              _getIconData(),
              size: 50,
              color: Colors.white,
            ),
          ),
        );
      },
    );
  }

  Widget _buildMessageArea() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(15),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Text(
        _displayText,
        style: const TextStyle(
          fontSize: 16,
          color: Colors.black87,
          height: 1.5,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildStatusArea() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (_currentState == AssistantState.listening ||
            _currentState == AssistantState.processing)
          Container(
            width: 10,
            height: 10,
            margin: const EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: _getStatusColor(),
            ),
          ),
        Text(
          _statusText,
          style: TextStyle(
            fontSize: 14,
            color: _getStatusColor(),
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        // حقل النص للكتابة
        Container(
          margin: const EdgeInsets.only(bottom: 15),
          padding: const EdgeInsets.symmetric(horizontal: 15),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(25),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _textController,
                  decoration: const InputDecoration(
                    hintText: 'اكتب رسالتك هنا...',
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(vertical: 12),
                  ),
                  textAlign: TextAlign.right,
                  onSubmitted: (text) {
                    if (text.trim().isNotEmpty) {
                      _processCommand(text.trim());
                      _textController.clear();
                    }
                  },
                ),
              ),
              IconButton(
                onPressed: () {
                  final text = _textController.text.trim();
                  if (text.isNotEmpty) {
                    _processCommand(text);
                    _textController.clear();
                  }
                },
                icon: const Icon(Icons.send, color: Colors.blue),
              ),
            ],
          ),
        ),

        // أزرار الإجراءات
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildActionButton(
              icon: _isListening ? Icons.mic_off : Icons.mic,
              label: _isListening ? 'إيقاف' : 'تحدث',
              onPressed: _isListening ? _stopListening : _startListening,
              color: _isListening ? Colors.red : Colors.blue,
            ),
            _buildActionButton(
              icon: Icons.volume_up,
              label: 'إعادة',
              onPressed: () => _responseEngine.speak(_displayText),
              color: Colors.green,
            ),
            _buildActionButton(
              icon: Icons.refresh,
              label: 'جديد',
              onPressed: _resetConversation,
              color: Colors.orange,
            ),
            _buildActionButton(
              icon: Icons.close,
              label: 'إغلاق',
              onPressed: () => Navigator.of(context).pop(),
              color: Colors.red,
            ),
          ],
        ),
      ],
    );
  }

  /// إعادة تعيين المحادثة
  void _resetConversation() {
    setState(() {
      _currentState = AssistantState.idle;
      _displayText = 'أهلاً، كيف يمكنني مساعدتك؟';
      _statusText = 'اضغط للتحدث أو اكتب رسالتك';
      _recognizedText = '';
    });
    _textController.clear();
    if (_isListening) {
      _stopListening();
    }
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback? onPressed,
    required Color color,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        ElevatedButton(
          onPressed: onPressed,
          style: ElevatedButton.styleFrom(
            backgroundColor: color,
            foregroundColor: Colors.white,
            shape: const CircleBorder(),
            padding: const EdgeInsets.all(15),
          ),
          child: Icon(icon, size: 24),
        ),
        const SizedBox(height: 5),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }

  List<Color> _getIconColors() {
    switch (_currentState) {
      case AssistantState.listening:
        return [Colors.blue, Colors.lightBlue];
      case AssistantState.processing:
        return [Colors.orange, Colors.deepOrange];
      case AssistantState.responding:
        return [Colors.green, Colors.lightGreen];
      case AssistantState.idle:
        return [Colors.grey, Colors.grey[400]!];
    }
  }

  IconData _getIconData() {
    switch (_currentState) {
      case AssistantState.listening:
        return Icons.mic;
      case AssistantState.processing:
        return Icons.psychology;
      case AssistantState.responding:
        return Icons.volume_up;
      case AssistantState.idle:
        return Icons.assistant;
    }
  }

  Color _getStatusColor() {
    switch (_currentState) {
      case AssistantState.listening:
        return Colors.blue;
      case AssistantState.processing:
        return Colors.orange;
      case AssistantState.responding:
        return Colors.green;
      case AssistantState.idle:
        return Colors.grey;
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _fadeController.dispose();
    _textController.dispose();

    // إيقاف الاستماع إذا كان نشطاً
    if (_speechToText.isListening) {
      _speechToText.stop();
    }

    super.dispose();
  }
}

/// حالات المساعد الصوتي
enum AssistantState {
  idle, // خامل
  listening, // يستمع
  processing, // يعالج
  responding, // يرد
}
