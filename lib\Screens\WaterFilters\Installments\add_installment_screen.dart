import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/model/water_filter_models.dart';
import 'package:mobile_pos/services/water_filter_service.dart';

class AddInstallmentScreen extends StatefulWidget {
  final WaterFilterSystem? system;

  const AddInstallmentScreen({super.key, this.system});

  @override
  State<AddInstallmentScreen> createState() => _AddInstallmentScreenState();
}

class _AddInstallmentScreenState extends State<AddInstallmentScreen> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();

  DateTime _selectedDate = DateTime.now().add(const Duration(days: 30));
  String? _selectedSystemId;
  List<WaterFilterSystem> _systems = [];
  bool _isLoading = false;
  bool _isLoadingSystems = true;

  @override
  void initState() {
    super.initState();
    if (widget.system != null) {
      _selectedSystemId = widget.system!.id;
    }
    _loadSystems();
  }

  @override
  void dispose() {
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _loadSystems() async {
    try {
      final data = await WaterFilterService.getData('Systems');
      final systems = <WaterFilterSystem>[];

      data.forEach((key, value) {
        try {
          final system = WaterFilterSystem.fromJson(
            Map<String, dynamic>.from(value),
          );
          // فقط الأنظمة النشطة التي لديها مبلغ متبقي
          if (system.status == FilterSystemStatus.active &&
              system.remainingAmount > 0) {
            systems.add(system);
          }
          debugPrint(
              'نظام ${system.serialNumber}: حالة=${system.status}, متبقي=${system.remainingAmount}');
        } catch (e) {
          debugPrint('خطأ في معالجة نظام: $e');
        }
      });

      setState(() {
        _systems = systems;
        _isLoadingSystems = false;
      });

      debugPrint('تم تحميل ${systems.length} نظام متاح للأقساط');
    } catch (e) {
      debugPrint('خطأ في تحميل الأنظمة: $e');
      setState(() => _isLoadingSystems = false);
    }
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365 * 2)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: kMainColor,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (date != null) {
      setState(() => _selectedDate = date);
    }
  }

  Future<void> _saveInstallment() async {
    if (!_formKey.currentState!.validate() || _selectedSystemId == null) {
      return;
    }

    setState(() => _isLoading = true);

    try {
      // الحصول على النظام المحدد
      final selectedSystem = _systems.firstWhere(
        (system) => system.id == _selectedSystemId,
      );

      // التحقق من أن المبلغ لا يتجاوز المبلغ المتبقي
      final amount = double.parse(_amountController.text);
      if (amount > selectedSystem.remainingAmount) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'المبلغ يتجاوز المبلغ المتبقي (${selectedSystem.remainingAmount.toStringAsFixed(2)} ج.م)',
              ),
              backgroundColor: Colors.red,
            ),
          );
          setState(() => _isLoading = false);
        }
        return;
      }

      // الحصول على عدد الأقساط الحالية لهذا النظام
      final installmentsData = await WaterFilterService.getData('Installments');
      int installmentNumber = 1;

      installmentsData.forEach((key, value) {
        try {
          final installment = WaterFilterInstallment.fromJson(
            Map<String, dynamic>.from(value),
          );
          if (installment.systemId == _selectedSystemId) {
            if (installment.installmentNumber >= installmentNumber) {
              installmentNumber = installment.installmentNumber + 1;
            }
          }
        } catch (e) {
          debugPrint('خطأ في معالجة قسط: $e');
        }
      });

      // إنشاء القسط الجديد
      final installment = WaterFilterInstallment(
        id: WaterFilterService.generateId(),
        systemId: _selectedSystemId!,
        installmentNumber: installmentNumber,
        amount: amount,
        dueDate: _selectedDate,
        status: InstallmentStatus.pending,
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // حفظ القسط
      final success = await WaterFilterService.saveData(
        'Installments/${installment.id}',
        installment.toJson(),
      );

      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إضافة القسط بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context, true);
        }
      } else {
        throw Exception('فشل في حفظ القسط');
      }
    } catch (e) {
      debugPrint('خطأ في حفظ القسط: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ القسط: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        title: Text(
          'إضافة قسط جديد',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        child: _isLoadingSystems
            ? const Center(child: CircularProgressIndicator())
            : Form(
                key: _formKey,
                child: ListView(
                  padding: const EdgeInsets.all(20),
                  children: [
                    // اختيار النظام
                    Text(
                      'النظام',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: kMainColor,
                      ),
                    ),
                    const SizedBox(height: 8),

                    // عرض رسالة إذا لم توجد أنظمة
                    if (_systems.isEmpty) ...[
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.orange.shade50,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.orange.shade200),
                        ),
                        child: Column(
                          children: [
                            Icon(
                              Icons.warning_amber,
                              color: Colors.orange.shade600,
                              size: 48,
                            ),
                            const SizedBox(height: 12),
                            Text(
                              'لا توجد أنظمة متاحة للأقساط',
                              style: GoogleFonts.cairo(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.orange.shade700,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'يجب إضافة أنظمة جديدة أو التأكد من وجود مبلغ متبقي',
                              style: GoogleFonts.cairo(
                                fontSize: 14,
                                color: Colors.orange.shade600,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 12),
                            ElevatedButton.icon(
                              onPressed: () {
                                Navigator.pop(context);
                                // يمكن إضافة navigation لشاشة إضافة نظام هنا
                              },
                              icon: const Icon(Icons.add),
                              label: Text(
                                'إضافة نظام جديد',
                                style: GoogleFonts.cairo(),
                              ),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.orange.shade600,
                                foregroundColor: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ] else ...[
                      Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: DropdownButtonFormField<String>(
                          value: _selectedSystemId,
                          decoration: const InputDecoration(
                            border: InputBorder.none,
                            contentPadding: EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                          ),
                          hint: Text(
                            'اختر النظام',
                            style:
                                GoogleFonts.cairo(color: Colors.grey.shade600),
                          ),
                          items: _systems.map((system) {
                            return DropdownMenuItem<String>(
                              value: system.id,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    'نظام ${system.serialNumber}',
                                    style: GoogleFonts.cairo(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  Text(
                                    'المتبقي: ${system.remainingAmount.toStringAsFixed(2)} ج.م',
                                    style: GoogleFonts.cairo(
                                      fontSize: 12,
                                      color: Colors.grey.shade600,
                                    ),
                                  ),
                                ],
                              ),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() => _selectedSystemId = value);
                          },
                          validator: (value) {
                            if (value == null) {
                              return 'يرجى اختيار النظام';
                            }
                            return null;
                          },
                        ),
                      ),
                    ],

                    const SizedBox(height: 20),

                    // المبلغ
                    Text(
                      'مبلغ القسط',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: kMainColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextFormField(
                      controller: _amountController,
                      keyboardType: TextInputType.number,
                      decoration: InputDecoration(
                        hintText: 'أدخل مبلغ القسط',
                        hintStyle:
                            GoogleFonts.cairo(color: Colors.grey.shade600),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(color: Colors.grey.shade300),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(color: Colors.grey.shade300),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(color: kMainColor),
                        ),
                        suffixText: 'ج.م',
                        suffixStyle:
                            GoogleFonts.cairo(color: Colors.grey.shade600),
                      ),
                      style: GoogleFonts.cairo(),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال مبلغ القسط';
                        }
                        final amount = double.tryParse(value);
                        if (amount == null || amount <= 0) {
                          return 'يرجى إدخال مبلغ صحيح';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 20),

                    // تاريخ الاستحقاق
                    Text(
                      'تاريخ الاستحقاق',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: kMainColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    InkWell(
                      onTap: _selectDate,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 16,
                        ),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.calendar_today,
                              color: kMainColor,
                              size: 20,
                            ),
                            const SizedBox(width: 12),
                            Text(
                              '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                              style: GoogleFonts.cairo(
                                fontSize: 16,
                                color: Colors.black87,
                              ),
                            ),
                            const Spacer(),
                            Icon(
                              Icons.arrow_drop_down,
                              color: Colors.grey.shade600,
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 20),

                    // ملاحظات
                    Text(
                      'ملاحظات (اختياري)',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: kMainColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextFormField(
                      controller: _notesController,
                      maxLines: 3,
                      decoration: InputDecoration(
                        hintText: 'أدخل أي ملاحظات إضافية...',
                        hintStyle:
                            GoogleFonts.cairo(color: Colors.grey.shade600),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(color: Colors.grey.shade300),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(color: Colors.grey.shade300),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(color: kMainColor),
                        ),
                      ),
                      style: GoogleFonts.cairo(),
                    ),

                    const SizedBox(height: 30),

                    // زر الحفظ
                    SizedBox(
                      width: double.infinity,
                      height: 50,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _saveInstallment,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: kMainColor,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 2,
                        ),
                        child: _isLoading
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2,
                                ),
                              )
                            : Text(
                                'إضافة القسط',
                                style: GoogleFonts.cairo(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                      ),
                    ),

                    const SizedBox(height: 20),
                  ],
                ),
              ),
      ),
    );
  }
}
