// دالة مساعدة لتحديث المديونية بشكل موحد
import 'dart:convert';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/currency.dart';

/// تحديث مديونية العميل بشكل موحد
///
/// [phoneNumber] رقم هاتف العميل
/// [amount] المبلغ المراد تحديثه (موجب للإضافة، سالب للخصم)
/// [updateType] نوع التحديث (إضافة مديونية، تحصيل مديونية)
/// [updateRemainingBalance] هل يتم تحديث الرصيد المتبقي أيضاً
/// [selectedInvoice] رقم الفاتورة المحددة (إذا كان التحديث مرتبط بفاتورة محددة)
Future<bool> updateCustomerDebt({
  required String phoneNumber,
  required int amount,
  required String updateType,
  bool updateRemainingBalance = true,
  String? selectedInvoice,
}) async {
  try {
    final ref = FirebaseDatabase.instance.ref(constUserId).child('Customers');
    ref.keepSynced(true);
    String? key;
    bool success = false;

    await ref.orderByKey().get().then((value) {
      for (var element in value.children) {
        var data = jsonDecode(jsonEncode(element.value));
        if (data['phoneNumber'] == phoneNumber) {
          key = element.key;

          // تحويل قيمة الدين السابقة إلى رقم صحيح مع معالجة الأخطاء
          int previousDue = 0;
          try {
            previousDue = element.child('due').value.toString().toInt();
          } catch (e) {
            // استخدام القيمة الافتراضية في حالة حدوث خطأ
            previousDue = 0;
            // خطأ في قراءة قيمة الدين السابقة
          }

          // حساب قيمة الدين الجديدة
          int totalDue = 0;
          if (updateType == 'add') {
            // إضافة مديونية (مثل إنشاء فاتورة جديدة)
            totalDue = previousDue + amount;
          } else if (updateType == 'collect') {
            // تحصيل مديونية
            totalDue = previousDue - amount;
            if (totalDue < 0) totalDue = 0; // منع القيم السالبة
          }

          // تحديث قيمة الدين
          Map<String, dynamic> updates = {'due': '$totalDue'};

          // تحديث الرصيد المتبقي إذا كان مطلوباً
          if (updateRemainingBalance &&
              (selectedInvoice == null || selectedInvoice.isEmpty)) {
            int openingBalanceCollection = 0;
            try {
              openingBalanceCollection =
                  element.child('remainedBalance').value.toString().toInt();
            } catch (e) {
              openingBalanceCollection = 0;
              // خطأ في قراءة قيمة الرصيد المتبقي
            }

            int remainBalance = 0;
            if (updateType == 'add') {
              remainBalance = openingBalanceCollection + amount;
            } else if (updateType == 'collect') {
              remainBalance = openingBalanceCollection - amount;
              if (remainBalance < 0) remainBalance = 0; // منع القيم السالبة
            }

            updates['remainedBalance'] = '$remainBalance';
          }

          // تنفيذ التحديث
          ref.child(key!).update(updates);
          success = true;

          // تسجيل التحديث
          logDebtUpdate(
              phoneNumber: phoneNumber,
              previousDue: previousDue,
              newDue: totalDue,
              amount: amount,
              updateType: updateType,
              invoiceNumber: selectedInvoice);
        }
      }
    });

    return success;
  } catch (e) {
    // خطأ في تحديث مديونية العميل
    EasyLoading.showError('حدث خطأ أثناء تحديث بيانات العميل');
    return false;
  }
}

/// تحديث معاملة الدين (الفاتورة)
Future<bool> updateInvoiceDebt(
    {required String type,
    required String invoice,
    required int remainDueAmount}) async {
  try {
    final ref = type == 'Supplier'
        ? FirebaseDatabase.instance.ref('$constUserId/Purchase Transition/')
        : FirebaseDatabase.instance.ref('$constUserId/Sales Transition/');
    ref.keepSynced(true);
    bool success = false;

    await ref.orderByKey().get().then((value) {
      for (var element in value.children) {
        var data = jsonDecode(jsonEncode(element.value));
        if (data['invoiceNumber'] == invoice) {
          ref.child(element.key.toString()).update({
            'dueAmount': '$remainDueAmount',
          });
          success = true;
        }
      }
    });

    return success;
  } catch (e) {
    // خطأ في تحديث معاملة الدين
    return false;
  }
}

/// تسجيل تحديث المديونية
void logDebtUpdate(
    {required String phoneNumber,
    required int previousDue,
    required int newDue,
    required int amount,
    required String updateType,
    String? invoiceNumber}) {
  try {
    final ref = FirebaseDatabase.instance.ref('$constUserId/DebtUpdateLogs');
    ref.push().set({
      'phoneNumber': phoneNumber,
      'previousDue': previousDue,
      'newDue': newDue,
      'amount': amount,
      'updateType': updateType,
      'invoiceNumber': invoiceNumber ?? '',
      'timestamp': DateTime.now().toString(),
    });
  } catch (e) {
    // خطأ في تسجيل تحديث المديونية
  }
}

/// التحقق من اتساق بيانات المديونية للعميل
Future<bool> verifyCustomerDebtConsistency(String phoneNumber) async {
  try {
    final ref = FirebaseDatabase.instance.ref(constUserId).child('Customers');
    ref.keepSynced(true);

    final snapshot =
        await ref.orderByChild('phoneNumber').equalTo(phoneNumber).get();
    if (!snapshot.exists) return false;

    for (var element in snapshot.children) {
      // قراءة قيمة الدين الحالية
      int currentDue = 0;
      try {
        currentDue = element.child('due').value.toString().toInt();
      } catch (e) {
        currentDue = 0;
      }

      // حساب إجمالي الدين من الفواتير
      int calculatedDue = await calculateTotalDueFromInvoices(phoneNumber);

      // التحقق من اتساق البيانات
      if (currentDue != calculatedDue) {
        // تصحيح البيانات
        await ref.child(element.key!).update({'due': '$calculatedDue'});

        // تسجيل عدم الاتساق
        logInconsistency(
            phoneNumber: phoneNumber,
            storedDue: currentDue,
            calculatedDue: calculatedDue);

        return false; // البيانات غير متسقة وتم تصحيحها
      }

      return true; // البيانات متسقة
    }

    return false;
  } catch (e) {
    // خطأ في التحقق من اتساق بيانات المديونية
    return false;
  }
}

/// حساب إجمالي الدين من الفواتير
Future<int> calculateTotalDueFromInvoices(String phoneNumber) async {
  int totalDue = 0;

  try {
    // فحص فواتير المبيعات
    final salesRef =
        FirebaseDatabase.instance.ref('$constUserId/Sales Transition/');
    final salesSnapshot =
        await salesRef.orderByChild('customerPhone').equalTo(phoneNumber).get();

    if (salesSnapshot.exists) {
      for (var element in salesSnapshot.children) {
        var data = jsonDecode(jsonEncode(element.value));
        int dueAmount = int.tryParse(data['dueAmount']?.toString() ?? '0') ?? 0;
        totalDue += dueAmount;
      }
    }

    // فحص فواتير المشتريات (للموردين)
    final purchaseRef =
        FirebaseDatabase.instance.ref('$constUserId/Purchase Transition/');
    final purchaseSnapshot = await purchaseRef
        .orderByChild('customerPhone')
        .equalTo(phoneNumber)
        .get();

    if (purchaseSnapshot.exists) {
      for (var element in purchaseSnapshot.children) {
        var data = jsonDecode(jsonEncode(element.value));
        int dueAmount = int.tryParse(data['dueAmount']?.toString() ?? '0') ?? 0;
        totalDue += dueAmount;
      }
    }

    return totalDue;
  } catch (e) {
    // خطأ في حساب إجمالي الدين من الفواتير
    return 0;
  }
}

/// تسجيل حالات عدم اتساق البيانات
void logInconsistency(
    {required String phoneNumber,
    required int storedDue,
    required int calculatedDue}) {
  try {
    final ref =
        FirebaseDatabase.instance.ref('$constUserId/DebtInconsistencyLogs');
    ref.push().set({
      'phoneNumber': phoneNumber,
      'storedDue': storedDue,
      'calculatedDue': calculatedDue,
      'difference': calculatedDue - storedDue,
      'timestamp': DateTime.now().toString(),
    });
  } catch (e) {
    // خطأ في تسجيل حالة عدم اتساق
  }
}

/// امتداد لتحويل النص إلى رقم صحيح مع معالجة الأخطاء
extension StringToInt on String {
  int toInt() {
    return int.tryParse(this) ?? 0;
  }
}
