import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/models/hrm_models.dart';
import 'package:nb_utils/nb_utils.dart';
import 'provider/designation_provider.dart';

class DesignationScreen extends ConsumerStatefulWidget {
  const DesignationScreen({super.key});

  static const String route = '/hrm/designation';

  @override
  ConsumerState<DesignationScreen> createState() => _DesignationScreenState();
}

class _DesignationScreenState extends ConsumerState<DesignationScreen> {
  String searchQuery = '';
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _departmentController = TextEditingController();
  DesignationModel? selectedDesignation;

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _departmentController.dispose();
    super.dispose();
  }

  void _resetForm() {
    _titleController.clear();
    _descriptionController.clear();
    _departmentController.clear();
    selectedDesignation = null;
  }

  @override
  Widget build(BuildContext context) {
    final designationsAsyncValue = ref.watch(designationListProvider);

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('المسميات الوظيفية'),
        backgroundColor: kMainColor,
        centerTitle: true,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        titleTextStyle: const TextStyle(
            color: Colors.white, fontSize: 20, fontWeight: FontWeight.bold),
      ),
      body: designationsAsyncValue.when(
        data: (designations) {
          // تطبيق البحث
          List<DesignationModel> filteredDesignations =
              designations.where((designation) {
            return searchQuery.isEmpty ||
                designation.title
                    .toLowerCase()
                    .contains(searchQuery.toLowerCase()) ||
                designation.department
                    .toLowerCase()
                    .contains(searchQuery.toLowerCase());
          }).toList();

          return Column(
            children: [
              // شريط البحث
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: TextField(
                  decoration: InputDecoration(
                    hintText: 'بحث بالعنوان أو القسم',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  onChanged: (value) {
                    setState(() {
                      searchQuery = value;
                    });
                  },
                ),
              ),

              // قائمة المسميات الوظيفية
              Expanded(
                child: filteredDesignations.isEmpty
                    ? const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.work_outline,
                                size: 80, color: Colors.grey),
                            SizedBox(height: 16),
                            Text(
                              'لا توجد مسميات وظيفية',
                              style: TextStyle(
                                  fontSize: 18, fontWeight: FontWeight.bold),
                            ),
                            SizedBox(height: 8),
                            Text(
                              'قم بإضافة مسميات وظيفية جديدة',
                              style: TextStyle(color: Colors.grey),
                            ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        itemCount: filteredDesignations.length,
                        itemBuilder: (context, index) {
                          final designation = filteredDesignations[index];
                          return Card(
                            margin: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 8),
                            elevation: 2,
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12)),
                            child: ListTile(
                              title: Text(
                                designation.title,
                                style: const TextStyle(
                                    fontWeight: FontWeight.bold),
                              ),
                              subtitle: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('القسم: ${designation.department}'),
                                  Text(
                                    designation.description,
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                              ),
                              trailing: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  IconButton(
                                    icon: const Icon(Icons.edit,
                                        color: Colors.blue),
                                    onPressed: () {
                                      _editDesignation(designation);
                                    },
                                  ),
                                  IconButton(
                                    icon: const Icon(Icons.delete,
                                        color: Colors.red),
                                    onPressed: () {
                                      _deleteDesignation(designation);
                                    },
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
              ),
            ],
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 60, color: Colors.red),
              const SizedBox(height: 16),
              const Text('حدث خطأ أثناء تحميل البيانات',
                  style: TextStyle(color: Colors.red, fontSize: 18)),
              const SizedBox(height: 8),
              Text('$error',
                  style: const TextStyle(color: Colors.grey, fontSize: 14)),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  final _ = ref.refresh(designationListProvider);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: kMainColor,
                ),
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: kMainColor,
        onPressed: () {
          _showAddDesignationDialog(context);
        },
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  void _showAddDesignationDialog(BuildContext context) {
    _resetForm();

    if (selectedDesignation != null) {
      _titleController.text = selectedDesignation!.title;
      _descriptionController.text = selectedDesignation!.description;
      _departmentController.text = selectedDesignation!.department;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(selectedDesignation == null
            ? 'إضافة مسمى وظيفي جديد'
            : 'تعديل المسمى الوظيفي'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: 'المسمى الوظيفي',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: _departmentController,
                decoration: const InputDecoration(
                  labelText: 'القسم',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: _descriptionController,
                maxLines: 3,
                decoration: const InputDecoration(
                  labelText: 'الوصف',
                  border: OutlineInputBorder(),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: kMainColor,
            ),
            onPressed: () {
              _saveDesignation(context);
            },
            child: Text(selectedDesignation == null ? 'إضافة' : 'تحديث'),
          ),
        ],
      ),
    );
  }

  void _saveDesignation(BuildContext context) async {
    if (_titleController.text.isEmpty || _departmentController.text.isEmpty) {
      toast('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    Navigator.pop(context);
    EasyLoading.show(
        status: selectedDesignation == null
            ? 'جاري الإضافة...'
            : 'جاري التحديث...');

    try {
      final designation = DesignationModel(
        id: selectedDesignation?.id ??
            DateTime.now().millisecondsSinceEpoch.toString(),
        title: _titleController.text,
        description: _descriptionController.text,
        department: _departmentController.text,
        createdAt: selectedDesignation?.createdAt ?? DateTime.now(),
      );

      if (selectedDesignation == null) {
        await ref
            .read(designationNotifierProvider.notifier)
            .addDesignation(designation);
        EasyLoading.showSuccess('تمت الإضافة بنجاح');
      } else {
        await ref
            .read(designationNotifierProvider.notifier)
            .updateDesignation(designation);
        EasyLoading.showSuccess('تم التحديث بنجاح');
      }
    } catch (e) {
      EasyLoading.showError('حدث خطأ: $e');
    }
  }

  void _editDesignation(DesignationModel designation) {
    setState(() {
      selectedDesignation = designation;
    });
    _titleController.text = designation.title;
    _descriptionController.text = designation.description;
    _departmentController.text = designation.department;
    _showAddDesignationDialog(context);
  }

  void _deleteDesignation(DesignationModel designation) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذا المسمى الوظيفي؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              EasyLoading.show(status: 'جاري الحذف...');
              try {
                await ref
                    .read(designationNotifierProvider.notifier)
                    .deleteDesignation(designation.id);
                EasyLoading.showSuccess('تم الحذف بنجاح');
              } catch (e) {
                EasyLoading.showError('حدث خطأ أثناء الحذف: $e');
              }
            },
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}
