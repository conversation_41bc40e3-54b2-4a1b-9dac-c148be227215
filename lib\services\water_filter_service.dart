import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/model/water_filter_models.dart';
import 'package:mobile_pos/services/firebase_database_service.dart';
import 'package:mobile_pos/services/firebase_listener_manager.dart';

/// خدمة إدارة فلاتر المياه الرئيسية
/// تتبع نفس نمط الخدمات الموجودة في المشروع
class WaterFilterService {
  // نمط Singleton للتأكد من وجود نسخة واحدة فقط
  static final WaterFilterService _instance = WaterFilterService._internal();
  factory WaterFilterService() => _instance;
  WaterFilterService._internal();

  // مراجع قاعدة البيانات
  static DatabaseReference get _database => FirebaseDatabase.instance.ref();
  static DatabaseReference get _waterFiltersRef =>
      _database.child(constUserId).child('WaterFilters');

  /// الحصول على مرجع المنتجات
  static DatabaseReference get productsRef =>
      _waterFiltersRef.child('Products');

  /// الحصول على مرجع العملاء
  static DatabaseReference get customersRef =>
      _waterFiltersRef.child('Customers');

  /// الحصول على مرجع الأنظمة
  static DatabaseReference get systemsRef => _waterFiltersRef.child('Systems');

  /// الحصول على مرجع الصيانة
  static DatabaseReference get maintenanceRef =>
      _waterFiltersRef.child('Maintenance');

  /// الحصول على مرجع الأقساط
  static DatabaseReference get installmentsRef =>
      _waterFiltersRef.child('Installments');

  /// الحصول على مرجع التقارير
  static DatabaseReference get reportsRef => _waterFiltersRef.child('Reports');

  /// تهيئة قاعدة البيانات
  static Future<void> initialize() async {
    try {
      debugPrint('🔧 تهيئة خدمة فلاتر المياه...');

      // التأكد من تهيئة Firebase Database Service
      FirebaseDatabaseService.initialize();

      debugPrint('✅ تم تهيئة خدمة فلاتر المياه بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة فلاتر المياه: $e');
    }
  }

  /// الاستماع للتغييرات في مسار معين
  static Stream<DatabaseEvent> listenToPath(
    String path, {
    String? ownerId,
    String? eventType = 'value',
    bool keepSynced = true,
  }) {
    final fullPath = 'WaterFilters/$path';
    return FirebaseDatabaseService.listenToPath(
      fullPath,
      ownerId: ownerId ?? 'water_filter_service',
      eventType: eventType,
      keepSynced: keepSynced,
    );
  }

  /// إلغاء المستمع
  static void cancelListener(String path, {String? ownerId}) {
    final fullPath = 'WaterFilters/$path';
    FirebaseDatabaseService.cancelListener(
      fullPath,
      ownerId: ownerId ?? 'water_filter_service',
    );
  }

  /// إلغاء جميع المستمعين لمالك معين
  static void cancelListenersByOwner(String ownerId) {
    FirebaseListenerManager().cancelListenersByOwner(ownerId);
  }

  /// الحصول على البيانات من مسار معين
  static Future<Map<String, dynamic>> getData(String path) async {
    try {
      debugPrint('📥 جلب البيانات من: WaterFilters/$path');

      final ref = _waterFiltersRef.child(path);
      final snapshot = await ref.get();

      if (snapshot.exists && snapshot.value != null) {
        final data = Map<String, dynamic>.from(snapshot.value as Map);
        debugPrint('✅ تم جلب ${data.length} عنصر من $path');
        return data;
      } else {
        debugPrint('⚠️ لا توجد بيانات في $path');
        return {};
      }
    } catch (e) {
      debugPrint('❌ خطأ في جلب البيانات من $path: $e');
      return {};
    }
  }

  /// حفظ البيانات في مسار معين
  static Future<bool> saveData(String path, Map<String, dynamic> data) async {
    try {
      debugPrint('💾 حفظ البيانات في: WaterFilters/$path');

      final ref = _waterFiltersRef.child(path);
      final sanitizedData = sanitizeData(addTimestamps(data));

      await ref.set(sanitizedData);
      debugPrint('✅ تم حفظ البيانات بنجاح في $path');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في حفظ البيانات في $path: $e');
      return false;
    }
  }

  /// تحديث البيانات في مسار معين
  static Future<bool> updateData(String path, Map<String, dynamic> data) async {
    try {
      debugPrint('🔄 تحديث البيانات في: WaterFilters/$path');

      final ref = _waterFiltersRef.child(path);
      final sanitizedData = sanitizeData(addTimestamps(data, isUpdate: true));

      await ref.update(sanitizedData);
      debugPrint('✅ تم تحديث البيانات بنجاح في $path');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في تحديث البيانات في $path: $e');
      return false;
    }
  }

  /// حذف البيانات من مسار معين
  static Future<bool> deleteData(String path) async {
    try {
      debugPrint('🗑️ حذف البيانات من: WaterFilters/$path');

      final ref = _waterFiltersRef.child(path);
      await ref.remove();

      debugPrint('✅ تم حذف البيانات بنجاح من $path');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في حذف البيانات من $path: $e');
      return false;
    }
  }

  /// الحصول على إحصائيات سريعة
  static Future<Map<String, dynamic>> getQuickStats() async {
    try {
      debugPrint('📊 جلب الإحصائيات السريعة لفلاتر المياه...');

      final stats = <String, dynamic>{
        'activeSystems': 0,
        'maintenanceDue': 0,
        'overdueInstallments': 0,
        'totalCustomers': 0,
        'totalProducts': 0,
        'monthlyRevenue': 0.0,
      };

      // جلب عدد الأنظمة النشطة
      final systemsSnapshot = await systemsRef.get();
      if (systemsSnapshot.exists) {
        int activeSystems = 0;
        int maintenanceDue = 0;

        for (final child in systemsSnapshot.children) {
          try {
            final systemData = Map<String, dynamic>.from(child.value as Map);
            final system = WaterFilterSystem.fromJson(systemData);

            if (system.status == FilterSystemStatus.active) {
              activeSystems++;
            }

            if (system.isMaintenanceOverdue ||
                system.status == FilterSystemStatus.needsMaintenance) {
              maintenanceDue++;
            }
          } catch (e) {
            debugPrint('خطأ في معالجة نظام: $e');
          }
        }

        stats['activeSystems'] = activeSystems;
        stats['maintenanceDue'] = maintenanceDue;
      }

      // جلب عدد الأقساط المتأخرة
      final installmentsSnapshot = await installmentsRef.get();
      if (installmentsSnapshot.exists) {
        int overdueInstallments = 0;

        for (final child in installmentsSnapshot.children) {
          try {
            final installmentData =
                Map<String, dynamic>.from(child.value as Map);
            final installment =
                WaterFilterInstallment.fromJson(installmentData);

            if (installment.isOverdue) {
              overdueInstallments++;
            }
          } catch (e) {
            debugPrint('خطأ في معالجة قسط: $e');
          }
        }

        stats['overdueInstallments'] = overdueInstallments;
      }

      // جلب عدد العملاء
      final customersSnapshot = await customersRef.get();
      if (customersSnapshot.exists) {
        stats['totalCustomers'] = customersSnapshot.children.length;
      }

      // جلب عدد المنتجات
      final productsSnapshot = await productsRef.get();
      if (productsSnapshot.exists) {
        stats['totalProducts'] = productsSnapshot.children.length;
      }

      debugPrint('✅ تم جلب الإحصائيات السريعة بنجاح');
      return stats;
    } catch (e) {
      debugPrint('❌ خطأ في جلب الإحصائيات السريعة: $e');
      return {
        'activeSystems': 0,
        'maintenanceDue': 0,
        'overdueInstallments': 0,
        'totalCustomers': 0,
        'totalProducts': 0,
        'monthlyRevenue': 0.0,
        'error': e.toString(),
      };
    }
  }

  /// التحقق من صحة البيانات
  static bool validateData(
      Map<String, dynamic> data, List<String> requiredFields) {
    for (final field in requiredFields) {
      if (!data.containsKey(field) || data[field] == null) {
        debugPrint('❌ حقل مطلوب مفقود: $field');
        return false;
      }
    }
    return true;
  }

  /// تنظيف البيانات
  static Map<String, dynamic> sanitizeData(Map<String, dynamic> data) {
    final sanitized = <String, dynamic>{};

    for (final entry in data.entries) {
      if (entry.value != null) {
        sanitized[entry.key] = entry.value;
      }
    }

    return sanitized;
  }

  /// إنشاء معرف فريد
  static String generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  /// تحديث الطابع الزمني
  static Map<String, dynamic> addTimestamps(
    Map<String, dynamic> data, {
    bool isUpdate = false,
  }) {
    final now = DateTime.now().toIso8601String();

    if (!isUpdate) {
      data['createdAt'] = now;
    }
    data['updatedAt'] = now;

    return data;
  }
}
