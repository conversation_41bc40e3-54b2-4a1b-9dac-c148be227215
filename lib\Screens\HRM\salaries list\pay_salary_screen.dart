import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/models/hrm_models.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:intl/intl.dart';
import 'model/pay_salary_model.dart';
import 'provider/salary_provider.dart';

class PaySalaryScreen extends ConsumerStatefulWidget {
  final List<EmployeeModel> listOfEmployees;
  final PaySalaryModel? payedSalary;

  const PaySalaryScreen({
    super.key,
    required this.listOfEmployees,
    this.payedSalary,
  });

  @override
  ConsumerState<PaySalaryScreen> createState() => _PaySalaryScreenState();
}

class _PaySalaryScreenState extends ConsumerState<PaySalaryScreen> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  EmployeeModel? selectedEmployee;
  DateTime selectedDate = DateTime.now();
  String selectedMonth = 'May'; // استخدام قيمة ثابتة من قائمة الشهور المتاحة
  String selectedYear = DateTime.now().year.toString();

  final TextEditingController _salaryController = TextEditingController();
  final TextEditingController _paidAmountController = TextEditingController();
  final TextEditingController _noteController = TextEditingController();

  final List<String> months = [
    'January', // يناير
    'February', // فبراير
    'March', // مارس
    'April', // أبريل
    'May', // مايو
    'June', // يونيو
    'July', // يوليو
    'August', // أغسطس
    'September', // سبتمبر
    'October', // أكتوبر
    'November', // نوفمبر
    'December' // ديسمبر
  ];

  // تحويل اسم الشهر الإنجليزي إلى العربي للعرض
  String getArabicMonthName(String englishMonth) {
    switch (englishMonth) {
      case 'January':
        return 'يناير';
      case 'February':
        return 'فبراير';
      case 'March':
        return 'مارس';
      case 'April':
        return 'أبريل';
      case 'May':
        return 'مايو';
      case 'June':
        return 'يونيو';
      case 'July':
        return 'يوليو';
      case 'August':
        return 'أغسطس';
      case 'September':
        return 'سبتمبر';
      case 'October':
        return 'أكتوبر';
      case 'November':
        return 'نوفمبر';
      case 'December':
        return 'ديسمبر';
      default:
        return englishMonth;
    }
  }

  final List<String> years = [
    '2020',
    '2021',
    '2022',
    '2023',
    '2024',
    '2025',
    '2026',
    '2027',
    '2028',
    '2029',
    '2030'
  ];

  @override
  void initState() {
    super.initState();

    if (widget.payedSalary != null) {
      // تعبئة البيانات للتعديل
      // التأكد من أن الشهر موجود في قائمة الشهور
      if (months.contains(widget.payedSalary!.month)) {
        selectedMonth = widget.payedSalary!.month;
      } else {
        // إذا لم يكن الشهر موجودًا، استخدم الشهر الحالي
        selectedMonth = 'May'; // أو أي شهر آخر من القائمة
      }

      // التأكد من أن السنة موجودة في قائمة السنوات
      if (years.contains(widget.payedSalary!.year)) {
        selectedYear = widget.payedSalary!.year;
      } else {
        // إذا لم تكن السنة موجودة، استخدم السنة الحالية
        selectedYear = DateTime.now().year.toString();
      }

      _salaryController.text = widget.payedSalary!.netSalary.toString();
      _paidAmountController.text = widget.payedSalary!.paySalary.toString();
      _noteController.text = widget.payedSalary!.note;
      selectedDate = widget.payedSalary!.payingDate;

      // البحث عن الموظف المناسب
      for (var employee in widget.listOfEmployees) {
        if (employee.name == widget.payedSalary!.employeeName) {
          selectedEmployee = employee;
          break;
        }
      }
    }
  }

  @override
  void dispose() {
    _salaryController.dispose();
    _paidAmountController.dispose();
    _noteController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.payedSalary == null ? 'دفع راتب' : 'تعديل راتب'),
        backgroundColor: kMainColor,
        centerTitle: true,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        titleTextStyle: const TextStyle(
            color: Colors.white, fontSize: 20, fontWeight: FontWeight.bold),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // اختيار الموظف
              DropdownButtonFormField<EmployeeModel>(
                decoration: const InputDecoration(
                  labelText: 'اختر الموظف',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.person),
                ),
                value: selectedEmployee,
                items: widget.listOfEmployees.map((employee) {
                  return DropdownMenuItem<EmployeeModel>(
                    value: employee,
                    child: Text(employee.name),
                  );
                }).toList(),
                onChanged: widget.payedSalary != null
                    ? null
                    : (value) {
                        setState(() {
                          selectedEmployee = value;
                          if (value != null) {
                            _salaryController.text = value.salary.toString();
                          }
                        });
                      },
                validator: (value) {
                  if (value == null) {
                    return 'يرجى اختيار موظف';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // اختيار الشهر والسنة
              Row(
                children: [
                  Expanded(
                    child: DropdownButtonFormField<String>(
                      decoration: const InputDecoration(
                        labelText: 'الشهر',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.calendar_month),
                      ),
                      value: selectedMonth,
                      items: months.map((month) {
                        return DropdownMenuItem<String>(
                          value: month,
                          child: Text(getArabicMonthName(month)),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          selectedMonth = value!;
                        });
                      },
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى اختيار الشهر';
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: DropdownButtonFormField<String>(
                      decoration: const InputDecoration(
                        labelText: 'السنة',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.calendar_today),
                      ),
                      value: selectedYear,
                      items: years.map((year) {
                        return DropdownMenuItem<String>(
                          value: year,
                          child: Text(year),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          selectedYear = value!;
                        });
                      },
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى اختيار السنة';
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // الراتب الأساسي والمبلغ المدفوع
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _salaryController,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        labelText: 'الراتب الأساسي',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.attach_money),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال الراتب الأساسي';
                        }
                        if (double.tryParse(value) == null) {
                          return 'يرجى إدخال رقم صحيح';
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      controller: _paidAmountController,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        labelText: 'المبلغ المدفوع',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.payments),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال المبلغ المدفوع';
                        }
                        if (double.tryParse(value) == null) {
                          return 'يرجى إدخال رقم صحيح';
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // تاريخ الدفع
              InkWell(
                onTap: () async {
                  final DateTime? picked = await showDatePicker(
                    context: context,
                    initialDate: selectedDate,
                    firstDate: DateTime(2020),
                    lastDate: DateTime(2030),
                  );
                  if (picked != null && picked != selectedDate) {
                    setState(() {
                      selectedDate = picked;
                    });
                  }
                },
                child: InputDecorator(
                  decoration: const InputDecoration(
                    labelText: 'تاريخ الدفع',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.date_range),
                  ),
                  child: Row(
                    children: [
                      Text(
                        DateFormat.yMd().format(selectedDate),
                        style: const TextStyle(fontSize: 16),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        getArabicMonthName(
                            DateFormat('MMMM').format(selectedDate)),
                        style:
                            const TextStyle(fontSize: 16, color: Colors.grey),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // ملاحظات
              TextFormField(
                controller: _noteController,
                maxLines: 3,
                decoration: const InputDecoration(
                  labelText: 'ملاحظات',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.note),
                ),
              ),
              const SizedBox(height: 32),

              // أزرار الحفظ والإلغاء
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 32, vertical: 12),
                    ),
                    child: const Text('إلغاء',
                        style: TextStyle(color: Colors.white)),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: () {
                      _saveSalary();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: kMainColor,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 32, vertical: 12),
                    ),
                    child: Text(
                      widget.payedSalary == null ? 'دفع' : 'تحديث',
                      style: const TextStyle(color: Colors.white),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _saveSalary() async {
    if (_formKey.currentState?.validate() ?? false) {
      if (selectedEmployee == null) {
        toast('يرجى اختيار موظف');
        return;
      }

      // لا نحتاج إلى حفظ نسخة من السياق لأننا نستخدم mounted

      try {
        final salary = PaySalaryModel(
          id: widget.payedSalary?.id ??
              DateTime.now().millisecondsSinceEpoch.toString(),
          employeeId: selectedEmployee!.id,
          employeeName: selectedEmployee!.name,
          month: selectedMonth,
          year: selectedYear,
          netSalary: double.parse(_salaryController.text),
          paySalary: double.parse(_paidAmountController.text),
          payingDate: selectedDate,
          note: _noteController.text,
        );

        final notifier = ref.read(salaryNotifierProvider.notifier);

        if (widget.payedSalary == null) {
          await notifier.addSalary(salary);
          toast('تم دفع الراتب بنجاح');
        } else {
          await notifier.updateSalary(salary);
          toast('تم تحديث الراتب بنجاح');
        }

        // التحقق من أن الويدجت لا تزال مثبتة قبل استخدام السياق
        if (mounted) {
          Navigator.pop(context);
        }
      } catch (e) {
        toast('حدث خطأ: $e');
      }
    }
  }
}
