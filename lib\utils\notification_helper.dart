import 'package:flutter/material.dart';
import 'package:mobile_pos/model/unified_notification_model.dart';
import 'package:mobile_pos/models/system_notification_model.dart';
import 'package:mobile_pos/services/hidden_notifications_service.dart';
import 'package:mobile_pos/services/local_notification_service.dart';
import 'package:mobile_pos/services/system_notification_service.dart';

/// مساعد للتعامل مع الإشعارات بشكل آمن
class NotificationHelper {
  /// الحصول على قيمة من بيانات الإشعار بشكل آمن
  static T? getDataValue<T>(Map<String, dynamic> data, String key,
      {T? defaultValue}) {
    try {
      final value = data[key];
      if (value == null) {
        return defaultValue;
      }

      if (value is T) {
        return value;
      }

      // محاولة تحويل القيمة إلى النوع المطلوب
      if (T == int && value is String) {
        return int.tryParse(value) as T?;
      } else if (T == double && value is String) {
        return double.tryParse(value) as T?;
      } else if (T == String && value is num) {
        return value.toString() as T?;
      }

      return defaultValue;
    } catch (e) {
      debugPrint('خطأ في الحصول على قيمة من بيانات الإشعار: $e');
      return defaultValue;
    }
  }

  /// الحصول على نوع الشاشة من بيانات الإشعار
  static String getScreenType(UnifiedNotificationModel notification) {
    try {
      // الحصول على نوع الشاشة من بيانات الإشعار
      final screen =
          getDataValue<String>(notification.data, 'screen', defaultValue: '');

      // إذا كان نوع الشاشة فارغًا، استخدم نوع الإشعار
      if (screen != null && screen.isEmpty) {
        switch (notification.type) {
          case SystemNotificationTypes.creditSale:
            return 'sale';
          case SystemNotificationTypes.creditPurchase:
            return 'purchase';
          case SystemNotificationTypes.expense:
            return 'expense';
          case SystemNotificationTypes.due:
          case SystemNotificationTypes.duePayment:
            return 'due';
          case 'inventory':
            return 'inventory';
          default:
            return '';
        }
      }

      return screen ?? '';
    } catch (e) {
      debugPrint('خطأ في الحصول على نوع الشاشة: $e');
      return '';
    }
  }

  /// الحصول على رقم الفاتورة من بيانات الإشعار
  static String getInvoiceNumber(UnifiedNotificationModel notification) {
    try {
      return getDataValue<String>(notification.data, 'invoiceNumber',
              defaultValue: '') ??
          '';
    } catch (e) {
      debugPrint('خطأ في الحصول على رقم الفاتورة: $e');
      return '';
    }
  }

  /// تعليم الإشعار كمقروء بشكل آمن
  static Future<bool> markAsRead(UnifiedNotificationModel notification) async {
    try {
      if (notification.isSystemNotification) {
        await SystemNotificationService.markAsRead(notification.id);
      } else {
        await LocalNotificationService.markAsRead(notification.id);
      }
      return true;
    } catch (e) {
      debugPrint('خطأ في تعليم الإشعار كمقروء: $e');
      return false;
    }
  }

  /// إخفاء الإشعار بشكل آمن
  static Future<bool> hideNotification(
      UnifiedNotificationModel notification) async {
    try {
      // إخفاء الإشعار للمستخدم الحالي فقط
      await HiddenNotificationsService.hideNotification(notification.id);

      // تعليم الإشعار كمقروء إذا كان إشعار نظام
      if (notification.isSystemNotification) {
        await SystemNotificationService.markAsRead(notification.id);
      }

      return true;
    } catch (e) {
      debugPrint('خطأ في إخفاء الإشعار: $e');
      return false;
    }
  }

  /// تحميل الإشعارات بشكل آمن
  static Future<List<UnifiedNotificationModel>> loadNotifications() async {
    try {
      // الحصول على الإشعارات المحلية من الخدمة
      final localNotifications = LocalNotificationService.getNotifications();

      // الحصول على الإشعارات بين المستخدمين من الخدمة
      final systemNotifications = SystemNotificationService.getNotifications();

      // تحويل الإشعارات إلى النموذج الموحد
      final notifications = [
        ...localNotifications.map((notification) =>
            UnifiedNotificationModel.fromLocalNotification(notification)),
        ...systemNotifications.map((notification) =>
            UnifiedNotificationModel.fromSystemNotification(notification)),
      ];

      // ترتيب الإشعارات حسب التاريخ (الأحدث أولاً)
      notifications.sort((a, b) => b.timestamp.compareTo(a.timestamp));

      return notifications;
    } catch (e) {
      debugPrint('خطأ في تحميل الإشعارات: $e');
      return [];
    }
  }

  /// إنشاء نموذج إشعار نظام وهمي
  static SystemNotificationModel createDummySystemNotification(
    UnifiedNotificationModel notification,
    String type,
  ) {
    try {
      return SystemNotificationModel(
        id: notification.id,
        senderId: '',
        senderName: notification.senderName ?? '',
        type: type,
        title: notification.title,
        message: notification.message,
        data: notification.data,
        timestamp: notification.timestamp,
        isRead: notification.isRead,
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء نموذج إشعار نظام وهمي: $e');
      // إنشاء نموذج افتراضي في حالة الخطأ
      return SystemNotificationModel(
        id: notification.id,
        senderId: '',
        senderName: '',
        type: type,
        title: notification.title,
        message: notification.message,
        data: {},
        timestamp: notification.timestamp,
        isRead: notification.isRead,
      );
    }
  }
}
