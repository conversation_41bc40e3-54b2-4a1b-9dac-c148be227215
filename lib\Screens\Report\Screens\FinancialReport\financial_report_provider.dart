import 'dart:developer';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:mobile_pos/currency.dart';
import 'package:mobile_pos/repository/get_expanse.dart';
import 'package:mobile_pos/Screens/HRM/salaries list/repo/salary_repo.dart';
import 'financial_report_model.dart';

/// مزود حالة الشهر المختار
final selectedFinancialMonthProvider = StateProvider<DateTime>((ref) {
  // إرجاع تاريخ اليوم الحالي مع ضبط اليوم إلى 1 والوقت إلى 00:00:00
  final now = DateTime.now();
  return DateTime(now.year, now.month, 1);
});

/// مزود بيانات التقرير المالي
final financialReportProvider =
    FutureProvider.family<FinancialReportModel, DateTime>(
  (ref, selectedMonth) async {
    // تحديد نطاق التاريخ للشهر المختار
    final startDate = DateTime(selectedMonth.year, selectedMonth.month, 1);

    // تحديد تاريخ النهاية بناءً على ما إذا كان الشهر المختار هو الشهر الحالي أم لا
    final now = DateTime.now();
    final isCurrentMonth =
        selectedMonth.year == now.year && selectedMonth.month == now.month;

    // إذا كان الشهر المختار هو الشهر الحالي، استخدم اليوم الحالي كتاريخ نهاية
    // وإلا استخدم آخر يوم في الشهر
    final endDate = isCurrentMonth
        ? DateTime(now.year, now.month, now.day, 23, 59, 59)
        : DateTime(selectedMonth.year, selectedMonth.month + 1, 0, 23, 59, 59);

    log('نطاق التاريخ: من ${startDate.toString()} إلى ${endDate.toString()}');

    // متغيرات لتخزين البيانات المالية
    double totalSales = 0;
    double costOfSales = 0;
    double totalExpenses = 0;
    double totalSalaries = 0;
    double totalPurchases = 0;
    double totalDue = 0;

    try {
      // التحقق من معرف المستخدم
      log('معرف المستخدم: $constUserId');
      if (constUserId.isEmpty) {
        log('تحذير: معرف المستخدم فارغ! سيتم محاولة الحصول عليه من FirebaseAuth');
        final currentUser = FirebaseAuth.instance.currentUser;
        if (currentUser != null) {
          log('تم العثور على المستخدم الحالي: ${currentUser.uid}');
          // استخدام معرف المستخدم من FirebaseAuth
          constUserId = currentUser.uid;

          // محاولة تحديث قيمة constUserId في ملف currency.dart
          try {
            final prefs = await SharedPreferences.getInstance();
            await prefs.setString('userId', constUserId);
            log('تم تحديث معرف المستخدم في SharedPreferences: $constUserId');
          } catch (e) {
            log('خطأ في تحديث معرف المستخدم في SharedPreferences: $e');
          }
        } else {
          log('خطأ: لا يوجد مستخدم مسجل الدخول!');
          return FinancialReportModel.empty();
        }
      }

      // 1. الحصول على بيانات المبيعات
      await _fetchSalesData(
        constUserId,
        startDate,
        endDate,
        (salesTotal, salesCost) {
          totalSales = salesTotal;
          costOfSales = salesCost;
          log('تم تحديث إجمالي المبيعات: $totalSales');
          log('تم تحديث تكلفة المبيعات: $costOfSales');
        },
      );

      // 2. الحصول على بيانات المشتريات
      await _fetchPurchasesData(
        constUserId,
        startDate,
        endDate,
        (purchasesTotal) {
          totalPurchases = purchasesTotal;
          log('تم تحديث إجمالي المشتريات: $totalPurchases');
        },
      );

      // 3. الحصول على بيانات المصروفات
      await _fetchExpensesData(
        constUserId,
        startDate,
        endDate,
        (expensesTotal) {
          totalExpenses = expensesTotal;
          log('تم تحديث إجمالي المصروفات: $totalExpenses');
        },
      );

      // 4. الحصول على بيانات المرتبات
      await _fetchSalariesData(
        constUserId,
        selectedMonth,
        (salariesTotal) {
          totalSalaries = salariesTotal;
          log('تم تحديث إجمالي المرتبات: $totalSalaries');
        },
      );

      // 5. الحصول على بيانات المديونية
      await _fetchDueData(
        constUserId,
        startDate,
        endDate,
        (dueTotal) {
          totalDue = dueTotal;
          log('تم تحديث إجمالي المديونية: $totalDue');
        },
      );

      // حساب المؤشرات المالية

      // مجمل الربح = إجمالي المبيعات - تكلفة المبيعات
      final grossProfit = totalSales - costOfSales;
      log('حساب مجمل الربح: $totalSales - $costOfSales = $grossProfit');

      // صافي الربح = مجمل الربح - إجمالي المصروفات - إجمالي المرتبات
      final netProfit = grossProfit - totalExpenses - totalSalaries;
      log('حساب صافي الربح: $grossProfit - $totalExpenses - $totalSalaries = $netProfit');

      // نسبة الربح = (صافي الربح / إجمالي المبيعات) × 100
      final profitMargin =
          totalSales > 0 ? (netProfit / totalSales) * 100 : 0.0;
      log('حساب نسبة الربح: ($netProfit / $totalSales) × 100 = $profitMargin%');

      // خزنة الشركة = إجمالي المبيعات - إجمالي المديونية - إجمالي المشتريات - إجمالي المصروفات - إجمالي المرتبات
      final companyCash = totalSales -
          totalDue -
          totalPurchases -
          totalExpenses -
          totalSalaries;
      log('حساب خزنة الشركة: $totalSales - $totalDue - $totalPurchases - $totalExpenses - $totalSalaries = $companyCash');

      // طباعة القيم المحسوبة للتشخيص
      log('===== ملخص التقرير المالي =====');
      log('الفترة: من ${startDate.toString()} إلى ${endDate.toString()}');
      log('إجمالي المبيعات: $totalSales');
      log('تكلفة المبيعات: $costOfSales');
      log('مجمل الربح: $grossProfit');
      log('إجمالي المصروفات: $totalExpenses');
      log('إجمالي المرتبات: $totalSalaries');
      log('صافي الربح: $netProfit');
      log('نسبة الربح: $profitMargin%');
      log('إجمالي المشتريات: $totalPurchases');
      log('إجمالي المديونية: $totalDue');
      log('خزنة الشركة: $companyCash');
      log('================================');

      // إنشاء نموذج التقرير المالي
      return FinancialReportModel(
        totalSales: totalSales,
        costOfSales: costOfSales,
        grossProfit: grossProfit,
        totalExpenses: totalExpenses,
        totalSalaries: totalSalaries,
        netProfit: netProfit,
        profitMargin: profitMargin,
        totalPurchases: totalPurchases,
        totalDue: totalDue,
        companyCash: companyCash,
      );
    } catch (e, stackTrace) {
      log('خطأ في الحصول على البيانات المالية: $e');
      log('تفاصيل الخطأ: $stackTrace');

      // محاولة تشخيص نوع الخطأ
      if (e.toString().contains('permission_denied')) {
        log('خطأ في الصلاحيات: لا يوجد صلاحية للوصول إلى البيانات');
      } else if (e.toString().contains('network')) {
        log('خطأ في الشبكة: تأكد من اتصالك بالإنترنت');
      } else if (e.toString().contains('null')) {
        log('خطأ في البيانات: قيمة فارغة غير متوقعة');
      }

      // إرجاع نموذج فارغ في حالة حدوث خطأ
      return FinancialReportModel.empty();
    }
  },
);

/// استرجاع بيانات المبيعات
Future<void> _fetchSalesData(
  String userId,
  DateTime startDate,
  DateTime endDate,
  Function(double totalSales, double costOfSales) onDataFetched,
) async {
  double totalSales = 0;
  double costOfSales = 0;
  double totalProfit = 0;

  try {
    // استخدام نفس الطريقة المستخدمة في شاشة الربح والخسارة
    final salesRef = FirebaseDatabase.instance.ref('$userId/Sales Transition');
    log('مسار المبيعات: $userId/Sales Transition');
    final salesSnapshot = await salesRef.orderByKey().get();

    // طباعة بيانات المبيعات للتشخيص
    log('بيانات المبيعات: ${salesSnapshot.exists ? 'موجودة' : 'غير موجودة'}');
    if (salesSnapshot.exists) {
      log('عدد سجلات المبيعات: ${salesSnapshot.children.length}');

      for (var child in salesSnapshot.children) {
        final data = child.value as Map<dynamic, dynamic>?;
        if (data != null) {
          // التحقق من وجود حقل purchaseDate أو saleDate
          String? dateStr = data['purchaseDate'] as String?;
          dateStr ??= data['saleDate'] as String?;

          if (dateStr == null) {
            log('تحذير: لا يوجد حقل تاريخ في سجل المبيعات: ${data['invoiceNumber']}');
            continue;
          }

          try {
            final saleDate = DateTime.parse(dateStr);

            // التحقق من أن تاريخ البيع ضمن الشهر المختار
            if ((saleDate.isAfter(startDate) ||
                    saleDate.isAtSameMomentAs(startDate)) &&
                (saleDate.isBefore(endDate) ||
                    saleDate.isAtSameMomentAs(endDate))) {
              log('تم العثور على معاملة بيع في النطاق: ${data['invoiceNumber']} - التاريخ: $dateStr');

              // إجمالي المبيعات
              final total =
                  double.tryParse(data['totalAmount']?.toString() ?? '0') ?? 0;
              totalSales += total;

              // استخدام حقل lossProfit مباشرة إذا كان موجودًا (كما في شاشة الربح والخسارة)
              if (data['lossProfit'] != null) {
                final lossProfit =
                    double.tryParse(data['lossProfit'].toString()) ?? 0;
                if (!lossProfit.isNegative) {
                  totalProfit += lossProfit;
                  log('الربح من الفاتورة ${data['invoiceNumber']}: $lossProfit');
                } else {
                  log('الخسارة من الفاتورة ${data['invoiceNumber']}: ${lossProfit.abs()}');
                }
              }

              // تكلفة المبيعات (من قائمة المنتجات)
              if (data['productList'] != null) {
                double invoiceCostOfSales = 0;
                final productList = data['productList'] as List<dynamic>;
                for (var product in productList) {
                  // استخراج سعر الشراء (التكلفة) للمنتج
                  final purchasePrice = double.tryParse(
                          product['productPurchasePrice']?.toString() ?? '0') ??
                      0;

                  // استخراج الكمية المباعة
                  final quantity =
                      double.tryParse(product['quantity']?.toString() ?? '0') ??
                          0;

                  // حساب تكلفة المبيعات لهذا المنتج (سعر الشراء × الكمية)
                  final itemCost = purchasePrice * quantity;

                  // إضافة تكلفة هذا المنتج إلى إجمالي تكلفة المبيعات للفاتورة
                  invoiceCostOfSales += itemCost;

                  // طباعة تفاصيل حساب تكلفة المبيعات للتشخيص
                  log('تفاصيل تكلفة المبيعات للمنتج: ${product['product_name'] ?? product['productName'] ?? 'غير معروف'} - سعر الشراء: $purchasePrice × الكمية: $quantity = $itemCost');
                }

                // إضافة تكلفة المبيعات للفاتورة إلى إجمالي تكلفة المبيعات
                costOfSales += invoiceCostOfSales;

                // طباعة إجمالي تكلفة المبيعات للفاتورة
                log('إجمالي تكلفة المبيعات للفاتورة ${data['invoiceNumber']}: $invoiceCostOfSales');

                // التحقق من صحة حساب الربح
                final invoiceProfit = total - invoiceCostOfSales;
                log('الربح المحسوب للفاتورة ${data['invoiceNumber']}: $invoiceProfit');
              }
            }
          } catch (e) {
            log('خطأ في معالجة تاريخ البيع: $e');
          }
        }
      }

      // التحقق من صحة حساب تكلفة المبيعات
      if (totalProfit > 0 && costOfSales == 0) {
        // إذا كان هناك ربح ولكن تكلفة المبيعات صفر، نحسب تكلفة المبيعات من الربح
        costOfSales = totalSales - totalProfit;
        log('تم حساب تكلفة المبيعات من الربح: $costOfSales');
      }

      log('إجمالي المبيعات: $totalSales');
      log('إجمالي تكلفة المبيعات: $costOfSales');
      log('إجمالي الربح: $totalProfit');
    } else {
      log('لا توجد بيانات مبيعات للمستخدم: $userId');
    }
  } catch (e) {
    log('خطأ في استرجاع بيانات المبيعات: $e');
  }

  // استدعاء الدالة المرجعية مع البيانات المسترجعة
  onDataFetched(totalSales, costOfSales);
}

/// استرجاع بيانات المشتريات
Future<void> _fetchPurchasesData(
  String userId,
  DateTime startDate,
  DateTime endDate,
  Function(double totalPurchases) onDataFetched,
) async {
  double totalPurchases = 0;

  try {
    final purchaseRef =
        FirebaseDatabase.instance.ref('$userId/Purchase Transition');
    log('مسار المشتريات: $userId/Purchase Transition');
    final purchaseSnapshot = await purchaseRef.get();

    // طباعة بيانات المشتريات للتشخيص
    log('بيانات المشتريات: ${purchaseSnapshot.exists ? 'موجودة' : 'غير موجودة'}');
    if (purchaseSnapshot.exists) {
      log('عدد سجلات المشتريات: ${purchaseSnapshot.children.length}');

      for (var child in purchaseSnapshot.children) {
        final data = child.value as Map<dynamic, dynamic>?;
        if (data != null && data['purchaseDate'] != null) {
          try {
            final purchaseDate =
                DateTime.parse(data['purchaseDate'].toString());

            // التحقق من أن تاريخ الشراء ضمن الشهر المختار
            if ((purchaseDate.isAfter(startDate) ||
                    purchaseDate.isAtSameMomentAs(startDate)) &&
                (purchaseDate.isBefore(endDate) ||
                    purchaseDate.isAtSameMomentAs(endDate))) {
              log('تم العثور على معاملة شراء في النطاق: ${data['invoiceNumber']} - التاريخ: ${data['purchaseDate']}');

              // إجمالي المشتريات
              final total =
                  double.tryParse(data['totalAmount']?.toString() ?? '0') ?? 0;
              totalPurchases += total;
            }
          } catch (e) {
            log('خطأ في معالجة تاريخ الشراء: $e');
          }
        }
      }
    } else {
      log('لا توجد بيانات مشتريات للمستخدم: $userId');
    }
  } catch (e) {
    log('خطأ في استرجاع بيانات المشتريات: $e');
  }

  // استدعاء الدالة المرجعية مع البيانات المسترجعة
  onDataFetched(totalPurchases);
}

/// استرجاع بيانات المصروفات
Future<void> _fetchExpensesData(
  String userId,
  DateTime startDate,
  DateTime endDate,
  Function(double totalExpenses) onDataFetched,
) async {
  double totalExpenses = 0;

  try {
    // استخدام نفس الطريقة المستخدمة في شاشة الربح والخسارة
    final expenseRepo = ExpenseRepo();
    log('جاري استرجاع بيانات المصروفات باستخدام ExpenseRepo...');

    // استرجاع جميع المصروفات
    final allExpenses = await expenseRepo.getAllExpense();

    log('تم استرجاع ${allExpenses.length} مصروف');

    // تصفية المصروفات حسب التاريخ وحساب الإجمالي
    // استخدام نفس الطريقة المستخدمة في شاشة الربح والخسارة
    for (var element in allExpenses) {
      try {
        final expenseDate = DateTime.parse(element.expenseDate);

        // التحقق من أن تاريخ المصروف ضمن النطاق المحدد
        if ((expenseDate.isAfter(startDate) ||
                expenseDate.isAtSameMomentAs(startDate)) &&
            (expenseDate.isBefore(endDate) ||
                expenseDate.isAtSameMomentAs(endDate))) {
          // إضافة قيمة المصروف إلى الإجمالي - استخدام نفس الطريقة المستخدمة في شاشة الربح والخسارة
          final amount = num.tryParse(element.amount) ?? 0;
          totalExpenses += amount;

          log('تم العثور على مصروف في النطاق: ${element.expanseFor} - التاريخ: ${element.expenseDate} - المبلغ: $amount');
          log('إجمالي المصروفات حتى الآن: $totalExpenses');
        }
      } catch (e) {
        log('خطأ في معالجة تاريخ المصروف: $e');
      }
    }

    log('إجمالي المصروفات النهائي: $totalExpenses');

    // إذا كانت المصروفات صفر، حاول استرجاع البيانات مباشرة من قاعدة البيانات
    if (totalExpenses == 0) {
      log('لم يتم العثور على مصروفات. محاولة استرجاع البيانات مباشرة من قاعدة البيانات...');

      // محاولة استرجاع البيانات من المسار الأصلي
      final expenseRef = FirebaseDatabase.instance.ref(userId).child('Expense');
      log('مسار المصروفات: $userId/Expense');
      final expenseSnapshot = await expenseRef.orderByKey().get();

      log('بيانات المصروفات: ${expenseSnapshot.exists ? 'موجودة' : 'غير موجودة'}');

      if (expenseSnapshot.exists) {
        log('عدد سجلات المصروفات: ${expenseSnapshot.children.length}');

        for (var element in expenseSnapshot.children) {
          try {
            final data = element.value as Map<dynamic, dynamic>;

            // طباعة مفاتيح البيانات للتشخيص
            log('مفاتيح بيانات المصروف: ${data.keys.join(', ')}');

            // التحقق من وجود حقل التاريخ
            if (data['expenseDate'] != null) {
              try {
                final expenseDate =
                    DateTime.parse(data['expenseDate'].toString());

                // التحقق من أن تاريخ المصروف ضمن النطاق المحدد
                if ((expenseDate.isAfter(startDate) ||
                        expenseDate.isAtSameMomentAs(startDate)) &&
                    (expenseDate.isBefore(endDate) ||
                        expenseDate.isAtSameMomentAs(endDate))) {
                  // إجمالي المصروفات
                  final amount =
                      num.tryParse(data['amount']?.toString() ?? '0') ?? 0;
                  totalExpenses += amount;

                  log('تم العثور على مصروف في النطاق: ${data['expanseFor']} - التاريخ: ${data['expenseDate']} - المبلغ: $amount');
                  log('إجمالي المصروفات حتى الآن: $totalExpenses');
                }
              } catch (e) {
                log('خطأ في معالجة تاريخ المصروف: $e');
              }
            }
          } catch (e) {
            log('خطأ في معالجة بيانات المصروف: $e');
          }
        }
      } else {
        // محاولة استرجاع البيانات من مسار بديل
        log('لا توجد بيانات مصروفات في المسار الأصلي. محاولة استرجاع البيانات من مسار بديل...');

        final alternativeExpenseRef =
            FirebaseDatabase.instance.ref('$userId/Expenses');
        log('مسار المصروفات البديل: $userId/Expenses');
        final alternativeExpenseSnapshot =
            await alternativeExpenseRef.orderByKey().get();

        log('بيانات المصروفات (من المسار البديل): ${alternativeExpenseSnapshot.exists ? 'موجودة' : 'غير موجودة'}');

        if (alternativeExpenseSnapshot.exists) {
          log('عدد سجلات المصروفات (من المسار البديل): ${alternativeExpenseSnapshot.children.length}');

          for (var element in alternativeExpenseSnapshot.children) {
            try {
              final data = element.value as Map<dynamic, dynamic>;

              // طباعة مفاتيح البيانات للتشخيص
              log('مفاتيح بيانات المصروف (من المسار البديل): ${data.keys.join(', ')}');

              // التحقق من وجود حقل التاريخ
              final dateField = data['expenseDate'] ?? data['date'];
              if (dateField != null) {
                try {
                  final expenseDate = DateTime.parse(dateField.toString());

                  // التحقق من أن تاريخ المصروف ضمن النطاق المحدد
                  if ((expenseDate.isAfter(startDate) ||
                          expenseDate.isAtSameMomentAs(startDate)) &&
                      (expenseDate.isBefore(endDate) ||
                          expenseDate.isAtSameMomentAs(endDate))) {
                    // إجمالي المصروفات
                    final amount =
                        num.tryParse(data['amount']?.toString() ?? '0') ?? 0;
                    totalExpenses += amount;

                    log('تم العثور على مصروف في النطاق (من المسار البديل): ${data['expanseFor'] ?? 'غير معروف'} - المبلغ: $amount');
                    log('إجمالي المصروفات حتى الآن (من المسار البديل): $totalExpenses');
                  }
                } catch (e) {
                  log('خطأ في معالجة تاريخ المصروف (من المسار البديل): $e');
                }
              }
            } catch (e) {
              log('خطأ في معالجة بيانات المصروف (من المسار البديل): $e');
            }
          }
        }
      }
    }
  } catch (e) {
    log('خطأ في استرجاع بيانات المصروفات: $e');
  }

  // استدعاء الدالة المرجعية مع البيانات المسترجعة
  log('إجمالي المصروفات النهائي: $totalExpenses');
  onDataFetched(totalExpenses);
}

/// استرجاع بيانات المرتبات
Future<void> _fetchSalariesData(
  String userId,
  DateTime selectedMonth,
  Function(double totalSalaries) onDataFetched,
) async {
  double totalSalaries = 0;

  try {
    // استخدام مستودع الرواتب مباشرة
    final salaryRepo = SalaryRepository();
    log('جاري استرجاع بيانات المرتبات باستخدام SalaryRepository...');

    // استرجاع جميع المرتبات
    final allSalaries = await salaryRepo.getAllPaidSalary();

    log('تم استرجاع ${allSalaries.length} راتب');

    // تصفية المرتبات حسب الشهر والسنة وحساب الإجمالي
    for (var salary in allSalaries) {
      try {
        // استخراج الشهر والسنة
        final salaryMonth = salary.month;
        final salaryYear = salary.year;

        log('شهر الراتب: $salaryMonth، سنة الراتب: $salaryYear');
        log('الشهر المطلوب: ${selectedMonth.month}، السنة المطلوبة: ${selectedMonth.year}');

        // التحقق من أن الراتب للشهر والسنة المختارين
        if (salaryMonth == selectedMonth.month.toString() &&
            salaryYear == selectedMonth.year.toString()) {
          // إجمالي المرتبات
          final amount = salary.paySalary;
          totalSalaries += amount;

          log('تم العثور على راتب للشهر المختار: ${salary.employeeName} - ${salary.paySalary}');
          log('إجمالي المرتبات حتى الآن: $totalSalaries');
        }
      } catch (e) {
        log('خطأ في معالجة بيانات الراتب: $e');
      }
    }

    log('إجمالي المرتبات النهائي: $totalSalaries');

    // إذا كانت المرتبات صفر، حاول استرجاع البيانات مباشرة من قاعدة البيانات
    if (totalSalaries == 0) {
      log('لم يتم العثور على مرتبات. محاولة استرجاع البيانات مباشرة من قاعدة البيانات...');

      // محاولة استرجاع البيانات من المسار الأصلي
      final salaryRef = FirebaseDatabase.instance.ref('HRM/Salaries');
      log('مسار المرتبات: HRM/Salaries');
      final salarySnapshot = await salaryRef.get();

      log('بيانات المرتبات: ${salarySnapshot.exists ? 'موجودة' : 'غير موجودة'}');

      if (salarySnapshot.exists) {
        log('عدد سجلات المرتبات: ${salarySnapshot.children.length}');

        for (var child in salarySnapshot.children) {
          try {
            final data = child.value as Map<dynamic, dynamic>?;
            if (data != null) {
              // طباعة مفاتيح البيانات للتشخيص
              log('مفاتيح بيانات الراتب: ${data.keys.join(', ')}');

              // استخراج الشهر والسنة
              final salaryMonth = data['month']?.toString() ?? '';
              final salaryYear = data['year']?.toString() ?? '';

              log('شهر الراتب: $salaryMonth، سنة الراتب: $salaryYear');

              // التحقق من أن الراتب للشهر والسنة المختارين
              if (salaryMonth == selectedMonth.month.toString() &&
                  salaryYear == selectedMonth.year.toString()) {
                // إجمالي المرتبات
                final amount =
                    double.tryParse(data['paySalary']?.toString() ?? '0') ?? 0;
                totalSalaries += amount;

                log('تم العثور على راتب للشهر المختار: ${data['employeeName']} - ${data['paySalary']}');
                log('إجمالي المرتبات حتى الآن: $totalSalaries');
              }
            }
          } catch (e) {
            log('خطأ في معالجة بيانات الراتب: $e');
          }
        }
      } else {
        // محاولة استرجاع البيانات من مسار بديل
        log('لا توجد بيانات مرتبات في المسار الأصلي. محاولة استرجاع البيانات من مسار بديل...');

        final alternativeSalaryRef =
            FirebaseDatabase.instance.ref('$userId/HRM/Salaries');
        log('مسار المرتبات البديل: $userId/HRM/Salaries');
        final alternativeSalarySnapshot = await alternativeSalaryRef.get();

        log('بيانات المرتبات (من المسار البديل): ${alternativeSalarySnapshot.exists ? 'موجودة' : 'غير موجودة'}');

        if (alternativeSalarySnapshot.exists) {
          log('عدد سجلات المرتبات (من المسار البديل): ${alternativeSalarySnapshot.children.length}');

          for (var child in alternativeSalarySnapshot.children) {
            try {
              final data = child.value as Map<dynamic, dynamic>?;
              if (data != null) {
                // طباعة مفاتيح البيانات للتشخيص
                log('مفاتيح بيانات الراتب (من المسار البديل): ${data.keys.join(', ')}');

                // استخراج الشهر والسنة
                final salaryMonth = data['month']?.toString() ?? '';
                final salaryYear = data['year']?.toString() ?? '';

                log('شهر الراتب (من المسار البديل): $salaryMonth، سنة الراتب: $salaryYear');

                // التحقق من أن الراتب للشهر والسنة المختارين
                if (salaryMonth == selectedMonth.month.toString() &&
                    salaryYear == selectedMonth.year.toString()) {
                  // إجمالي المرتبات
                  final amount =
                      double.tryParse(data['paySalary']?.toString() ?? '0') ??
                          0;
                  totalSalaries += amount;

                  log('تم العثور على راتب للشهر المختار (من المسار البديل): ${data['employeeName']} - ${data['paySalary']}');
                  log('إجمالي المرتبات حتى الآن (من المسار البديل): $totalSalaries');
                }
              }
            } catch (e) {
              log('خطأ في معالجة بيانات الراتب (من المسار البديل): $e');
            }
          }
        } else {
          log('لا توجد بيانات مرتبات للمستخدم: $userId (في أي من المسارين)');
        }
      }
    }
  } catch (e) {
    log('خطأ في استرجاع بيانات المرتبات: $e');
  }

  // استدعاء الدالة المرجعية مع البيانات المسترجعة
  log('إجمالي المرتبات النهائي: $totalSalaries');
  onDataFetched(totalSalaries);
}

/// استرجاع بيانات المديونية
Future<void> _fetchDueData(
  String userId,
  DateTime startDate,
  DateTime endDate,
  Function(double totalDue) onDataFetched,
) async {
  double totalDue = 0;

  try {
    final dueRef = FirebaseDatabase.instance.ref('$userId/Due Transaction');
    log('مسار المديونية: $userId/Due Transaction');
    final dueSnapshot = await dueRef.get();

    // طباعة بيانات المديونية للتشخيص
    log('بيانات المديونية: ${dueSnapshot.exists ? 'موجودة' : 'غير موجودة'}');
    if (dueSnapshot.exists) {
      log('عدد سجلات المديونية: ${dueSnapshot.children.length}');

      for (var child in dueSnapshot.children) {
        final data = child.value as Map<dynamic, dynamic>?;
        if (data != null && data['purchaseDate'] != null) {
          try {
            final dueDate = DateTime.parse(data['purchaseDate'].toString());

            // التحقق من أن تاريخ المديونية ضمن الشهر المختار
            if ((dueDate.isAfter(startDate) ||
                    dueDate.isAtSameMomentAs(startDate)) &&
                (dueDate.isBefore(endDate) ||
                    dueDate.isAtSameMomentAs(endDate))) {
              log('تم العثور على مديونية في النطاق: ${data['purchaseDate']}');

              // إجمالي المديونية المتبقية
              final amount = double.tryParse(
                      data['dueAmountAfterPay']?.toString() ?? '0') ??
                  0;
              totalDue += amount;
            }
          } catch (e) {
            log('خطأ في معالجة تاريخ المديونية: $e');
          }
        }
      }
    } else {
      log('لا توجد بيانات مديونية للمستخدم: $userId');
    }
  } catch (e) {
    log('خطأ في استرجاع بيانات المديونية: $e');
  }

  // استدعاء الدالة المرجعية مع البيانات المسترجعة
  onDataFetched(totalDue);
}
