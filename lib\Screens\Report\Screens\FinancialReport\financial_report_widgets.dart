import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/const_commas.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/currency.dart';

/// بناء عنصر ملخص سريع
Widget buildSummaryItem({
  required BuildContext context,
  required String title,
  required String value,
  required IconData icon,
  required Color color,
  required Color textColor,
}) {
  return Container(
    width: MediaQuery.of(context).size.width * 0.4,
    padding: const EdgeInsets.all(15),
    decoration: BoxDecoration(
      color: color,
      borderRadius: BorderRadius.circular(12),
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: textColor, size: 20),
            const SizedBox(width: 8),
            Text(
              title,
              style: GoogleFonts.cairo(
                color: textColor,
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
          ],
        ),
        const SizedBox(height: 10),
        Text(
          value,
          style: GoogleFonts.cairo(
            color: textColor,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
      ],
    ),
  );
}

/// بناء دائرة ملخص للقيم المالية
Widget buildSummaryCircle({
  required String title,
  required double value,
  required Color color,
}) {
  return Column(
    children: [
      Container(
        width: 80,
        height: 80,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: color.withAlpha(50),
          border: Border.all(color: color, width: 2),
        ),
        child: Center(
          child: Text(
            myFormat.format(value),
            style: GoogleFonts.cairo(
              color: color,
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
        ),
      ),
      const SizedBox(height: 5),
      Text(
        title,
        style: GoogleFonts.cairo(
          color: Colors.black87,
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
      ),
    ],
  );
}

/// بناء عنصر مالي في التقرير
Widget buildFinancialItem({
  required String title,
  required double value,
  required Color color,
  bool isBold = false,
  bool isPercentage = false,
  IconData? icon,
}) {
  return Row(
    mainAxisAlignment: MainAxisAlignment.spaceBetween,
    children: [
      Row(
        children: [
          if (icon != null) ...[
            Icon(icon, color: color, size: 18),
            const SizedBox(width: 8),
          ],
          Text(
            title,
            style: GoogleFonts.cairo(
              fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
              fontSize: isBold ? 16 : 14,
            ),
          ),
        ],
      ),
      Text(
        isPercentage
            ? '${value.toStringAsFixed(2)}%'
            : '${myFormat.format(value)} $currency',
        style: GoogleFonts.cairo(
          color: color,
          fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
          fontSize: isBold ? 16 : 14,
        ),
      ),
    ],
  );
}

/// بناء بطاقة مالية
Widget buildFinancialCard({
  required String title,
  required IconData icon,
  required Color iconColor,
  required List<Widget> children,
}) {
  return Card(
    elevation: 4,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(15),
    ),
    child: Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        gradient: LinearGradient(
          colors: [Colors.white, Colors.grey.shade50],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
      ),
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: iconColor),
              const SizedBox(width: 10),
              Text(
                title,
                style: GoogleFonts.cairo(
                  color: kMainColor,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          ...children,
        ],
      ),
    ),
  );
}

/// بناء قائمة الأشهر للاختيار
List<DropdownMenuItem<DateTime>> buildMonthDropdownItems() {
  final items = <DropdownMenuItem<DateTime>>[];
  final now = DateTime.now();

  // إضافة الأشهر الـ 12 الماضية
  for (int i = 0; i < 12; i++) {
    final date = DateTime(now.year, now.month - i, 1);
    items.add(
      DropdownMenuItem(
        value: date,
        child: Text(
          '${_getMonthName(date.month)} ${date.year}',
          style: GoogleFonts.cairo(),
        ),
      ),
    );
  }

  return items;
}

/// الحصول على اسم الشهر بالعربية
String _getMonthName(int month) {
  const months = [
    'يناير',
    'فبراير',
    'مارس',
    'إبريل',
    'مايو',
    'يونيو',
    'يوليو',
    'أغسطس',
    'سبتمبر',
    'أكتوبر',
    'نوفمبر',
    'ديسمبر'
  ];
  return months[month - 1];
}
