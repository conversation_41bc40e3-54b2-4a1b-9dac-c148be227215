import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';

/// خدمة قاعدة البيانات المحلية
/// تستخدم لتخزين البيانات محليًا على ويندوز كبديل لـ Firebase Realtime Database
class LocalDatabaseService {
  static final LocalDatabaseService _instance =
      LocalDatabaseService._internal();
  static Database? _database;
  static bool _isInitialized = false;

  // الحصول على مثيل الخدمة (Singleton)
  factory LocalDatabaseService() {
    return _instance;
  }

  LocalDatabaseService._internal();

  /// التحقق مما إذا كانت الخدمة مهيأة
  static bool get isInitialized => _isInitialized;

  /// الحصول على قاعدة البيانات
  static Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  /// تهيئة قاعدة البيانات
  static Future<Database> _initDatabase() async {
    try {
      // تهيئة sqflite_ffi على ويندوز
      if (Platform.isWindows) {
        // تهيئة sqflite_ffi
        sqfliteFfiInit();
        // تعيين databaseFactory
        databaseFactory = databaseFactoryFfi;
        debugPrint('تم تهيئة databaseFactoryFfi على ويندوز');
      }

      // الحصول على مسار قاعدة البيانات
      Directory documentsDirectory = await getApplicationDocumentsDirectory();
      String path = join(documentsDirectory.path, 'local_firebase.db');

      // فتح قاعدة البيانات
      return await openDatabase(
        path,
        version: 1,
        onCreate: _onCreate,
        onUpgrade: _onUpgrade,
      );
    } catch (e) {
      debugPrint('خطأ في تهيئة قاعدة البيانات المحلية: $e');
      rethrow;
    }
  }

  /// إنشاء جداول قاعدة البيانات
  static Future<void> _onCreate(Database db, int version) async {
    await db.execute('''
      CREATE TABLE firebase_data (
        path TEXT PRIMARY KEY,
        data TEXT,
        timestamp INTEGER
      )
    ''');

    await db.execute('''
      CREATE TABLE user_data (
        user_id TEXT PRIMARY KEY,
        display_name TEXT,
        email TEXT,
        photo_url TEXT,
        subscription_type TEXT,
        last_login INTEGER
      )
    ''');

    await db.execute('''
      CREATE TABLE subscription_data (
        user_id TEXT,
        feature TEXT,
        enabled INTEGER,
        PRIMARY KEY (user_id, feature)
      )
    ''');
  }

  /// ترقية قاعدة البيانات
  static Future<void> _onUpgrade(
      Database db, int oldVersion, int newVersion) async {
    // يمكن إضافة عمليات ترقية قاعدة البيانات هنا في المستقبل
  }

  /// تهيئة الخدمة
  static Future<bool> initialize() async {
    try {
      await database;
      _isInitialized = true;
      debugPrint('تم تهيئة خدمة قاعدة البيانات المحلية بنجاح');
      return true;
    } catch (e) {
      debugPrint('خطأ في تهيئة خدمة قاعدة البيانات المحلية: $e');
      _isInitialized = false;
      return false;
    }
  }

  /// حفظ البيانات في قاعدة البيانات المحلية
  static Future<void> saveData(String path, dynamic data) async {
    try {
      final db = await database;
      final jsonData = json.encode(data);
      final timestamp = DateTime.now().millisecondsSinceEpoch;

      await db.insert(
        'firebase_data',
        {
          'path': path,
          'data': jsonData,
          'timestamp': timestamp,
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      debugPrint('تم حفظ البيانات للمسار: $path');
    } catch (e) {
      debugPrint('خطأ في حفظ البيانات للمسار $path: $e');
    }
  }

  /// الحصول على البيانات من قاعدة البيانات المحلية
  static Future<Map<String, dynamic>?> getData(String path) async {
    try {
      final db = await database;
      final List<Map<String, dynamic>> maps = await db.query(
        'firebase_data',
        where: 'path = ?',
        whereArgs: [path],
      );

      if (maps.isNotEmpty) {
        final jsonData = maps.first['data'] as String;
        return json.decode(jsonData);
      }

      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على البيانات للمسار $path: $e');
      return null;
    }
  }

  /// حفظ بيانات المستخدم
  static Future<void> saveUserData({
    required String userId,
    required String displayName,
    required String email,
    String? photoUrl,
    required String subscriptionType,
  }) async {
    try {
      final db = await database;
      final timestamp = DateTime.now().millisecondsSinceEpoch;

      await db.insert(
        'user_data',
        {
          'user_id': userId,
          'display_name': displayName,
          'email': email,
          'photo_url': photoUrl,
          'subscription_type': subscriptionType,
          'last_login': timestamp,
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      debugPrint('تم حفظ بيانات المستخدم: $userId');
    } catch (e) {
      debugPrint('خطأ في حفظ بيانات المستخدم $userId: $e');
    }
  }

  /// الحصول على بيانات المستخدم
  static Future<Map<String, dynamic>?> getUserData(String userId) async {
    try {
      final db = await database;
      final List<Map<String, dynamic>> maps = await db.query(
        'user_data',
        where: 'user_id = ?',
        whereArgs: [userId],
      );

      if (maps.isNotEmpty) {
        return maps.first;
      }

      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على بيانات المستخدم $userId: $e');
      return null;
    }
  }

  /// حفظ بيانات الاشتراك
  static Future<void> saveSubscriptionFeature({
    required String userId,
    required String feature,
    required bool enabled,
  }) async {
    try {
      final db = await database;

      await db.insert(
        'subscription_data',
        {
          'user_id': userId,
          'feature': feature,
          'enabled': enabled ? 1 : 0,
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      debugPrint('تم حفظ ميزة الاشتراك: $feature للمستخدم: $userId');
    } catch (e) {
      debugPrint('خطأ في حفظ ميزة الاشتراك $feature للمستخدم $userId: $e');
    }
  }

  /// الحصول على بيانات ميزة الاشتراك
  static Future<bool> getSubscriptionFeature({
    required String userId,
    required String feature,
  }) async {
    try {
      final db = await database;
      final List<Map<String, dynamic>> maps = await db.query(
        'subscription_data',
        where: 'user_id = ? AND feature = ?',
        whereArgs: [userId, feature],
      );

      if (maps.isNotEmpty) {
        return maps.first['enabled'] == 1;
      }

      // القيمة الافتراضية هي true
      return true;
    } catch (e) {
      debugPrint(
          'خطأ في الحصول على ميزة الاشتراك $feature للمستخدم $userId: $e');
      // القيمة الافتراضية هي true في حالة الخطأ
      return true;
    }
  }

  /// حذف البيانات
  static Future<void> deleteData(String path) async {
    try {
      final db = await database;
      await db.delete(
        'firebase_data',
        where: 'path = ?',
        whereArgs: [path],
      );

      debugPrint('تم حذف البيانات للمسار: $path');
    } catch (e) {
      debugPrint('خطأ في حذف البيانات للمسار $path: $e');
    }
  }

  /// مسح جميع البيانات
  static Future<void> clearAllData() async {
    try {
      final db = await database;
      await db.delete('firebase_data');
      await db.delete('user_data');
      await db.delete('subscription_data');

      debugPrint('تم مسح جميع البيانات');
    } catch (e) {
      debugPrint('خطأ في مسح جميع البيانات: $e');
    }
  }
}
