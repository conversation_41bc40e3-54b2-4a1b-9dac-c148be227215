import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';
import 'package:mobile_pos/Screens/Home/home_screen.dart';
import 'package:mobile_pos/Screens/Report/reports.dart';
import 'package:mobile_pos/Screens/Settings/settings_screen.dart';
import 'package:mobile_pos/generated/l10n.dart' as lang;
import 'package:mobile_pos/subscription.dart';
import 'package:mobile_pos/widgets/quick_ai_assistant_modal.dart';
import 'package:mobile_pos/widgets/animated_ai_icon.dart';
import 'package:restart_app/restart_app.dart';

import '../../constant.dart';
import '../../currency.dart';
import '../Sales/sales_contact.dart';

class Home extends StatefulWidget {
  const Home({super.key});

  @override
  // ignore: library_private_types_in_public_api
  _HomeState createState() => _HomeState();
}

class _HomeState extends State<Home> {
  void signOutAutoLogin() async {
    try {
      CurrentUserData currentUserData = CurrentUserData();
      debugPrint('تم تعطيل تسجيل الخروج التلقائي مؤقتاً');
    } catch (e) {
      debugPrint('خطأ في signOutAutoLogin: $e');
    }
  }

  int _selectedIndex = 0;
  bool isDeviceConnected = false;
  bool isAlertSet = false;
  bool isNoInternet = false;
  bool _isAIAssistantActive = false;

  static const List<Widget> _widgetOptions = <Widget>[
    HomeScreen(),
    SalesContact(isFromHome: true),
    Reports(isFromHome: true),
    SettingScreen()
  ];

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  void _showAIAssistant() {
    setState(() {
      _isAIAssistantActive = true;
    });

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const QuickAIAssistantModal(),
    ).then((_) {
      // عند إغلاق المساعد
      setState(() {
        _isAIAssistantActive = false;
      });
    });
  }

  @override
  void initState() {
    super.initState();
    isSubUser ? signOutAutoLogin() : null;
    Subscription.getUserLimitsData(context: context, wannaShowMsg: true);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      body: Center(
        child: _widgetOptions.elementAt(_selectedIndex),
      ),
      floatingActionButton: Container(
        width: 70,
        height: 70,
        child: AnimatedAIIcon(
          size: 60,
          isActive: _isAIAssistantActive,
          onTap: _showAIAssistant,
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      bottomNavigationBar: SizedBox(
        height: 48, // تم تقليل الارتفاع بنسبة 20% (من 60 إلى 48)
        child: BottomAppBar(
          shape: const CircularNotchedRectangle(),
          notchMargin: 6.0, // تقليل المسافة قليلاً
          elevation: 6.0,
          padding: const EdgeInsets.symmetric(
              horizontal: 8, vertical: 2), // ضبط الـ padding
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              IconButton(
                icon: Icon(
                  FeatherIcons.home,
                  color: _selectedIndex == 0 ? kMainColor : Colors.grey,
                  size: 24, // تكبير حجم الأيقونة
                ),
                onPressed: () => _onItemTapped(0),
                padding: const EdgeInsets.all(4), // تقليل الـ padding الداخلي
                constraints: const BoxConstraints(
                    minWidth: 40, minHeight: 40), // تحديد الحد الأدنى للحجم
              ),
              IconButton(
                icon: Icon(
                  FeatherIcons.shoppingCart,
                  color: _selectedIndex == 1 ? kMainColor : Colors.grey,
                  size: 24, // تكبير حجم الأيقونة
                ),
                onPressed: () => _onItemTapped(1),
                padding: const EdgeInsets.all(4),
                constraints: const BoxConstraints(minWidth: 40, minHeight: 40),
              ),
              const SizedBox(width: 40), // مساحة للأيقونة المتحركة
              IconButton(
                icon: Icon(
                  FeatherIcons.fileText,
                  color: _selectedIndex == 2 ? kMainColor : Colors.grey,
                  size: 24, // تكبير حجم الأيقونة
                ),
                onPressed: () => _onItemTapped(2),
                padding: const EdgeInsets.all(4),
                constraints: const BoxConstraints(minWidth: 40, minHeight: 40),
              ),
              IconButton(
                icon: Icon(
                  FeatherIcons.settings,
                  color: _selectedIndex == 3 ? kMainColor : Colors.grey,
                  size: 24, // تكبير حجم الأيقونة
                ),
                onPressed: () => _onItemTapped(3),
                padding: const EdgeInsets.all(4),
                constraints: const BoxConstraints(minWidth: 40, minHeight: 40),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
