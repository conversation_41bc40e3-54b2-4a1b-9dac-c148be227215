# 📊 مصادر البيانات - وحدة الخزينة

## 🔥 Firebase Realtime Database

وحدة الخزينة تستخدم **Firebase Realtime Database** كمصدر البيانات الرئيسي. جميع البيانات تُحفظ تحت مسار المستخدم الحالي.

### 🗂️ هيكل قاعدة البيانات

```
Firebase Realtime Database
└── [constUserId]/                    # معرف المستخدم الحالي
    └── Treasury/                     # مجلد الخزينة الرئيسي
        ├── Balance/                  # رصيد الخزينة
        │   ├── currentBalance        # الرصيد الحالي
        │   ├── totalIncome          # إجمالي الإيرادات
        │   ├── totalExpense         # إجمالي المصروفات
        │   ├── lastUpdated          # تاريخ آخر تحديث
        │   └── lastTransactionId    # معرف آخر معاملة
        └── Transactions/             # المعاملات المالية
            └── [transactionId]/      # معرف المعاملة
                ├── id               # معرف المعاملة
                ├── date             # تاريخ المعاملة
                ├── type             # نوع المعاملة (income/expense)
                ├── category         # فئة المعاملة
                ├── description      # وصف المعاملة
                ├── amount           # المبلغ
                ├── paymentMethod    # طريقة الدفع
                ├── referenceNumber  # رقم المرجع
                ├── notes            # ملاحظات
                ├── createdBy        # منشئ المعاملة
                ├── createdAt        # تاريخ الإنشاء
                └── updatedAt        # تاريخ التحديث
```

## 📝 أمثلة على البيانات

### 💰 مثال على رصيد الخزينة:
```json
{
  "constUserId": {
    "Treasury": {
      "Balance": {
        "currentBalance": "15000",
        "totalIncome": "25000",
        "totalExpense": "10000",
        "lastUpdated": "2024-01-15T10:30:00.000Z",
        "lastTransactionId": "-NqJ8xK9mP2vQ1rS3tU4"
      }
    }
  }
}
```

### 💸 مثال على معاملة إيراد:
```json
{
  "constUserId": {
    "Treasury": {
      "Transactions": {
        "-NqJ8xK9mP2vQ1rS3tU4": {
          "id": "-NqJ8xK9mP2vQ1rS3tU4",
          "date": "2024-01-15T00:00:00.000Z",
          "type": "income",
          "category": "مبيعات",
          "description": "بيع منتجات للعميل أحمد",
          "amount": "5000",
          "paymentMethod": "نقدي",
          "referenceNumber": "INV-2024-001",
          "notes": "دفع نقدي كامل",
          "createdBy": "current_user",
          "createdAt": "2024-01-15T10:30:00.000Z",
          "updatedAt": "2024-01-15T10:30:00.000Z"
        }
      }
    }
  }
}
```

### 💳 مثال على معاملة مصروف:
```json
{
  "constUserId": {
    "Treasury": {
      "Transactions": {
        "-NqJ8xK9mP2vQ1rS3tU5": {
          "id": "-NqJ8xK9mP2vQ1rS3tU5",
          "date": "2024-01-15T00:00:00.000Z",
          "type": "expense",
          "category": "رواتب",
          "description": "راتب الموظف محمد",
          "amount": "3000",
          "paymentMethod": "تحويل بنكي",
          "referenceNumber": "SAL-2024-001",
          "notes": "راتب شهر يناير",
          "createdBy": "current_user",
          "createdAt": "2024-01-15T11:00:00.000Z",
          "updatedAt": "2024-01-15T11:00:00.000Z"
        }
      }
    }
  }
}
```

## 🔧 كيفية الوصول للبيانات

### 1. **من خلال الخدمات (Services)**
```dart
// الحصول على جميع المعاملات
final treasuryService = TreasuryService();
final transactions = await treasuryService.getAllTransactions();

// الحصول على الرصيد
final balance = await treasuryService.getBalance();
```

### 2. **من خلال مقدمي البيانات (Providers)**
```dart
// مراقبة المعاملات في الوقت الفعلي
final transactionsAsync = ref.watch(treasuryTransactionsProvider);

// مراقبة الرصيد في الوقت الفعلي
final balanceAsync = ref.watch(treasuryBalanceProvider);
```

### 3. **مباشرة من Firebase**
```dart
final ref = FirebaseDatabaseService.getReference(
  '$constUserId/Treasury/Transactions',
  keepSynced: true,
);
final snapshot = await ref.get();
```

## 🔄 تزامن البيانات

### **المزامنة الفورية:**
- جميع التغييرات تُحفظ فوراً في Firebase
- التحديثات تظهر في الوقت الفعلي لجميع المستخدمين
- استخدام `keepSynced: true` للحصول على أداء أفضل

### **إدارة الحالة:**
- استخدام Riverpod لإدارة الحالة
- Stream providers للمراقبة المستمرة
- تحديث تلقائي للواجهة عند تغيير البيانات

## 🛡️ الأمان والأذونات

### **قواعد الأمان في Firebase:**
```json
{
  "rules": {
    "$userId": {
      "Treasury": {
        ".read": "$userId === auth.uid",
        ".write": "$userId === auth.uid"
      }
    }
  }
}
```

### **التحقق من الصحة:**
- التحقق من صحة البيانات قبل الحفظ
- منع القيم السالبة للمبالغ
- التأكد من وجود جميع الحقول المطلوبة

## 📈 الأداء والتحسين

### **تحسينات الأداء:**
- استخدام `keepSynced: true` للبيانات المهمة
- تحميل البيانات بشكل تدريجي (pagination)
- تخزين مؤقت للبيانات المستخدمة بكثرة

### **إدارة الذاكرة:**
- إلغاء المستمعين عند عدم الحاجة
- تنظيف الموارد في dispose()
- استخدام weak references حيث أمكن

## 🔍 استكشاف الأخطاء

### **مشاكل شائعة:**

1. **عدم ظهور البيانات:**
   - تأكد من صحة `constUserId`
   - تحقق من اتصال الإنترنت
   - راجع قواعد الأمان في Firebase

2. **بطء في التحميل:**
   - تحقق من حجم البيانات
   - استخدم الفلترة والتصفح
   - قم بتحسين استعلامات Firebase

3. **عدم تحديث البيانات:**
   - تأكد من استخدام Stream providers
   - تحقق من إعدادات keepSynced
   - راجع منطق تحديث الحالة

## 📊 مراقبة الاستخدام

### **إحصائيات مفيدة:**
- عدد المعاملات اليومية
- حجم البيانات المنقولة
- أوقات الاستجابة
- معدل الأخطاء

### **أدوات المراقبة:**
- Firebase Console
- Firebase Performance Monitoring
- Firebase Analytics
- سجلات التطبيق المحلية

---

**ملاحظة مهمة:** جميع البيانات محمية بقواعد أمان Firebase ولا يمكن الوصول إليها إلا من قبل المستخدم المالك للبيانات.
