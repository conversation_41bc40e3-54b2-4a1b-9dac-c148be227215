import 'dart:convert';
import 'dart:io';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import '../models/treasury_transaction_model.dart';
import '../models/treasury_report_model.dart';
import 'treasury_service.dart';

class TreasuryReportService {
  final TreasuryService _treasuryService = TreasuryService();

  // Generate daily report
  Future<TreasuryReportModel> generateDailyReport(DateTime date) async {
    final startDate = DateTime(date.year, date.month, date.day);
    final endDate = DateTime(date.year, date.month, date.day, 23, 59, 59);
    
    return await _generateReport('daily', startDate, endDate);
  }

  // Generate weekly report
  Future<TreasuryReportModel> generateWeeklyReport(DateTime weekStart) async {
    final startDate = weekStart;
    final endDate = weekStart.add(const Duration(days: 6, hours: 23, minutes: 59, seconds: 59));
    
    return await _generateReport('weekly', startDate, endDate);
  }

  // Generate monthly report
  Future<TreasuryReportModel> generateMonthlyReport(DateTime month) async {
    final startDate = DateTime(month.year, month.month, 1);
    final endDate = DateTime(month.year, month.month + 1, 0, 23, 59, 59);
    
    return await _generateReport('monthly', startDate, endDate);
  }

  // Generate yearly report
  Future<TreasuryReportModel> generateYearlyReport(DateTime year) async {
    final startDate = DateTime(year.year, 1, 1);
    final endDate = DateTime(year.year, 12, 31, 23, 59, 59);
    
    return await _generateReport('yearly', startDate, endDate);
  }

  // Generate custom report
  Future<TreasuryReportModel> generateCustomReport(DateTime startDate, DateTime endDate) async {
    return await _generateReport('custom', startDate, endDate);
  }

  // Private method to generate report
  Future<TreasuryReportModel> _generateReport(String reportType, DateTime startDate, DateTime endDate) async {
    try {
      final transactions = await _treasuryService.getTransactionsByDateRange(startDate, endDate);
      
      double totalIncome = 0;
      double totalExpense = 0;
      Map<String, TreasuryReportItem> categoryItems = {};
      
      for (var transaction in transactions) {
        if (transaction.isIncome) {
          totalIncome += transaction.amountAsDouble;
        } else {
          totalExpense += transaction.amountAsDouble;
        }
        
        // Group by category
        final key = '${transaction.category}_${transaction.type}';
        if (categoryItems.containsKey(key)) {
          final item = categoryItems[key]!;
          final newAmount = item.amountAsDouble + transaction.amountAsDouble;
          final newCount = item.countAsInt + 1;
          
          categoryItems[key] = TreasuryReportItem(
            category: transaction.category,
            type: transaction.type,
            amount: newAmount.toString(),
            count: newCount.toString(),
            percentage: '0', // Will be calculated later
          );
        } else {
          categoryItems[key] = TreasuryReportItem(
            category: transaction.category,
            type: transaction.type,
            amount: transaction.amount,
            count: '1',
            percentage: '0', // Will be calculated later
          );
        }
      }
      
      // Calculate percentages
      final totalAmount = totalIncome + totalExpense;
      for (var key in categoryItems.keys) {
        final item = categoryItems[key]!;
        final percentage = totalAmount > 0 ? (item.amountAsDouble / totalAmount) * 100 : 0;
        categoryItems[key] = TreasuryReportItem(
          category: item.category,
          type: item.type,
          amount: item.amount,
          count: item.count,
          percentage: percentage.toStringAsFixed(2),
        );
      }
      
      final netAmount = totalIncome - totalExpense;
      
      return TreasuryReportModel(
        reportId: DateTime.now().millisecondsSinceEpoch.toString(),
        reportType: reportType,
        startDate: startDate.toIso8601String(),
        endDate: endDate.toIso8601String(),
        totalIncome: totalIncome.toString(),
        totalExpense: totalExpense.toString(),
        netAmount: netAmount.toString(),
        transactionCount: transactions.length.toString(),
        generatedAt: DateTime.now().toIso8601String(),
        generatedBy: 'system', // يمكن تحديثه لاحقاً لإضافة معلومات المستخدم
        items: categoryItems.values.toList(),
      );
    } catch (e) {
      print('خطأ في إنشاء التقرير: $e');
      return TreasuryReportModel(
        reportId: '',
        reportType: reportType,
        startDate: startDate.toIso8601String(),
        endDate: endDate.toIso8601String(),
        totalIncome: '0',
        totalExpense: '0',
        netAmount: '0',
        transactionCount: '0',
        generatedAt: DateTime.now().toIso8601String(),
        generatedBy: 'system',
        items: [],
      );
    }
  }

  // Export report to JSON
  Future<String?> exportReportToJson(TreasuryReportModel report) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final fileName = 'treasury_report_${report.reportType}_${DateFormat('yyyyMMdd_HHmmss').format(DateTime.now())}.json';
      final file = File('${directory.path}/$fileName');
      
      final jsonString = jsonEncode(report.toJson());
      await file.writeAsString(jsonString);
      
      return file.path;
    } catch (e) {
      print('خطأ في تصدير التقرير إلى JSON: $e');
      return null;
    }
  }

  // Export report to CSV
  Future<String?> exportReportToCsv(TreasuryReportModel report) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final fileName = 'treasury_report_${report.reportType}_${DateFormat('yyyyMMdd_HHmmss').format(DateTime.now())}.csv';
      final file = File('${directory.path}/$fileName');
      
      final csvContent = StringBuffer();
      
      // Header
      csvContent.writeln('تقرير الخزينة - ${_getReportTypeInArabic(report.reportType)}');
      csvContent.writeln('الفترة,${DateFormat('yyyy/MM/dd').format(report.startDateAsDateTime)} - ${DateFormat('yyyy/MM/dd').format(report.endDateAsDateTime)}');
      csvContent.writeln('تاريخ الإنشاء,${DateFormat('yyyy/MM/dd HH:mm').format(report.generatedAtAsDateTime)}');
      csvContent.writeln('');
      
      // Summary
      csvContent.writeln('ملخص التقرير');
      csvContent.writeln('إجمالي الإيرادات,${report.totalIncome}');
      csvContent.writeln('إجمالي المصروفات,${report.totalExpense}');
      csvContent.writeln('صافي المبلغ,${report.netAmount}');
      csvContent.writeln('عدد المعاملات,${report.transactionCount}');
      csvContent.writeln('');
      
      // Details by category
      csvContent.writeln('التفاصيل حسب الفئة');
      csvContent.writeln('الفئة,النوع,المبلغ,العدد,النسبة المئوية');
      
      for (var item in report.items) {
        csvContent.writeln('${item.category},${_getTypeInArabic(item.type)},${item.amount},${item.count},${item.percentage}%');
      }
      
      await file.writeAsString(csvContent.toString());
      
      return file.path;
    } catch (e) {
      print('خطأ في تصدير التقرير إلى CSV: $e');
      return null;
    }
  }

  // Get category summary
  Future<Map<String, double>> getCategorySummary(DateTime startDate, DateTime endDate) async {
    final transactions = await _treasuryService.getTransactionsByDateRange(startDate, endDate);
    Map<String, double> summary = {};
    
    for (var transaction in transactions) {
      if (summary.containsKey(transaction.category)) {
        summary[transaction.category] = summary[transaction.category]! + transaction.amountAsDouble;
      } else {
        summary[transaction.category] = transaction.amountAsDouble;
      }
    }
    
    return summary;
  }

  // Get monthly comparison
  Future<Map<String, Map<String, double>>> getMonthlyComparison(int year) async {
    Map<String, Map<String, double>> comparison = {};
    
    for (int month = 1; month <= 12; month++) {
      final startDate = DateTime(year, month, 1);
      final endDate = DateTime(year, month + 1, 0, 23, 59, 59);
      
      final transactions = await _treasuryService.getTransactionsByDateRange(startDate, endDate);
      
      double income = 0;
      double expense = 0;
      
      for (var transaction in transactions) {
        if (transaction.isIncome) {
          income += transaction.amountAsDouble;
        } else {
          expense += transaction.amountAsDouble;
        }
      }
      
      comparison[DateFormat('MMMM', 'ar').format(startDate)] = {
        'income': income,
        'expense': expense,
        'net': income - expense,
      };
    }
    
    return comparison;
  }

  // Helper methods
  String _getReportTypeInArabic(String reportType) {
    switch (reportType) {
      case 'daily':
        return 'يومي';
      case 'weekly':
        return 'أسبوعي';
      case 'monthly':
        return 'شهري';
      case 'yearly':
        return 'سنوي';
      case 'custom':
        return 'مخصص';
      default:
        return reportType;
    }
  }

  String _getTypeInArabic(String type) {
    switch (type) {
      case 'income':
        return 'إيراد';
      case 'expense':
        return 'مصروف';
      default:
        return type;
    }
  }
}
