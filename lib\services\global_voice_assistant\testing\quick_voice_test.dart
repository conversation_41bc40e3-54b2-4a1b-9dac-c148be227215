// بسم الله الرحمن الرحيم
// اختبار سريع للصوت - AmrDevPOS

import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:flutter_tts/flutter_tts.dart';
import '../core/voice_assistant_initialization_manager.dart';

/// شاشة اختبار سريع للصوت
class QuickVoiceTestScreen extends StatefulWidget {
  const QuickVoiceTestScreen({super.key});

  @override
  State<QuickVoiceTestScreen> createState() => _QuickVoiceTestScreenState();
}

class _QuickVoiceTestScreenState extends State<QuickVoiceTestScreen> {
  final VoiceAssistantInitializationManager _initManager =
      VoiceAssistantInitializationManager();
  final stt.SpeechToText _speechToText = stt.SpeechToText();
  final FlutterTts _flutterTts = FlutterTts();

  bool _isListening = false;
  bool _isSpeaking = false;
  String _recognizedText = '';
  String _statusMessage = 'اضغط على الأزرار لاختبار الصوت';

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    try {
      await _initManager.initializeGlobalVoiceAssistant();
      await _speechToText.initialize();
      await _flutterTts.setLanguage('ar-SA');
      setState(() {
        _statusMessage = 'جاهز للاختبار! 🎤';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'خطأ في التهيئة: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('🎤 اختبار سريع للصوت'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // أيقونة الحالة
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: LinearGradient(
                  colors: _isListening
                      ? [Colors.red, Colors.red.shade700]
                      : _isSpeaking
                          ? [Colors.green, Colors.green.shade700]
                          : [Colors.blue, Colors.blue.shade700],
                ),
                boxShadow: [
                  BoxShadow(
                    color: (_isListening
                            ? Colors.red
                            : _isSpeaking
                                ? Colors.green
                                : Colors.blue)
                        .withOpacity(0.3),
                    blurRadius: 20,
                    spreadRadius: 5,
                  ),
                ],
              ),
              child: Icon(
                _isListening
                    ? Icons.mic
                    : _isSpeaking
                        ? Icons.volume_up
                        : Icons.mic_none,
                size: 60,
                color: Colors.white,
              ),
            ),

            const SizedBox(height: 30),

            // رسالة الحالة
            Container(
              padding: const EdgeInsets.all(15),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(15),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Text(
                _statusMessage,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ),

            const SizedBox(height: 20),

            // النص المتعرف عليه
            if (_recognizedText.isNotEmpty)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(15),
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  borderRadius: BorderRadius.circular(15),
                  border: Border.all(color: Colors.green.shade300),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'النص المتعرف عليه:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    const SizedBox(height: 5),
                    Text(
                      _recognizedText,
                      style: const TextStyle(fontSize: 16),
                    ),
                  ],
                ),
              ),

            const SizedBox(height: 30),

            // أزرار الاختبار
            Column(
              children: [
                // زر اختبار التعرف على الكلام
                SizedBox(
                  width: double.infinity,
                  height: 60,
                  child: ElevatedButton.icon(
                    onPressed: _isListening ? _stopListening : _startListening,
                    icon: Icon(_isListening ? Icons.stop : Icons.mic),
                    label: Text(
                      _isListening
                          ? 'إيقاف الاستماع'
                          : 'اختبار التعرف على الكلام',
                      style: const TextStyle(fontSize: 16),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _isListening ? Colors.red : Colors.blue,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(15),
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: 15),

                // زر اختبار تحويل النص إلى كلام
                SizedBox(
                  width: double.infinity,
                  height: 60,
                  child: ElevatedButton.icon(
                    onPressed: _isSpeaking ? _stopSpeaking : _testTextToSpeech,
                    icon: Icon(_isSpeaking ? Icons.stop : Icons.volume_up),
                    label: Text(
                      _isSpeaking
                          ? 'إيقاف النطق'
                          : 'اختبار تحويل النص إلى كلام',
                      style: const TextStyle(fontSize: 16),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _isSpeaking ? Colors.red : Colors.green,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(15),
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: 15),

                // زر اختبار الأذونات
                SizedBox(
                  width: double.infinity,
                  height: 60,
                  child: ElevatedButton.icon(
                    onPressed: _testPermissions,
                    icon: const Icon(Icons.security),
                    label: const Text(
                      'اختبار الأذونات',
                      style: TextStyle(fontSize: 16),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(15),
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: 15),

                // زر نطق النص المتعرف عليه
                if (_recognizedText.isNotEmpty)
                  SizedBox(
                    width: double.infinity,
                    height: 60,
                    child: ElevatedButton.icon(
                      onPressed: () => _speakText(_recognizedText),
                      icon: const Icon(Icons.play_arrow),
                      label: const Text(
                        'نطق النص المتعرف عليه',
                        style: TextStyle(fontSize: 16),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.purple,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(15),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _startListening() async {
    try {
      final permission = await Permission.microphone.request();
      if (permission != PermissionStatus.granted) {
        setState(() {
          _statusMessage = 'يرجى السماح بالوصول للميكروفون';
        });
        return;
      }

      setState(() {
        _isListening = true;
        _statusMessage = 'أستمع إليك الآن... تحدث بوضوح';
        _recognizedText = '';
      });

      await _speechToText.listen(
        onResult: (result) {
          setState(() {
            _recognizedText = result.recognizedWords;
            _statusMessage = _recognizedText.isEmpty
                ? 'أستمع إليك الآن...'
                : 'سمعت: $_recognizedText';
          });

          if (result.finalResult) {
            _stopListening();
          }
        },
        localeId: 'ar-SA',
        listenFor: const Duration(seconds: 10),
        pauseFor: const Duration(seconds: 3),
      );
    } catch (e) {
      setState(() {
        _isListening = false;
        _statusMessage = 'خطأ في بدء الاستماع: $e';
      });
    }
  }

  void _stopListening() {
    if (_isListening) {
      _speechToText.stop();
      setState(() {
        _isListening = false;
        _statusMessage = _recognizedText.isEmpty
            ? 'لم يتم التعرف على أي كلام'
            : 'تم التعرف على: $_recognizedText';
      });
    }
  }

  Future<void> _testTextToSpeech() async {
    await _speakText('مرحباً، هذا اختبار لتحويل النص إلى كلام باللغة العربية');
  }

  Future<void> _speakText(String text) async {
    try {
      setState(() {
        _isSpeaking = true;
        _statusMessage = 'جاري النطق...';
      });

      await _flutterTts.speak(text);

      // انتظار انتهاء النطق
      await Future.delayed(const Duration(seconds: 3));

      setState(() {
        _isSpeaking = false;
        _statusMessage = 'تم النطق بنجاح ✅';
      });
    } catch (e) {
      setState(() {
        _isSpeaking = false;
        _statusMessage = 'خطأ في النطق: $e';
      });
    }
  }

  void _stopSpeaking() {
    if (_isSpeaking) {
      _flutterTts.stop();
      setState(() {
        _isSpeaking = false;
        _statusMessage = 'تم إيقاف النطق';
      });
    }
  }

  Future<void> _testPermissions() async {
    try {
      setState(() {
        _statusMessage = 'جاري فحص الأذونات...';
      });

      final micStatus = await Permission.microphone.status;

      setState(() {
        _statusMessage = 'إذن الميكروفون: $micStatus';
      });

      if (micStatus != PermissionStatus.granted) {
        final newStatus = await Permission.microphone.request();
        setState(() {
          _statusMessage = 'إذن الميكروفون الجديد: $newStatus';
        });
      }
    } catch (e) {
      setState(() {
        _statusMessage = 'خطأ في فحص الأذونات: $e';
      });
    }
  }
}
