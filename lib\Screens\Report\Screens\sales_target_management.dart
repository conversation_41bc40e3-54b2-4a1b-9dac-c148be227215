// شاشة إدارة أهداف المبيعات
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:mobile_pos/Provider/sales_performance_provider.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/empty_screen_widget.dart';
import 'package:mobile_pos/models/sales_performance_model.dart';
import 'package:nb_utils/nb_utils.dart';

/// شاشة إدارة أهداف المبيعات
class SalesTargetManagement extends ConsumerStatefulWidget {
  const SalesTargetManagement({super.key});

  @override
  ConsumerState<SalesTargetManagement> createState() =>
      _SalesTargetManagementState();
}

class _SalesTargetManagementState extends ConsumerState<SalesTargetManagement> {
  @override
  Widget build(BuildContext context) {
    final targetsData = ref.watch(salesTargetProvider);

    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        title: Text(
          'إدارة أهداف المبيعات',
          style: GoogleFonts.poppins(
            color: Colors.white,
            fontSize: 20.0,
          ),
        ),
        iconTheme: const IconThemeData(color: Colors.white),
        centerTitle: true,
        backgroundColor: kMainColor,
        elevation: 0.0,
      ),
      body: Container(
        alignment: Alignment.topCenter,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(30),
            topLeft: Radius.circular(30),
          ),
        ),
        child: targetsData.when(
          data: (data) {
            if (data.isEmpty) {
              return const EmptyScreenWidget();
            }

            return ListView.builder(
              padding: const EdgeInsets.all(15),
              itemCount: data.length,
              itemBuilder: (context, index) {
                final target = data[index];
                return Card(
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: ListTile(
                    contentPadding: const EdgeInsets.all(15),
                    title: Text(
                      _getTargetTypeText(target.targetType),
                      style: GoogleFonts.poppins(
                        color: kMainColor,
                        fontSize: 16.0,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 5),
                        Text(
                          'المندوب: ${target.userName}',
                          style: GoogleFonts.poppins(
                            color: kGreyTextColor,
                            fontSize: 12.0,
                          ),
                        ),
                        Text(
                          'القيمة: ${target.targetValue.toStringAsFixed(0)}',
                          style: GoogleFonts.poppins(
                            color: kGreyTextColor,
                            fontSize: 12.0,
                          ),
                        ),
                        Text(
                          'الفترة: ${DateFormat('yyyy/MM/dd').format(target.startDate)} - ${DateFormat('yyyy/MM/dd').format(target.endDate)}',
                          style: GoogleFonts.poppins(
                            color: kGreyTextColor,
                            fontSize: 12.0,
                          ),
                        ),
                        if (target.description.isNotEmpty)
                          Text(
                            'الوصف: ${target.description}',
                            style: GoogleFonts.poppins(
                              color: kGreyTextColor,
                              fontSize: 12.0,
                            ),
                          ),
                      ],
                    ),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          icon: const Icon(Icons.edit, color: kMainColor),
                          onPressed: () {
                            _showTargetDialog(target: target);
                          },
                        ),
                        IconButton(
                          icon: const Icon(Icons.delete, color: Colors.red),
                          onPressed: () {
                            _showDeleteConfirmation(target);
                          },
                        ),
                      ],
                    ),
                  ),
                );
              },
            );
          },
          loading: () => const Center(
            child: CircularProgressIndicator(),
          ),
          error: (error, stackTrace) => Center(
            child: Text(
              'حدث خطأ: $error',
              style: const TextStyle(
                color: Colors.red,
              ),
            ),
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: kMainColor,
        onPressed: () {
          _showTargetDialog();
        },
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  void _showTargetDialog({SalesTargetModel? target}) async {
    final performanceData = ref.read(salesPerformanceProvider);

    if (performanceData.value == null || performanceData.value!.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يمكن إضافة هدف. لا توجد بيانات للمستخدمين.'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final users = performanceData.value!
        .map((p) => {'id': p.userId, 'name': p.userName})
        .toList();

    String? selectedUserId = target?.userId ?? users.first['id'];
    TargetType selectedType = target?.targetType ?? TargetType.amount;
    final targetValueController =
        TextEditingController(text: target?.targetValue.toString() ?? '');
    final descriptionController =
        TextEditingController(text: target?.description ?? '');

    DateTime startDate = target?.startDate ?? DateTime.now();
    DateTime endDate =
        target?.endDate ?? DateTime.now().add(const Duration(days: 30));

    final startDateController =
        TextEditingController(text: DateFormat('yyyy/MM/dd').format(startDate));
    final endDateController =
        TextEditingController(text: DateFormat('yyyy/MM/dd').format(endDate));

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          target == null ? 'إضافة هدف جديد' : 'تعديل الهدف',
          style: GoogleFonts.poppins(
            color: kMainColor,
            fontSize: 18.0,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // اختيار المستخدم
              DropdownButtonFormField<String>(
                value: selectedUserId,
                decoration: InputDecoration(
                  labelText: 'المستخدم',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(5),
                  ),
                ),
                items: users.map((user) {
                  return DropdownMenuItem<String>(
                    value: user['id'],
                    child: Text(user['name'] ?? ''),
                  );
                }).toList(),
                onChanged: (value) {
                  selectedUserId = value;
                },
              ),
              const SizedBox(height: 15),

              // اختيار نوع الهدف
              DropdownButtonFormField<TargetType>(
                value: selectedType,
                decoration: InputDecoration(
                  labelText: 'نوع الهدف',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(5),
                  ),
                ),
                items: const [
                  DropdownMenuItem<TargetType>(
                    value: TargetType.amount,
                    child: Text('المبيعات (المبلغ)'),
                  ),
                  DropdownMenuItem<TargetType>(
                    value: TargetType.sales,
                    child: Text('المبيعات (العدد)'),
                  ),
                  DropdownMenuItem<TargetType>(
                    value: TargetType.customers,
                    child: Text('العملاء'),
                  ),
                ],
                onChanged: (value) {
                  selectedType = value!;
                },
              ),
              const SizedBox(height: 15),

              // قيمة الهدف
              AppTextField(
                textFieldType: TextFieldType.NUMBER,
                controller: targetValueController,
                decoration: InputDecoration(
                  labelText: 'قيمة الهدف',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(5),
                  ),
                ),
              ),
              const SizedBox(height: 15),

              // تاريخ البداية
              AppTextField(
                textFieldType: TextFieldType.NAME,
                controller: startDateController,
                decoration: InputDecoration(
                  labelText: 'تاريخ البداية',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(5),
                  ),
                  prefixIcon: const Icon(Icons.calendar_today),
                ),
                readOnly: true,
                onTap: () async {
                  final picked = await showDatePicker(
                    context: context,
                    initialDate: startDate,
                    firstDate: DateTime(2020),
                    lastDate: DateTime(2030),
                  );

                  if (picked != null) {
                    setState(() {
                      startDate = picked;
                      startDateController.text =
                          DateFormat('yyyy/MM/dd').format(picked);
                    });
                  }
                },
              ),
              const SizedBox(height: 15),

              // تاريخ النهاية
              AppTextField(
                textFieldType: TextFieldType.NAME,
                controller: endDateController,
                decoration: InputDecoration(
                  labelText: 'تاريخ النهاية',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(5),
                  ),
                  prefixIcon: const Icon(Icons.calendar_today),
                ),
                readOnly: true,
                onTap: () async {
                  final picked = await showDatePicker(
                    context: context,
                    initialDate: endDate,
                    firstDate: startDate,
                    lastDate: DateTime(2030),
                  );

                  if (picked != null) {
                    setState(() {
                      endDate = picked;
                      endDateController.text =
                          DateFormat('yyyy/MM/dd').format(picked);
                    });
                  }
                },
              ),
              const SizedBox(height: 15),

              // الوصف
              AppTextField(
                textFieldType: TextFieldType.MULTILINE,
                controller: descriptionController,
                decoration: InputDecoration(
                  labelText: 'الوصف',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(5),
                  ),
                ),
                maxLines: 3,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              if (targetValueController.text.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('يرجى إدخال قيمة الهدف'),
                    backgroundColor: Colors.red,
                  ),
                );
                return;
              }

              if (selectedUserId == null) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('يرجى اختيار المستخدم'),
                    backgroundColor: Colors.red,
                  ),
                );
                return;
              }

              final userName = users.firstWhere(
                      (user) => user['id'] == selectedUserId)['name'] ??
                  '';

              final newTarget = SalesTargetModel(
                id: target?.id ?? '',
                userId: selectedUserId!,
                userName: userName,
                targetType: selectedType,
                targetValue: double.tryParse(targetValueController.text) ?? 0,
                startDate: startDate,
                endDate: endDate,
                description: descriptionController.text,
              );

              if (target == null) {
                ref.read(salesTargetProvider.notifier).addTarget(newTarget);
              } else {
                ref.read(salesTargetProvider.notifier).updateTarget(newTarget);
              }

              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: kMainColor,
            ),
            child: Text(
              target == null ? 'إضافة' : 'تحديث',
              style: const TextStyle(
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(SalesTargetModel target) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الهدف'),
        content: const Text('هل أنت متأكد من حذف هذا الهدف؟'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              ref.read(salesTargetProvider.notifier).deleteTarget(target.id);
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: const Text(
              'حذف',
              style: TextStyle(
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getTargetTypeText(TargetType type) {
    switch (type) {
      case TargetType.amount:
        return 'هدف المبيعات (المبلغ)';
      case TargetType.sales:
        return 'هدف المبيعات (العدد)';
      case TargetType.customers:
        return 'هدف العملاء';
    }
  }
}
