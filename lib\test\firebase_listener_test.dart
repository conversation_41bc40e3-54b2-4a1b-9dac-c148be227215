import 'package:flutter/material.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:mobile_pos/services/firebase_listener_manager.dart';
import 'package:mobile_pos/utils/firebase_listener_helper.dart';

/// شاشة اختبار بسيطة لاختبار مدير المستمعين الجديد
class FirebaseListenerTest extends StatefulWidget {
  const FirebaseListenerTest({super.key});

  @override
  State<FirebaseListenerTest> createState() => _FirebaseListenerTestState();
}

class _FirebaseListenerTestState extends State<FirebaseListenerTest>
    with FirebaseListenerHelper {
  // عدد المستمعين النشطة
  int _activeListenersCount = 0;
  
  // نتائج الاختبار
  final List<String> _testResults = [];
  
  @override
  void initState() {
    super.initState();
    _updateActiveListenersCount();
  }
  
  /// تحديث عدد المستمعين النشطة
  void _updateActiveListenersCount() {
    setState(() {
      _activeListenersCount = FirebaseListenerManager().activeListenersCount;
    });
  }
  
  /// اختبار إنشاء مستمع
  Future<void> _testCreateListener() async {
    const path = 'test/simple_listener';
    
    try {
      // إنشاء مستمع
      final stream = listenToPath(path);
      
      // الاستماع للأحداث
      stream.listen((event) {
        setState(() {
          _testResults.add('تم استلام حدث من المسار $path: ${event.snapshot.value}');
        });
      });
      
      // إرسال بيانات اختبار
      await FirebaseDatabase.instance.ref(path).set({'test': 'data1'});
      
      // تحديث عدد المستمعين النشطة
      _updateActiveListenersCount();
      
      setState(() {
        _testResults.add('تم إنشاء مستمع للمسار $path بنجاح');
      });
    } catch (e) {
      setState(() {
        _testResults.add('خطأ في إنشاء مستمع للمسار $path: $e');
      });
    }
  }
  
  /// اختبار إنشاء مستمع مكرر
  Future<void> _testCreateDuplicateListener() async {
    const path = 'test/duplicate_listener';
    
    try {
      // إنشاء مستمع أول
      final stream1 = listenToPath(path);
      
      // إنشاء مستمع ثاني لنفس المسار
      final stream2 = listenToPath(path);
      
      // الاستماع للأحداث من المستمع الأول
      stream1.listen((event) {
        setState(() {
          _testResults.add('مستمع 1: تم استلام حدث من المسار $path');
        });
      });
      
      // الاستماع للأحداث من المستمع الثاني
      stream2.listen((event) {
        setState(() {
          _testResults.add('مستمع 2: تم استلام حدث من المسار $path');
        });
      });
      
      // إرسال بيانات اختبار
      await FirebaseDatabase.instance.ref(path).set({'test': 'data2'});
      
      // تحديث عدد المستمعين النشطة
      _updateActiveListenersCount();
      
      setState(() {
        _testResults.add('تم اختبار المستمعين المكررين بنجاح');
      });
    } catch (e) {
      setState(() {
        _testResults.add('خطأ في اختبار المستمعين المكررين: $e');
      });
    }
  }
  
  /// اختبار إلغاء المستمع
  Future<void> _testCancelListener() async {
    const path = 'test/cancel_listener';
    
    try {
      // إنشاء مستمع
      final stream = listenToPath(path);
      
      // الاستماع للأحداث
      stream.listen((event) {
        setState(() {
          _testResults.add('تم استلام حدث من المسار $path');
        });
      });
      
      // إرسال بيانات اختبار
      await FirebaseDatabase.instance.ref(path).set({'test': 'data3'});
      
      // تحديث عدد المستمعين النشطة
      _updateActiveListenersCount();
      
      // إلغاء المستمع
      await cancelListener(path);
      
      // تحديث عدد المستمعين النشطة بعد الإلغاء
      _updateActiveListenersCount();
      
      setState(() {
        _testResults.add('تم إلغاء المستمع للمسار $path بنجاح');
      });
    } catch (e) {
      setState(() {
        _testResults.add('خطأ في إلغاء المستمع للمسار $path: $e');
      });
    }
  }
  
  /// مسح نتائج الاختبار
  void _clearTestResults() {
    setState(() {
      _testResults.clear();
    });
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار مدير مستمعي Firebase'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'عدد المستمعين النشطة: $_activeListenersCount',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _testCreateListener,
                    child: const Text('إنشاء مستمع'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _testCreateDuplicateListener,
                    child: const Text('إنشاء مستمع مكرر'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _testCancelListener,
                    child: const Text('إلغاء مستمع'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _clearTestResults,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                    ),
                    child: const Text('مسح النتائج'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _updateActiveListenersCount,
                    child: const Text('تحديث العداد'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              'نتائج الاختبار:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            Expanded(
              child: ListView.builder(
                itemCount: _testResults.length,
                itemBuilder: (context, index) {
                  final result = _testResults[_testResults.length - 1 - index];
                  return ListTile(
                    title: Text(result),
                    leading: Icon(
                      result.contains('خطأ')
                          ? Icons.error
                          : Icons.check_circle,
                      color: result.contains('خطأ') ? Colors.red : Colors.green,
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
