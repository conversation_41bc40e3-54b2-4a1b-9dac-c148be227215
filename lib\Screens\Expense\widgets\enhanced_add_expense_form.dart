// بسم الله الرحمن الرحيم
// نموذج محسن لإضافة المصروفات

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:mobile_pos/const_commas.dart';
import '../../../constant.dart';

/// نموذج محسن لإضافة المصروفات
class EnhancedAddExpenseForm extends StatefulWidget {
  final Function(Map<String, dynamic>) onSubmit;
  final Map<String, dynamic>? initialData;

  const EnhancedAddExpenseForm({
    super.key,
    required this.onSubmit,
    this.initialData,
  });

  @override
  State<EnhancedAddExpenseForm> createState() => _EnhancedAddExpenseFormState();
}

class _EnhancedAddExpenseFormState extends State<EnhancedAddExpenseForm> {
  final _formKey = GlobalKey<FormState>();
  final _expenseForController = TextEditingController();
  final _amountController = TextEditingController();
  final _noteController = TextEditingController();
  final _referenceController = TextEditingController();

  DateTime _selectedDate = DateTime.now();
  String _selectedCategory = 'نوع المصروف';
  String _selectedPaymentType = 'Cash';
  String _amountText = '';

  final List<String> _paymentMethods = [
    'Cash',
    'Bank',
    'Card',
    'Mobile Payment',
    'Snacks',
  ];

  final List<String> _expenseCategories = [
    'رواتب',
    'إيجار',
    'كهرباء',
    'مياه',
    'هاتف وإنترنت',
    'وقود',
    'صيانة',
    'مواد خام',
    'تسويق',
    'مصروفات إدارية',
    'ضرائب',
    'تأمين',
    'أخرى',
  ];

  @override
  void initState() {
    super.initState();
    if (widget.initialData != null) {
      _loadInitialData();
    }
  }

  void _loadInitialData() {
    final data = widget.initialData!;
    _expenseForController.text = data['expanseFor'] ?? '';
    _amountController.text = data['amount'] ?? '';
    _noteController.text = data['note'] ?? '';
    _referenceController.text = data['referenceNo'] ?? '';
    _selectedCategory = data['category'] ?? 'نوع المصروف';
    _selectedPaymentType = data['paymentType'] ?? 'Cash';
    if (data['expenseDate'] != null) {
      _selectedDate = DateTime.parse(data['expenseDate']);
    }
  }

  @override
  void dispose() {
    _expenseForController.dispose();
    _amountController.dispose();
    _noteController.dispose();
    _referenceController.dispose();
    super.dispose();
  }

  void _selectDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2015),
      lastDate: DateTime(2101),
    );
    
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  void _selectCategory() async {
    final selected = await showModalBottomSheet<String>(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => _CategorySelectionSheet(
        categories: _expenseCategories,
        selectedCategory: _selectedCategory,
      ),
    );
    
    if (selected != null) {
      setState(() {
        _selectedCategory = selected;
      });
    }
  }

  void _submitForm() {
    if (_formKey.currentState!.validate() && _selectedCategory != 'نوع المصروف') {
      final data = {
        'expenseDate': _selectedDate.toString(),
        'category': _selectedCategory,
        'expanseFor': _expenseForController.text,
        'paymentType': _selectedPaymentType,
        'amount': _amountText,
        'referenceNo': _referenceController.text,
        'note': _noteController.text,
        'account': '',
      };
      
      widget.onSubmit(data);
    } else if (_selectedCategory == 'نوع المصروف') {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'من فضلك اختر فئة المصروف',
            style: GoogleFonts.cairo(),
          ),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // تاريخ المصروف
          _buildSectionTitle('تاريخ المصروف'),
          _buildDateField(),
          const SizedBox(height: 20),

          // فئة المصروف
          _buildSectionTitle('فئة المصروف'),
          _buildCategoryField(),
          const SizedBox(height: 20),

          // وصف المصروف
          _buildSectionTitle('وصف المصروف'),
          _buildExpenseForField(),
          const SizedBox(height: 20),

          // طريقة الدفع
          _buildSectionTitle('طريقة الدفع'),
          _buildPaymentTypeField(),
          const SizedBox(height: 20),

          // المبلغ
          _buildSectionTitle('المبلغ'),
          _buildAmountField(),
          const SizedBox(height: 20),

          // رقم المرجع
          _buildSectionTitle('رقم المرجع (اختياري)'),
          _buildReferenceField(),
          const SizedBox(height: 20),

          // الملاحظات
          _buildSectionTitle('الملاحظات (اختياري)'),
          _buildNoteField(),
          const SizedBox(height: 30),

          // زر الحفظ
          SizedBox(
            width: double.infinity,
            height: 50,
            child: ElevatedButton(
              onPressed: _submitForm,
              style: ElevatedButton.styleFrom(
                backgroundColor: kMainColor,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                'حفظ المصروف',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        title,
        style: GoogleFonts.cairo(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: Colors.black87,
        ),
      ),
    );
  }

  Widget _buildDateField() {
    return InkWell(
      onTap: _selectDate,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Icon(
              Icons.calendar_today,
              color: kMainColor,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                DateFormat('dd MMMM yyyy', 'ar').format(_selectedDate),
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: Colors.black87,
                ),
              ),
            ),
            Icon(
              Icons.arrow_drop_down,
              color: Colors.grey.shade600,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryField() {
    return InkWell(
      onTap: _selectCategory,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Icon(
              Icons.category,
              color: kMainColor,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                _selectedCategory,
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: _selectedCategory == 'نوع المصروف' 
                      ? Colors.grey.shade600 
                      : Colors.black87,
                ),
              ),
            ),
            Icon(
              Icons.arrow_drop_down,
              color: Colors.grey.shade600,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExpenseForField() {
    return TextFormField(
      controller: _expenseForController,
      style: GoogleFonts.cairo(fontSize: 14),
      decoration: InputDecoration(
        hintText: 'ادخل وصف المصروف',
        prefixIcon: Icon(Icons.description, color: kMainColor),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: kMainColor, width: 2),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'من فضلك ادخل وصف المصروف';
        }
        return null;
      },
    );
  }

  Widget _buildPaymentTypeField() {
    return DropdownButtonFormField<String>(
      value: _selectedPaymentType,
      decoration: InputDecoration(
        prefixIcon: Icon(Icons.payment, color: kMainColor),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: kMainColor, width: 2),
        ),
      ),
      items: _paymentMethods.map((method) {
        return DropdownMenuItem(
          value: method,
          child: Text(
            _getPaymentTypeText(method),
            style: GoogleFonts.cairo(fontSize: 14),
          ),
        );
      }).toList(),
      onChanged: (value) {
        setState(() {
          _selectedPaymentType = value!;
        });
      },
    );
  }

  Widget _buildAmountField() {
    return TextFormField(
      controller: _amountController,
      keyboardType: TextInputType.number,
      style: GoogleFonts.cairo(fontSize: 14),
      decoration: InputDecoration(
        hintText: 'ادخل المبلغ',
        prefixIcon: Icon(Icons.attach_money, color: kMainColor),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: kMainColor, width: 2),
        ),
      ),
      onChanged: (value) {
        _amountText = value.replaceAll(',', '');
        if (_amountText.isNotEmpty && double.tryParse(_amountText) != null) {
          var formattedText = myFormat.format(int.parse(_amountText));
          _amountController.value = _amountController.value.copyWith(
            text: formattedText,
            selection: TextSelection.collapsed(offset: formattedText.length),
          );
        }
      },
      validator: (value) {
        if (_amountText.isEmpty) {
          return 'من فضلك ادخل المبلغ';
        } else if (double.tryParse(_amountText) == null) {
          return 'ادخل مبلغ صحيح';
        }
        return null;
      },
    );
  }

  Widget _buildReferenceField() {
    return TextFormField(
      controller: _referenceController,
      style: GoogleFonts.cairo(fontSize: 14),
      decoration: InputDecoration(
        hintText: 'ادخل رقم المرجع',
        prefixIcon: Icon(Icons.confirmation_number, color: kMainColor),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: kMainColor, width: 2),
        ),
      ),
    );
  }

  Widget _buildNoteField() {
    return TextFormField(
      controller: _noteController,
      maxLines: 3,
      style: GoogleFonts.cairo(fontSize: 14),
      decoration: InputDecoration(
        hintText: 'ادخل ملاحظات إضافية',
        prefixIcon: Icon(Icons.note_alt, color: kMainColor),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: kMainColor, width: 2),
        ),
      ),
    );
  }

  String _getPaymentTypeText(String paymentType) {
    switch (paymentType.toLowerCase()) {
      case 'cash':
        return 'نقدي';
      case 'bank':
        return 'بنكي';
      case 'card':
        return 'بطاقة';
      case 'mobile payment':
        return 'دفع إلكتروني';
      case 'snacks':
        return 'وجبات خفيفة';
      default:
        return paymentType;
    }
  }
}

/// شيت اختيار الفئة
class _CategorySelectionSheet extends StatelessWidget {
  final List<String> categories;
  final String selectedCategory;

  const _CategorySelectionSheet({
    required this.categories,
    required this.selectedCategory,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // المقبض
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 20),
          
          // العنوان
          Text(
            'اختر فئة المصروف',
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 20),
          
          // قائمة الفئات
          Flexible(
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: categories.length,
              itemBuilder: (context, index) {
                final category = categories[index];
                final isSelected = category == selectedCategory;
                
                return ListTile(
                  leading: Icon(
                    _getCategoryIcon(category),
                    color: isSelected ? kMainColor : Colors.grey.shade600,
                  ),
                  title: Text(
                    category,
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                      color: isSelected ? kMainColor : Colors.black87,
                    ),
                  ),
                  trailing: isSelected 
                      ? Icon(Icons.check, color: kMainColor)
                      : null,
                  onTap: () {
                    Navigator.pop(context, category);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  IconData _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'رواتب':
        return Icons.people_outline;
      case 'إيجار':
        return Icons.home_outlined;
      case 'كهرباء':
        return Icons.electrical_services_outlined;
      case 'مياه':
        return Icons.water_drop_outlined;
      case 'هاتف وإنترنت':
        return Icons.phone_outlined;
      case 'وقود':
        return Icons.local_gas_station_outlined;
      case 'صيانة':
        return Icons.build_outlined;
      case 'مواد خام':
        return Icons.inventory_outlined;
      case 'تسويق':
        return Icons.campaign_outlined;
      case 'مصروفات إدارية':
        return Icons.business_outlined;
      case 'ضرائب':
        return Icons.receipt_long_outlined;
      case 'تأمين':
        return Icons.security_outlined;
      default:
        return Icons.category_outlined;
    }
  }
}
