import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/treasury_report_model.dart';
import '../services/treasury_report_service.dart';

// Treasury report service provider
final treasuryReportServiceProvider = Provider<TreasuryReportService>((ref) {
  return TreasuryReportService();
});

// Generate report provider
final generateReportProvider = StateNotifierProvider<GenerateReportNotifier, AsyncValue<TreasuryReportModel?>>((ref) {
  final reportService = ref.read(treasuryReportServiceProvider);
  return GenerateReportNotifier(reportService);
});

class GenerateReportNotifier extends StateNotifier<AsyncValue<TreasuryReportModel?>> {
  final TreasuryReportService _reportService;

  GenerateReportNotifier(this._reportService) : super(const AsyncValue.data(null));

  Future<void> generateDailyReport(DateTime date) async {
    state = const AsyncValue.loading();
    
    try {
      final report = await _reportService.generateDailyReport(date);
      state = AsyncValue.data(report);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> generateWeeklyReport(DateTime weekStart) async {
    state = const AsyncValue.loading();
    
    try {
      final report = await _reportService.generateWeeklyReport(weekStart);
      state = AsyncValue.data(report);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> generateMonthlyReport(DateTime month) async {
    state = const AsyncValue.loading();
    
    try {
      final report = await _reportService.generateMonthlyReport(month);
      state = AsyncValue.data(report);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> generateYearlyReport(DateTime year) async {
    state = const AsyncValue.loading();
    
    try {
      final report = await _reportService.generateYearlyReport(year);
      state = AsyncValue.data(report);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> generateCustomReport(DateTime startDate, DateTime endDate) async {
    state = const AsyncValue.loading();
    
    try {
      final report = await _reportService.generateCustomReport(startDate, endDate);
      state = AsyncValue.data(report);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  void clearReport() {
    state = const AsyncValue.data(null);
  }
}

// Export report provider
final exportReportProvider = StateNotifierProvider<ExportReportNotifier, AsyncValue<String?>>((ref) {
  final reportService = ref.read(treasuryReportServiceProvider);
  return ExportReportNotifier(reportService);
});

class ExportReportNotifier extends StateNotifier<AsyncValue<String?>> {
  final TreasuryReportService _reportService;

  ExportReportNotifier(this._reportService) : super(const AsyncValue.data(null));

  Future<void> exportToJson(TreasuryReportModel report) async {
    state = const AsyncValue.loading();
    
    try {
      final filePath = await _reportService.exportReportToJson(report);
      state = AsyncValue.data(filePath);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> exportToCsv(TreasuryReportModel report) async {
    state = const AsyncValue.loading();
    
    try {
      final filePath = await _reportService.exportReportToCsv(report);
      state = AsyncValue.data(filePath);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  void clearExport() {
    state = const AsyncValue.data(null);
  }
}

// Category summary provider
final categorySummaryProvider = FutureProvider.family<Map<String, double>, Map<String, DateTime>>((ref, dateRange) async {
  final reportService = ref.read(treasuryReportServiceProvider);
  return await reportService.getCategorySummary(dateRange['startDate']!, dateRange['endDate']!);
});

// Monthly comparison provider
final monthlyComparisonProvider = FutureProvider.family<Map<String, Map<String, double>>, int>((ref, year) async {
  final reportService = ref.read(treasuryReportServiceProvider);
  return await reportService.getMonthlyComparison(year);
});

// Report types provider
final reportTypesProvider = Provider<List<Map<String, String>>>((ref) {
  return [
    {'key': 'daily', 'name': 'تقرير يومي', 'description': 'تقرير المعاملات لليوم المحدد'},
    {'key': 'weekly', 'name': 'تقرير أسبوعي', 'description': 'تقرير المعاملات للأسبوع المحدد'},
    {'key': 'monthly', 'name': 'تقرير شهري', 'description': 'تقرير المعاملات للشهر المحدد'},
    {'key': 'yearly', 'name': 'تقرير سنوي', 'description': 'تقرير المعاملات للسنة المحددة'},
    {'key': 'custom', 'name': 'تقرير مخصص', 'description': 'تقرير المعاملات لفترة مخصصة'},
  ];
});

// Quick reports provider
final quickReportsProvider = Provider<List<Map<String, dynamic>>>((ref) {
  final now = DateTime.now();
  
  return [
    {
      'title': 'اليوم',
      'subtitle': 'تقرير اليوم الحالي',
      'type': 'daily',
      'date': now,
      'icon': 'today',
    },
    {
      'title': 'هذا الأسبوع',
      'subtitle': 'تقرير الأسبوع الحالي',
      'type': 'weekly',
      'date': now.subtract(Duration(days: now.weekday - 1)),
      'icon': 'week',
    },
    {
      'title': 'هذا الشهر',
      'subtitle': 'تقرير الشهر الحالي',
      'type': 'monthly',
      'date': DateTime(now.year, now.month, 1),
      'icon': 'month',
    },
    {
      'title': 'هذه السنة',
      'subtitle': 'تقرير السنة الحالية',
      'type': 'yearly',
      'date': DateTime(now.year, 1, 1),
      'icon': 'year',
    },
    {
      'title': 'الشهر الماضي',
      'subtitle': 'تقرير الشهر الماضي',
      'type': 'monthly',
      'date': DateTime(now.year, now.month - 1, 1),
      'icon': 'last_month',
    },
    {
      'title': 'السنة الماضية',
      'subtitle': 'تقرير السنة الماضية',
      'type': 'yearly',
      'date': DateTime(now.year - 1, 1, 1),
      'icon': 'last_year',
    },
  ];
});

// Report filters provider
final reportFiltersProvider = StateNotifierProvider<ReportFiltersNotifier, Map<String, dynamic>>((ref) {
  return ReportFiltersNotifier();
});

class ReportFiltersNotifier extends StateNotifier<Map<String, dynamic>> {
  ReportFiltersNotifier() : super({
    'reportType': 'monthly',
    'startDate': DateTime.now().subtract(const Duration(days: 30)),
    'endDate': DateTime.now(),
    'includeIncome': true,
    'includeExpense': true,
    'selectedCategories': <String>[],
    'groupBy': 'category', // 'category', 'type', 'date'
  });

  void updateReportType(String reportType) {
    state = {...state, 'reportType': reportType};
  }

  void updateDateRange(DateTime startDate, DateTime endDate) {
    state = {...state, 'startDate': startDate, 'endDate': endDate};
  }

  void updateIncludeIncome(bool include) {
    state = {...state, 'includeIncome': include};
  }

  void updateIncludeExpense(bool include) {
    state = {...state, 'includeExpense': include};
  }

  void updateSelectedCategories(List<String> categories) {
    state = {...state, 'selectedCategories': categories};
  }

  void updateGroupBy(String groupBy) {
    state = {...state, 'groupBy': groupBy};
  }

  void resetFilters() {
    state = {
      'reportType': 'monthly',
      'startDate': DateTime.now().subtract(const Duration(days: 30)),
      'endDate': DateTime.now(),
      'includeIncome': true,
      'includeExpense': true,
      'selectedCategories': <String>[],
      'groupBy': 'category',
    };
  }
}
