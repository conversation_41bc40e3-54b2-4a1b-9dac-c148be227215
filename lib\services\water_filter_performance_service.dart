import 'package:flutter/foundation.dart';
import 'package:mobile_pos/services/water_filter_cache_service.dart';
import 'package:mobile_pos/services/water_filter_notification_service.dart';

/// خدمة تحسين الأداء لنظام فلاتر المياه
class WaterFilterPerformanceService {
  static final WaterFilterPerformanceService _instance = 
      WaterFilterPerformanceService._internal();
  factory WaterFilterPerformanceService() => _instance;
  WaterFilterPerformanceService._internal();

  final WaterFilterCacheService _cacheService = WaterFilterCacheService();
  final WaterFilterNotificationService _notificationService = 
      WaterFilterNotificationService();

  /// تهيئة النظام وتحسين الأداء
  Future<void> initializeSystem() async {
    try {
      debugPrint('🚀 بدء تهيئة نظام فلاتر المياه...');
      
      // تحميل البيانات الأساسية في الخلفية
      await _preloadEssentialData();
      
      // بدء الفحص الدوري للإشعارات
      _startPeriodicNotificationCheck();
      
      // تنظيف البيانات القديمة
      await _cleanupOldData();
      
      debugPrint('✅ تم تهيئة النظام بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة النظام: $e');
    }
  }

  /// تحميل البيانات الأساسية مسبقاً
  Future<void> _preloadEssentialData() async {
    try {
      debugPrint('📦 تحميل البيانات الأساسية...');
      
      // تحميل البيانات بشكل متوازي
      await Future.wait([
        _cacheService.getCachedProducts(),
        _cacheService.getCachedCustomers(),
        _cacheService.getCachedSystems(),
        _cacheService.getCachedStats(),
      ]);
      
      debugPrint('✅ تم تحميل البيانات الأساسية');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل البيانات الأساسية: $e');
    }
  }

  /// بدء الفحص الدوري للإشعارات
  void _startPeriodicNotificationCheck() {
    debugPrint('🔔 بدء الفحص الدوري للإشعارات');
    _notificationService.schedulePeriodicCheck();
  }

  /// تنظيف البيانات القديمة
  Future<void> _cleanupOldData() async {
    try {
      debugPrint('🧹 تنظيف البيانات القديمة...');
      
      // مسح Cache القديم
      _cacheService.clearAllCache();
      
      // إعادة تحميل البيانات الطازجة
      await _preloadEssentialData();
      
      debugPrint('✅ تم تنظيف البيانات القديمة');
    } catch (e) {
      debugPrint('❌ خطأ في تنظيف البيانات: $e');
    }
  }

  /// تحسين أداء البحث
  Future<Map<String, dynamic>> optimizeSearchPerformance() async {
    try {
      debugPrint('🔍 تحسين أداء البحث...');
      
      final startTime = DateTime.now();
      
      // تحميل جميع البيانات للبحث
      final results = await Future.wait([
        _cacheService.getCachedProducts(),
        _cacheService.getCachedCustomers(),
        _cacheService.getCachedSystems(),
      ]);
      
      final endTime = DateTime.now();
      final loadTime = endTime.difference(startTime).inMilliseconds;
      
      final stats = {
        'products_count': results[0].length,
        'customers_count': results[1].length,
        'systems_count': results[2].length,
        'load_time_ms': loadTime,
        'performance_rating': _calculatePerformanceRating(loadTime),
      };
      
      debugPrint('✅ تحسين البحث مكتمل: ${stats['load_time_ms']}ms');
      return stats;
    } catch (e) {
      debugPrint('❌ خطأ في تحسين البحث: $e');
      return {};
    }
  }

  /// حساب تقييم الأداء
  String _calculatePerformanceRating(int loadTimeMs) {
    if (loadTimeMs < 500) return 'ممتاز';
    if (loadTimeMs < 1000) return 'جيد جداً';
    if (loadTimeMs < 2000) return 'جيد';
    if (loadTimeMs < 3000) return 'مقبول';
    return 'يحتاج تحسين';
  }

  /// تحسين أداء التقارير
  Future<Map<String, dynamic>> optimizeReportsPerformance() async {
    try {
      debugPrint('📊 تحسين أداء التقارير...');
      
      final startTime = DateTime.now();
      
      // تحميل البيانات المطلوبة للتقارير
      final systems = await _cacheService.getCachedSystems();
      final products = await _cacheService.getCachedProducts();
      final customers = await _cacheService.getCachedCustomers();
      
      // حساب الإحصائيات الأساسية
      final now = DateTime.now();
      final thisMonth = DateTime(now.year, now.month);
      
      final monthlyStats = {
        'new_systems': systems.where((s) => 
          s.installationDate.isAfter(thisMonth)).length,
        'maintenance_due': systems.where((s) => 
          s.nextMaintenanceDate.isBefore(now)).length,
        'low_stock_products': products.where((p) => p.stock <= 5).length,
        'total_revenue': systems.fold(0.0, (sum, s) => sum + s.totalCost),
        'total_paid': systems.fold(0.0, (sum, s) => sum + s.paidAmount),
      };
      
      final endTime = DateTime.now();
      final processTime = endTime.difference(startTime).inMilliseconds;
      
      debugPrint('✅ تحسين التقارير مكتمل: ${processTime}ms');
      
      return {
        'stats': monthlyStats,
        'process_time_ms': processTime,
        'performance_rating': _calculatePerformanceRating(processTime),
      };
    } catch (e) {
      debugPrint('❌ خطأ في تحسين التقارير: $e');
      return {};
    }
  }

  /// مراقبة استخدام الذاكرة
  Map<String, dynamic> monitorMemoryUsage() {
    try {
      final cacheInfo = _cacheService.getCacheInfo();
      
      return {
        'cache_info': cacheInfo,
        'memory_status': 'مراقب',
        'optimization_suggestions': _getOptimizationSuggestions(cacheInfo),
      };
    } catch (e) {
      debugPrint('❌ خطأ في مراقبة الذاكرة: $e');
      return {};
    }
  }

  /// الحصول على اقتراحات التحسين
  List<String> _getOptimizationSuggestions(Map<String, dynamic> cacheInfo) {
    final suggestions = <String>[];
    
    final productsCount = cacheInfo['products_count'] ?? 0;
    final customersCount = cacheInfo['customers_count'] ?? 0;
    final systemsCount = cacheInfo['systems_count'] ?? 0;
    
    if (productsCount > 1000) {
      suggestions.add('عدد المنتجات كبير - فكر في تطبيق pagination');
    }
    
    if (customersCount > 500) {
      suggestions.add('عدد العملاء كبير - فكر في تطبيق lazy loading');
    }
    
    if (systemsCount > 1000) {
      suggestions.add('عدد الأنظمة كبير - فكر في تطبيق فلترة ذكية');
    }
    
    if (suggestions.isEmpty) {
      suggestions.add('الأداء جيد - لا توجد اقتراحات حالياً');
    }
    
    return suggestions;
  }

  /// تحسين شامل للنظام
  Future<Map<String, dynamic>> performComprehensiveOptimization() async {
    try {
      debugPrint('🔧 بدء التحسين الشامل للنظام...');
      
      final startTime = DateTime.now();
      
      // تنفيذ جميع التحسينات
      final results = await Future.wait([
        optimizeSearchPerformance(),
        optimizeReportsPerformance(),
        _notificationService.getNotificationStats(),
      ]);
      
      final endTime = DateTime.now();
      final totalTime = endTime.difference(startTime).inMilliseconds;
      
      final optimizationReport = {
        'search_optimization': results[0],
        'reports_optimization': results[1],
        'notification_stats': results[2],
        'memory_usage': monitorMemoryUsage(),
        'total_optimization_time_ms': totalTime,
        'overall_rating': _calculatePerformanceRating(totalTime),
        'timestamp': DateTime.now().toIso8601String(),
      };
      
      debugPrint('✅ التحسين الشامل مكتمل: ${totalTime}ms');
      return optimizationReport;
    } catch (e) {
      debugPrint('❌ خطأ في التحسين الشامل: $e');
      return {
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// إعادة تعيين النظام
  Future<void> resetSystem() async {
    try {
      debugPrint('🔄 إعادة تعيين النظام...');
      
      // مسح جميع البيانات المؤقتة
      _cacheService.clearAllCache();
      
      // إعادة تهيئة النظام
      await initializeSystem();
      
      debugPrint('✅ تم إعادة تعيين النظام بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في إعادة تعيين النظام: $e');
    }
  }

  /// الحصول على تقرير الأداء
  Future<Map<String, dynamic>> getPerformanceReport() async {
    try {
      final report = await performComprehensiveOptimization();
      
      return {
        'system_status': 'نشط',
        'last_optimization': report['timestamp'],
        'performance_summary': {
          'search_rating': report['search_optimization']?['performance_rating'] ?? 'غير محدد',
          'reports_rating': report['reports_optimization']?['performance_rating'] ?? 'غير محدد',
          'overall_rating': report['overall_rating'] ?? 'غير محدد',
        },
        'recommendations': _getOptimizationSuggestions(
          report['memory_usage']?['cache_info'] ?? {}),
        'detailed_report': report,
      };
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء تقرير الأداء: $e');
      return {
        'system_status': 'خطأ',
        'error': e.toString(),
      };
    }
  }

  /// فحص صحة النظام
  Future<Map<String, dynamic>> performHealthCheck() async {
    try {
      debugPrint('🏥 فحص صحة النظام...');
      
      final healthStatus = {
        'cache_service': 'سليم',
        'notification_service': 'سليم',
        'database_connection': 'سليم',
        'memory_usage': 'طبيعي',
        'overall_health': 'ممتاز',
      };
      
      // فحص Cache
      try {
        await _cacheService.getCachedStats();
      } catch (e) {
        healthStatus['cache_service'] = 'خطأ';
        healthStatus['overall_health'] = 'يحتاج انتباه';
      }
      
      // فحص الإشعارات
      try {
        await _notificationService.getNotificationStats();
      } catch (e) {
        healthStatus['notification_service'] = 'خطأ';
        healthStatus['overall_health'] = 'يحتاج انتباه';
      }
      
      debugPrint('✅ فحص الصحة مكتمل: ${healthStatus['overall_health']}');
      return healthStatus;
    } catch (e) {
      debugPrint('❌ خطأ في فحص الصحة: $e');
      return {
        'overall_health': 'خطأ',
        'error': e.toString(),
      };
    }
  }
}
