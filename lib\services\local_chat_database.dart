import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:mobile_pos/models/chat/firebase_chat_conversation.dart';
import 'package:mobile_pos/models/chat/firebase_chat_message.dart';
import 'package:mobile_pos/models/chat/firebase_chat_user.dart';
import 'package:mobile_pos/models/chat/chat_user.dart';

/// خدمة قاعدة البيانات المحلية للدردشة
class LocalChatDatabase {
  static const String _conversationsBoxName = 'chat_conversations';
  static const String _messagesBoxName = 'chat_messages';
  static const String _usersBoxName = 'chat_users';
  static const String _lastSyncTimeKey = 'last_sync_time';

  static Box<String>? _conversationsBox;
  static Box<String>? _messagesBox;
  static Box<String>? _usersBox;
  static Box<String>? _metadataBox;

  /// تهيئة قاعدة البيانات المحلية
  static Future<void> initialize() async {
    try {
      // التحقق مما إذا كانت قاعدة البيانات مهيأة بالفعل
      if (isInitialized()) {
        debugPrint('قاعدة البيانات المحلية للدردشة مهيأة بالفعل');
        return;
      }

      await Hive.initFlutter();

      _conversationsBox = await Hive.openBox<String>(_conversationsBoxName);
      _messagesBox = await Hive.openBox<String>(_messagesBoxName);
      _usersBox = await Hive.openBox<String>(_usersBoxName);
      _metadataBox = await Hive.openBox<String>('chat_metadata');

      debugPrint('تم تهيئة قاعدة البيانات المحلية للدردشة بنجاح');
    } catch (e) {
      debugPrint('خطأ في تهيئة قاعدة البيانات المحلية للدردشة: $e');
    }
  }

  /// التحقق مما إذا كانت قاعدة البيانات مهيأة
  static bool isInitialized() {
    return _conversationsBox != null &&
        _messagesBox != null &&
        _usersBox != null &&
        _metadataBox != null;
  }

  /// جمع عدد الرسائل غير المقروءة لكل مستخدم
  static Map<String, int> _collectUnreadCounts(
      FirebaseChatConversation conversation) {
    final Map<String, int> unreadCounts = {};

    // الحصول على جميع المشاركين في المحادثة
    for (final userId in conversation.participants) {
      // الحصول على عدد الرسائل غير المقروءة لهذا المستخدم
      final count = conversation.getUnreadCountForUser(userId);
      unreadCounts[userId] = count;
    }

    return unreadCounts;
  }

  /// حفظ المحادثات في قاعدة البيانات المحلية
  static Future<void> saveConversations(
      List<FirebaseChatConversation> conversations) async {
    try {
      if (_conversationsBox == null) {
        debugPrint('لم يتم تهيئة صندوق المحادثات');
        return;
      }

      // حفظ كل محادثة
      for (final conversation in conversations) {
        final key = conversation.id ?? '';
        if (key.isEmpty) continue;

        // تحويل المحادثة إلى JSON
        final Map<String, dynamic> conversationMap = {
          'id': conversation.id,
          'participants': conversation.participants,
          'lastMessageText': conversation.lastMessageText,
          'lastMessageDate': conversation.lastMessageDate.toIso8601String(),
          'lastMessageSenderId': conversation.lastMessageSenderId,
          'lastMessageType': conversation.lastMessageType,
          'isGroup': conversation.isGroup,
          'groupName': conversation.groupName,
          'groupDescription': conversation.groupDescription,
          // جمع عدد الرسائل غير المقروءة لكل مستخدم
          'unreadCounts': _collectUnreadCounts(conversation),
          'muted': conversation.muted,
          'pinned': conversation.pinned,
        };

        // حفظ المحادثة
        await _conversationsBox!.put(key, jsonEncode(conversationMap));
      }

      // تحديث وقت آخر مزامنة
      await _updateLastSyncTime();

      debugPrint(
          'تم حفظ ${conversations.length} محادثة في قاعدة البيانات المحلية');
    } catch (e) {
      debugPrint('خطأ في حفظ المحادثات في قاعدة البيانات المحلية: $e');
    }
  }

  /// الحصول على المحادثات من قاعدة البيانات المحلية
  static List<FirebaseChatConversation> getConversations() {
    try {
      if (_conversationsBox == null) {
        debugPrint('لم يتم تهيئة صندوق المحادثات');
        return [];
      }

      final List<FirebaseChatConversation> conversations = [];

      // الحصول على كل المحادثات
      for (final key in _conversationsBox!.keys) {
        final conversationJson = _conversationsBox!.get(key);
        if (conversationJson == null) continue;

        try {
          // تحويل JSON إلى محادثة
          final Map<String, dynamic> conversationMap =
              jsonDecode(conversationJson);

          final conversation = FirebaseChatConversation(
            id: conversationMap['id'],
            participants:
                List<String>.from(conversationMap['participants'] ?? []),
            lastMessageText: conversationMap['lastMessageText'] ?? '',
            lastMessageSenderId: conversationMap['lastMessageSenderId'] ?? '',
            lastMessageType: conversationMap['lastMessageType'] ?? 'text',
          );

          // تحويل التاريخ
          if (conversationMap['lastMessageDate'] != null) {
            conversation.lastMessageDate =
                DateTime.parse(conversationMap['lastMessageDate']);
          }

          // تعيين الخصائص الأخرى
          conversation.isGroup = conversationMap['isGroup'] ?? false;
          conversation.groupName = conversationMap['groupName'];
          conversation.groupDescription = conversationMap['groupDescription'];

          // تحويل عدد الرسائل غير المقروءة لكل مستخدم
          if (conversationMap['unreadCounts'] != null) {
            final unreadCounts =
                Map<String, int>.from(conversationMap['unreadCounts']);
            // تعيين عدد الرسائل غير المقروءة لكل مستخدم
            unreadCounts.forEach((userId, count) {
              conversation.setUnreadCountForUser(userId, count);
            });
          }

          // تحويل القوائم الأخرى
          conversation.muted =
              List<String>.from(conversationMap['muted'] ?? []);
          conversation.pinned =
              List<String>.from(conversationMap['pinned'] ?? []);

          conversations.add(conversation);
        } catch (e) {
          debugPrint('خطأ في تحويل المحادثة: $e');
        }
      }

      // ترتيب المحادثات حسب آخر رسالة
      conversations.sort((a, b) {
        return b.lastMessageDate.compareTo(a.lastMessageDate);
      });

      debugPrint(
          'تم استرجاع ${conversations.length} محادثة من قاعدة البيانات المحلية');
      return conversations;
    } catch (e) {
      debugPrint('خطأ في استرجاع المحادثات من قاعدة البيانات المحلية: $e');
      return [];
    }
  }

  /// حفظ الرسائل في قاعدة البيانات المحلية
  static Future<void> saveMessages(
      String conversationId, List<FirebaseChatMessage> messages) async {
    try {
      if (_messagesBox == null) {
        debugPrint('لم يتم تهيئة صندوق الرسائل');
        return;
      }

      // حفظ كل رسالة
      for (final message in messages) {
        final key = message.id ?? '';
        if (key.isEmpty) continue;

        // تحويل الرسالة إلى JSON
        final Map<String, dynamic> messageMap = {
          'id': message.id,
          'text': message.text,
          'senderId': message.senderId,
          'senderName': message.senderName,
          'recipientId': message.recipientId,
          'conversationId': message.conversationId,
          'messageType': message.messageType,
          'isRead': message.isRead,
          'isDelivered': message.isDelivered,
          'isDeleted': message.isDeleted,
          'deletedFor': message.deletedFor,
          'reactions': message.reactions,
          'replyTo': message.replyTo,
          'mediaUrl': message.mediaUrl,
          'createdAt': message.createdAt?.toIso8601String(),
        };

        // حفظ الرسالة
        await _messagesBox!.put(key, jsonEncode(messageMap));
      }

      debugPrint('تم حفظ ${messages.length} رسالة في قاعدة البيانات المحلية');
    } catch (e) {
      debugPrint('خطأ في حفظ الرسائل في قاعدة البيانات المحلية: $e');
    }
  }

  /// الحصول على الرسائل من قاعدة البيانات المحلية
  static List<FirebaseChatMessage> getMessages(String conversationId) {
    try {
      if (_messagesBox == null) {
        debugPrint('لم يتم تهيئة صندوق الرسائل');
        return [];
      }

      final List<FirebaseChatMessage> messages = [];

      // الحصول على كل الرسائل
      for (final key in _messagesBox!.keys) {
        final messageJson = _messagesBox!.get(key);
        if (messageJson == null) continue;

        try {
          // تحويل JSON إلى رسالة
          final Map<String, dynamic> messageMap = jsonDecode(messageJson);

          // التحقق من أن الرسالة تنتمي للمحادثة المطلوبة
          if (messageMap['conversationId'] != conversationId) continue;

          final message = FirebaseChatMessage(
            id: messageMap['id'],
            text: messageMap['text'] ?? '',
            senderId: messageMap['senderId'] ?? '',
            senderName: messageMap['senderName'] ?? '',
            recipientId: messageMap['recipientId'] ?? '',
            conversationId: messageMap['conversationId'] ?? '',
            messageType: messageMap['messageType'] ?? '',
            isRead: messageMap['isRead'] ?? false,
            isDelivered: messageMap['isDelivered'] ?? false,
            isDeleted: messageMap['isDeleted'] ?? false,
            deletedFor: List<String>.from(messageMap['deletedFor'] ?? []),
            replyTo: messageMap['replyTo'],
            mediaUrl: messageMap['mediaUrl'],
          );

          // تعيين تاريخ الإنشاء
          if (messageMap['createdAt'] != null) {
            message.createdAt = DateTime.parse(messageMap['createdAt']);
          }

          // تحويل التفاعلات
          if (messageMap['reactions'] != null) {
            message.reactions =
                Map<String, dynamic>.from(messageMap['reactions']);
          }

          messages.add(message);
        } catch (e) {
          debugPrint('خطأ في تحويل الرسالة: $e');
        }
      }

      // ترتيب الرسائل حسب التاريخ
      messages.sort((a, b) {
        if (a.createdAt == null && b.createdAt == null) return 0;
        if (a.createdAt == null) return -1;
        if (b.createdAt == null) return 1;
        return a.createdAt!.compareTo(b.createdAt!);
      });

      debugPrint(
          'تم استرجاع ${messages.length} رسالة من قاعدة البيانات المحلية');
      return messages;
    } catch (e) {
      debugPrint('خطأ في استرجاع الرسائل من قاعدة البيانات المحلية: $e');
      return [];
    }
  }

  /// تحويل ChatUser إلى FirebaseChatUser
  static FirebaseChatUser _convertToFirebaseChatUser(dynamic user) {
    if (user is FirebaseChatUser) {
      return user;
    } else if (user is ChatUser) {
      return FirebaseChatUser(
        id: user.objectId,
        userId: user.userId,
        name: user.name,
        email: user.email,
        profileImage: user.profileImage,
        deviceToken: user.deviceToken,
        lastSeen: user.lastSeen,
        isOnline: user.isOnline,
        phone: user.phone,
        role: user.role,
      );
    } else {
      throw ArgumentError('نوع المستخدم غير مدعوم');
    }
  }

  /// حفظ المستخدمين في قاعدة البيانات المحلية
  static Future<void> saveUsers(List<dynamic> users) async {
    try {
      if (_usersBox == null) {
        debugPrint('لم يتم تهيئة صندوق المستخدمين');
        return;
      }

      // حفظ كل مستخدم
      for (final user in users) {
        final firebaseUser = _convertToFirebaseChatUser(user);
        final key = firebaseUser.userId;
        if (key.isEmpty) continue;

        // تحويل المستخدم إلى JSON
        final Map<String, dynamic> userMap = {
          'id': firebaseUser.id,
          'userId': firebaseUser.userId,
          'name': firebaseUser.name,
          'email': firebaseUser.email,
          'profileImage': firebaseUser.profileImage,
          'deviceToken': firebaseUser.deviceToken,
          'lastSeen': firebaseUser.lastSeen.toIso8601String(),
          'isOnline': firebaseUser.isOnline,
          'phone': firebaseUser.phone,
          'role': firebaseUser.role,
        };

        // حفظ المستخدم
        await _usersBox!.put(key, jsonEncode(userMap));
      }

      debugPrint('تم حفظ ${users.length} مستخدم في قاعدة البيانات المحلية');
    } catch (e) {
      debugPrint('خطأ في حفظ المستخدمين في قاعدة البيانات المحلية: $e');
    }
  }

  /// الحصول على المستخدمين من قاعدة البيانات المحلية
  static List<FirebaseChatUser> getUsers() {
    try {
      if (_usersBox == null) {
        debugPrint('لم يتم تهيئة صندوق المستخدمين');
        return [];
      }

      final List<FirebaseChatUser> users = [];

      // الحصول على كل المستخدمين
      for (final key in _usersBox!.keys) {
        final userJson = _usersBox!.get(key);
        if (userJson == null) continue;

        try {
          // تحويل JSON إلى مستخدم
          final Map<String, dynamic> userMap = jsonDecode(userJson);

          final user = FirebaseChatUser(
            id: userMap['id'],
            userId: userMap['userId'] ?? '',
            name: userMap['name'] ?? '',
            email: userMap['email'] ?? '',
            profileImage: userMap['profileImage'] ?? '',
            deviceToken: userMap['deviceToken'] ?? '',
            isOnline: userMap['isOnline'] ?? false,
            phone: userMap['phone'] ?? '',
            role: userMap['role'] ?? '',
          );

          // تحويل التاريخ
          if (userMap['lastSeen'] != null) {
            user.lastSeen = DateTime.parse(userMap['lastSeen']);
          }

          users.add(user);
        } catch (e) {
          debugPrint('خطأ في تحويل المستخدم: $e');
        }
      }

      debugPrint('تم استرجاع ${users.length} مستخدم من قاعدة البيانات المحلية');
      return users;
    } catch (e) {
      debugPrint('خطأ في استرجاع المستخدمين من قاعدة البيانات المحلية: $e');
      return [];
    }
  }

  /// تحديث حالة قراءة الرسائل محلياً
  static Future<void> markMessagesAsRead(
      String conversationId, String senderId) async {
    try {
      if (_messagesBox == null || _conversationsBox == null) {
        debugPrint('لم يتم تهيئة صندوق الرسائل أو المحادثات');
        return;
      }

      // الحصول على الرسائل غير المقروءة
      final messages = getMessages(conversationId);
      bool hasUnreadMessages = false;

      // تحديث حالة القراءة للرسائل
      for (final message in messages) {
        if (message.senderId == senderId && !message.isRead) {
          hasUnreadMessages = true;
          message.isRead = true;

          // تحويل الرسالة إلى JSON
          final Map<String, dynamic> messageMap = {
            'id': message.id,
            'text': message.text,
            'senderId': message.senderId,
            'senderName': message.senderName,
            'recipientId': message.recipientId,
            'conversationId': message.conversationId,
            'messageType': message.messageType,
            'isRead': true, // تحديث حالة القراءة
            'isDelivered': message.isDelivered,
            'isDeleted': message.isDeleted,
            'deletedFor': message.deletedFor,
            'reactions': message.reactions,
            'replyTo': message.replyTo,
            'mediaUrl': message.mediaUrl,
            'createdAt': message.createdAt?.toIso8601String(),
          };

          // حفظ الرسالة المحدثة
          await _messagesBox!.put(message.id!, jsonEncode(messageMap));
        }
      }

      // تحديث عدد الرسائل غير المقروءة في المحادثة
      if (hasUnreadMessages) {
        // الحصول على المحادثة
        final conversationJson = _conversationsBox!.get(conversationId);
        if (conversationJson != null) {
          final Map<String, dynamic> conversationMap =
              jsonDecode(conversationJson);

          // تحديث عدد الرسائل غير المقروءة
          Map<String, dynamic> unreadCounts =
              Map<String, dynamic>.from(conversationMap['unreadCounts'] ?? {});
          unreadCounts[senderId] = 0;
          conversationMap['unreadCounts'] = unreadCounts;

          // حفظ المحادثة المحدثة
          await _conversationsBox!
              .put(conversationId, jsonEncode(conversationMap));
        }
      }

      debugPrint('تم تحديث حالة قراءة الرسائل محلياً');
    } catch (e) {
      debugPrint('خطأ في تحديث حالة قراءة الرسائل محلياً: $e');
    }
  }

  /// تحديث وقت آخر مزامنة
  static Future<void> _updateLastSyncTime() async {
    try {
      if (_metadataBox == null) return;
      await _metadataBox!
          .put(_lastSyncTimeKey, DateTime.now().toIso8601String());
    } catch (e) {
      debugPrint('خطأ في تحديث وقت آخر مزامنة: $e');
    }
  }

  /// الحصول على وقت آخر مزامنة
  static DateTime? getLastSyncTime() {
    try {
      if (_metadataBox == null) return null;
      final lastSyncTimeStr = _metadataBox!.get(_lastSyncTimeKey);
      if (lastSyncTimeStr == null) return null;
      return DateTime.parse(lastSyncTimeStr);
    } catch (e) {
      debugPrint('خطأ في الحصول على وقت آخر مزامنة: $e');
      return null;
    }
  }

  /// مسح قاعدة البيانات المحلية
  static Future<void> clearDatabase() async {
    try {
      await _conversationsBox?.clear();
      await _messagesBox?.clear();
      await _usersBox?.clear();
      await _metadataBox?.delete(_lastSyncTimeKey);
      debugPrint('تم مسح قاعدة البيانات المحلية');
    } catch (e) {
      debugPrint('خطأ في مسح قاعدة البيانات المحلية: $e');
    }
  }
}
