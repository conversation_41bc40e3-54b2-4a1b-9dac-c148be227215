import 'dart:async';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../model/device_session_model.dart';

/// خدمة إدارة جلسات الأجهزة
class DeviceSessionService {
  static final DeviceSessionService _instance = DeviceSessionService._internal();
  factory DeviceSessionService() => _instance;
  DeviceSessionService._internal();

  final DatabaseReference _database = FirebaseDatabase.instance.ref();
  Timer? _heartbeatTimer;
  String? _currentSessionId;

  /// الحد الأقصى لعدد الأجهزة المسموح بها لكل مستخدم
  static const int maxDevicesPerUser = 1;

  /// مدة انتهاء صلاحية الجلسة (بالدقائق)
  static const int sessionTimeoutMinutes = 30;

  /// التحقق من إمكانية تسجيل الدخول على جهاز جديد
  Future<bool> canLoginOnDevice(String userId) async {
    try {
      // الحصول على معرف الجهاز الحالي
      final deviceId = await DeviceSessionModel.getDeviceId();
      
      // التحقق من الجلسات النشطة للمستخدم
      final activeSessions = await getActiveUserSessions(userId);
      
      // التحقق من وجود جلسة نشطة على نفس الجهاز
      final existingSession = activeSessions.where(
        (session) => session.deviceId == deviceId
      ).firstOrNull;
      
      if (existingSession != null) {
        // الجهاز له جلسة نشطة بالفعل، يمكن تسجيل الدخول
        return true;
      }
      
      // التحقق من عدد الأجهزة النشطة
      if (activeSessions.length >= maxDevicesPerUser) {
        debugPrint('تم تجاوز الحد الأقصى للأجهزة: ${activeSessions.length}/$maxDevicesPerUser');
        return false;
      }
      
      return true;
    } catch (e) {
      debugPrint('خطأ في التحقق من إمكانية تسجيل الدخول: $e');
      return false;
    }
  }

  /// إنشاء جلسة جديدة للجهاز
  Future<String?> createDeviceSession(String userId) async {
    try {
      final deviceId = await DeviceSessionModel.getDeviceId();
      final deviceName = await DeviceSessionModel.getDeviceName();
      final platform = DeviceSessionModel.getPlatform();
      final sessionId = DeviceSessionModel.generateSessionId();
      
      final session = DeviceSessionModel(
        deviceId: deviceId,
        deviceName: deviceName,
        platform: platform,
        userId: userId,
        loginTime: DateTime.now(),
        lastActivity: DateTime.now(),
        isActive: true,
        sessionId: sessionId,
      );
      
      // حفظ الجلسة في Firebase
      await _database
          .child('device_sessions')
          .child(userId)
          .child(sessionId)
          .set(session.toMap());
      
      // حفظ معرف الجلسة محلياً
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('current_session_id', sessionId);
      
      _currentSessionId = sessionId;
      
      // بدء مراقبة نشاط الجلسة
      _startHeartbeat(userId, sessionId);
      
      debugPrint('تم إنشاء جلسة جديدة: $sessionId');
      return sessionId;
    } catch (e) {
      debugPrint('خطأ في إنشاء جلسة الجهاز: $e');
      return null;
    }
  }

  /// الحصول على الجلسات النشطة للمستخدم
  Future<List<DeviceSessionModel>> getActiveUserSessions(String userId) async {
    try {
      final snapshot = await _database
          .child('device_sessions')
          .child(userId)
          .get();
      
      if (!snapshot.exists) {
        return [];
      }
      
      final sessions = <DeviceSessionModel>[];
      final data = snapshot.value as Map<dynamic, dynamic>;
      
      for (final entry in data.entries) {
        try {
          final sessionData = Map<String, dynamic>.from(entry.value);
          final session = DeviceSessionModel.fromMap(sessionData);
          
          // التحقق من انتهاء صلاحية الجلسة
          final timeDifference = DateTime.now().difference(session.lastActivity);
          if (timeDifference.inMinutes <= sessionTimeoutMinutes && session.isActive) {
            sessions.add(session);
          } else {
            // إزالة الجلسة المنتهية الصلاحية
            await _database
                .child('device_sessions')
                .child(userId)
                .child(session.sessionId)
                .remove();
          }
        } catch (e) {
          debugPrint('خطأ في معالجة بيانات الجلسة: $e');
        }
      }
      
      return sessions;
    } catch (e) {
      debugPrint('خطأ في الحصول على الجلسات النشطة: $e');
      return [];
    }
  }

  /// تحديث نشاط الجلسة
  Future<void> updateSessionActivity(String userId, String sessionId) async {
    try {
      await _database
          .child('device_sessions')
          .child(userId)
          .child(sessionId)
          .child('lastActivity')
          .set(DateTime.now().toIso8601String());
    } catch (e) {
      debugPrint('خطأ في تحديث نشاط الجلسة: $e');
    }
  }

  /// إنهاء جلسة الجهاز
  Future<void> endDeviceSession(String userId, String sessionId) async {
    try {
      await _database
          .child('device_sessions')
          .child(userId)
          .child(sessionId)
          .remove();
      
      // إزالة معرف الجلسة من التخزين المحلي
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('current_session_id');
      
      _currentSessionId = null;
      _stopHeartbeat();
      
      debugPrint('تم إنهاء الجلسة: $sessionId');
    } catch (e) {
      debugPrint('خطأ في إنهاء جلسة الجهاز: $e');
    }
  }

  /// إنهاء جميع الجلسات للمستخدم
  Future<void> endAllUserSessions(String userId) async {
    try {
      await _database
          .child('device_sessions')
          .child(userId)
          .remove();
      
      debugPrint('تم إنهاء جميع جلسات المستخدم: $userId');
    } catch (e) {
      debugPrint('خطأ في إنهاء جميع الجلسات: $e');
    }
  }

  /// بدء مراقبة نشاط الجلسة
  void _startHeartbeat(String userId, String sessionId) {
    _stopHeartbeat();
    
    _heartbeatTimer = Timer.periodic(
      const Duration(minutes: 5),
      (timer) async {
        await updateSessionActivity(userId, sessionId);
      },
    );
  }

  /// إيقاف مراقبة نشاط الجلسة
  void _stopHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = null;
  }

  /// الحصول على معرف الجلسة الحالية
  Future<String?> getCurrentSessionId() async {
    if (_currentSessionId != null) {
      return _currentSessionId;
    }
    
    try {
      final prefs = await SharedPreferences.getInstance();
      _currentSessionId = prefs.getString('current_session_id');
      return _currentSessionId;
    } catch (e) {
      debugPrint('خطأ في الحصول على معرف الجلسة الحالية: $e');
      return null;
    }
  }

  /// التحقق من صحة الجلسة الحالية
  Future<bool> isCurrentSessionValid(String userId) async {
    try {
      final sessionId = await getCurrentSessionId();
      if (sessionId == null) return false;
      
      final snapshot = await _database
          .child('device_sessions')
          .child(userId)
          .child(sessionId)
          .get();
      
      if (!snapshot.exists) return false;
      
      final sessionData = Map<String, dynamic>.from(snapshot.value as Map);
      final session = DeviceSessionModel.fromMap(sessionData);
      
      // التحقق من انتهاء صلاحية الجلسة
      final timeDifference = DateTime.now().difference(session.lastActivity);
      return timeDifference.inMinutes <= sessionTimeoutMinutes && session.isActive;
    } catch (e) {
      debugPrint('خطأ في التحقق من صحة الجلسة: $e');
      return false;
    }
  }

  /// تنظيف الجلسات المنتهية الصلاحية
  Future<void> cleanupExpiredSessions() async {
    try {
      final snapshot = await _database.child('device_sessions').get();
      
      if (!snapshot.exists) return;
      
      final usersData = snapshot.value as Map<dynamic, dynamic>;
      
      for (final userEntry in usersData.entries) {
        final userId = userEntry.key.toString();
        final sessionsData = userEntry.value as Map<dynamic, dynamic>;
        
        for (final sessionEntry in sessionsData.entries) {
          try {
            final sessionData = Map<String, dynamic>.from(sessionEntry.value);
            final session = DeviceSessionModel.fromMap(sessionData);
            
            final timeDifference = DateTime.now().difference(session.lastActivity);
            if (timeDifference.inMinutes > sessionTimeoutMinutes) {
              await _database
                  .child('device_sessions')
                  .child(userId)
                  .child(session.sessionId)
                  .remove();
              
              debugPrint('تم حذف جلسة منتهية الصلاحية: ${session.sessionId}');
            }
          } catch (e) {
            debugPrint('خطأ في معالجة جلسة للتنظيف: $e');
          }
        }
      }
    } catch (e) {
      debugPrint('خطأ في تنظيف الجلسات المنتهية الصلاحية: $e');
    }
  }
}
