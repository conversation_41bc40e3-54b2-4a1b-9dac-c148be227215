// ignore_for_file: avoid_print

import 'dart:convert';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/Provider/profile_provider.dart';
import 'package:mobile_pos/model/daily_transaction_model.dart';
import 'package:mobile_pos/model/product_model.dart';
import 'package:mobile_pos/model/user_role_model.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:restart_app/restart_app.dart';

import '../Screens/tax report/tax_model.dart';
import '../currency.dart';

import '../model/add_to_cart_model.dart';

const kMainColor = Color(0xff16181D); // لون نبيتي غامق
const kGreyTextColor = Color(0xFF828282);
const kBorderColorTextField = Color(0xFFC2C2C2);
const kBorderColor = Color(0xff7D7D7D);
const kDarkWhite = Color(0xFFF1F7F7);
const kTitleColor = Color(0xFF000000);
const kWhite = Color(0xFFFFFFFF);
const kAlertColor = Color(0xFFFF8C34);
const kPremiumPlanColor = Color(0xFF282930);
const kPremiumPlanColor2 = Color(0xFFFF5F00);

///_______Purchase Code_____________________________
// Purchase code ????? ?? ???? ???? ????? - ?? ????? ?? validation
String purchaseCode = '';
const kMaroonAccent = Color(0xFFE57820); // نبيتي أكسنت
List<String> selectedNumbers = [];

String calculateProductVat({required AddToCartModel product}) {
  if (product.taxType == 'Inclusive') {
    double taxRate = product.groupTaxRate / 100;
    return (((double.tryParse(product.productPurchasePrice) ?? 0) /
                    (taxRate + 1) *
                    taxRate) *
                product.quantity) >
            0
        ? (((double.tryParse(product.productPurchasePrice) ?? 0) /
                    (taxRate + 1) *
                    taxRate) *
                product.quantity)
            .toStringAsFixed(2)
        : '';
  } else {
    return (((product.groupTaxRate *
                        (double.tryParse(product.productPurchasePrice) ?? 0)) /
                    100) *
                product.quantity) >
            0
        ? (((product.groupTaxRate *
                        (double.tryParse(product.productPurchasePrice) ?? 0)) /
                    100) *
                product.quantity)
            .toStringAsFixed(2)
        : '';
  }
}

String calculateProductVatPurchase({required ProductModel product}) {
  if (product.taxType == 'Inclusive') {
    double taxRate = product.groupTaxRate / 100;
    return (((double.tryParse(product.productPurchasePrice) ?? 0) /
                (taxRate + 1) *
                taxRate) *
            (double.tryParse(product.productStock) ?? 0))
        .toStringAsFixed(2);
  } else {
    return (((product.groupTaxRate *
                    (double.tryParse(product.productPurchasePrice) ?? 0)) /
                100) *
            (double.tryParse(product.productStock) ?? 0))
        .toStringAsFixed(2);
  }
}

bool isVatAdded({required List<AddToCartModel> products}) {
  return products.any((element) => element.groupTaxRate > 0);
}

///________Demo_mode__________________
String appName = 'AmrDev';
String invoiceName = 'AmrDev_';
String splashScreenLogo = 'assets/images/logo1.png';
String loginScreenLogo = 'assets/images/sblogo.png';
bool isDemo = false;
String demoText = 'مش هتقدر تغير أي حاجة في وضع التجربة';

void postDailyTransaction({
  required DailyTransactionModel dailyTransactionModel,
}) async {
  final DatabaseReference personalInformationRef = FirebaseDatabase.instance
      .ref()
      .child(constUserId)
      .child('Personal Information');
  num remainingBalance = 0;
  personalInformationRef.keepSynced(true);

  await personalInformationRef.get().then((value) {
    var data = jsonDecode(jsonEncode(value.value));
    remainingBalance =
        num.tryParse(data['remainingShopBalance'].toString()) ?? 0;
  });

  if (dailyTransactionModel.type == 'Sale' ||
      dailyTransactionModel.type == 'Due Collection' ||
      dailyTransactionModel.type == 'Income') {
    remainingBalance += dailyTransactionModel.paymentIn;
  } else {
    remainingBalance -= dailyTransactionModel.paymentOut;
  }

  dailyTransactionModel.remainingBalance = remainingBalance;

  ///________post_remaining Balance_on_personal_information___________________________________________________
  personalInformationRef.update({'remainingShopBalance': remainingBalance});

  ///_________dailyTransaction_Posting________________________________________________________________________
  DatabaseReference dailyTransactionRef = FirebaseDatabase.instance.ref(
    "$constUserId/Daily Transaction",
  );
  await dailyTransactionRef.push().set(dailyTransactionModel.toJson());
  dailyTransactionRef.keepSynced(true);
}

Future<String?> getSaleID({required String id}) async {
  String? key;
  await FirebaseDatabase.instance
      .ref()
      .child('Admin Panel')
      .child('Seller List')
      .orderByKey()
      .get()
      .then((value) async {
    for (var element in value.children) {
      var data = jsonDecode(jsonEncode(element.value));
      if (data['userId'].toString() == id) {
        key = element.key.toString();
      }
    }
  });
  return key;
}

void increaseStock(String productCode, num quantity) async {
  final ref = FirebaseDatabase.instance.ref(constUserId).child('Products');
  ref.keepSynced(true);

  ref.orderByKey().get().then((value) {
    for (var element in value.children) {
      var data = jsonDecode(jsonEncode(element.value));
      if (data['productCode'] == productCode) {
        String? key = element.key;
        num previousStock =
            num.tryParse(element.child('productStock').value.toString()) ?? 0;
        print(previousStock);
        num remainStock = previousStock + quantity;
        ref.child(key!).update({'productStock': '$remainStock'});
      }
    }
  });
}

void decreaseStock(String productCode, num quantity) async {
  final ref = FirebaseDatabase.instance.ref(constUserId).child('Products');
  ref.keepSynced(true);

  ref.orderByKey().get().then((value) {
    for (var element in value.children) {
      var data = jsonDecode(jsonEncode(element.value));
      if (data['productCode'] == productCode) {
        String? key = element.key;
        num previousStock =
            num.tryParse(element.child('productStock').value.toString()) ?? 0;
        print(previousStock);
        num remainStock = previousStock - quantity;
        ref.child(key!).update({'productStock': '$remainStock'});
      }
    }
  });
}

void updateFromShopRemainBalance({
  required num paidAmount,
  required bool isFromPurchase,
  required WidgetRef t,
}) async {
  if (paidAmount > 0) {
    t.watch(profileDetailsProvider).when(
          data: (data) {
            final ref = FirebaseDatabase.instance.ref(
              '$constUserId/Personal Information',
            );
            ref.keepSynced(true);

            ref.update({
              'remainingShopBalance': isFromPurchase
                  ? (data.remainingShopBalance ?? 0) + paidAmount
                  : (data.remainingShopBalance ?? 0) - paidAmount,
            });
          },
          error: (error, stackTrace) {},
          loading: () {},
        );
  }
}

void getSpecificCustomersDueUpdate({
  required String phoneNumber,
  required bool isDuePaid,
  required num due,
}) async {
  final ref = FirebaseDatabase.instance.ref(constUserId).child('Customers');
  ref.keepSynced(true);
  String? key;

  ref.orderByKey().get().then((value) {
    for (var element in value.children) {
      var data = jsonDecode(jsonEncode(element.value));
      if (data['phoneNumber'] == phoneNumber) {
        key = element.key;
        num previousDue = element.child('due').value.toString().toInt();

        num totalDue;

        isDuePaid ? totalDue = previousDue + due : totalDue = previousDue - due;
        ref.child(key!).update({'due': '$totalDue'});
      }
    }
  });
}

final kTextStyle = GoogleFonts.manrope(color: Colors.white);

bool connected = false;
bool isPrintEnable = true;
List<String> paymentsTypeList = ['نقدي', 'دفع موبايل'];

const String onesignalAppId = '************************************';

bool isReportShow = false;

//___________currency__________________________

const String appVersion = '0.0.4';
const String playStoreUrl = "market://details?id=com.amrdev.mobilepos";

const String paypalClientId =
    'ASWARYNRARFIbKf8U4u5Bq9-8tYVszzpkfRhohErQil3izlffjVQE-L0K2M0_bobdPhj2Qyf7uHoGctI';
const String paypalClientSecret =
    'EDNYPyTGpziJzfVhqsf75iodgFGSCOZAKXTHuD9YR5PWt5ruwc1HIzgT6STEznFfGro5E8h466i0sPtb';
const bool sandbox = true;

const kButtonDecoration = BoxDecoration(
  borderRadius: BorderRadius.all(Radius.circular(5)),
);

Future<String> getUserID() async {
  final prefs = await SharedPreferences.getInstance();
  final String? uid = prefs.getString('userId');

  return uid ?? '';
}

const kInputDecoration = InputDecoration(
  hintStyle: TextStyle(color: kBorderColorTextField),
  filled: true,
  fillColor: Colors.white70,
  enabledBorder: OutlineInputBorder(
    borderRadius: BorderRadius.all(Radius.circular(8.0)),
    borderSide: BorderSide(color: kBorderColorTextField, width: 2),
  ),
  focusedBorder: OutlineInputBorder(
    borderRadius: BorderRadius.all(Radius.circular(6.0)),
    borderSide: BorderSide(color: kBorderColorTextField, width: 2),
  ),
);

OutlineInputBorder outlineInputBorder() {
  return OutlineInputBorder(
    borderRadius: BorderRadius.circular(1.0),
    borderSide: const BorderSide(color: kBorderColorTextField),
  );
}

final otpInputDecoration = InputDecoration(
  contentPadding: const EdgeInsets.symmetric(vertical: 5.0),
  border: outlineInputBorder(),
  focusedBorder: outlineInputBorder(),
  enabledBorder: outlineInputBorder(),
);

final List<String> businessCategory = [
  'حقائب وأمتعة',
  'كتب وقرطاسية',
  'ملابس',
  'مواد بناء وخامات',
  'قهوة وشاي',
  'مستحضرات تجميل ومجوهرات',
  'كمبيوتر وإلكترونيات',
  'تجارة إلكترونية',
  'أثاث',
  'متجر عام',
  'هدايا وألعاب وزهور',
  'بقالة وفواكه ومخبوزات',
  'حرف يدوية',
  'منزل ومطبخ',
  'أدوات صحية وحديد',
  'إنترنت ودش وتلفزيون',
  'مغسلة',
  'تصنيع',
  'شحن موبايل',
  'دراجات نارية وقطع غيار',
  'موبايل وأجهزة',
  'صيدلية',
  'دواجن وزراعة',
  'حيوانات أليفة وإكسسوارات',
  'مطحنة أرز',
  'سوبر ماركت',
  'نظارات شمسية',
  'خدمات وإصلاح',
  'رياضة وتمارين',
  'أحذية',
  'صالون وصالون تجميل',
  'إيجار محلات ومكاتب',
  'تذاكر سفر وإيجار',
  'تجارة',
  'ألومنيوم وزجاج تايلندي',
  'مركبات وقطع غيار',
  'أخرى',
];

List<String> language = [
  'إنجليزي',
  'إسباني',
  'هندي',
  'عربي',
  'فرنسي',
  'بنغالي',
  'تركي',
  'صيني',
  'ياباني',
  'روماني',
  'ألماني',
  'فيتنامي',
  'إيطالي',
  'تايلندي',
  'برتغالي',
  'عبري',
  'بولندي',
  'مجري',
  'فنلندي',
  'كوري',
  'ماليزي',
  'إندونيسي',
  'أوكراني',
  'بوسني',
  'يوناني',
  'هولندي',
  'أردو',
  'سنهالي',
  'فارسي',
  'صربي',
  'خمير',
  'لاوسي',
  'روسي',
  'كانادا',
  'ماراثي',
  'تاميل',
  'أفريقاني',
  'تشيكي',
  'سويدي',
  'سلوفاكي',
  'سواحيلي',
  'ألباني',
  'دنماركي',
  'أذربيجاني',
  'كازاخي',
  'كرواتي',
  'نيبالي',
];

List<String> baseFlagsCode = [
  'US',
  'ES',
  'IN',
  'SA',
  'FR',
  'BD',
  'TR',
  'CN',
  'JP',
  'RO',
  'DE',
  'VN',
  'IT',
  'TH',
  'PT',
  'IL',
  'PL',
  'HU',
  'FI',
  'KR',
  'MY',
  'ID',
  'UA',
  'BA',
  'GR',
  'NL',
  'Pk',
  'LK',
  'IR',
  'RS',
  'KH',
  'LA',
  'RU',
  'IN',
  'IN',
  'IN',
  'ZA',
  'CZ',
  'SE',
  'SK',
  'TZ',
  'AL',
  'DK',
  'AZ',
  'KZ',
  'HR',
  'NP',
];

List<String> productCategory = [
  'أزياء',
  'إلكترونيات',
  'كمبيوتر',
  'أجهزة',
  'ساعات',
  'ملابس',
];

List<String> userRole = ['مدير عام', 'مدير', 'مستخدم'];

List<String> paymentType = ['شيك', 'إيداع', 'نقدي', 'تحويل', 'مبيعات'];
List<String> posStats = ['يومي', 'شهري', 'سنوي'];
List<String> saleStats = ['أسبوعي', 'شهري', 'سنوي'];

bool isRtl = false;

///------------------Language---------------------------------
// List<String> countryList = [
//   'English',
//   'Spanish',
//   'Hindi',
//   'Arabic',
//   'France',
//   'Bengali',
//   'Turkish',
//   'Chinese',
//   'Japanese',
//   'Romanian',
//   'Germany',
//   'Vietnamese',
//   'Italian',
//   'Thai',
//   'Portuguese',
//   'Hebrew',
//   'Polish',
//   'Hungarian',
//   'Finland',
//   'Korean',
//   'Malay',
//   'Indonesian',
//   'Ukrainian',
//   'Bosnian',
//   'Greek',
//   'Dutch',
//   'Urdu',
//   'Sinhala',
//   'Persian',
//   'Serbian',
//   'Khmer',
//   'Lao',
//   'Russian',
//   'Kannada',
//   'Marathi',
//   'Tamil',
//   'Afrikaans',
//   'Czech',
//   'Swedish',
//   'Slovak',
//   'Swahili',
//   'Albanian',
//   'Danish',
//   'Azerbaijani',
//   'Kazakh',
//   'Croatian',
//   'Nepali'
// ];
// String selectedCountry = 'English';

void checkCurrentUserAndRestartApp() {
  final User? user = FirebaseAuth.instance.currentUser;
  if (user?.uid == null) {
    Restart.restartApp();
  }
}

List<TaxModel> getAllTaxFromCartList({required List<AddToCartModel> cart}) {
  List<TaxModel> data = [];
  for (var element in cart) {
    if (element.subTaxes.isNotEmpty) {
      for (var element1 in element.subTaxes) {
        if (!data.any((element2) => element2.name == element1.name)) {
          data.add(element1);
        }
      }
    }
  }
  return data;
}

//_______________________vat_percentage________________________________
String defaultVat = 'vat';

///______________User Role System____________
String constUserId = '';
bool isSubUser = false;
String constSubUserTitle = '';
String subUserEmail = '';
String searchItems = '';
String mainLoginPassword = '';
String mainLoginEmail = '';

UserRoleModel finalUserRoleModel = UserRoleModel(
  email: '',
  userTitle: '',
  databaseId: '',
  salePermission: false,
  partiesPermission: false,
  purchasePermission: false,
  productPermission: false,
  profileEditPermission: false,
  addExpensePermission: false,
  lossProfitPermission: false,
  dueListPermission: false,
  stockPermission: false,
  reportsPermission: false,
  salesListPermission: false,
  purchaseListPermission: false,
  // الصلاحيات الجديدة - الذكاء الاصطناعي
  aiChatPermission: false,
  aiAssistantPermission: false,
  voiceAssistantPermission: false,
  // الخزينة
  treasuryPermission: false,
  cashBoxPermission: false,
  // إدارة التوصيل
  deliveryManagementPermission: false,
  // الموارد البشرية
  hrmPermission: false,
  employeesPermission: false,
  designationPermission: false,
  salariesPermission: false,
  // التقارير المتقدمة
  financialReportsPermission: false,
  salesTargetsPermission: false,
  taxReportsPermission: false,
  // الإعدادات المتقدمة
  userLogsPermission: false,
  notificationsPermission: false,
  warrantyPermission: false,
  settingsPermission: false,
  userManagementPermission: false,
  // صلاحيات إضافية
  ledgerPermission: false,
  // فلاتر المياه - الصلاحية العامة
  waterFiltersPermission: false,
  // فلاتر المياه - صلاحيات مفصلة (للمستخدم الرئيسي كلها true)
  waterFilterProductsPermission: false,
  waterFilterCustomersPermission: false,
  waterFilterSystemsPermission: false,
  waterFilterMaintenancePermission: false,
  waterFilterInstallmentsPermission: false,
  waterFilterReportsPermission: false,
  // حالة التفعيل
  isActive: false,
);

Future<void> setUserDataOnLocalData({
  required String uid,
  required String subUserTitle,
  required bool isSubUser,
}) async {
  final prefs = await SharedPreferences.getInstance();
  await prefs.setString('userId', uid);
  await prefs.setString('subUserTitle', subUserTitle);
  await prefs.setBool('isSubUser', isSubUser);
}

Future<void> getUserDataFromLocal() async {
  final prefs = await SharedPreferences.getInstance();
  constUserId = prefs.getString('userId') ?? '';
  constSubUserTitle = prefs.getString('subUserTitle') ?? '';
  isSubUser = prefs.getBool('isSubUser') ?? false;

  // debugPrint('🔍 getUserDataFromLocal - isSubUser: $isSubUser');
  // debugPrint('🔍 getUserDataFromLocal - constUserId: $constUserId');

  String? data = prefs.getString("userPermission");
  if (data != null && isSubUser) {
    try {
      Map<String, dynamic> userData = jsonDecode(data);

      // تحديث البيانات إذا كانت الصلاحيات الجديدة غير موجودة
      bool needsUpdate = false;
      if (!userData.containsKey('waterFilterProductsPermission')) {
        // إذا كان لديه صلاحية فلاتر المياه العامة، أعطه كل الصلاحيات المفصلة
        bool hasWaterFiltersPermission =
            userData['waterFiltersPermission'] ?? false;
        userData['waterFilterProductsPermission'] = hasWaterFiltersPermission;
        userData['waterFilterCustomersPermission'] = hasWaterFiltersPermission;
        userData['waterFilterSystemsPermission'] = hasWaterFiltersPermission;
        userData['waterFilterMaintenancePermission'] =
            hasWaterFiltersPermission;
        userData['waterFilterInstallmentsPermission'] =
            hasWaterFiltersPermission;
        userData['waterFilterReportsPermission'] = hasWaterFiltersPermission;
        needsUpdate = true;
        debugPrint('🔄 تم إضافة الصلاحيات الجديدة للمستخدم');
      }

      if (needsUpdate) {
        // حفظ البيانات المحدثة
        await prefs.setString('userPermission', jsonEncode(userData));
        debugPrint('✅ تم تحديث وحفظ بيانات المستخدم');
      }

      finalUserRoleModel = UserRoleModel.fromJson(userData);
      // debugPrint('✅ تم تحميل بيانات الصلاحيات من SharedPreferences');
      // debugPrint('🔍 صلاحية المبيعات: ${finalUserRoleModel.salePermission}');
      // debugPrint('🔍 صلاحية الأطراف: ${finalUserRoleModel.partiesPermission}');
      // debugPrint(
      //     '💧 صلاحية فلاتر المياه: ${finalUserRoleModel.waterFiltersPermission}');
      // debugPrint(
      //     '🛒 صلاحية منتجات فلاتر المياه: ${finalUserRoleModel.waterFilterProductsPermission}');
    } catch (e) {
      // debugPrint('❌ خطأ في تحميل بيانات الصلاحيات: $e');
      // إبقاء القيم الافتراضية (false) في حالة الخطأ
    }
  } else if (isSubUser) {
    // debugPrint('⚠️ لا توجد بيانات صلاحيات محفوظة للمستخدم الفرعي');
    // إبقاء القيم الافتراضية (false)
  } else {
    // debugPrint('✅ مستخدم رئيسي - لا حاجة لتحميل الصلاحيات');
  }
}

String userPermissionErrorText = 'ليس لديك صلاحية للوصول لهذه الصفحة';

Future<bool> checkUserRolePermission({required String type}) async {
  await getUserDataFromLocal();
  bool permission = true;

  if (isSubUser) {
    switch (type) {
      // الصلاحيات الأساسية
      case 'sale':
        permission = finalUserRoleModel.salePermission;
        break;
      case 'salesList':
        permission = finalUserRoleModel.salesListPermission;
        break;
      case 'expense':
        permission = finalUserRoleModel.addExpensePermission;
        break;
      case 'dueList':
        permission = finalUserRoleModel.dueListPermission;
        break;
      case 'lossProfitScreen':
        permission = finalUserRoleModel.lossProfitPermission;
        break;
      case 'parties':
        permission = finalUserRoleModel.partiesPermission;
        break;
      case 'product':
        permission = finalUserRoleModel.productPermission;
        break;
      case 'purchaseList':
        permission = finalUserRoleModel.purchaseListPermission;
        break;
      case 'purchase':
        permission = finalUserRoleModel.purchasePermission;
        break;
      case 'reports':
        permission = finalUserRoleModel.reportsPermission;
        break;
      case 'stockList':
        permission = finalUserRoleModel.stockPermission;
        break;
      case 'profileEdit':
        permission = finalUserRoleModel.profileEditPermission;
        break;
      case 'ledger':
        permission = finalUserRoleModel.ledgerPermission;
        break;

      // الصلاحيات الجديدة - الذكاء الاصطناعي
      case 'chat':
      case 'aiChat':
      case 'Chat':
      case 'AI Chat':
        permission = finalUserRoleModel.aiChatPermission;
        break;
      case 'aiAssistant':
      case 'AI Assistant':
        permission = finalUserRoleModel.aiAssistantPermission;
        break;
      case 'voiceAssistant':
      case 'Voice Assistant':
        permission = finalUserRoleModel.voiceAssistantPermission;
        break;

      // الخزينة
      case 'treasury':
      case 'Treasury':
      case 'cashBox':
      case 'CashBox':
      case 'Cash Box':
        permission = finalUserRoleModel.treasuryPermission;
        break;

      // إدارة التوصيل
      case 'deliveryManagement':
      case 'DeliveryManagement':
      case 'Delivery Management':
        permission = finalUserRoleModel.deliveryManagementPermission;
        break;

      // الموارد البشرية
      case 'hrm':
      case 'HRM':
        permission = finalUserRoleModel.hrmPermission;
        break;
      case 'employee':
      case 'employees':
      case 'Employee':
      case 'Employees':
        permission = finalUserRoleModel.employeesPermission;
        break;
      case 'designation':
      case 'Designation':
        permission = finalUserRoleModel.designationPermission;
        break;
      case 'salaries':
      case 'Salaries':
        permission = finalUserRoleModel.salariesPermission;
        break;

      // التقارير المتقدمة
      case 'financialReports':
      case 'Financial Reports':
        permission = finalUserRoleModel.financialReportsPermission;
        break;
      case 'salesTargets':
      case 'Sales Targets':
        permission = finalUserRoleModel.salesTargetsPermission;
        break;
      case 'taxReports':
      case 'Tax Reports':
        permission = finalUserRoleModel.taxReportsPermission;
        break;

      // الإعدادات المتقدمة
      case 'userLogs':
      case 'User Logs':
        permission = finalUserRoleModel.userLogsPermission;
        break;
      case 'notifications':
      case 'Notifications':
        permission = finalUserRoleModel.notificationsPermission;
        break;
      case 'warranty':
      case 'Warranty':
        permission = finalUserRoleModel.warrantyPermission;
        break;
      case 'settings':
      case 'Settings':
        permission = finalUserRoleModel.settingsPermission;
        break;
      case 'userManagement':
      case 'User Management':
      case 'User Roles':
        permission = finalUserRoleModel.userManagementPermission;
        break;
      case 'waterFilters':
      case 'WaterFilters':
      case 'Water Filters':
      case 'فلاتر المياه':
        permission = finalUserRoleModel.waterFiltersPermission;
        break;

      // فلاتر المياه - صلاحيات مفصلة
      case 'waterFilterProducts':
      case 'WaterFilterProducts':
      case 'Water Filter Products':
      case 'منتجات فلاتر المياه':
        permission = finalUserRoleModel.waterFilterProductsPermission;
        break;
      case 'waterFilterCustomers':
      case 'WaterFilterCustomers':
      case 'Water Filter Customers':
      case 'عملاء فلاتر المياه':
        permission = finalUserRoleModel.waterFilterCustomersPermission;
        break;
      case 'waterFilterSystems':
      case 'WaterFilterSystems':
      case 'Water Filter Systems':
      case 'أنظمة فلاتر المياه':
        permission = finalUserRoleModel.waterFilterSystemsPermission;
        break;
      case 'waterFilterMaintenance':
      case 'WaterFilterMaintenance':
      case 'Water Filter Maintenance':
      case 'صيانة فلاتر المياه':
        permission = finalUserRoleModel.waterFilterMaintenancePermission;
        break;
      case 'waterFilterInstallments':
      case 'WaterFilterInstallments':
      case 'Water Filter Installments':
      case 'أقساط فلاتر المياه':
        permission = finalUserRoleModel.waterFilterInstallmentsPermission;
        break;
      case 'waterFilterReports':
      case 'WaterFilterReports':
      case 'Water Filter Reports':
      case 'تقارير فلاتر المياه':
        permission = finalUserRoleModel.waterFilterReportsPermission;
        break;

      default:
        permission = true;
        break;
    }
    if (permission) {
      return permission;
    } else {
      EasyLoading.showError(userPermissionErrorText);
      return permission;
    }
  } else {
    // للمستخدم الرئيسي، فلاتر المياه تحتاج تفعيل صريح
    switch (type) {
      case 'waterFilters':
      case 'WaterFilters':
      case 'Water Filters':
      case 'فلاتر المياه':
        return finalUserRoleModel.waterFiltersPermission;
      case 'waterFilterProducts':
      case 'WaterFilterProducts':
      case 'Water Filter Products':
      case 'منتجات فلاتر المياه':
        return finalUserRoleModel.waterFilterProductsPermission;
      case 'waterFilterCustomers':
      case 'WaterFilterCustomers':
      case 'Water Filter Customers':
      case 'عملاء فلاتر المياه':
        return finalUserRoleModel.waterFilterCustomersPermission;
      case 'waterFilterSystems':
      case 'WaterFilterSystems':
      case 'Water Filter Systems':
      case 'أنظمة فلاتر المياه':
        return finalUserRoleModel.waterFilterSystemsPermission;
      case 'waterFilterMaintenance':
      case 'WaterFilterMaintenance':
      case 'Water Filter Maintenance':
      case 'صيانة فلاتر المياه':
        return finalUserRoleModel.waterFilterMaintenancePermission;
      case 'waterFilterInstallments':
      case 'WaterFilterInstallments':
      case 'Water Filter Installments':
      case 'أقساط فلاتر المياه':
        return finalUserRoleModel.waterFilterInstallmentsPermission;
      case 'waterFilterReports':
      case 'WaterFilterReports':
      case 'Water Filter Reports':
      case 'تقارير فلاتر المياه':
        return finalUserRoleModel.waterFilterReportsPermission;
      default:
        return true;
    }
  }
}
