// ignore_for_file: prefer_const_constructors, deprecated_member_use, use_build_context_synchronously, duplicate_ignore, unused_local_variable, unused_import, use_super_parameters, library_private_types_in_public_api, avoid_print, unnecessary_type_check

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../Models/item_report_model.dart';
import '../Providers/item_report_provider.dart';
import '../Services/export_service.dart';
import 'package:open_file/open_file.dart';

class ItemReportScreen extends ConsumerStatefulWidget {
  const ItemReportScreen({super.key});

  @override
  ConsumerState<ItemReportScreen> createState() => _ItemReportScreenState();
}

class _ItemReportScreenState extends ConsumerState<ItemReportScreen> {
  // متغيرات التاريخ
  DateTime fromDate = DateTime(2024, 12, 1);
  DateTime toDate = DateTime.now();

  // متغيرات التحكم في النص
  final TextEditingController fromDateController = TextEditingController();
  final TextEditingController toDateController = TextEditingController();
  final TextEditingController searchController = TextEditingController(); // إضافة متحكم البحث
  String searchQuery = ''; // متغير لتخزين نص البحث

  @override
  void initState() {
    super.initState();
    print('تاريخ البداية: $fromDate');
    print('تاريخ النهاية: $toDate');
    
    // تحديث التواريخ في حقول النص
    fromDateController.text = DateFormat.yMMMd().format(fromDate);
    toDateController.text = DateFormat.yMMMd().format(toDate);
    
    // جلب التقارير
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(itemReportProvider.notifier).getItemReports(fromDate, toDate);
    });
  }

  @override
  void dispose() {
    fromDateController.dispose();
    toDateController.dispose();
    searchController.dispose(); // تنظيف متحكم البحث
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final reportsAsync = ref.watch(itemReportProvider);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('تقارير الأصناف'),
        centerTitle: true,
        actions: [
          // زر تصدير التقرير
          IconButton(
            icon: const Icon(Icons.download),
            onPressed: () async {
              final reports = ref.read(itemReportProvider).value;
              if (reports != null && reports.isNotEmpty) {
                final filePath = await ExportService.exportToExcel(reports);
                if (filePath != null) {
                  // فتح الملف بعد التصدير
                  await OpenFile.open(filePath);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('تم تصدير التقرير بنجاح')),
                  );
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('حدث خطأ أثناء تصدير التقرير'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('لا توجد بيانات للتصدير'),
                    backgroundColor: Colors.orange,
                  ),
                );
              }
            },
            tooltip: 'تصدير التقرير',
          ),
        ],
      ),
      body: Column(
        children: [
          // حقول اختيار التاريخ
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: fromDateController,
                        decoration: const InputDecoration(
                          labelText: 'من تاريخ',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () async {
                          final picked = await showDatePicker(
                            context: context,
                            initialDate: fromDate,
                            firstDate: DateTime(2020),
                            lastDate: DateTime.now(),
                          );
                          if (picked != null) {
                            setState(() {
                              fromDate = picked;
                              fromDateController.text = DateFormat.yMMMd().format(picked);
                            });
                            ref.read(itemReportProvider.notifier).getItemReports(fromDate, toDate);
                          }
                        },
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: TextFormField(
                        controller: toDateController,
                        decoration: const InputDecoration(
                          labelText: 'إلى تاريخ',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: () async {
                          final picked = await showDatePicker(
                            context: context,
                            initialDate: toDate,
                            firstDate: DateTime(2020),
                            lastDate: DateTime.now(),
                          );
                          if (picked != null) {
                            setState(() {
                              toDate = picked;
                              toDateController.text = DateFormat.yMMMd().format(picked);
                            });
                            ref.read(itemReportProvider.notifier).getItemReports(fromDate, toDate);
                          }
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                // حقل البحث
                TextFormField(
                  controller: searchController,
                  decoration: const InputDecoration(
                    labelText: 'بحث عن صنف',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.search),
                  ),
                  onChanged: (value) {
                    setState(() {
                      searchQuery = value;
                    });
                  },
                ),
              ],
            ),
          ),
          
          // عرض التقارير
          Expanded(
            child: reportsAsync.when(
              data: (reports) {
                // فلترة التقارير حسب البحث
                final filteredReports = reports.where((report) =>
                  report.productName.toLowerCase().contains(searchQuery.toLowerCase())
                ).toList();
                
                if (filteredReports.isEmpty) {
                  return const Center(
                    child: Text(
                      'لا توجد بيانات في هذه الفترة',
                      style: TextStyle(fontSize: 18),
                    ),
                  );
                }
                
                return ListView.builder(
                  itemCount: filteredReports.length,
                  itemBuilder: (context, index) {
                    final report = filteredReports[index];
                    return Card(
                      elevation: 4,
                      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      child: ExpansionTile(
                        title: Row(
                          children: [
                            Icon(
                              Icons.inventory_2,
                              color: Theme.of(context).primaryColor,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                report.productName,
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: report.currentStock > 0 ? Colors.green.shade100 : Colors.red.shade100,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                'المخزون: ${report.currentStock.toStringAsFixed(2)}',
                                style: TextStyle(
                                  color: report.currentStock > 0 ? Colors.green.shade900 : Colors.red.shade900,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              children: [
                                // معلومات المشتريات
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: Colors.blue.shade50,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      const Text(
                                        'معلومات المشتريات',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 16,
                                        ),
                                      ),
                                      const Divider(),
                                      Text('إجمالي المشتريات: ${report.totalPurchases.toStringAsFixed(2)}'),
                                      Text('متوسط سعر الشراء: ${report.averagePurchasePrice.toStringAsFixed(2)}'),
                                      Text('آخر شراء: ${DateFormat.yMMMd().format(report.lastPurchaseDate)}'),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 8),
                                // معلومات المبيعات
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: Colors.green.shade50,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      const Text(
                                        'معلومات المبيعات',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 16,
                                        ),
                                      ),
                                      const Divider(),
                                      Text('إجمالي المبيعات: ${report.totalSales.toStringAsFixed(2)}'),
                                      Text('متوسط سعر البيع: ${report.averageSalePrice.toStringAsFixed(2)}'),
                                      Text('آخر بيع: ${DateFormat.yMMMd().format(report.lastSaleDate)}'),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 8),
                                // معلومات الربح
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: Colors.purple.shade50,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      const Text(
                                        'إجمالي الربح:',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 16,
                                        ),
                                      ),
                                      Text(
                                        '${report.profit.toStringAsFixed(2)}',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 18,
                                          color: report.profit >= 0 ? Colors.green.shade700 : Colors.red.shade700,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Center(
                child: Text(
                  'حدث خطأ: $error',
                  style: const TextStyle(color: Colors.red),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
