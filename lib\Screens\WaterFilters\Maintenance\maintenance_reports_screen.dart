import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/model/water_filter_models.dart';
import 'package:mobile_pos/services/maintenance_service.dart';
import 'package:mobile_pos/services/water_filter_service.dart';
import 'package:fl_chart/fl_chart.dart';

class MaintenanceReportsScreen extends StatefulWidget {
  const MaintenanceReportsScreen({super.key});

  @override
  State<MaintenanceReportsScreen> createState() =>
      _MaintenanceReportsScreenState();
}

class _MaintenanceReportsScreenState extends State<MaintenanceReportsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // بيانات التقارير
  Map<String, dynamic> _overviewStats = {};
  List<MaintenanceSchedule> _allSchedules = [];
  List<MaintenanceRecord> _allRecords = [];
  List<WaterFilterSystem> _allSystems = [];

  // فلاتر التقارير
  final DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  final DateTime _endDate = DateTime.now();
  MaintenanceType? _selectedType;
  ScheduleStatus? _selectedStatus;

  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadReportsData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadReportsData() async {
    setState(() => _isLoading = true);

    try {
      // تحميل جميع البيانات
      final schedules = await MaintenanceService.getAllSchedules();
      final systems = await _loadAllSystems();
      final records = await _loadAllRecords();

      setState(() {
        _allSchedules = schedules;
        _allSystems = systems;
        _allRecords = records;
        _calculateOverviewStats();
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات التقارير: $e');
      setState(() => _isLoading = false);
    }
  }

  Future<List<WaterFilterSystem>> _loadAllSystems() async {
    try {
      final data = await WaterFilterService.getData('Systems');
      final systems = <WaterFilterSystem>[];

      data.forEach((key, value) {
        try {
          final system = WaterFilterSystem.fromJson(
            Map<String, dynamic>.from(value),
          );
          systems.add(system);
        } catch (e) {
          debugPrint('خطأ في معالجة نظام: $e');
        }
      });

      return systems;
    } catch (e) {
      debugPrint('خطأ في تحميل الأنظمة: $e');
      return [];
    }
  }

  Future<List<MaintenanceRecord>> _loadAllRecords() async {
    try {
      final data =
          await WaterFilterService.getData('WaterFilters/Maintenance/Records');
      final records = <MaintenanceRecord>[];

      data.forEach((key, value) {
        try {
          final record = MaintenanceRecord.fromJson(
            Map<String, dynamic>.from(value),
          );
          records.add(record);
        } catch (e) {
          debugPrint('خطأ في معالجة سجل صيانة: $e');
        }
      });

      return records;
    } catch (e) {
      debugPrint('خطأ في تحميل سجلات الصيانة: $e');
      return [];
    }
  }

  void _calculateOverviewStats() {
    final now = DateTime.now();
    final thisMonth = DateTime(now.year, now.month, 1);
    final lastMonth = DateTime(now.year, now.month - 1, 1);

    // إحصائيات الجدولات
    final totalSchedules = _allSchedules.length;
    final completedSchedules =
        _allSchedules.where((s) => s.status == ScheduleStatus.completed).length;
    final overdueSchedules = _allSchedules.where((s) => s.isOverdue).length;
    final thisMonthSchedules = _allSchedules
        .where((s) =>
            s.scheduledDate.isAfter(thisMonth) && s.scheduledDate.isBefore(now))
        .length;

    // إحصائيات الأنظمة
    final totalSystems = _allSystems.length;
    final activeSystems =
        _allSystems.where((s) => s.status == FilterSystemStatus.active).length;
    final systemsNeedMaintenance =
        _allSystems.where((s) => s.isMaintenanceOverdue).length;

    // إحصائيات التكاليف
    final totalMaintenanceCost =
        _allRecords.fold(0.0, (sum, record) => sum + record.cost);
    final thisMonthCost = _allRecords
        .where(
            (r) => r.startTime.isAfter(thisMonth) && r.startTime.isBefore(now))
        .fold(0.0, (sum, record) => sum + record.cost);

    // معدل الإنجاز
    final completionRate =
        totalSchedules > 0 ? (completedSchedules / totalSchedules * 100) : 0.0;

    setState(() {
      _overviewStats = {
        'totalSchedules': totalSchedules,
        'completedSchedules': completedSchedules,
        'overdueSchedules': overdueSchedules,
        'thisMonthSchedules': thisMonthSchedules,
        'totalSystems': totalSystems,
        'activeSystems': activeSystems,
        'systemsNeedMaintenance': systemsNeedMaintenance,
        'totalMaintenanceCost': totalMaintenanceCost,
        'thisMonthCost': thisMonthCost,
        'completionRate': completionRate,
      };
    });
  }

  List<MaintenanceSchedule> get _filteredSchedules {
    return _allSchedules.where((schedule) {
      // فلتر التاريخ
      if (schedule.scheduledDate.isBefore(_startDate) ||
          schedule.scheduledDate.isAfter(_endDate)) {
        return false;
      }

      // فلتر النوع
      if (_selectedType != null && schedule.type != _selectedType) {
        return false;
      }

      // فلتر الحالة
      if (_selectedStatus != null && schedule.status != _selectedStatus) {
        return false;
      }

      return true;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        title: Text(
          'تقارير الصيانة',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list, color: Colors.white),
            onPressed: _showFilterDialog,
          ),
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: _loadReportsData,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          labelStyle: GoogleFonts.cairo(fontWeight: FontWeight.bold),
          tabs: const [
            Tab(text: 'نظرة عامة', icon: Icon(Icons.dashboard)),
            Tab(text: 'الأداء', icon: Icon(Icons.trending_up)),
            Tab(text: 'التكاليف', icon: Icon(Icons.attach_money)),
            Tab(text: 'التفاصيل', icon: Icon(Icons.list_alt)),
          ],
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : TabBarView(
                controller: _tabController,
                children: [
                  _buildOverviewTab(),
                  _buildPerformanceTab(),
                  _buildCostTab(),
                  _buildDetailsTab(),
                ],
              ),
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // إحصائيات سريعة
          _buildSectionTitle('الإحصائيات العامة'),
          const SizedBox(height: 16),

          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.2,
            children: [
              _buildStatCard(
                title: 'إجمالي الجدولات',
                value: '${_overviewStats['totalSchedules'] ?? 0}',
                icon: Icons.schedule,
                color: Colors.blue,
              ),
              _buildStatCard(
                title: 'مكتملة',
                value: '${_overviewStats['completedSchedules'] ?? 0}',
                icon: Icons.check_circle,
                color: Colors.green,
              ),
              _buildStatCard(
                title: 'متأخرة',
                value: '${_overviewStats['overdueSchedules'] ?? 0}',
                icon: Icons.warning,
                color: Colors.red,
              ),
              _buildStatCard(
                title: 'هذا الشهر',
                value: '${_overviewStats['thisMonthSchedules'] ?? 0}',
                icon: Icons.calendar_today,
                color: Colors.purple,
              ),
            ],
          ),

          const SizedBox(height: 24),

          // معدل الإنجاز
          _buildSectionTitle('معدل الإنجاز'),
          const SizedBox(height: 16),

          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.shade200,
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'معدل إنجاز الصيانة',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '${(_overviewStats['completionRate'] ?? 0).toStringAsFixed(1)}%',
                      style: GoogleFonts.cairo(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                LinearProgressIndicator(
                  value: (_overviewStats['completionRate'] ?? 0) / 100,
                  backgroundColor: Colors.grey.shade200,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
                  minHeight: 8,
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // حالة الأنظمة
          _buildSectionTitle('حالة الأنظمة'),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: _buildSystemStatusCard(
                  title: 'أنظمة نشطة',
                  value: '${_overviewStats['activeSystems'] ?? 0}',
                  total: '${_overviewStats['totalSystems'] ?? 0}',
                  color: Colors.green,
                  icon: Icons.check_circle,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSystemStatusCard(
                  title: 'تحتاج صيانة',
                  value: '${_overviewStats['systemsNeedMaintenance'] ?? 0}',
                  total: '${_overviewStats['totalSystems'] ?? 0}',
                  color: Colors.orange,
                  icon: Icons.build,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('أداء الصيانة'),
          const SizedBox(height: 16),

          // رسم بياني لأنواع الصيانة
          Container(
            height: 300,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.shade200,
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'توزيع أنواع الصيانة',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: _buildMaintenanceTypePieChart(),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // إحصائيات الأداء
          _buildSectionTitle('إحصائيات الأداء'),
          const SizedBox(height: 16),

          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.5,
            children: [
              _buildPerformanceCard(
                title: 'متوسط وقت الإنجاز',
                value: _calculateAverageCompletionTime(),
                icon: Icons.timer,
                color: Colors.blue,
              ),
              _buildPerformanceCard(
                title: 'معدل النجاح',
                value:
                    '${(_overviewStats['completionRate'] ?? 0).toStringAsFixed(1)}%',
                icon: Icons.trending_up,
                color: Colors.green,
              ),
              _buildPerformanceCard(
                title: 'صيانة طارئة',
                value:
                    '${_getMaintenanceCountByType(MaintenanceType.emergency)}',
                icon: Icons.warning,
                color: Colors.red,
              ),
              _buildPerformanceCard(
                title: 'صيانة دورية',
                value: '${_getMaintenanceCountByType(MaintenanceType.routine)}',
                icon: Icons.schedule,
                color: Colors.purple,
              ),
            ],
          ),

          const SizedBox(height: 20),

          // اتجاهات الصيانة
          _buildSectionTitle('اتجاهات الصيانة'),
          const SizedBox(height: 16),

          Container(
            height: 250,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.shade200,
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'الصيانة خلال الأشهر الماضية',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: _buildMaintenanceTrendChart(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCostTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('تحليل التكاليف'),
          const SizedBox(height: 16),

          // إجمالي التكاليف
          Row(
            children: [
              Expanded(
                child: _buildCostCard(
                  title: 'إجمالي التكاليف',
                  value:
                      '${(_overviewStats['totalMaintenanceCost'] ?? 0).toStringAsFixed(2)} ج.م',
                  icon: Icons.attach_money,
                  color: Colors.green,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildCostCard(
                  title: 'تكاليف هذا الشهر',
                  value:
                      '${(_overviewStats['thisMonthCost'] ?? 0).toStringAsFixed(2)} ج.م',
                  icon: Icons.calendar_today,
                  color: Colors.blue,
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // متوسط التكاليف
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.5,
            children: [
              _buildCostCard(
                title: 'متوسط تكلفة الصيانة',
                value: '${_calculateAverageCost().toStringAsFixed(2)} ج.م',
                icon: Icons.calculate,
                color: Colors.orange,
              ),
              _buildCostCard(
                title: 'أعلى تكلفة',
                value: '${_getHighestCost().toStringAsFixed(2)} ج.م',
                icon: Icons.trending_up,
                color: Colors.red,
              ),
              _buildCostCard(
                title: 'أقل تكلفة',
                value: '${_getLowestCost().toStringAsFixed(2)} ج.م',
                icon: Icons.trending_down,
                color: Colors.teal,
              ),
              _buildCostCard(
                title: 'عدد الصيانات',
                value: '${_allRecords.length}',
                icon: Icons.build,
                color: Colors.purple,
              ),
            ],
          ),

          const SizedBox(height: 20),

          // رسم بياني للتكاليف
          Container(
            height: 300,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.shade200,
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'تطور التكاليف الشهرية',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: _buildCostTrendChart(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailsTab() {
    final filteredSchedules = _filteredSchedules;

    return Column(
      children: [
        // فلاتر سريعة
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  'عرض ${filteredSchedules.length} من ${_allSchedules.length} جدولة',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
              ),
              TextButton.icon(
                onPressed: _showFilterDialog,
                icon: const Icon(Icons.filter_list, size: 16),
                label: Text(
                  'فلترة',
                  style: GoogleFonts.cairo(fontSize: 12),
                ),
              ),
            ],
          ),
        ),

        // قائمة الجدولات المفصلة
        Expanded(
          child: filteredSchedules.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.search_off,
                        size: 80,
                        color: Colors.grey.shade400,
                      ),
                      const SizedBox(height: 20),
                      Text(
                        'لا توجد جدولات تطابق الفلاتر',
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: filteredSchedules.length,
                  itemBuilder: (context, index) {
                    final schedule = filteredSchedules[index];
                    return _buildDetailedScheduleCard(schedule);
                  },
                ),
        ),
      ],
    );
  }

  // Helper Methods
  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: GoogleFonts.cairo(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: kMainColor,
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 12),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSystemStatusCard({
    required String title,
    required String value,
    required String total,
    required Color color,
    required IconData icon,
  }) {
    final percentage = int.tryParse(total) != null && int.parse(total) > 0
        ? (int.parse(value) / int.parse(total) * 100)
        : 0.0;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(width: 8),
              Text(
                title,
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            '$value من $total',
            style: GoogleFonts.cairo(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: percentage / 100,
            backgroundColor: Colors.grey.shade200,
            valueColor: AlwaysStoppedAnimation<Color>(color),
            minHeight: 6,
          ),
          const SizedBox(height: 4),
          Text(
            '${percentage.toStringAsFixed(1)}%',
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 28),
          const SizedBox(height: 8),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 11,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCostCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 28),
          const SizedBox(height: 8),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 11,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDetailedScheduleCard(MaintenanceSchedule schedule) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: schedule.status.color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: schedule.type.color.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        schedule.type.icon,
                        color: schedule.type.color,
                        size: 16,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      schedule.type.arabicName,
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: schedule.type.color,
                      ),
                    ),
                  ],
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: schedule.status.color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    schedule.status.arabicName,
                    style: GoogleFonts.cairo(
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      color: schedule.status.color,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              schedule.description,
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: Colors.grey.shade700,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.calendar_today,
                    size: 14, color: Colors.grey.shade600),
                const SizedBox(width: 4),
                Text(
                  '${schedule.scheduledDate.day}/${schedule.scheduledDate.month}/${schedule.scheduledDate.year}',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
                const SizedBox(width: 16),
                Icon(Icons.access_time, size: 14, color: Colors.grey.shade600),
                const SizedBox(width: 4),
                Text(
                  schedule.scheduledTime.format(context),
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Chart Widgets (Simplified)
  Widget _buildMaintenanceTypePieChart() {
    return Center(
      child: Text(
        'رسم بياني دائري لأنواع الصيانة\n(يتطلب مكتبة fl_chart)',
        style: GoogleFonts.cairo(
          color: Colors.grey.shade600,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildMaintenanceTrendChart() {
    return Center(
      child: Text(
        'رسم بياني خطي لاتجاهات الصيانة\n(يتطلب مكتبة fl_chart)',
        style: GoogleFonts.cairo(
          color: Colors.grey.shade600,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildCostTrendChart() {
    return Center(
      child: Text(
        'رسم بياني لتطور التكاليف\n(يتطلب مكتبة fl_chart)',
        style: GoogleFonts.cairo(
          color: Colors.grey.shade600,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  // Calculation Methods
  String _calculateAverageCompletionTime() {
    if (_allRecords.isEmpty) return '0 ساعة';

    final totalHours = _allRecords
        .where((record) => record.workDuration != null)
        .fold(0, (sum, record) => sum + record.workDuration!.inHours);

    final averageHours = totalHours / _allRecords.length;
    return '${averageHours.toStringAsFixed(1)} ساعة';
  }

  int _getMaintenanceCountByType(MaintenanceType type) {
    return _allSchedules.where((schedule) => schedule.type == type).length;
  }

  double _calculateAverageCost() {
    if (_allRecords.isEmpty) return 0.0;

    final totalCost = _allRecords.fold(0.0, (sum, record) => sum + record.cost);
    return totalCost / _allRecords.length;
  }

  double _getHighestCost() {
    if (_allRecords.isEmpty) return 0.0;

    return _allRecords
        .map((record) => record.cost)
        .reduce((a, b) => a > b ? a : b);
  }

  double _getLowestCost() {
    if (_allRecords.isEmpty) return 0.0;

    return _allRecords
        .map((record) => record.cost)
        .reduce((a, b) => a < b ? a : b);
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'فلترة التقارير',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'فلاتر التقارير قيد التطوير',
              style: GoogleFonts.cairo(),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'إغلاق',
              style: GoogleFonts.cairo(),
            ),
          ),
        ],
      ),
    );
  }
}
