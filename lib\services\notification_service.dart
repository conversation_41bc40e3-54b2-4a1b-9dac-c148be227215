import 'dart:convert';
import 'dart:io';
import 'package:firebase_database/firebase_database.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

/// خدمة إدارة الإشعارات
class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;

  // مفتاح FCM Server لإرسال الإشعارات
  static const String _fcmServerKey = 'AIzaSyAGoBaxRPDVn9q5xXopc5LiDRfguk0LIik';

  /// تهيئة خدمة الإشعارات
  Future<void> initialize() async {
    // طلب أذونات الإشعارات
    await _requestPermissions();

    // تهيئة إشعارات Firebase
    await _initializeFirebaseMessaging();
  }

  /// طلب أذونات الإشعارات
  Future<void> _requestPermissions() async {
    if (Platform.isIOS) {
      await _firebaseMessaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
      );
    }
  }

  /// تهيئة إشعارات Firebase
  Future<void> _initializeFirebaseMessaging() async {
    // الاستماع للإشعارات عندما يكون التطبيق في الخلفية
    FirebaseMessaging.onMessageOpenedApp.listen(_handleMessageOpenedApp);

    // الاستماع للإشعارات عندما يكون التطبيق مفتوحًا
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // التعامل مع الإشعارات التي تم النقر عليها عندما كان التطبيق مغلقًا
    final RemoteMessage? initialMessage =
        await _firebaseMessaging.getInitialMessage();
    if (initialMessage != null) {
      _handleMessageOpenedApp(initialMessage);
    }
  }

  /// التعامل مع الإشعارات في الواجهة الأمامية
  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    debugPrint(
        'تم استلام إشعار في الواجهة الأمامية: ${message.notification?.title}');

    // لعرض الإشعارات المحلية عندما يكون التطبيق في الواجهة الأمامية
    // يجب إعادة تفعيل مكتبة flutter_local_notifications وإضافة الكود التالي:
    /*
    if (message.notification != null) {
      await _showLocalNotification(
        id: message.hashCode,
        title: message.notification!.title ?? 'إشعار جديد',
        body: message.notification!.body ?? '',
        payload: jsonEncode(message.data),
      );
    }
    */
  }

  /// التعامل مع النقر على الإشعار لفتح التطبيق
  void _handleMessageOpenedApp(RemoteMessage message) {
    debugPrint('تم النقر على إشعار: ${message.notification?.title}');

    // هنا يمكن إضافة منطق للتنقل إلى شاشة معينة بناءً على بيانات الإشعار
    final data = message.data;
    if (data.containsKey('screen')) {
      // التنقل إلى الشاشة المحددة باستخدام data['screen']
      // مثال: navigatorKey.currentState?.pushNamed(data['screen']);
    }
  }

  /// الحصول على رمز الجهاز
  Future<String?> getDeviceToken() async {
    return await _firebaseMessaging.getToken();
  }

  /// تخزين رمز الجهاز للمستخدم في قاعدة البيانات
  Future<void> saveUserDeviceToken(String userId) async {
    try {
      final token = await getDeviceToken();
      if (token != null) {
        // تخزين رمز الجهاز في قاعدة البيانات
        final databaseRef = FirebaseDatabase.instance.ref();
        await databaseRef.child('UserTokens').child(userId).set(token);
        debugPrint('تم تخزين رمز الجهاز للمستخدم: $userId');

        // الاشتراك في موضوع الدردشة العامة
        await subscribeToTopic('public_chat');
      }
    } catch (e) {
      debugPrint('خطأ في تخزين رمز الجهاز: $e');
    }
  }

  /// إرسال إشعار إلى مستخدم محدد
  Future<bool> sendNotificationToUser({
    required String userToken,
    required String title,
    required String body,
    Map<String, dynamic>? data,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('https://fcm.googleapis.com/fcm/send'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'key=$_fcmServerKey',
        },
        body: jsonEncode({
          'to': userToken,
          'notification': {
            'title': title,
            'body': body,
            'sound': 'default',
          },
          'data': data ?? {},
          'priority': 'high',
        }),
      );

      debugPrint('استجابة إرسال الإشعار: ${response.statusCode}');
      debugPrint('محتوى الاستجابة: ${response.body}');

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('خطأ في إرسال الإشعار: $e');
      return false;
    }
  }

  /// إرسال إشعار رسالة دردشة
  Future<bool> sendChatMessageNotification({
    required String userToken,
    required String messageText,
    required String messageType,
    required String senderId,
    required String messageId,
    required String senderName,
  }) async {
    return await sendNotificationToUser(
      userToken: userToken,
      title: 'رسالة جديدة من $senderName',
      body: messageType == 'text' ? messageText : 'رسالة صوتية',
      data: {
        'screen': 'Chat',
        'senderId': senderId,
        'messageId': messageId,
        'messageType': messageType,
      },
    );
  }

  /// إرسال إشعار إلى موضوع محدد
  Future<bool> sendNotificationToTopic({
    required String topic,
    required String title,
    required String body,
    Map<String, dynamic>? data,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('https://fcm.googleapis.com/fcm/send'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'key=$_fcmServerKey',
        },
        body: jsonEncode({
          'to': '/topics/$topic',
          'notification': {
            'title': title,
            'body': body,
            'sound': 'default',
          },
          'data': data ?? {},
          'priority': 'high',
        }),
      );

      debugPrint('استجابة إرسال الإشعار للموضوع: ${response.statusCode}');
      return response.statusCode == 200;
    } catch (e) {
      debugPrint('خطأ في إرسال الإشعار للموضوع: $e');
      return false;
    }
  }

  /// الاشتراك في موضوع
  Future<void> subscribeToTopic(String topic) async {
    await _firebaseMessaging.subscribeToTopic(topic);
    debugPrint('تم الاشتراك في الموضوع: $topic');
  }

  /// إلغاء الاشتراك من موضوع
  Future<void> unsubscribeFromTopic(String topic) async {
    await _firebaseMessaging.unsubscribeFromTopic(topic);
    debugPrint('تم إلغاء الاشتراك من الموضوع: $topic');
  }

  /// اختبار إرسال إشعار للجهاز الحالي
  Future<bool> sendTestNotification() async {
    try {
      final token = await getDeviceToken();
      if (token == null) {
        debugPrint('لم يتم العثور على رمز الجهاز');
        return false;
      }

      debugPrint('رمز الجهاز: $token');

      final result = await sendNotificationToUser(
        userToken: token,
        title: 'إشعار اختبار',
        body: 'هذا إشعار اختبار للتحقق من عمل الإشعارات',
        data: {
          'screen': 'Home',
          'testTime': DateTime.now().millisecondsSinceEpoch.toString(),
        },
      );

      return result;
    } catch (e) {
      debugPrint('خطأ في إرسال إشعار الاختبار: $e');
      return false;
    }
  }
}
