// import 'package:flutter/material.dart';
// import 'package:flutter_easyloading/flutter_easyloading.dart';
// import 'package:google_fonts/google_fonts.dart';
// import 'package:mobile_pos/constant.dart';
// import 'package:mobile_pos/services/back4app_notification_service.dart';
// import 'package:firebase_auth/firebase_auth.dart';

// class NotificationTestScreen extends StatefulWidget {
//   const NotificationTestScreen({super.key});

//   @override
//   State<NotificationTestScreen> createState() => _NotificationTestScreenState();
// }

// class _NotificationTestScreenState extends State<NotificationTestScreen> {
//   final TextEditingController _titleController = TextEditingController();
//   final TextEditingController _messageController = TextEditingController();
//   final TextEditingController _recipientIdController = TextEditingController();
//   final Back4AppNotificationService _notificationService =
//       Back4AppNotificationService();
//   bool _isSending = false;

//   @override
//   void initState() {
//     super.initState();
//     // تعيين معرف المستخدم الحالي كمستلم افتراضي
//     _recipientIdController.text = FirebaseAuth.instance.currentUser?.uid ?? '';
//   }

//   @override
//   void dispose() {
//     _titleController.dispose();
//     _messageController.dispose();
//     _recipientIdController.dispose();
//     super.dispose();
//   }

//   Future<void> _sendNotification() async {
//     // التحقق من ملء جميع الحقول
//     if (_titleController.text.isEmpty ||
//         _messageController.text.isEmpty ||
//         _recipientIdController.text.isEmpty) {
//       ScaffoldMessenger.of(context).showSnackBar(
//         const SnackBar(content: Text('يرجى ملء جميع الحقول')),
//       );
//       return;
//     }

//     // حفظ القيم قبل العملية غير المتزامنة
//     final String title = _titleController.text;
//     final String message = _messageController.text;
//     final String recipientId = _recipientIdController.text;

//     // تحديث حالة الإرسال
//     setState(() {
//       _isSending = true;
//     });

//     EasyLoading.show(status: 'جاري إرسال الإشعار...');

//     try {
//       final success = await _notificationService.createNotification(
//         title: title,
//         message: message,
//         type: 'test',
//         recipientId: recipientId,
//         data: {
//           'screen': 'NotificationTest',
//           'timestamp': DateTime.now().millisecondsSinceEpoch,
//         },
//       );

//       // التحقق من أن الـ Widget لا يزال مثبتًا في شجرة الـ Widget
//       if (!mounted) return;

//       EasyLoading.dismiss();

//       if (success) {
//         ScaffoldMessenger.of(context).showSnackBar(
//           const SnackBar(
//             content: Text('تم إرسال الإشعار بنجاح'),
//             backgroundColor: Colors.green,
//           ),
//         );
//         // مسح الحقول بعد الإرسال الناجح
//         _titleController.clear();
//         _messageController.clear();
//       } else {
//         ScaffoldMessenger.of(context).showSnackBar(
//           const SnackBar(
//             content: Text('فشل في إرسال الإشعار'),
//             backgroundColor: Colors.red,
//           ),
//         );
//       }
//     } catch (e) {
//       // التحقق من أن الـ Widget لا يزال مثبتًا في شجرة الـ Widget
//       if (!mounted) return;

//       EasyLoading.dismiss();
//       ScaffoldMessenger.of(context).showSnackBar(
//         SnackBar(
//           content: Text('خطأ: $e'),
//           backgroundColor: Colors.red,
//         ),
//       );
//     } finally {
//       // التحقق من أن الـ Widget لا يزال مثبتًا في شجرة الـ Widget
//       if (mounted) {
//         setState(() {
//           _isSending = false;
//         });
//       }
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: Text(
//           'اختبار الإشعارات',
//           style: GoogleFonts.poppins(
//             color: Colors.white,
//           ),
//         ),
//         iconTheme: const IconThemeData(color: Colors.white),
//         centerTitle: true,
//         backgroundColor: kMainColor,
//         elevation: 0.0,
//       ),
//       body: Container(
//         decoration: const BoxDecoration(
//           color: Colors.white,
//           borderRadius: BorderRadius.only(
//             topRight: Radius.circular(30),
//             topLeft: Radius.circular(30),
//           ),
//         ),
//         child: Padding(
//           padding: const EdgeInsets.all(20.0),
//           child: SingleChildScrollView(
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.stretch,
//               children: [
//                 const SizedBox(height: 20),
//                 Text(
//                   'إرسال إشعار اختباري',
//                   style: GoogleFonts.poppins(
//                     fontSize: 18,
//                     fontWeight: FontWeight.bold,
//                   ),
//                   textAlign: TextAlign.center,
//                 ),
//                 const SizedBox(height: 20),
//                 TextField(
//                   controller: _recipientIdController,
//                   decoration: InputDecoration(
//                     labelText: 'معرف المستلم',
//                     border: OutlineInputBorder(
//                       borderRadius: BorderRadius.circular(10),
//                     ),
//                     prefixIcon: const Icon(Icons.person),
//                   ),
//                 ),
//                 const SizedBox(height: 15),
//                 TextField(
//                   controller: _titleController,
//                   decoration: InputDecoration(
//                     labelText: 'عنوان الإشعار',
//                     border: OutlineInputBorder(
//                       borderRadius: BorderRadius.circular(10),
//                     ),
//                     prefixIcon: const Icon(Icons.title),
//                   ),
//                 ),
//                 const SizedBox(height: 15),
//                 TextField(
//                   controller: _messageController,
//                   maxLines: 3,
//                   decoration: InputDecoration(
//                     labelText: 'نص الإشعار',
//                     border: OutlineInputBorder(
//                       borderRadius: BorderRadius.circular(10),
//                     ),
//                     prefixIcon: const Icon(Icons.message),
//                   ),
//                 ),
//                 const SizedBox(height: 30),
//                 ElevatedButton(
//                   onPressed: _isSending ? null : _sendNotification,
//                   style: ElevatedButton.styleFrom(
//                     backgroundColor: kMainColor,
//                     padding: const EdgeInsets.symmetric(vertical: 15),
//                     shape: RoundedRectangleBorder(
//                       borderRadius: BorderRadius.circular(10),
//                     ),
//                   ),
//                   child: Text(
//                     _isSending ? 'جاري الإرسال...' : 'إرسال الإشعار',
//                     style: GoogleFonts.poppins(
//                       fontSize: 16,
//                       color: Colors.white,
//                     ),
//                   ),
//                 ),
//                 const SizedBox(height: 20),
//                 const Divider(),
//                 const SizedBox(height: 20),
//                 Text(
//                   'ملاحظات:',
//                   style: GoogleFonts.poppins(
//                     fontSize: 16,
//                     fontWeight: FontWeight.bold,
//                   ),
//                 ),
//                 const SizedBox(height: 10),
//                 Text(
//                   '1. تأكد من تكوين Cloud Function في Back4App',
//                   style: GoogleFonts.poppins(fontSize: 14),
//                 ),
//                 const SizedBox(height: 5),
//                 Text(
//                   '2. تأكد من تعيين مفتاح خادم FCM في Back4App',
//                   style: GoogleFonts.poppins(fontSize: 14),
//                 ),
//                 const SizedBox(height: 5),
//                 Text(
//                   '3. يجب أن يكون المستلم مسجلاً في Back4App مع رمز جهاز صالح',
//                   style: GoogleFonts.poppins(fontSize: 14),
//                 ),
//               ],
//             ),
//           ),
//         ),
//       ),
//     );
//   }
// }
