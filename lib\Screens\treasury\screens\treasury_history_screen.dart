import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';
import 'package:intl/intl.dart';
import 'package:mobile_pos/constant.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:nb_utils/nb_utils.dart';

import '../providers/treasury_provider.dart';
import '../widgets/transaction_item_widget.dart';
import 'add_treasury_transaction_screen.dart';

class TreasuryHistoryScreen extends ConsumerStatefulWidget {
  const TreasuryHistoryScreen({super.key});

  @override
  ConsumerState<TreasuryHistoryScreen> createState() => _TreasuryHistoryScreenState();
}

class _TreasuryHistoryScreenState extends ConsumerState<TreasuryHistoryScreen> {
  String _selectedFilter = 'all'; // 'all', 'income', 'expense'
  String _selectedCategory = 'all';
  String _searchQuery = '';
  DateTime? _startDate;
  DateTime? _endDate;
  
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Create filters for transactions
    final filters = <String, dynamic>{
      'type': _selectedFilter == 'all' ? null : _selectedFilter,
      'category': _selectedCategory == 'all' ? null : _selectedCategory,
      'searchQuery': _searchQuery,
    };
    
    if (_startDate != null && _endDate != null) {
      filters['startDate'] = _startDate;
      filters['endDate'] = _endDate;
    }
    
    final filteredTransactions = ref.watch(filteredTransactionsProvider(filters));
    final categories = ref.watch(transactionCategoriesProvider);

    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        title: Text(
          'تاريخ المعاملات',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontSize: 20.0,
            fontWeight: FontWeight.bold,
          ),
        ),
        iconTheme: const IconThemeData(color: Colors.white),
        centerTitle: true,
        elevation: 0.0,
        actions: [
          IconButton(
            icon: const Icon(FeatherIcons.filter),
            onPressed: _showFilterDialog,
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(30),
            topLeft: Radius.circular(30),
          ),
        ),
        child: Column(
          children: [
            // Search and Filter Section
            Container(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  // Search Bar
                  TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'البحث في المعاملات...',
                      hintStyle: GoogleFonts.cairo(color: Colors.grey),
                      prefixIcon: const Icon(FeatherIcons.search),
                      suffixIcon: _searchQuery.isNotEmpty
                          ? IconButton(
                              icon: const Icon(FeatherIcons.x),
                              onPressed: () {
                                _searchController.clear();
                                setState(() {
                                  _searchQuery = '';
                                });
                              },
                            )
                          : null,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10),
                        borderSide: const BorderSide(color: kBorderColorTextField),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10),
                        borderSide: const BorderSide(color: kMainColor),
                      ),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                      });
                    },
                  ),

                  const SizedBox(height: 15),

                  // Filter Chips
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: [
                        _buildFilterChip('الكل', 'all'),
                        const SizedBox(width: 8),
                        _buildFilterChip('الإيرادات', 'income'),
                        const SizedBox(width: 8),
                        _buildFilterChip('المصروفات', 'expense'),
                        if (_startDate != null && _endDate != null) ...[
                          const SizedBox(width: 8),
                          _buildDateRangeChip(),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Transactions List
            Expanded(
              child: filteredTransactions.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            FeatherIcons.inbox,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'لا توجد معاملات',
                            style: GoogleFonts.cairo(
                              fontSize: 18,
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'جرب تغيير المرشحات أو إضافة معاملة جديدة',
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              color: Colors.grey[500],
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      itemCount: filteredTransactions.length,
                      itemBuilder: (context, index) {
                        final transaction = filteredTransactions[index];
                        return TransactionItemWidget(
                          transaction: transaction,
                          onTap: () {
                            _showTransactionDetails(transaction);
                          },
                          onEdit: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => AddTreasuryTransactionScreen(
                                  transaction: transaction,
                                ),
                              ),
                            );
                          },
                          onDelete: () {
                            _confirmDeleteTransaction(transaction);
                          },
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const AddTreasuryTransactionScreen(),
            ),
          );
        },
        backgroundColor: kMainColor,
        child: const Icon(FeatherIcons.plus, color: Colors.white),
      ),
    );
  }

  Widget _buildFilterChip(String label, String value) {
    final isSelected = _selectedFilter == value;
    
    return FilterChip(
      label: Text(
        label,
        style: GoogleFonts.cairo(
          color: isSelected ? Colors.white : kMainColor,
          fontWeight: FontWeight.w600,
        ),
      ),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedFilter = value;
        });
      },
      backgroundColor: Colors.white,
      selectedColor: kMainColor,
      checkmarkColor: Colors.white,
      side: BorderSide(
        color: isSelected ? kMainColor : kBorderColorTextField,
      ),
    );
  }

  Widget _buildDateRangeChip() {
    return FilterChip(
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(FeatherIcons.calendar, size: 16, color: Colors.white),
          const SizedBox(width: 4),
          Text(
            '${DateFormat('MM/dd').format(_startDate!)} - ${DateFormat('MM/dd').format(_endDate!)}',
            style: GoogleFonts.cairo(
              color: Colors.white,
              fontWeight: FontWeight.w600,
              fontSize: 12,
            ),
          ),
        ],
      ),
      selected: true,
      onSelected: (selected) {
        if (!selected) {
          setState(() {
            _startDate = null;
            _endDate = null;
          });
        }
      },
      backgroundColor: Colors.white,
      selectedColor: Colors.blue,
      checkmarkColor: Colors.white,
      deleteIcon: const Icon(FeatherIcons.x, size: 16, color: Colors.white),
      onDeleted: () {
        setState(() {
          _startDate = null;
          _endDate = null;
        });
      },
    );
  }

  void _showFilterDialog() {
    final categories = ref.read(transactionCategoriesProvider);
    
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => StatefulBuilder(
        builder: (context, setModalState) => Container(
          height: MediaQuery.of(context).size.height * 0.7,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(25),
              topRight: Radius.circular(25),
            ),
          ),
          child: Column(
            children: [
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(top: 10),
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'تصفية المعاملات',
                        style: GoogleFonts.cairo(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: kTitleColor,
                        ),
                      ),
                      const SizedBox(height: 20),

                      // Category Filter
                      Text(
                        'الفئة',
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: kTitleColor,
                        ),
                      ),
                      const SizedBox(height: 10),
                      DropdownButtonFormField<String>(
                        value: _selectedCategory,
                        decoration: InputDecoration(
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                          contentPadding: const EdgeInsets.symmetric(horizontal: 15, vertical: 12),
                        ),
                        items: [
                          DropdownMenuItem(
                            value: 'all',
                            child: Text('جميع الفئات', style: GoogleFonts.cairo()),
                          ),
                          ...categories.map((category) {
                            return DropdownMenuItem(
                              value: category,
                              child: Text(category, style: GoogleFonts.cairo()),
                            );
                          }),
                        ],
                        onChanged: (value) {
                          setModalState(() {
                            _selectedCategory = value ?? 'all';
                          });
                        },
                      ),

                      const SizedBox(height: 20),

                      // Date Range Filter
                      Text(
                        'نطاق التاريخ',
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: kTitleColor,
                        ),
                      ),
                      const SizedBox(height: 10),
                      Row(
                        children: [
                          Expanded(
                            child: GestureDetector(
                              onTap: () async {
                                final date = await showDatePicker(
                                  context: context,
                                  initialDate: _startDate ?? DateTime.now().subtract(const Duration(days: 30)),
                                  firstDate: DateTime(2020),
                                  lastDate: DateTime.now(),
                                );
                                if (date != null) {
                                  setModalState(() {
                                    _startDate = date;
                                  });
                                }
                              },
                              child: Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  border: Border.all(color: kBorderColorTextField),
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: Row(
                                  children: [
                                    const Icon(FeatherIcons.calendar, size: 16),
                                    const SizedBox(width: 8),
                                    Text(
                                      _startDate != null 
                                          ? DateFormat('yyyy/MM/dd').format(_startDate!)
                                          : 'من تاريخ',
                                      style: GoogleFonts.cairo(),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 10),
                          Expanded(
                            child: GestureDetector(
                              onTap: () async {
                                final date = await showDatePicker(
                                  context: context,
                                  initialDate: _endDate ?? DateTime.now(),
                                  firstDate: _startDate ?? DateTime(2020),
                                  lastDate: DateTime.now(),
                                );
                                if (date != null) {
                                  setModalState(() {
                                    _endDate = date;
                                  });
                                }
                              },
                              child: Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  border: Border.all(color: kBorderColorTextField),
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: Row(
                                  children: [
                                    const Icon(FeatherIcons.calendar, size: 16),
                                    const SizedBox(width: 8),
                                    Text(
                                      _endDate != null 
                                          ? DateFormat('yyyy/MM/dd').format(_endDate!)
                                          : 'إلى تاريخ',
                                      style: GoogleFonts.cairo(),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),

                      const Spacer(),

                      // Action Buttons
                      Row(
                        children: [
                          Expanded(
                            child: OutlinedButton(
                              onPressed: () {
                                setModalState(() {
                                  _selectedCategory = 'all';
                                  _startDate = null;
                                  _endDate = null;
                                });
                                setState(() {
                                  _selectedCategory = 'all';
                                  _startDate = null;
                                  _endDate = null;
                                });
                              },
                              child: Text('إعادة تعيين', style: GoogleFonts.cairo()),
                            ),
                          ),
                          const SizedBox(width: 10),
                          Expanded(
                            child: ElevatedButton(
                              onPressed: () {
                                setState(() {
                                  _selectedCategory = _selectedCategory;
                                  _startDate = _startDate;
                                  _endDate = _endDate;
                                });
                                Navigator.pop(context);
                              },
                              style: ElevatedButton.styleFrom(backgroundColor: kMainColor),
                              child: Text('تطبيق', style: GoogleFonts.cairo(color: Colors.white)),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showTransactionDetails(transaction) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(top: 10),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'تفاصيل المعاملة',
                          style: GoogleFonts.cairo(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: kTitleColor,
                          ),
                        ),
                        Row(
                          children: [
                            IconButton(
                              icon: const Icon(FeatherIcons.edit2, color: Colors.blue),
                              onPressed: () {
                                Navigator.pop(context);
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => AddTreasuryTransactionScreen(
                                      transaction: transaction,
                                    ),
                                  ),
                                );
                              },
                            ),
                            IconButton(
                              icon: const Icon(FeatherIcons.trash2, color: Colors.red),
                              onPressed: () {
                                Navigator.pop(context);
                                _confirmDeleteTransaction(transaction);
                              },
                            ),
                          ],
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    _buildDetailRow('النوع', transaction.isIncome ? 'إيراد' : 'مصروف'),
                    _buildDetailRow('الفئة', transaction.category),
                    _buildDetailRow('الوصف', transaction.description),
                    _buildDetailRow('المبلغ', '${transaction.amountAsDouble}'),
                    _buildDetailRow('طريقة الدفع', transaction.paymentMethod),
                    _buildDetailRow('رقم المرجع', transaction.referenceNumber),
                    _buildDetailRow('التاريخ', DateFormat('yyyy/MM/dd').format(transaction.dateAsDateTime)),
                    if (transaction.notes.isNotEmpty)
                      _buildDetailRow('ملاحظات', transaction.notes),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: GoogleFonts.cairo(
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: GoogleFonts.cairo(
                color: kTitleColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _confirmDeleteTransaction(transaction) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'حذف المعاملة',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        content: Text(
          'هل أنت متأكد من حذف هذه المعاملة؟ لا يمكن التراجع عن هذا الإجراء.',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              EasyLoading.show(status: 'جاري الحذف...');
              
              final notifier = ref.read(addTransactionProvider.notifier);
              await notifier.deleteTransaction(transaction.id);
              
              final result = ref.read(addTransactionProvider);
              result.when(
                data: (success) {
                  if (success) {
                    EasyLoading.showSuccess('تم حذف المعاملة بنجاح');
                  } else {
                    EasyLoading.showError('فشل في حذف المعاملة');
                  }
                },
                loading: () {},
                error: (error, stack) {
                  EasyLoading.showError('خطأ: $error');
                },
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text('حذف', style: GoogleFonts.cairo(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}
