import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/model/water_filter_models.dart';
import 'package:mobile_pos/services/water_filter_product_service.dart';
import 'package:mobile_pos/Screens/WaterFilters/Products/add_water_filter_product_screen.dart';
import 'package:mobile_pos/Screens/WaterFilters/Products/edit_water_filter_product_screen.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

class WaterFilterProductsScreen extends StatefulWidget {
  const WaterFilterProductsScreen({super.key});

  @override
  State<WaterFilterProductsScreen> createState() => _WaterFilterProductsScreenState();
}

class _WaterFilterProductsScreenState extends State<WaterFilterProductsScreen> {
  List<WaterFilterProduct> _products = [];
  List<WaterFilterProduct> _filteredProducts = [];
  bool _isLoading = true;
  String _searchQuery = '';
  WaterFilterCategory? _selectedCategory;

  @override
  void initState() {
    super.initState();
    _loadProducts();
  }

  @override
  void dispose() {
    // إلغاء المستمعين
    WaterFilterProductService.cancelProductsListener(
      ownerId: 'water_filter_products_screen',
    );
    super.dispose();
  }

  Future<void> _loadProducts() async {
    try {
      setState(() => _isLoading = true);
      
      final products = await WaterFilterProductService.getAllProducts();
      
      if (mounted) {
        setState(() {
          _products = products;
          _filteredProducts = products;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        EasyLoading.showError('خطأ في تحميل المنتجات: $e');
      }
    }
  }

  void _filterProducts() {
    setState(() {
      _filteredProducts = _products.where((product) {
        final matchesSearch = _searchQuery.isEmpty ||
            product.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            product.brand.toLowerCase().contains(_searchQuery.toLowerCase());
        
        final matchesCategory = _selectedCategory == null ||
            product.category == _selectedCategory;
        
        return matchesSearch && matchesCategory;
      }).toList();
    });
  }

  void _onSearchChanged(String query) {
    setState(() => _searchQuery = query);
    _filterProducts();
  }

  void _onCategoryChanged(WaterFilterCategory? category) {
    setState(() => _selectedCategory = category);
    _filterProducts();
  }

  Future<void> _deleteProduct(WaterFilterProduct product) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'تأكيد الحذف',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        content: Text(
          'هل أنت متأكد من حذف المنتج "${product.name}"؟',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: Text('حذف', style: GoogleFonts.cairo()),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      EasyLoading.show(status: 'جاري الحذف...', dismissOnTap: false);
      
      final success = await WaterFilterProductService.deleteProduct(product.id);
      
      EasyLoading.dismiss();
      
      if (success) {
        EasyLoading.showSuccess('تم حذف المنتج بنجاح');
        _loadProducts();
      } else {
        EasyLoading.showError('فشل في حذف المنتج');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        elevation: 0,
        title: Text(
          'إدارة المنتجات',
          style: GoogleFonts.cairo(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: _loadProducts,
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        child: Column(
          children: [
            // شريط البحث والفلترة
            _buildSearchAndFilter(),
            
            // قائمة المنتجات
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _filteredProducts.isEmpty
                      ? _buildEmptyState()
                      : _buildProductsList(),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const AddWaterFilterProductScreen(),
            ),
          );
          
          if (result == true) {
            _loadProducts();
          }
        },
        backgroundColor: kMainColor,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildSearchAndFilter() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            onChanged: _onSearchChanged,
            decoration: InputDecoration(
              hintText: 'البحث في المنتجات...',
              hintStyle: GoogleFonts.cairo(color: Colors.grey),
              prefixIcon: const Icon(Icons.search, color: kMainColor),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(15),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(15),
                borderSide: const BorderSide(color: kMainColor),
              ),
              filled: true,
              fillColor: Colors.grey.shade50,
            ),
            style: GoogleFonts.cairo(),
          ),
          
          const SizedBox(height: 15),
          
          // فلتر الفئات
          DropdownButtonFormField<WaterFilterCategory?>(
            value: _selectedCategory,
            onChanged: _onCategoryChanged,
            decoration: InputDecoration(
              labelText: 'فلترة حسب الفئة',
              labelStyle: GoogleFonts.cairo(color: kMainColor),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(15),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(15),
                borderSide: const BorderSide(color: kMainColor),
              ),
              filled: true,
              fillColor: Colors.grey.shade50,
            ),
            items: [
              DropdownMenuItem<WaterFilterCategory?>(
                value: null,
                child: Text('جميع الفئات', style: GoogleFonts.cairo()),
              ),
              ...WaterFilterCategory.values.map((category) =>
                DropdownMenuItem<WaterFilterCategory?>(
                  value: category,
                  child: Text(category.arabicName, style: GoogleFonts.cairo()),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inventory_2_outlined,
            size: 80,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 20),
          Text(
            'لا توجد منتجات',
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 10),
          Text(
            'اضغط على زر + لإضافة منتج جديد',
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: Colors.grey.shade500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductsList() {
    return ListView.builder(
      padding: const EdgeInsets.all(20),
      itemCount: _filteredProducts.length,
      itemBuilder: (context, index) {
        final product = _filteredProducts[index];
        return _buildProductCard(product);
      },
    );
  }

  Widget _buildProductCard(WaterFilterProduct product) {
    return Card(
      margin: const EdgeInsets.only(bottom: 15),
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      child: Padding(
        padding: const EdgeInsets.all(15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // صورة المنتج
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    color: Colors.grey.shade200,
                  ),
                  child: product.imageUrl != null
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(10),
                          child: Image.network(
                            product.imageUrl!,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) =>
                                const Icon(Icons.image_not_supported),
                          ),
                        )
                      : const Icon(Icons.water_drop, color: kMainColor, size: 30),
                ),
                
                const SizedBox(width: 15),
                
                // معلومات المنتج
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        product.name,
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 5),
                      Text(
                        product.brand,
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                      const SizedBox(height: 5),
                      Text(
                        product.category.arabicName,
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          color: kMainColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // الإجراءات
                PopupMenuButton<String>(
                  onSelected: (value) async {
                    if (value == 'edit') {
                      final result = await Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => EditWaterFilterProductScreen(product: product),
                        ),
                      );
                      
                      if (result == true) {
                        _loadProducts();
                      }
                    } else if (value == 'delete') {
                      _deleteProduct(product);
                    }
                  },
                  itemBuilder: (context) => [
                    PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          const Icon(Icons.edit, color: kMainColor),
                          const SizedBox(width: 10),
                          Text('تعديل', style: GoogleFonts.cairo()),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          const Icon(Icons.delete, color: Colors.red),
                          const SizedBox(width: 10),
                          Text('حذف', style: GoogleFonts.cairo()),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            
            const SizedBox(height: 15),
            
            // معلومات إضافية
            Row(
              children: [
                _buildInfoChip('السعر: ${product.price.toStringAsFixed(2)} ج.م'),
                const SizedBox(width: 10),
                _buildInfoChip('المخزون: ${product.stock}'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoChip(String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
      decoration: BoxDecoration(
        color: kMainColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Text(
        text,
        style: GoogleFonts.cairo(
          fontSize: 12,
          color: kMainColor,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}
