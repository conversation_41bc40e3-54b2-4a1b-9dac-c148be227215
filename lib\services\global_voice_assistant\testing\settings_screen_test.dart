import 'package:flutter/material.dart';
import '../overlay/voice_assistant_settings_screen.dart';

/// شاشة اختبار سريع لشاشة إعدادات المساعد الصوتي
class SettingsScreenTestWidget extends StatelessWidget {
  const SettingsScreenTestWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار شاشة الإعدادات'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.settings_voice,
              size: 100,
              color: Colors.blue,
            ),
            
            const SizedBox(height: 20),
            
            const Text(
              'اختبار شاشة إعدادات المساعد الصوتي',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 10),
            
            const Text(
              'اضغط على الزر أدناه لفتح شاشة الإعدادات',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 30),
            
            ElevatedButton.icon(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const VoiceAssistantSettingsScreen(),
                  ),
                );
              },
              icon: const Icon(Icons.settings),
              label: const Text('فتح شاشة الإعدادات'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 30,
                  vertical: 15,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // معلومات إضافية
            Container(
              margin: const EdgeInsets.all(20),
              padding: const EdgeInsets.all(15),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(10),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'ملاحظات الاختبار:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                  SizedBox(height: 5),
                  Text('• تحقق من تحميل جميع الإعدادات'),
                  Text('• اختبر تفعيل/إلغاء تفعيل الخدمة'),
                  Text('• تأكد من عمل جميع الأزرار'),
                  Text('• راجع رسائل الحالة والأخطاء'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
