import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mobile_pos/models/hrm_models.dart';
import '../../Designation/repo/designation_repo.dart';

// Provider for designation repository
final designationRepositoryProvider = Provider<DesignationRepository>((ref) {
  return DesignationRepository();
});

// Provider for designation list
final designationListProvider = StreamProvider<List<DesignationModel>>((ref) {
  final designationRepo = ref.watch(designationRepositoryProvider);
  return designationRepo.getAllDesignations().asStream();
});

// Provider for selected designation
final selectedDesignationProvider =
    StateProvider<DesignationModel?>((ref) => null);

// Provider for designation notifier
final designationNotifierProvider =
    StateNotifierProvider<DesignationNotifier, AsyncValue<void>>((ref) {
  final designationRepo = ref.watch(designationRepositoryProvider);
  return DesignationNotifier(designationRepo);
});

// Notifier for designation operations
class DesignationNotifier extends StateNotifier<AsyncValue<void>> {
  final DesignationRepository _designationRepo;

  DesignationNotifier(this._designationRepo)
      : super(const AsyncValue.data(null));

  // Add a new designation
  Future<void> addDesignation(DesignationModel designation) async {
    state = const AsyncValue.loading();
    try {
      await _designationRepo.addDesignation(designation: designation);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  // Update an existing designation
  Future<void> updateDesignation(DesignationModel designation) async {
    state = const AsyncValue.loading();
    try {
      await _designationRepo.updateDesignation(designation: designation);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  // Delete a designation
  Future<void> deleteDesignation(String id) async {
    state = const AsyncValue.loading();
    try {
      await _designationRepo.deleteDesignation(id: id);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }
}
