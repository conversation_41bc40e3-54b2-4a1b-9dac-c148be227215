import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../model/pay_salary_model.dart';
import '../repo/salary_repo.dart';

// Provider for salary repository
final salaryRepositoryProvider = Provider<SalaryRepository>((ref) {
  return SalaryRepository();
});

// Provider for paid salary list
final paidSalaryListProvider = FutureProvider<List<PaySalaryModel>>((ref) {
  final salaryRepo = ref.watch(salaryRepositoryProvider);
  return salaryRepo.getAllPaidSalary();
});

// Provider for selected salary
final selectedSalaryProvider = StateProvider<PaySalaryModel?>((ref) => null);

// Notifier for salary operations
class SalaryNotifier extends StateNotifier<AsyncValue<List<PaySalaryModel>>> {
  final SalaryRepository _repository;

  SalaryNotifier(this._repository) : super(const AsyncValue.loading()) {
    loadSalaries();
  }

  Future<void> loadSalaries() async {
    state = const AsyncValue.loading();
    try {
      final salaries = await _repository.getAllPaidSalary();
      state = AsyncValue.data(salaries);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<void> addSalary(PaySalaryModel salary) async {
    try {
      await _repository.paySalary(salary);
      loadSalaries();
    } catch (e) {
      rethrow;
    }
  }

  Future<void> updateSalary(PaySalaryModel salary) async {
    try {
      await _repository.updateSalary(salary);
      loadSalaries();
    } catch (e) {
      rethrow;
    }
  }

  Future<void> deleteSalary(String id) async {
    try {
      await _repository.deletePaidSalary(id: id);
      loadSalaries();
    } catch (e) {
      rethrow;
    }
  }
}

// Provider for salary notifier
final salaryNotifierProvider =
    StateNotifierProvider<SalaryNotifier, AsyncValue<List<PaySalaryModel>>>(
        (ref) {
  final repository = ref.watch(salaryRepositoryProvider);
  return SalaryNotifier(repository);
});
