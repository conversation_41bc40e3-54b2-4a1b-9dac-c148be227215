import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../core/voice_assistant_core_service.dart';
import '../core/voice_assistant_permissions_service.dart';
import '../../../constant.dart';

/// شاشة إعدادات الخصوصية والأمان للمساعد الصوتي
class VoiceAssistantPrivacySettingsScreen extends StatefulWidget {
  const VoiceAssistantPrivacySettingsScreen({super.key});

  @override
  State<VoiceAssistantPrivacySettingsScreen> createState() =>
      _VoiceAssistantPrivacySettingsScreenState();
}

class _VoiceAssistantPrivacySettingsScreenState
    extends State<VoiceAssistantPrivacySettingsScreen> {
  final VoiceAssistantCoreService _coreService = VoiceAssistantCoreService();
  final VoiceAssistantPermissionsService _permissionsService =
      VoiceAssistantPermissionsService();

  bool _isLoading = true;
  Map<String, dynamic> _status = {};
  Map<String, dynamic> _permissionsReport = {};

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    setState(() => _isLoading = true);

    try {
      final status = _coreService.getStatus();
      final permissionsReport =
          await _permissionsService.getPermissionsReport();

      setState(() {
        _status = status;
        _permissionsReport = permissionsReport;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الإعدادات: $e');
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'إعدادات الخصوصية والأمان',
          style: GoogleFonts.cairo(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: kMainColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildPrivacySection(),
                  const SizedBox(height: 20),
                  _buildPermissionsSection(),
                  const SizedBox(height: 20),
                  _buildDataManagementSection(),
                  const SizedBox(height: 20),
                  _buildSecuritySection(),
                ],
              ),
            ),
    );
  }

  Widget _buildPrivacySection() {
    final privacySettings = _status['privacySettings'] ?? {};

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.privacy_tip, color: kMainColor, size: 24),
                const SizedBox(width: 8),
                Text(
                  'إعدادات الخصوصية',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: kTitleColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildSwitchTile(
              title: 'تفعيل المساعد الصوتي',
              subtitle: 'تشغيل/إيقاف المساعد الصوتي',
              value: _status['isEnabled'] ?? false,
              onChanged: (value) async {
                await _coreService.setEnabled(value);
                _loadSettings();
              },
            ),
            _buildSwitchTile(
              title: 'جمع البيانات',
              subtitle: 'السماح بجمع بيانات الاستخدام لتحسين الخدمة',
              value: privacySettings['dataCollection'] ?? false,
              onChanged: (value) async {
                await _coreService.updatePrivacySettings(
                  enableDataCollection: value,
                );
                _loadSettings();
              },
            ),
            _buildSwitchTile(
              title: 'تسجيل الصوت',
              subtitle: 'حفظ التسجيلات الصوتية محلياً',
              value: privacySettings['voiceRecording'] ?? false,
              onChanged: (value) async {
                await _coreService.updatePrivacySettings(
                  enableVoiceRecording: value,
                );
                _loadSettings();
              },
            ),
            _buildSwitchTile(
              title: 'المزامنة السحابية',
              subtitle: 'مزامنة البيانات مع الخدمات السحابية',
              value: privacySettings['cloudSync'] ?? false,
              onChanged: (value) async {
                await _coreService.updatePrivacySettings(
                  enableCloudSync: value,
                );
                _loadSettings();
              },
            ),
            _buildSwitchTile(
              title: 'المعالجة المحلية',
              subtitle: 'معالجة الأوامر محلياً بدلاً من الخوادم الخارجية',
              value: privacySettings['localProcessing'] ?? true,
              onChanged: (value) async {
                await _coreService.updatePrivacySettings(
                  enableLocalProcessing: value,
                );
                _loadSettings();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionsSection() {
    final essential = _permissionsReport['essential'] ?? {};
    final optional = _permissionsReport['optional'] ?? {};
    final recommendations = _permissionsReport['recommendations'] ?? [];

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.security, color: kMainColor, size: 24),
                const SizedBox(width: 8),
                Text(
                  'الأذونات',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: kTitleColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'الأذونات الأساسية:',
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: kTitleColor,
              ),
            ),
            const SizedBox(height: 8),
            _buildPermissionTile(
              'الميكروفون',
              essential['microphone'] ?? false,
              isRequired: true,
            ),
            const SizedBox(height: 12),
            Text(
              'الأذونات الاختيارية:',
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: kTitleColor,
              ),
            ),
            const SizedBox(height: 8),
            _buildPermissionTile(
              'النوافذ العائمة',
              optional['systemAlertWindow'] ?? false,
              isRequired: false,
            ),
            _buildPermissionTile(
              'تحسين البطارية',
              optional['ignoreBatteryOptimizations'] ?? false,
              isRequired: false,
            ),
            if (recommendations.isNotEmpty) ...[
              const SizedBox(height: 16),
              Text(
                'التوصيات:',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: kTitleColor,
                ),
              ),
              const SizedBox(height: 8),
              ...recommendations.map((rec) => Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Row(
                      children: [
                        const Icon(Icons.info_outline,
                            size: 16, color: Colors.blue),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            rec,
                            style: GoogleFonts.cairo(fontSize: 14),
                          ),
                        ),
                      ],
                    ),
                  )),
            ],
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () async {
                  await _permissionsService.requestOptionalPermissions();
                  _loadSettings();
                },
                icon: const Icon(Icons.settings),
                label: Text(
                  'طلب الأذونات الاختيارية',
                  style: GoogleFonts.cairo(),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: kMainColor,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataManagementSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.storage, color: kMainColor, size: 24),
                const SizedBox(width: 8),
                Text(
                  'إدارة البيانات',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: kTitleColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.delete_outline, color: Colors.red),
              title: Text(
                'حذف بيانات الصوت',
                style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
              ),
              subtitle: Text(
                'حذف جميع التسجيلات الصوتية المحفوظة',
                style: GoogleFonts.cairo(),
              ),
              onTap: () => _showDeleteDataDialog(),
            ),
            ListTile(
              leading: const Icon(Icons.refresh, color: Colors.orange),
              title: Text(
                'إعادة تعيين الإعدادات',
                style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
              ),
              subtitle: Text(
                'إعادة جميع الإعدادات إلى القيم الافتراضية',
                style: GoogleFonts.cairo(),
              ),
              onTap: () => _showResetSettingsDialog(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSecuritySection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.shield, color: kMainColor, size: 24),
                const SizedBox(width: 8),
                Text(
                  'الأمان',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: kTitleColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.check_circle, color: Colors.green.shade600),
                      const SizedBox(width: 8),
                      Text(
                        'البيانات آمنة',
                        style: GoogleFonts.cairo(
                          fontWeight: FontWeight.w600,
                          color: Colors.green.shade800,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '• جميع البيانات محفوظة محلياً على جهازك\n'
                    '• لا يتم إرسال بيانات شخصية للخوادج الخارجية\n'
                    '• التسجيلات الصوتية مشفرة ومحمية\n'
                    '• يمكنك حذف البيانات في أي وقت',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: Colors.green.shade700,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool) onChanged,
  }) {
    return SwitchListTile(
      title: Text(
        title,
        style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
      ),
      subtitle: Text(
        subtitle,
        style: GoogleFonts.cairo(fontSize: 14),
      ),
      value: value,
      onChanged: onChanged,
      activeColor: kMainColor,
    );
  }

  Widget _buildPermissionTile(String title, bool granted,
      {required bool isRequired}) {
    return ListTile(
      leading: Icon(
        granted ? Icons.check_circle : Icons.cancel,
        color:
            granted ? Colors.green : (isRequired ? Colors.red : Colors.orange),
      ),
      title: Text(
        title,
        style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
      ),
      subtitle: Text(
        granted ? 'ممنوح' : (isRequired ? 'مطلوب' : 'اختياري'),
        style: GoogleFonts.cairo(fontSize: 14),
      ),
      trailing: isRequired && !granted
          ? TextButton(
              onPressed: () => _permissionsService.openAppSettings(),
              child: Text(
                'منح',
                style: GoogleFonts.cairo(color: kMainColor),
              ),
            )
          : null,
    );
  }

  void _showDeleteDataDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'حذف بيانات الصوت',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        content: Text(
          'هل أنت متأكد من حذف جميع التسجيلات الصوتية؟ لا يمكن التراجع عن هذا الإجراء.',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          TextButton(
            onPressed: () async {
              final navigator = Navigator.of(context);
              final scaffoldMessenger = ScaffoldMessenger.of(context);

              navigator.pop();
              // حذف بيانات الصوت المحفوظة
              await _deleteVoiceData();

              scaffoldMessenger.showSnackBar(
                SnackBar(
                  content: Text(
                    'تم حذف بيانات الصوت',
                    style: GoogleFonts.cairo(),
                  ),
                ),
              );
            },
            child: Text(
              'حذف',
              style: GoogleFonts.cairo(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteVoiceData() async {
    try {
      // حذف بيانات بصمة الصوت
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('voice_fingerprint_data');
      await prefs.remove('voice_fingerprint_registered');
      await prefs.remove('user_voice_hash');

      debugPrint('✅ تم حذف بيانات الصوت المحفوظة');
    } catch (e) {
      debugPrint('❌ خطأ في حذف بيانات الصوت: $e');
    }
  }

  void _showResetSettingsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'إعادة تعيين الإعدادات',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        content: Text(
          'هل أنت متأكد من إعادة تعيين جميع الإعدادات؟',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          TextButton(
            onPressed: () async {
              final navigator = Navigator.of(context);
              final scaffoldMessenger = ScaffoldMessenger.of(context);

              navigator.pop();
              await _permissionsService.resetPermissionsState();
              _loadSettings();

              scaffoldMessenger.showSnackBar(
                SnackBar(
                  content: Text(
                    'تم إعادة تعيين الإعدادات',
                    style: GoogleFonts.cairo(),
                  ),
                ),
              );
            },
            child: Text(
              'إعادة تعيين',
              style: GoogleFonts.cairo(color: Colors.orange),
            ),
          ),
        ],
      ),
    );
  }
}
