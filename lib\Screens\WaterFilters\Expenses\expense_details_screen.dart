import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/model/water_filter_models.dart';
import 'package:mobile_pos/services/water_filter_expense_service.dart';
import 'package:mobile_pos/Screens/WaterFilters/Expenses/add_expense_screen.dart';

class ExpenseDetailsScreen extends StatefulWidget {
  final WaterFilterExpense expense;

  const ExpenseDetailsScreen({super.key, required this.expense});

  @override
  State<ExpenseDetailsScreen> createState() => _ExpenseDetailsScreenState();
}

class _ExpenseDetailsScreenState extends State<ExpenseDetailsScreen> {
  late WaterFilterExpense _expense;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _expense = widget.expense;
  }

  Future<void> _deleteExpense() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'تأكيد الحذف',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        content: Text(
          'هل أنت متأكد من حذف هذا المصروف؟\nلا يمكن التراجع عن هذا الإجراء.',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(
              'إلغاء',
              style: GoogleFonts.cairo(color: Colors.grey),
            ),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(
              'حذف',
              style: GoogleFonts.cairo(color: Colors.red),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() => _isLoading = true);

      final success =
          await WaterFilterExpenseService.deleteExpense(_expense.id);

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حذف المصروف بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context, true);
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('فشل في حذف المصروف'),
            backgroundColor: Colors.red,
          ),
        );
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        title: Text(
          'تفاصيل المصروف',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit, color: Colors.white),
            onPressed: () async {
              if (!mounted) return;
              final navigator = Navigator.of(context);
              final result = await navigator.push(
                MaterialPageRoute(
                  builder: (context) => AddExpenseScreen(expense: _expense),
                ),
              );

              if (result == true && mounted) {
                navigator.pop(true);
              }
            },
          ),
          IconButton(
            icon: const Icon(Icons.delete, color: Colors.white),
            onPressed: _isLoading ? null : _deleteExpense,
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // رأس المصروف
                    _buildExpenseHeader(),

                    const SizedBox(height: 24),

                    // تفاصيل المصروف
                    _buildExpenseDetails(),

                    const SizedBox(height: 24),

                    // معلومات إضافية
                    _buildAdditionalInfo(),
                  ],
                ),
              ),
      ),
    );
  }

  Widget _buildExpenseHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            _getCategoryColor().withOpacity(0.1),
            _getCategoryColor().withOpacity(0.05)
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: _getCategoryColor().withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _getCategoryColor().withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  _getCategoryIcon(),
                  color: _getCategoryColor(),
                  size: 32,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _expense.title,
                      style: GoogleFonts.cairo(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: kTitleColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getCategoryColor().withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        _expense.category.arabicName,
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          color: _getCategoryColor(),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'المبلغ',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  Text(
                    '${_expense.amount.toStringAsFixed(2)} ج.م',
                    style: GoogleFonts.cairo(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                    ),
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    'التاريخ',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  Text(
                    '${_expense.date.day}/${_expense.date.month}/${_expense.date.year}',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: kTitleColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildExpenseDetails() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'تفاصيل المصروف',
          style: GoogleFonts.cairo(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: kMainColor,
          ),
        ),
        const SizedBox(height: 16),
        _buildDetailItem(
          icon: Icons.description,
          title: 'الوصف',
          value: _expense.description,
        ),
        _buildDetailItem(
          icon: Icons.payment,
          title: 'طريقة الدفع',
          value: _expense.paymentMethod.arabicName,
        ),
        if (_expense.vendorName != null)
          _buildDetailItem(
            icon: Icons.business,
            title: 'اسم المورد',
            value: _expense.vendorName!,
          ),
        if (_expense.invoiceNumber != null)
          _buildDetailItem(
            icon: Icons.receipt,
            title: 'رقم الفاتورة',
            value: _expense.invoiceNumber!,
          ),
        if (_expense.isRecurring)
          _buildDetailItem(
            icon: Icons.repeat,
            title: 'مصروف متكرر',
            value: _expense.recurringIntervalDays != null
                ? 'كل ${_expense.recurringIntervalDays} يوم'
                : 'نعم',
          ),
        if (_expense.notes != null && _expense.notes!.isNotEmpty)
          _buildDetailItem(
            icon: Icons.note,
            title: 'ملاحظات',
            value: _expense.notes!,
            isMultiline: true,
          ),
      ],
    );
  }

  Widget _buildAdditionalInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'معلومات إضافية',
          style: GoogleFonts.cairo(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: kMainColor,
          ),
        ),
        const SizedBox(height: 16),
        if (_expense.createdAt != null)
          _buildDetailItem(
            icon: Icons.access_time,
            title: 'تاريخ الإنشاء',
            value:
                '${_expense.createdAt!.day}/${_expense.createdAt!.month}/${_expense.createdAt!.year} - ${_expense.createdAt!.hour}:${_expense.createdAt!.minute.toString().padLeft(2, '0')}',
          ),
        if (_expense.updatedAt != null &&
            _expense.updatedAt != _expense.createdAt)
          _buildDetailItem(
            icon: Icons.update,
            title: 'آخر تحديث',
            value:
                '${_expense.updatedAt!.day}/${_expense.updatedAt!.month}/${_expense.updatedAt!.year} - ${_expense.updatedAt!.hour}:${_expense.updatedAt!.minute.toString().padLeft(2, '0')}',
          ),
        _buildDetailItem(
          icon: Icons.fingerprint,
          title: 'معرف المصروف',
          value: _expense.id,
        ),
      ],
    );
  }

  Widget _buildDetailItem({
    required IconData icon,
    required String title,
    required String value,
    bool isMultiline = false,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        crossAxisAlignment:
            isMultiline ? CrossAxisAlignment.start : CrossAxisAlignment.center,
        children: [
          Icon(icon, color: kMainColor, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: kTitleColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getCategoryColor() {
    switch (_expense.category) {
      case ExpenseCategory.transportation:
        return Colors.blue;
      case ExpenseCategory.materials:
        return Colors.orange;
      case ExpenseCategory.tools:
        return Colors.purple;
      case ExpenseCategory.maintenance:
        return Colors.green;
      case ExpenseCategory.marketing:
        return Colors.pink;
      case ExpenseCategory.office:
        return Colors.teal;
      case ExpenseCategory.utilities:
        return Colors.indigo;
      case ExpenseCategory.salaries:
        return Colors.brown;
      case ExpenseCategory.rent:
        return Colors.red;
      case ExpenseCategory.insurance:
        return Colors.cyan;
      case ExpenseCategory.other:
        return Colors.grey;
    }
  }

  IconData _getCategoryIcon() {
    switch (_expense.category) {
      case ExpenseCategory.transportation:
        return Icons.directions_car;
      case ExpenseCategory.materials:
        return Icons.inventory_2;
      case ExpenseCategory.tools:
        return Icons.build;
      case ExpenseCategory.maintenance:
        return Icons.settings;
      case ExpenseCategory.marketing:
        return Icons.campaign;
      case ExpenseCategory.office:
        return Icons.business;
      case ExpenseCategory.utilities:
        return Icons.electrical_services;
      case ExpenseCategory.salaries:
        return Icons.people;
      case ExpenseCategory.rent:
        return Icons.home;
      case ExpenseCategory.insurance:
        return Icons.security;
      case ExpenseCategory.other:
        return Icons.more_horiz;
    }
  }
}
