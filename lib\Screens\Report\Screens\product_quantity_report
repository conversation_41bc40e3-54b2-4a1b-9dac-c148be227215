// بسم الله الرحمن الرحيم توكلنا على الله

// ignore_for_file: prefer_const_constructors, unused_local_variable, deprecated_member_use

import 'package:flutter/material.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/model/product_model.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:firebase_database/firebase_database.dart';

class ProductQuantityReport extends StatelessWidget {
  const ProductQuantityReport({super.key});

  @override
  Widget build(BuildContext context) {
    // هنا بنبدأ بناء واجهة التقرير
    return Scaffold(
      appBar: AppBar(
        title: Text('تقرير الكميات'),
        backgroundColor: kMainColor,
      ),
      body: Center(
        child: FutureBuilder<List<ProductModel>>(
          // هنا بنجيب البيانات من قاعدة البيانات
          future: fetchProductData(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return CircularProgressIndicator(); // لو لسه البيانات مش جاهزة
            } else if (snapshot.hasError) {
              return Text('حدث خطأ: ${snapshot.error}'); // لو في خطأ
            }

            // هنا بنبدأ عرض البيانات
            final products = snapshot.data!;
            int count = 0;
            final reProducts = products;
            DateTime fromDate =
                DateTime.now().subtract(Duration(days: 30)); // المدة المحددة
            DateTime toDate = DateTime.now();
            int totalSell = 0;
            if (count == 0) {
              count++;
              for (var element in reProducts) {
                // هنا يجب حساب الكميات المتاحة
                // استخدام productStock كمؤشر على الكمية المتاحة
                totalSell += int.parse(element.productStock);
              }
            }
            return reProducts.isNotEmpty
                ? Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(left: 20, right: 20.0),
                        child: Container(
                          height: 120,
                          width: double.infinity,
                          decoration: BoxDecoration(
                            borderRadius:
                                const BorderRadius.all(Radius.circular(15)),
                            color: kMainColor.withOpacity(0.08),
                          ),
                          child: Center(
                            child: ListTile(
                              leading: Container(
                                height: 40,
                                width: 40,
                                decoration: const BoxDecoration(
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(80)),
                                  image: DecorationImage(
                                    image: AssetImage(
                                      'assets/images/ledger_total_sale.png',
                                    ),
                                  ),
                                ),
                              ),
                              title: Text(
                                "الكميات المباعة والمشتراة",
                                style: const TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              subtitle: Text('إجمالي المبيعات والمشتريات'),
                            ),
                          ),
                        ),
                      ),
                      Expanded(
                        child: ListView.builder(
                          physics: const AlwaysScrollableScrollPhysics(),
                          itemCount: reProducts.length,
                          itemBuilder: (context, index) {
                            return GestureDetector(
                              onTap: () {
                                // هنا يمكن إضافة وظيفة عند الضغط على المنتج
                              },
                              child: Column(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(20),
                                    width: context.width(),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Flexible(
                                              child: Text(
                                                reProducts[index].productName,
                                                style: const TextStyle(
                                                    fontSize: 16),
                                                maxLines: 2,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ),
                                            Text(
                                              'الكمية: ${reProducts[index].productStock}',
                                              style: const TextStyle(
                                                  color: Colors.black,
                                                  fontWeight: FontWeight.bold),
                                            ),
                                          ],
                                        ),
                                        const SizedBox(height: 10),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  )
                : Center(child: Text('لا توجد بيانات'));
          },
        ),
      ),
    );
  }

  Future<List<ProductModel>> fetchProductData() async {
    // بسم الله الرحمن الرحيم توكلنا على الله
    List<ProductModel> productList = [];

    // استرجاع البيانات من Firebase
    final DatabaseReference ref = FirebaseDatabase.instance.ref();
    // تعديل نوع المتغير ليتوافق مع DatabaseEvent
    DatabaseEvent event = await ref.once();
    // هنا يجب التعامل مع البيانات المسترجعة من قاعدة البيانات
    if (event.snapshot.value != null) {
      Map<dynamic, dynamic> jsonData =
          event.snapshot.value as Map<dynamic, dynamic>;
      jsonData.forEach((key, value) {
        if (value['Daily Transaction'] != null) {
          Map<dynamic, dynamic> transactionMap =
              value['Daily Transaction'] as Map<dynamic, dynamic>;
          transactionMap.forEach((transactionKey, transactionValue) {
            if (transactionValue['saleTransactionModel'] != null) {
              Map<dynamic, dynamic> saleTransactionModelMap =
                  transactionValue['saleTransactionModel']
                      as Map<dynamic, dynamic>;
              var products = saleTransactionModelMap['productList'];
              for (var product in products) {
                productList.add(ProductModel(
                  productName: product['productName'] ?? 'غير متوفر',
                  productStock: product['quantity']?.toString() ?? '0',
                  productCategory: product['category'] ?? 'غير متوفر',
                  size: product['size'] ?? 'غير متوفر',
                  color: product['color'] ?? 'غير متوفر',
                  productDescription: product['description'] ?? 'لا يوجد وصف',
                  weight: product['weight'] ?? '0',
                  capacity: product['capacity'] ?? '0',
                  type: product['type'] ?? 'غير متوفر',
                  brandName: product['brand'] ?? 'غير متوفر',
                  productCode: product['code'] ?? 'غير متوفر',
                  productUnit: product['unit'] ?? 'غير متوفر',
                  productSalePrice: product['salePrice'] ?? '0',
                  productPurchasePrice: product['purchasePrice'] ?? '0',
                  productDiscount: product['discount'] ?? '0',
                  productWholeSalePrice: product['wholeSalePrice'] ?? '0',
                  productDealerPrice: product['dealerPrice'] ?? '0',
                  productManufacturer: product['manufacturer'] ?? 'غير متوفر',
                  warehouseName: product['warehouseName'] ?? 'غير متوفر',
                  warehouseId: product['warehouseId'] ?? 'غير متوفر',
                  productPicture: product['picture'] ?? 'لا يوجد صورة',
                  serialNumber: product['serialNumber'] ?? [],
                  lowerStockAlert: product['lowerStockAlert'] ?? 0,
                  taxType: product['taxType'] ?? 'غير متوفر',
                  margin: product['margin'] ?? 0,
                  excTax: product['excTax'] ?? 0,
                  incTax: product['incTax'] ?? 0,
                  groupTaxName: product['groupTaxName'] ?? 'غير متوفر',
                  groupTaxRate: product['groupTaxRate'] ?? 0,
                  subTaxes: product['subTaxes'] ?? [],
                ));
              }
            }
          });
        }
      });
    }

    return productList;
  }
}
