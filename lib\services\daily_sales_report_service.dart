import 'dart:convert';
import 'package:firebase_database/firebase_database.dart';
import 'package:intl/intl.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/currency.dart';
import 'package:mobile_pos/model/transition_model.dart';
import 'package:mobile_pos/models/daily_sales_report_model.dart';

/// خدمة تقرير المبيعات اليومي
class DailySalesReportService {
  static final DailySalesReportService _instance =
      DailySalesReportService._internal();
  factory DailySalesReportService() => _instance;
  DailySalesReportService._internal();

  /// جلب تقرير المبيعات اليومي
  Future<DailySalesReportModel> getDailySalesReport(DateTime date) async {
    try {
      // تحويل التاريخ إلى تنسيق مناسب للمقارنة
      final dateString = DateFormat('yyyy-MM-dd').format(date);
      final startOfDay = DateTime(date.year, date.month, date.day);
      final endOfDay = DateTime(date.year, date.month, date.day, 23, 59, 59);

      // جلب بيانات المبيعات من Firebase
      final salesRef =
          FirebaseDatabase.instance.ref('$constUserId/Sales Transition');
      final snapshot = await salesRef.get();

      List<SalesTransitionModel> dailySales = [];

      if (snapshot.exists && snapshot.value != null) {
        final salesData = snapshot.value as Map<dynamic, dynamic>;

        // فلترة المبيعات حسب التاريخ
        salesData.forEach((key, value) {
          try {
            final saleData = Map<String, dynamic>.from(value as Map);
            final saleDate = DateTime.parse(saleData['purchaseDate'] ?? '');

            // التحقق من أن المبيعة في نفس اليوم
            if (saleDate
                    .isAfter(startOfDay.subtract(const Duration(seconds: 1))) &&
                saleDate.isBefore(endOfDay.add(const Duration(seconds: 1)))) {
              // إضافة المفتاح للمعاملة
              saleData['key'] = key;

              final transaction = SalesTransitionModel.fromJson(saleData);
              dailySales.add(transaction);
            }
          } catch (e) {
            // تجاهل المعاملات التي بها أخطاء في التحويل
            print('خطأ في تحويل معاملة المبيعات: $e');
          }
        });
      }

      // ترتيب المبيعات حسب الوقت (الأحدث أولاً)
      dailySales.sort((a, b) => DateTime.parse(b.purchaseDate)
          .compareTo(DateTime.parse(a.purchaseDate)));

      // إنشاء التقرير
      return DailySalesReportModel.fromTransactions(date, dailySales);
    } catch (e) {
      print('خطأ في جلب تقرير المبيعات اليومي: $e');

      // إرجاع تقرير فارغ في حالة الخطأ
      return DailySalesReportModel.fromTransactions(date, []);
    }
  }

  /// جلب المبيعات لفترة زمنية محددة
  Future<List<SalesTransitionModel>> getSalesForDateRange(
      DateTime startDate, DateTime endDate) async {
    try {
      final salesRef =
          FirebaseDatabase.instance.ref('$constUserId/Sales Transition');
      final snapshot = await salesRef.get();

      List<SalesTransitionModel> sales = [];

      if (snapshot.exists && snapshot.value != null) {
        final salesData = snapshot.value as Map<dynamic, dynamic>;

        salesData.forEach((key, value) {
          try {
            final saleData = Map<String, dynamic>.from(value as Map);
            final saleDate = DateTime.parse(saleData['purchaseDate'] ?? '');

            // التحقق من أن المبيعة في الفترة المحددة
            if (saleDate
                    .isAfter(startDate.subtract(const Duration(seconds: 1))) &&
                saleDate.isBefore(endDate.add(const Duration(seconds: 1)))) {
              saleData['key'] = key;
              final transaction = SalesTransitionModel.fromJson(saleData);
              sales.add(transaction);
            }
          } catch (e) {
            print('خطأ في تحويل معاملة المبيعات: $e');
          }
        });
      }

      return sales;
    } catch (e) {
      print('خطأ في جلب المبيعات للفترة المحددة: $e');
      return [];
    }
  }

  /// حساب إحصائيات سريعة لتاريخ معين
  Future<Map<String, dynamic>> getQuickStats(DateTime date) async {
    try {
      final report = await getDailySalesReport(date);

      return {
        'totalInvoices': report.summary.totalInvoices,
        'totalSales': report.summary.totalSales,
        'cashSales': report.paymentSummary.cashSales,
        'creditSales': report.paymentSummary.creditSales,
        'topSellingProduct': report.productsSummary.isNotEmpty
            ? report.productsSummary.first.productName
            : 'لا توجد مبيعات',
      };
    } catch (e) {
      print('خطأ في حساب الإحصائيات السريعة: $e');
      return {
        'totalInvoices': 0,
        'totalSales': 0.0,
        'cashSales': 0.0,
        'creditSales': 0.0,
        'topSellingProduct': 'لا توجد مبيعات',
      };
    }
  }

  /// التحقق من وجود مبيعات في تاريخ معين
  Future<bool> hasSalesOnDate(DateTime date) async {
    try {
      final stats = await getQuickStats(date);
      return stats['totalInvoices'] > 0;
    } catch (e) {
      return false;
    }
  }
}
