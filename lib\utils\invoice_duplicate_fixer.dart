// ignore_for_file: unnecessary_brace_in_string_interps

// تعليقات توضيحية للتحسينات التي تمت إضافتها
// تم تحسين معالجة الأخطاء في هذا الملف للتعامل مع أنواع البيانات المختلفة في Firebase
// وتجنب حدوث خروج مفاجئ (crash) عند محاولة تحويل البيانات.
//
// التحسينات الرئيسية:
// 1. تحسين التعامل مع أنواع البيانات المختلفة في Firebase (int, String, Map, etc.)
// 2. استخدام int.tryParse بدلاً من التحويل المباشر لتجنب الأخطاء
// 3. إضافة قيم افتراضية آمنة في حالة فشل التحويل
// 4. تحسين معالجة الأخطاء وإضافة المزيد من رسائل التصحيح

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/currency.dart';
import 'dart:math' as math;
import 'dart:async'; // إضافة استيراد لـ TimeoutException
import 'package:firebase_core/firebase_core.dart'; // إضافة استيراد لـ FirebaseException

/// نوع الفاتورة (عادية أو مصححة)
enum InvoiceType {
  /// فاتورة عادية
  normal,

  /// فاتورة مصححة
  corrected
}

/// نوع تقرير حالة إصلاح الفواتير
enum FixStatus {
  scanSales, // جاري فحص فواتير المبيعات
  scanDaily, // جاري فحص المعاملات اليومية
  scanDue, // جاري فحص المعاملات الآجلة
  analyzing, // جاري تحليل البيانات
  fixing, // جاري إصلاح الفواتير المكررة
  completed // تم الانتهاء
}

/// فئة تقرير حالة إصلاح الفواتير
class FixStatusReport {
  final FixStatus status;
  final String message;
  final double progress;
  final int fixedCount;
  final int totalInvoices;
  final int scannedInvoices;
  final int duplicatesFound;
  final int currentStep;
  final int totalSteps;
  final String currentOperation;
  final String nextOperation;

  FixStatusReport({
    required this.status,
    required this.message,
    required this.progress,
    this.fixedCount = 0,
    this.totalInvoices = 0,
    this.scannedInvoices = 0,
    this.duplicatesFound = 0,
    this.currentStep = 0,
    this.totalSteps =
        6, // إجمالي عدد الخطوات (فحص المبيعات، فحص اليومية، فحص الآجلة، تحليل، إصلاح، انتهاء)
    this.currentOperation = '',
    this.nextOperation = '',
  });

  /// الحصول على وصف الخطوة الحالية
  String get stepDescription {
    switch (status) {
      case FixStatus.scanSales:
        return 'فحص فواتير المبيعات';
      case FixStatus.scanDaily:
        return 'فحص المعاملات اليومية';
      case FixStatus.scanDue:
        return 'فحص المعاملات الآجلة';
      case FixStatus.analyzing:
        return 'تحليل البيانات';
      case FixStatus.fixing:
        return 'إصلاح الفواتير المكررة';
      case FixStatus.completed:
        return 'اكتمال العملية';
    }
  }

  /// الحصول على وصف الخطوة التالية
  String get nextStepDescription {
    switch (status) {
      case FixStatus.scanSales:
        return 'فحص المعاملات اليومية';
      case FixStatus.scanDaily:
        return 'فحص المعاملات الآجلة';
      case FixStatus.scanDue:
        return 'تحليل البيانات';
      case FixStatus.analyzing:
        return 'إصلاح الفواتير المكررة';
      case FixStatus.fixing:
        return 'اكتمال العملية';
      case FixStatus.completed:
        return '';
    }
  }
}

/// فئة بيانات الفاتورة
class InvoiceData {
  final String key;
  final String invoiceNumber;
  final String customerName;
  final String customerPhone;
  final String tableType;

  InvoiceData({
    required this.key,
    required this.invoiceNumber,
    required this.customerName,
    required this.customerPhone,
    required this.tableType,
  });
}

/// فئة لإصلاح أرقام الفواتير المكررة
class InvoiceDuplicateFixer {
  /// جمع الفواتير من جدول معين على دفعات
  /// تم إضافة هذه الدالة لتحسين استهلاك الذاكرة عند جمع عدد كبير من الفواتير
  static Future<void> _collectInvoicesInBatches({
    required DatabaseReference ref,
    required String tableType,
    required List<InvoiceData> allInvoices,
    required int batchSize,
    Function(FixStatusReport)? onStatusUpdate,
  }) async {
    try {
      debugPrint('بدء جمع الفواتير من جدول $tableType على دفعات');

      // الحصول على عدد السجلات الإجمالي (بدون تحميل البيانات)
      final countSnapshot = await ref.get();
      if (!countSnapshot.exists) {
        debugPrint('لا توجد بيانات في جدول $tableType');
        return;
      }

      // تقدير عدد السجلات
      final int totalRecords = countSnapshot.children.length;
      debugPrint('عدد السجلات التقديري في جدول $tableType: $totalRecords');

      // تحديد عدد الدفعات
      final int batchCount = (totalRecords / batchSize).ceil();
      debugPrint('سيتم جمع البيانات على $batchCount دفعة');

      // جمع البيانات على دفعات
      int processedRecords = 0;

      // استخدام طريقة أبسط لجمع البيانات لتجنب مشاكل الترتيب والتصفية
      // هذه الطريقة أبطأ ولكنها أكثر موثوقية
      final allData = countSnapshot.children.toList();

      // معالجة البيانات على دفعات
      for (int i = 0; i < allData.length; i += batchSize) {
        final int end =
            (i + batchSize < allData.length) ? i + batchSize : allData.length;
        final batch = allData.sublist(i, end);

        // معالجة الدفعة الحالية
        int batchCount = 0;
        for (var child in batch) {
          // استخراج رقم الفاتورة
          String invoiceNumber = '';
          if (tableType == 'daily') {
            invoiceNumber = child.child('id').value?.toString() ?? '';
          } else {
            invoiceNumber =
                child.child('invoiceNumber').value?.toString() ?? '';
          }

          // تخطي السجلات بدون رقم فاتورة
          if (invoiceNumber.isEmpty) continue;

          // إضافة الفاتورة إلى القائمة
          allInvoices.add(InvoiceData(
            key: child.key ?? '',
            invoiceNumber: invoiceNumber,
            customerName: child.child('customerName').value?.toString() ?? '',
            customerPhone: child.child('customerPhone').value?.toString() ?? '',
            tableType: tableType,
          ));

          batchCount++;
        }

        // تحديث عدد السجلات المعالجة
        processedRecords += batchCount;

        // إرسال تقرير بالتقدم
        if (onStatusUpdate != null) {
          onStatusUpdate(FixStatusReport(
            status: _getStatusForTableType(tableType),
            message:
                'جاري جمع الفواتير من جدول $tableType (${processedRecords} من ${totalRecords})',
            progress: _getProgressForTableType(
                tableType, processedRecords, totalRecords),
            currentStep: _getStepForTableType(tableType),
            totalSteps: 6,
            currentOperation: 'فحص جدول $tableType',
            nextOperation: _getNextOperationForTableType(tableType),
            scannedInvoices: processedRecords,
            totalInvoices: totalRecords,
          ));
        }

        // إعطاء فرصة لجامع القمامة لتحرير الذاكرة
        await Future.delayed(const Duration(milliseconds: 20));
      }

      debugPrint('تم جمع ${allInvoices.length} فاتورة من جدول $tableType');
    } catch (e) {
      debugPrint('خطأ في جمع الفواتير من جدول $tableType: $e');
    }
  }

  /// الحصول على حالة التقرير حسب نوع الجدول
  static FixStatus _getStatusForTableType(String tableType) {
    switch (tableType) {
      case 'sales':
        return FixStatus.scanSales;
      case 'daily':
        return FixStatus.scanDaily;
      case 'due':
        return FixStatus.scanDue;
      default:
        return FixStatus.scanSales;
    }
  }

  /// الحصول على نسبة التقدم حسب نوع الجدول
  static double _getProgressForTableType(
      String tableType, int processed, int total) {
    switch (tableType) {
      case 'sales':
        return 0.1 + (0.2 * processed / total);
      case 'daily':
        return 0.3 + (0.2 * processed / total);
      case 'due':
        return 0.5 + (0.2 * processed / total);
      default:
        return 0.1;
    }
  }

  /// الحصول على رقم الخطوة حسب نوع الجدول
  static int _getStepForTableType(String tableType) {
    switch (tableType) {
      case 'sales':
        return 1;
      case 'daily':
        return 2;
      case 'due':
        return 3;
      default:
        return 1;
    }
  }

  /// الحصول على العملية التالية حسب نوع الجدول
  static String _getNextOperationForTableType(String tableType) {
    switch (tableType) {
      case 'sales':
        return 'فحص المعاملات اليومية';
      case 'daily':
        return 'فحص المعاملات الآجلة';
      case 'due':
        return 'تحليل البيانات';
      default:
        return 'فحص المعاملات اليومية';
    }
  }

  /// الحصول على مسار الجدول حسب نوع الجدول
  static String _getTablePath(String tableType) {
    switch (tableType) {
      case 'sales':
        return 'Sales Transition';
      case 'daily':
        return 'Daily Transaction';
      case 'due':
        return 'Due Transaction';
      default:
        return 'Sales Transition';
    }
  }

  /// الحصول على مرجع الجدول حسب نوع الجدول
  static DatabaseReference _getTableRef(String tableType) {
    // استخدام constUserId بدلاً من FirebaseAuth.instance.currentUser!.uid
    // لضمان التوافق مع بقية الكود
    switch (tableType) {
      case 'sales':
        return FirebaseDatabase.instance.ref("$constUserId/Sales Transition");
      case 'daily':
        return FirebaseDatabase.instance.ref("$constUserId/Daily Transaction");
      case 'due':
        return FirebaseDatabase.instance.ref("$constUserId/Due Transaction");
      default:
        return FirebaseDatabase.instance.ref("$constUserId/Sales Transition");
    }
  }

  /// تحويل رقم فاتورة طويل إلى رقم فاتورة قصير
  /// يمكن استدعاء هذه الدالة من أي مكان في التطبيق
  /// تم تحديثها لإنتاج أرقام فواتير أقصر (6-8 أحرف) بدون بادئة
  static String shortenInvoiceNumber(String longInvoiceNumber) {
    try {
      // تقسيم الرقم الطويل إلى أجزاء
      final parts = longInvoiceNumber.split('_');
      if (parts.length < 3) {
        // إذا كان التنسيق غير صحيح، نعيد الرقم كما هو
        return longInvoiceNumber;
      }

      // استخراج التاريخ والعداد
      final dateCode = parts[1];
      final counter = parts.length > 2 ? parts[2] : '';

      // استخدام فقط الشهر واليوم من التاريخ (MMDD)
      String shortDateCode;
      if (dateCode.length >= 8) {
        // استخراج الشهر واليوم من التاريخ بتنسيق YYYYMMDD
        shortDateCode = dateCode.substring(4, 8);
      } else {
        // إذا كان التنسيق غير معروف، نستخدم التاريخ كما هو
        shortDateCode = dateCode.length > 4
            ? dateCode.substring(dateCode.length - 4)
            : dateCode;
      }

      // إنشاء رقم فاتورة قصير (6-8 أحرف)
      // استخدام تنسيق: MMddXXXX حيث MMDD هو الشهر واليوم وXXXX هو رقم العداد
      final shortInvoiceNumber = '$shortDateCode${counter.padLeft(4, '0')}';

      return shortInvoiceNumber;
    } catch (e) {
      debugPrint('خطأ في تحويل رقم الفاتورة: $e');
      // في حالة الخطأ، نعيد الرقم كما هو
      return longInvoiceNumber;
    }
  }

  /// إصلاح أرقام الفواتير المكررة
  /// تم تحسين الدالة لتتبع التقدم بشكل أفضل وعرض رسائل السجل
  /// تم تحسين إدارة الذاكرة ومعالجة البيانات على دفعات لتجنب استنفاد الذاكرة
  /// تم إضافة معلمة maxInvoices لتحديد الحد الأقصى لعدد الفواتير التي سيتم معالجتها
  static Future<int> fixDuplicateInvoices({
    Function(FixStatusReport)? onStatusUpdate,
    int batchSize = 50, // حجم الدفعة الواحدة للمعالجة
    int maxInvoices =
        200, // الحد الأقصى لعدد الفواتير التي سيتم معالجتها في كل مرة
  }) async {
    try {
      // 1. إرسال تقرير بدء الفحص
      debugPrint('بدء عملية فحص وإصلاح الفواتير المكررة');
      if (onStatusUpdate != null) {
        onStatusUpdate(FixStatusReport(
          status: FixStatus.scanSales,
          message: 'جاري فحص الفواتير في جدول المبيعات...',
          progress: 0.1,
          currentStep: 1,
          totalSteps: 6,
          currentOperation: 'فحص فواتير المبيعات',
          nextOperation: 'فحص المعاملات اليومية',
        ));
      }

      // 2. تحضير المراجع لجداول قاعدة البيانات
      final salesRef =
          FirebaseDatabase.instance.ref("$constUserId/Sales Transition");
      final dailyRef =
          FirebaseDatabase.instance.ref("$constUserId/Daily Transaction");
      final dueRef =
          FirebaseDatabase.instance.ref("$constUserId/Due Transaction");

      // 3. جمع البيانات من جميع الجداول على دفعات
      final allInvoices = <InvoiceData>[];

      // 3.1 جمع البيانات من جدول Sales Transition على دفعات
      await _collectInvoicesInBatches(
        ref: salesRef,
        tableType: 'sales',
        allInvoices: allInvoices,
        batchSize: batchSize,
        onStatusUpdate: onStatusUpdate,
      );

      // 3.2 جمع البيانات من جدول Daily Transaction على دفعات
      await _collectInvoicesInBatches(
        ref: dailyRef,
        tableType: 'daily',
        allInvoices: allInvoices,
        batchSize: batchSize,
        onStatusUpdate: onStatusUpdate,
      );

      // 3.3 جمع البيانات من جدول Due Transaction على دفعات
      await _collectInvoicesInBatches(
        ref: dueRef,
        tableType: 'due',
        allInvoices: allInvoices,
        batchSize: batchSize,
        onStatusUpdate: onStatusUpdate,
      );

      // 4. إرسال تقرير بعدد الفواتير التي تم جمعها
      final totalInvoicesCount = allInvoices.length;
      debugPrint('إجمالي الفواتير التي تم جمعها: $totalInvoicesCount');

      // 5. تنظيم الفواتير حسب رقم الفاتورة
      final invoiceMap = <String, List<Map<String, dynamic>>>{};

      // تحويل قائمة InvoiceData إلى Map للمعالجة
      for (var invoice in allInvoices) {
        if (!invoiceMap.containsKey(invoice.invoiceNumber)) {
          invoiceMap[invoice.invoiceNumber] = [];
        }

        invoiceMap[invoice.invoiceNumber]!.add({
          'type': invoice.tableType,
          'key': invoice.key,
          'path':
              "$constUserId/${_getTablePath(invoice.tableType)}/${invoice.key}",
          'customerName': invoice.customerName,
          'customerPhone': invoice.customerPhone,
          'ref': _getTableRef(invoice.tableType).child(invoice.key),
        });
      }

      // تحرير الذاكرة بعد الانتهاء من استخدام قائمة allInvoices
      allInvoices.clear();

      // إرسال تقرير بدء تحليل الفواتير
      if (onStatusUpdate != null) {
        onStatusUpdate(FixStatusReport(
          status: FixStatus.analyzing,
          message: 'جاري تحليل الفواتير للبحث عن التكرارات...',
          progress: 0.7,
          currentStep: 4,
          totalSteps: 6,
          currentOperation: 'تحليل البيانات',
          nextOperation: 'إصلاح الفواتير المكررة',
          scannedInvoices: totalInvoicesCount,
          totalInvoices: totalInvoicesCount,
        ));
      }

      // 2. تحليل الفواتير للبحث عن التكرارات
      int fixedCount = 0;
      List<Map<String, dynamic>> duplicateEntries = [];
      int potentialDuplicatesCount = 0;

      // قائمة لتخزين مجموعات الفواتير المرتبطة
      List<List<Map<String, dynamic>>> relatedInvoiceGroups = [];

      // قاموس لتخزين مجموعات العملاء لكل رقم فاتورة
      Map<String, Map<String, List<Map<String, dynamic>>>>
          invoiceCustomerGroups = {};

      // تحليل الفواتير وتجميعها حسب رقم الفاتورة والعميل
      for (var invoiceNumber in invoiceMap.keys) {
        var references = invoiceMap[invoiceNumber]!;

        if (references.length <= 1) {
          continue; // لا يوجد تكرار
        }

        potentialDuplicatesCount++;
        debugPrint(
            'فحص الفاتورة رقم $invoiceNumber (${references.length} سجل)');

        // تجميع المراجع حسب العميل (الاسم ورقم الهاتف)
        Map<String, List<Map<String, dynamic>>> customerGroups = {};

        for (var ref in references) {
          String customerName = ref['customerName'] as String;
          String customerPhone = ref['customerPhone'] as String;
          String customerKey = '${customerName}_${customerPhone}';

          if (!customerGroups.containsKey(customerKey)) {
            customerGroups[customerKey] = [];
          }

          customerGroups[customerKey]!.add(ref);
        }

        invoiceCustomerGroups[invoiceNumber] = customerGroups;
        debugPrint(
            '  تم تجميع الفاتورة رقم $invoiceNumber حسب ${customerGroups.length} عميل');
      }

      // تحليل مجموعات العملاء لكل رقم فاتورة
      for (var invoiceNumber in invoiceCustomerGroups.keys) {
        var customerGroups = invoiceCustomerGroups[invoiceNumber]!;

        // إذا كان هناك أكثر من عميل واحد لنفس رقم الفاتورة، فهذا يعني وجود تكرار حقيقي
        if (customerGroups.length > 1) {
          debugPrint(
              '!! تم العثور على تكرار حقيقي: رقم الفاتورة $invoiceNumber مستخدم لـ ${customerGroups.length} عميل مختلف');

          // نحتفظ بالعميل الأول ونعتبر الباقي تكرارات
          var customerKeys = customerGroups.keys.toList();
          var firstCustomerKey = customerKeys[0];
          var firstCustomerRefs = customerGroups[firstCustomerKey]!;

          // إضافة مراجع العميل الأول إلى مجموعة مرتبطة
          List<Map<String, dynamic>> relatedGroup =
              List.from(firstCustomerRefs);
          relatedInvoiceGroups.add(relatedGroup);
          int groupId = relatedInvoiceGroups.length - 1;

          // معالجة العملاء الآخرين كتكرارات
          for (int i = 1; i < customerKeys.length; i++) {
            var customerKey = customerKeys[i];
            var customerRefs = customerGroups[customerKey]!;

            debugPrint(
                '  معالجة العميل $customerKey (${customerRefs.length} سجل) كتكرار');

            // إضافة جميع مراجع هذا العميل إلى قائمة التكرارات
            for (var ref in customerRefs) {
              duplicateEntries.add({
                'ref': ref,
                'invoiceNumber': invoiceNumber,
                'customerKey': customerKey,
                'type': ref['type'] as String,
                'index': i,
                'groupId': groupId,
                'isDifferentCustomer': true // علامة تشير إلى أن هذا لعميل مختلف
              });
              debugPrint(
                  '    إضافة سجل من نوع ${ref['type']} إلى قائمة التكرارات');
            }
          }
        } else {
          // إذا كان هناك عميل واحد فقط، نتحقق من وجود تكرارات في نفس نوع الجدول
          for (var customerKey in customerGroups.keys) {
            var customerRefs = customerGroups[customerKey]!;

            // تجميع المراجع حسب نوع الجدول
            Map<String, List<Map<String, dynamic>>> typeGroups = {};

            for (var ref in customerRefs) {
              String type = ref['type'] as String;

              if (!typeGroups.containsKey(type)) {
                typeGroups[type] = [];
              }

              typeGroups[type]!.add(ref);
            }

            // التحقق من وجود تكرارات في نفس نوع الجدول
            bool hasDuplicatesInSameType = false;
            for (var type in typeGroups.keys) {
              if (typeGroups[type]!.length > 1) {
                hasDuplicatesInSameType = true;
                break;
              }
            }

            if (hasDuplicatesInSameType) {
              debugPrint(
                  '  تم العثور على تكرارات في نفس نوع الجدول للعميل $customerKey');

              // إنشاء مجموعة للفواتير المرتبطة
              List<Map<String, dynamic>> relatedGroup = [];

              // إضافة جميع المراجع من جميع الأنواع إلى المجموعة
              for (var type in typeGroups.keys) {
                var refs = typeGroups[type]!;

                // إذا كان هناك أكثر من مرجع واحد من نفس النوع
                if (refs.length > 1) {
                  debugPrint(
                      '    تم العثور على ${refs.length} سجل من نوع $type لنفس العميل');

                  // نحتفظ بالمرجع الأول ونضيف المراجع المكررة إلى قائمة التكرارات
                  relatedGroup
                      .add(refs[0]); // إضافة المرجع الأول إلى المجموعة المرتبطة

                  for (var i = 1; i < refs.length; i++) {
                    duplicateEntries.add({
                      'ref': refs[i],
                      'invoiceNumber': invoiceNumber,
                      'customerKey': customerKey,
                      'type': type,
                      'index': i,
                      'groupId':
                          relatedInvoiceGroups.length, // إضافة معرف المجموعة
                      'isDifferentCustomer': false // نفس العميل
                    });
                    relatedGroup.add(refs[i]);
                    debugPrint(
                        '      إضافة السجل رقم $i من نوع $type إلى قائمة التكرارات');
                  }
                } else if (refs.isNotEmpty) {
                  // إضافة المرجع الوحيد من هذا النوع إلى المجموعة المرتبطة
                  relatedGroup.add(refs[0]);
                }
              }

              // إضافة المجموعة إلى قائمة المجموعات المرتبطة
              if (relatedGroup.isNotEmpty) {
                relatedInvoiceGroups.add(relatedGroup);
                debugPrint(
                    '    تم إنشاء مجموعة مرتبطة تحتوي على ${relatedGroup.length} سجل');
              }
            } else {
              // لا توجد تكرارات في نفس نوع الجدول، لكن هناك سجلات في جداول مختلفة
              // هذه تعتبر فواتير مرتبطة وليست تكرارات
              debugPrint(
                  '  الفاتورة رقم $invoiceNumber للعميل $customerKey موجودة في ${typeGroups.length} جدول مختلف (فواتير مرتبطة)');
            }
          }
        }
      }

      debugPrint(
          'تم العثور على ${duplicateEntries.length} فاتورة مكررة من أصل $potentialDuplicatesCount فاتورة محتملة');

      // إرسال تقرير بنتائج الفحص
      if (onStatusUpdate != null) {
        if (duplicateEntries.isEmpty) {
          onStatusUpdate(FixStatusReport(
            status: FixStatus.completed,
            message: 'لا توجد فواتير مكررة',
            progress: 1.0,
            currentStep: 6,
            totalSteps: 6,
            currentOperation: 'اكتمال العملية',
            nextOperation: '',
            scannedInvoices: totalInvoicesCount,
            totalInvoices: totalInvoicesCount,
            duplicatesFound: 0,
          ));
          return 0;
        } else {
          onStatusUpdate(FixStatusReport(
            status: FixStatus.fixing,
            message:
                'تم العثور على ${duplicateEntries.length} فاتورة مكررة. جاري الإصلاح...',
            progress: 0.8,
            currentStep: 5,
            totalSteps: 6,
            currentOperation: 'إصلاح الفواتير المكررة',
            nextOperation: 'اكتمال العملية',
            scannedInvoices: totalInvoicesCount,
            totalInvoices: totalInvoicesCount,
            duplicatesFound: duplicateEntries.length,
          ));
        }
      }

      // 3. إصلاح الفواتير المكررة
      int totalDuplicates = duplicateEntries.length;
      int processedCount = 0;

      // تنظيم الفواتير المكررة حسب المجموعة
      Map<int, List<Map<String, dynamic>>> groupedEntries = {};
      for (var entry in duplicateEntries) {
        int groupId = entry['groupId'] as int;
        if (!groupedEntries.containsKey(groupId)) {
          groupedEntries[groupId] = [];
        }
        groupedEntries[groupId]!.add(entry);
      }

      // تطبيق الحد الأقصى لعدد الفواتير التي سيتم معالجتها
      int totalEntriesToProcess = 0;
      List<int> groupIdsToProcess = [];

      // حساب عدد الفواتير التي سيتم معالجتها وتحديد المجموعات
      for (var groupId in groupedEntries.keys) {
        int entriesInGroup = groupedEntries[groupId]!.length;

        // إذا كان إضافة هذه المجموعة سيتجاوز الحد الأقصى، نتوقف
        if (totalEntriesToProcess + entriesInGroup > maxInvoices) {
          break;
        }

        totalEntriesToProcess += entriesInGroup;
        groupIdsToProcess.add(groupId);
      }

      debugPrint(
          'سيتم معالجة $totalEntriesToProcess فاتورة من أصل $totalDuplicates (الحد الأقصى: $maxInvoices)');

      // تحديث إجمالي عدد الفواتير التي سيتم معالجتها
      totalDuplicates = totalEntriesToProcess;

      debugPrint(
          'تم تنظيم الفواتير المكررة في ${groupedEntries.length} مجموعة');

      // معالجة كل مجموعة من الفواتير المرتبطة
      int groupIndex = 0;
      for (var groupId in groupIdsToProcess) {
        var entriesInGroup = groupedEntries[groupId]!;
        groupIndex++;

        // التحقق مما إذا كانت المجموعة تحتوي على فواتير لعملاء مختلفين
        bool hasDifferentCustomers =
            entriesInGroup.any((entry) => entry['isDifferentCustomer'] == true);

        // توليد رقم فاتورة جديد أساسي للمجموعة
        String baseInvoiceNumber = await _generateNewInvoiceNumber();

        if (hasDifferentCustomers) {
          debugPrint(
              'معالجة المجموعة $groupIndex: توليد رقم فاتورة أساسي $baseInvoiceNumber لـ ${entriesInGroup.length} فاتورة لعملاء مختلفين');
        } else {
          debugPrint(
              'معالجة المجموعة $groupIndex: توليد رقم فاتورة أساسي $baseInvoiceNumber لـ ${entriesInGroup.length} فاتورة مكررة لنفس العميل');
        }

        // تحديث جميع الفواتير في المجموعة مع زيادة الرقم لكل فاتورة
        for (var i = 0; i < entriesInGroup.length; i++) {
          var entry = entriesInGroup[i];
          var ref = entry['ref'] as Map<String, dynamic>;
          var oldInvoiceNumber = entry['invoiceNumber'] as String;
          var customerKey = entry['customerKey'] as String;
          var type = entry['type'] as String;
          var index = entry['index'] as int;
          var isDifferentCustomer =
              entry['isDifferentCustomer'] as bool? ?? false;

          // توليد رقم فاتورة فريد لكل فاتورة في المجموعة عن طريق زيادة الرقم الأساسي
          String newInvoiceNumber;
          if (i == 0) {
            // استخدام الرقم الأساسي للفاتورة الأولى
            newInvoiceNumber = baseInvoiceNumber;
          } else {
            // زيادة الرقم بمقدار 1 لكل فاتورة لاحقة
            newInvoiceNumber = _incrementInvoiceNumber(baseInvoiceNumber, i);
          }

          debugPrint(
              'إصلاح الفاتورة المكررة: العميل=$customerKey، النوع=$type، الفاتورة القديمة=$oldInvoiceNumber، الفاتورة الجديدة=$newInvoiceNumber، المؤشر=$index');

          // قائمة بالفواتير المشكلة التي يجب تجاوزها
          final List<String> problematicInvoices = [
            '-OFO7DSFYFq4NbomP-IF', // الفاتورة التي تسبب مشكلة استنفاد الذاكرة
          ];

          // التحقق مما إذا كانت الفاتورة الحالية في قائمة الفواتير المشكلة
          bool updateSuccess;
          if (problematicInvoices.contains(ref['key'])) {
            debugPrint('⚠️ تجاوز الفاتورة المشكلة: ${ref['key']}');
            // نتظاهر بأن العملية نجحت لتجاوز هذه الفاتورة
            updateSuccess = true;
          } else {
            // تحديث المرجع المكرر
            updateSuccess =
                await _updateInvoiceReference(ref, newInvoiceNumber);
          }

          if (updateSuccess) {
            fixedCount++;
            debugPrint('✓ تم تحديث الفاتورة بنجاح');
          } else {
            debugPrint('✗ فشل تحديث الفاتورة');
          }

          // عرض رسالة السجل
          String logMessage =
              'تم إصلاح فاتورة مكررة: $oldInvoiceNumber → $newInvoiceNumber';
          if (isDifferentCustomer) {
            logMessage =
                'تم إصلاح فاتورة مكررة لعميل مختلف: $oldInvoiceNumber → $newInvoiceNumber';
          }
          debugPrint(logMessage);

          // إرسال تقرير تقدم الإصلاح
          processedCount++;
          if (onStatusUpdate != null) {
            double progress = 0.8 + (0.2 * (processedCount / totalDuplicates));
            onStatusUpdate(FixStatusReport(
              status: FixStatus.fixing,
              message:
                  'جاري إصلاح المجموعة $groupIndex: الفاتورة $processedCount من $totalDuplicates: $logMessage',
              progress: progress > 1.0 ? 1.0 : progress,
              fixedCount: fixedCount,
              currentStep: 5,
              totalSteps: 6,
              currentOperation: 'إصلاح الفواتير المكررة',
              nextOperation: 'اكتمال العملية',
              scannedInvoices: totalInvoicesCount,
              totalInvoices: totalInvoicesCount,
              duplicatesFound: duplicateEntries.length,
            ));
          }
        }

        // تحديث الفواتير المرتبطة في المجموعة
        if (groupId < relatedInvoiceGroups.length) {
          var relatedGroup = relatedInvoiceGroups[groupId];

          // إذا كانت المجموعة تحتوي على فواتير لعملاء مختلفين، لا نقوم بتحديث الفواتير المرتبطة للعميل الأول
          // لأننا نريد الحفاظ على رقم الفاتورة الأصلي للعميل الأول
          if (!hasDifferentCustomers) {
            for (var relatedRef in relatedGroup) {
              // تخطي الفواتير التي تم تحديثها بالفعل (المكررة)
              bool isAlreadyProcessed = false;
              for (var entry in entriesInGroup) {
                if (entry['ref'] == relatedRef) {
                  isAlreadyProcessed = true;
                  break;
                }
              }

              if (!isAlreadyProcessed) {
                var type = relatedRef['type'] as String;
                var path = relatedRef['path'] as String;
                debugPrint(
                    'تحديث فاتورة مرتبطة: النوع=$type، المسار=$path، الفاتورة الجديدة=$baseInvoiceNumber');

                // قائمة بالفواتير المشكلة التي يجب تجاوزها
                final List<String> problematicInvoices = [
                  '-OFO7DSFYFq4NbomP-IF', // الفاتورة التي تسبب مشكلة استنفاد الذاكرة
                ];

                // التحقق مما إذا كانت الفاتورة الحالية في قائمة الفواتير المشكلة
                bool updateSuccess;
                if (problematicInvoices.contains(relatedRef['key'])) {
                  debugPrint(
                      '⚠️ تجاوز الفاتورة المرتبطة المشكلة: ${relatedRef['key']}');
                  // نتظاهر بأن العملية نجحت لتجاوز هذه الفاتورة
                  updateSuccess = true;
                } else {
                  // تحديث المرجع المرتبط
                  updateSuccess = await _updateInvoiceReference(
                      relatedRef, baseInvoiceNumber);
                }

                if (updateSuccess) {
                  debugPrint('✓ تم تحديث الفاتورة المرتبطة بنجاح');
                } else {
                  debugPrint('✗ فشل تحديث الفاتورة المرتبطة');
                }
              }
            }
          } else {
            debugPrint(
                'تم تخطي تحديث الفواتير المرتبطة للعميل الأول لأن المجموعة تحتوي على فواتير لعملاء مختلفين');
          }
        }
      }

      debugPrint(
          'تم إصلاح $fixedCount فاتورة مكررة من أصل $totalEntriesToProcess (من إجمالي ${duplicateEntries.length} فاتورة مكررة)');

      // إرسال تقرير الانتهاء
      if (onStatusUpdate != null) {
        // تحديد ما إذا كانت هناك فواتير متبقية لم تتم معالجتها
        bool hasMoreInvoices = totalEntriesToProcess < duplicateEntries.length;

        onStatusUpdate(FixStatusReport(
          status: FixStatus.completed,
          message: fixedCount > 0
              ? hasMoreInvoices
                  ? 'تم إصلاح $fixedCount فاتورة مكررة بنجاح. هناك ${duplicateEntries.length - totalEntriesToProcess} فاتورة متبقية.'
                  : 'تم إصلاح $fixedCount فاتورة مكررة بنجاح'
              : 'لا توجد فواتير مكررة',
          progress: 1.0,
          fixedCount: fixedCount,
          currentStep: 6,
          totalSteps: 6,
          currentOperation: 'اكتمال العملية',
          nextOperation: hasMoreInvoices
              ? 'تشغيل الأداة مرة أخرى لمعالجة الفواتير المتبقية'
              : '',
          scannedInvoices: totalInvoicesCount,
          totalInvoices: totalInvoicesCount,
          duplicatesFound: duplicateEntries.length,
        ));
      }

      return fixedCount;
    } catch (e) {
      debugPrint('خطأ في إصلاح أرقام الفواتير المكررة: $e');
      if (onStatusUpdate != null) {
        onStatusUpdate(FixStatusReport(
          status: FixStatus.completed,
          message: 'حدث خطأ أثناء إصلاح الفواتير المكررة: $e',
          progress: 1.0,
          fixedCount: -1,
          currentStep: 6,
          totalSteps: 6,
          currentOperation: 'حدث خطأ',
          nextOperation: '',
        ));
      }
      return -1;
    }
  }

  /// توليد رقم فاتورة جديد باستخدام عداد مركزي في Firebase
  /// يستخدم معاملات ذرية لضمان عدم تكرار الأرقام
  /// تم تحسين الدالة لإنتاج أرقام فواتير قصيرة (6-8 أحرف) بتنسيق موحد
  ///
  /// تم تحسين معالجة الأخطاء للتعامل مع أنواع البيانات المختلفة في Firebase
  /// وتجنب حدوث خروج مفاجئ (crash) عند محاولة تحويل البيانات
  ///
  /// تم تحسين إدارة الذاكرة وإضافة آلية للتعافي من الأخطاء
  static Future<String> _generateNewInvoiceNumber({
    InvoiceType type = InvoiceType.normal,
    DateTime? dateTime,
    int retryCount = 0,
  }) async {
    // التحقق من عدد المحاولات لتجنب التكرار اللانهائي
    if (retryCount > 3) {
      debugPrint('تجاوز الحد الأقصى لعدد المحاولات، استخدام طريقة بديلة');
      return await _generateFallbackInvoiceNumber(
          type: type, dateTime: dateTime);
    }

    try {
      // استخدام التاريخ المحدد أو التاريخ الحالي
      final DateTime now = dateTime ?? DateTime.now();

      // تنسيق التاريخ بشكل مختصر (MMDD) - شهر ويوم فقط
      final dateCode = DateFormat('MMdd').format(now);

      // مسار العداد المركزي في Firebase
      final String counterKey =
          type == InvoiceType.normal ? 'normal_counter' : 'corrected_counter';
      final counterPath = '$constUserId/invoice_counters/$counterKey';

      // استخدام معاملة Firebase لضمان عملية ذرية
      final DatabaseReference counterRef =
          FirebaseDatabase.instance.ref(counterPath);

      // إضافة مهلة زمنية لتجنب الانتظار إلى ما لا نهاية
      const timeout = Duration(seconds: 5);

      // تنفيذ المعاملة الذرية مع مهلة زمنية
      final TransactionResult result =
          await counterRef.runTransaction((Object? currentData) {
        // إذا كان المسار غير موجود أو القيمة null، نبدأ من 1
        int currentValue = 0;

        // التعامل مع أنواع البيانات المختلفة بشكل آمن
        if (currentData != null) {
          try {
            if (currentData is int) {
              // إذا كانت البيانات من نوع int
              currentValue = currentData;
            } else if (currentData is String) {
              // إذا كانت البيانات من نوع String
              currentValue = int.tryParse(currentData) ?? 0;
            } else if (currentData is Map) {
              // إذا كانت البيانات من نوع Map
              var valueStr = currentData['value']?.toString();
              currentValue = int.tryParse(valueStr ?? '0') ?? 0;
            } else {
              // أي نوع آخر، نحاول تحويله إلى String ثم إلى int
              currentValue = int.tryParse(currentData.toString()) ?? 0;
            }
          } catch (e) {
            // في حالة حدوث خطأ في التحويل، نستخدم القيمة الافتراضية
            debugPrint(
                'خطأ في تحويل قيمة العداد: $e، استخدام القيمة الافتراضية 0');
            currentValue = 0;
          }
        }

        // التحقق من صحة القيمة
        if (currentValue < 0 || currentValue > 9999) {
          // إذا كانت القيمة خارج النطاق المقبول، نعيد تعيينها
          debugPrint(
              'قيمة العداد خارج النطاق المقبول: $currentValue، إعادة تعيينها إلى 0');
          currentValue = 0;
        }

        return Transaction.success(currentValue + 1);
      }).timeout(timeout, onTimeout: () {
        // في حالة انتهاء المهلة الزمنية، نستخدم طريقة بديلة
        debugPrint('انتهت المهلة الزمنية للمعاملة، استخدام طريقة بديلة');
        throw TimeoutException('انتهت المهلة الزمنية للمعاملة');
      });

      if (result.committed) {
        // الحصول على القيمة الجديدة بطريقة آمنة
        int newCounter = 1; // قيمة افتراضية

        if (result.snapshot.value != null) {
          try {
            if (result.snapshot.value is int) {
              // إذا كانت القيمة من نوع int
              newCounter = result.snapshot.value as int;
            } else if (result.snapshot.value is String) {
              // إذا كانت القيمة من نوع String
              newCounter = int.tryParse(result.snapshot.value as String) ?? 1;
            } else if (result.snapshot.value is Map) {
              // إذا كانت القيمة من نوع Map
              var valueMap = result.snapshot.value as Map;
              var valueStr = valueMap['value']?.toString();
              newCounter = int.tryParse(valueStr ?? '1') ?? 1;
            } else {
              // أي نوع آخر، نحاول تحويله إلى String ثم إلى int
              newCounter = int.tryParse(result.snapshot.value.toString()) ?? 1;
            }
          } catch (e) {
            // في حالة حدوث خطأ في التحويل، نستخدم القيمة الافتراضية
            debugPrint(
                'خطأ في تحويل قيمة العداد: $e، استخدام القيمة الافتراضية 1');
            newCounter = 1;
          }
        }

        // التحقق من صحة القيمة
        if (newCounter <= 0 || newCounter > 9999) {
          debugPrint(
              'قيمة العداد غير صالحة: $newCounter، استخدام قيمة افتراضية');
          newCounter = (DateTime.now().millisecondsSinceEpoch % 9000) +
              1000; // قيمة عشوائية بين 1000 و 9999
        }

        // إنشاء رقم فاتورة قصير (6-8 أحرف)
        // استخدام تنسيق: MMddXXXX حيث MMDD هو الشهر واليوم وXXXX هو رقم العداد
        // إضافة بادئة 'C' للفواتير المصححة
        final String prefix = type == InvoiceType.corrected ? 'C' : '';
        final String baseInvoiceNumber =
            '$prefix$dateCode${newCounter.toString().padLeft(4, '0')}';

        try {
          // التحقق من فرادة الرقم وتوليد رقم فريد إذا كان موجوداً
          // إضافة مهلة زمنية لتجنب الانتظار إلى ما لا نهاية
          final String invoiceNumber =
              await _verifyInvoiceNumberUniqueness(baseInvoiceNumber)
                  .timeout(const Duration(seconds: 3), onTimeout: () {
            // في حالة انتهاء المهلة الزمنية، نستخدم الرقم الأساسي
            debugPrint(
                'انتهت المهلة الزمنية للتحقق من فرادة الرقم، استخدام الرقم الأساسي');
            return baseInvoiceNumber;
          });

          // تسجيل الرقم في السجل المركزي للفواتير
          // إضافة مهلة زمنية لتجنب الانتظار إلى ما لا نهاية
          await _registerInvoiceNumber(invoiceNumber, type)
              .timeout(const Duration(seconds: 2), onTimeout: () {
            // في حالة انتهاء المهلة الزمنية، نتجاهل التسجيل ونستمر
            debugPrint(
                'انتهت المهلة الزمنية لتسجيل الرقم في السجل المركزي، تجاهل التسجيل');
            return null;
          });

          debugPrint(
              'تم توليد رقم فاتورة جديد: $invoiceNumber (العداد: $newCounter، النوع: $type)');
          return invoiceNumber;
        } catch (verifyError) {
          // في حالة حدوث خطأ في التحقق من فرادة الرقم، نستخدم الرقم الأساسي
          debugPrint(
              'خطأ في التحقق من فرادة الرقم: $verifyError، استخدام الرقم الأساسي');
          return baseInvoiceNumber;
        }
      } else {
        // في حالة فشل المعاملة، نستخدم طريقة بديلة
        debugPrint('فشلت المعاملة الذرية، استخدام طريقة بديلة');
        return await _generateFallbackInvoiceNumber(type: type, dateTime: now);
      }
    } catch (e) {
      if (e is FirebaseException) {
        debugPrint(
            'خطأ في Firebase أثناء توليد رقم فاتورة جديد: ${e.code} - ${e.message}');
      } else if (e is TimeoutException) {
        debugPrint('انتهت المهلة الزمنية: $e');
      } else {
        debugPrint('خطأ غير متوقع في توليد رقم فاتورة جديد: $e');
      }

      // إعادة المحاولة مرة أخرى مع زيادة عداد المحاولات
      if (retryCount < 2) {
        debugPrint('إعادة المحاولة (${retryCount + 1}/3)...');
        // إضافة تأخير قبل إعادة المحاولة
        await Future.delayed(const Duration(milliseconds: 500));
        return await _generateNewInvoiceNumber(
          type: type,
          dateTime: dateTime,
          retryCount: retryCount + 1,
        );
      }

      return await _generateFallbackInvoiceNumber(type: type);
    }
  }

  /// تسجيل رقم الفاتورة في السجل المركزي
  static Future<void> _registerInvoiceNumber(
      String invoiceNumber, InvoiceType type) async {
    try {
      final centralRef =
          FirebaseDatabase.instance.ref("$constUserId/invoice_registry");

      await centralRef.child(invoiceNumber).set({
        'timestamp': ServerValue.timestamp,
        'type': type == InvoiceType.normal ? 'normal' : 'corrected',
        'createdAt': DateTime.now().toString(),
        'status': 'active'
      });

      debugPrint('تم تسجيل رقم الفاتورة $invoiceNumber في السجل المركزي');
    } catch (e) {
      // لا نريد أن يفشل توليد رقم الفاتورة إذا فشل التسجيل في السجل المركزي
      debugPrint('خطأ في تسجيل رقم الفاتورة في السجل المركزي: $e');
    }
  }

  /// توليد رقم فاتورة بديل في حالة فشل الطريقة الأساسية
  /// تم تحسين الدالة لإنتاج أرقام فواتير أقصر (6-8 أحرف) بتنسيق موحد
  /// تم تحسين إدارة الذاكرة وإضافة آلية للتعافي من الأخطاء
  static Future<String> _generateFallbackInvoiceNumber({
    InvoiceType type = InvoiceType.normal,
    DateTime? dateTime,
    int retryCount = 0,
  }) async {
    // التحقق من عدد المحاولات لتجنب التكرار اللانهائي
    if (retryCount > 2) {
      // في حالة تجاوز الحد الأقصى لعدد المحاولات، نستخدم رقم آمن جدًا
      final timestamp = DateTime.now().millisecondsSinceEpoch % 1000000;
      final String prefix = type == InvoiceType.corrected ? 'C' : '';
      final secureNumber = '$prefix${timestamp.toString().padLeft(6, '0')}';
      debugPrint(
          'تم توليد رقم فاتورة آمن بعد تجاوز عدد المحاولات: $secureNumber');
      return secureNumber;
    }

    try {
      // استخدام التاريخ المحدد أو التاريخ الحالي
      final DateTime now = dateTime ?? DateTime.now();

      // تنسيق التاريخ بشكل مختصر (MMDD) - شهر ويوم فقط
      final dateCode = DateFormat('MMdd').format(now);

      // استخدام مزيج من الوقت الحالي ورقم عشوائي لتجنب التكرار
      final timestamp = DateTime.now().millisecondsSinceEpoch % 1000;
      final random = math.Random();
      final randomValue = random.nextInt(900) + 100; // رقم عشوائي بين 100 و 999

      // استخدام قيمة فريدة تجمع بين الوقت الحالي والرقم العشوائي
      final uniqueValue = (timestamp + randomValue) % 10000;

      // إضافة بادئة 'C' للفواتير المصححة
      final String prefix = type == InvoiceType.corrected ? 'C' : '';

      // إنشاء رقم فاتورة قصير (7-8 أحرف)
      final String fallbackNumber =
          '$prefix$dateCode${uniqueValue.toString().padLeft(4, '0')}';

      try {
        // تسجيل الرقم في السجل المركزي مع مهلة زمنية
        await _registerInvoiceNumber(fallbackNumber, type)
            .timeout(const Duration(seconds: 2), onTimeout: () {
          // في حالة انتهاء المهلة الزمنية، نتجاهل التسجيل ونستمر
          debugPrint(
              'انتهت المهلة الزمنية لتسجيل الرقم في السجل المركزي، تجاهل التسجيل');
          return null;
        });
      } catch (registerError) {
        // تجاهل أخطاء التسجيل في السجل المركزي
        debugPrint(
            'خطأ في تسجيل الرقم في السجل المركزي: $registerError، تجاهل التسجيل');
      }

      debugPrint('تم توليد رقم فاتورة بديل: $fallbackNumber (النوع: $type)');
      return fallbackNumber;
    } on Exception catch (e) {
      debugPrint('خطأ في توليد رقم فاتورة بديل: $e');

      // إعادة المحاولة مرة أخرى مع زيادة عداد المحاولات
      if (retryCount < 2) {
        debugPrint(
            'إعادة المحاولة لتوليد رقم فاتورة بديل (${retryCount + 1}/3)...');
        // إضافة تأخير قبل إعادة المحاولة
        await Future.delayed(const Duration(milliseconds: 300));
        return await _generateFallbackInvoiceNumber(
          type: type,
          dateTime: dateTime,
          retryCount: retryCount + 1,
        );
      }

      // في حالة فشل كل المحاولات، نستخدم رقم آمن جدًا
      final timestamp = DateTime.now().millisecondsSinceEpoch % 1000000;
      final String prefix = type == InvoiceType.corrected ? 'C' : '';
      final secureNumber = '$prefix${timestamp.toString().padLeft(6, '0')}';

      debugPrint('تم توليد رقم فاتورة آمن بعد فشل كل الطرق: $secureNumber');
      return secureNumber;
    } catch (e) {
      // معالجة أي نوع آخر من الأخطاء
      debugPrint('خطأ غير متوقع في توليد رقم فاتورة بديل: $e');

      // في حالة فشل كل الطرق، نستخدم رقم آمن جدًا
      final timestamp = DateTime.now().millisecondsSinceEpoch % 1000000;
      final String prefix = type == InvoiceType.corrected ? 'C' : '';
      final secureNumber = '$prefix${timestamp.toString().padLeft(6, '0')}';

      debugPrint('تم توليد رقم فاتورة آمن بعد خطأ غير متوقع: $secureNumber');
      return secureNumber;
    }
  }

  /// التحقق من فرادة رقم الفاتورة وتوليد رقم فريد إذا كان موجوداً
  /// تم تبسيط الدالة وتحسينها لإنتاج أرقام فواتير أقصر
  /// تم تحسين إدارة الذاكرة وإضافة آلية للتعافي من الأخطاء
  static Future<String> _verifyInvoiceNumberUniqueness(String invoiceNumber,
      {int retryCount = 0}) async {
    // التحقق من عدد المحاولات لتجنب التكرار اللانهائي
    if (retryCount > 3) {
      // في حالة تجاوز الحد الأقصى لعدد المحاولات، نستخدم الرقم الأصلي
      debugPrint(
          'تجاوز الحد الأقصى لعدد المحاولات، استخدام الرقم الأصلي: $invoiceNumber');
      return invoiceNumber;
    }

    try {
      // إضافة مهلة زمنية لتجنب الانتظار إلى ما لا نهاية
      const timeout = Duration(seconds: 3);

      // التحقق من فرادة الرقم مع مهلة زمنية
      bool isUnique = await _isInvoiceNumberUnique(invoiceNumber)
          .timeout(timeout, onTimeout: () {
        // في حالة انتهاء المهلة الزمنية، نفترض أن الرقم فريد
        debugPrint(
            'انتهت المهلة الزمنية للتحقق من فرادة الرقم، نفترض أنه فريد');
        return true;
      });

      if (isUnique) {
        return invoiceNumber;
      }

      // إذا كان الرقم موجوداً، نضيف رقماً تسلسليًا ونتحقق مرة أخرى
      int attempts = 0;
      String newNumber = invoiceNumber;

      // محاولة إضافة أرقام تسلسلية للحصول على رقم فريد (بحد أقصى 3 محاولات)
      while (!isUnique && attempts < 3) {
        attempts++;
        // إضافة رقم تسلسلي إلى نهاية الرقم
        newNumber = '$invoiceNumber${attempts.toString().padLeft(2, '0')}';

        // التحقق من فرادة الرقم الجديد مع مهلة زمنية
        isUnique = await _isInvoiceNumberUnique(newNumber).timeout(timeout,
            onTimeout: () {
          // في حالة انتهاء المهلة الزمنية، نفترض أن الرقم فريد
          debugPrint(
              'انتهت المهلة الزمنية للتحقق من فرادة الرقم، نفترض أنه فريد');
          return true;
        });

        // إعطاء فرصة لجامع القمامة لتحرير الذاكرة
        await Future.delayed(const Duration(milliseconds: 50));
      }

      if (isUnique) {
        debugPrint('تم توليد رقم فاتورة فريد بعد $attempts محاولة: $newNumber');
        return newNumber;
      } else {
        // إذا فشلت المحاولات، نستخدم تنسيق مختلف يعتمد على الوقت الحالي
        final dateCode = DateFormat('MMdd').format(DateTime.now());
        final timestamp = DateTime.now().millisecondsSinceEpoch % 10000;

        // إنشاء رقم فاتورة جديد (8 أحرف)
        newNumber = '$dateCode${timestamp.toString().padLeft(4, '0')}';

        debugPrint(
            'تم توليد رقم فاتورة فريد باستخدام التاريخ والوقت: $newNumber');
        return newNumber;
      }
    } on TimeoutException catch (e) {
      debugPrint('انتهت المهلة الزمنية في التحقق من فرادة رقم الفاتورة: $e');

      // إعادة المحاولة مرة أخرى مع زيادة عداد المحاولات
      if (retryCount < 2) {
        debugPrint(
            'إعادة المحاولة بعد انتهاء المهلة الزمنية (${retryCount + 1}/3)...');
        // إضافة تأخير قبل إعادة المحاولة
        await Future.delayed(const Duration(milliseconds: 300));
        return await _verifyInvoiceNumberUniqueness(
          invoiceNumber,
          retryCount: retryCount + 1,
        );
      }

      // في حالة انتهاء المهلة الزمنية، نستخدم الرقم الأصلي
      return invoiceNumber;
    } catch (e) {
      debugPrint('خطأ في التحقق من فرادة رقم الفاتورة: $e');

      // إعادة المحاولة مرة أخرى مع زيادة عداد المحاولات
      if (retryCount < 2) {
        debugPrint('إعادة المحاولة بعد خطأ (${retryCount + 1}/3)...');
        // إضافة تأخير قبل إعادة المحاولة
        await Future.delayed(const Duration(milliseconds: 300));
        return await _verifyInvoiceNumberUniqueness(
          invoiceNumber,
          retryCount: retryCount + 1,
        );
      }

      // في حالة الخطأ، نستخدم الرقم الأصلي
      return invoiceNumber;
    }
  }

  /// التحقق مما إذا كان رقم الفاتورة موجوداً بالفعل في قاعدة البيانات
  /// تم تحسين الدالة لتكون أكثر كفاءة وتستخدم مرجع مركزي للفواتير
  /// تم تحسين إدارة الذاكرة وإضافة آلية للتعافي من الأخطاء
  static Future<bool> _isInvoiceNumberUnique(String invoiceNumber) async {
    try {
      // إضافة مهلة زمنية لتجنب الانتظار إلى ما لا نهاية
      const timeout = Duration(seconds: 2);

      // استخدام مرجع مركزي للفواتير المستخدمة
      final centralRef =
          FirebaseDatabase.instance.ref("$constUserId/invoice_registry");

      // التحقق أولاً من السجل المركزي للفواتير (أسرع طريقة)
      DataSnapshot centralSnapshot;
      try {
        centralSnapshot =
            await centralRef.child(invoiceNumber).get().timeout(timeout);
      } on TimeoutException {
        // في حالة انتهاء المهلة الزمنية، نفترض أن الرقم غير موجود
        debugPrint(
            'انتهت المهلة الزمنية للتحقق من السجل المركزي، نفترض أن الرقم غير موجود');
        // نعيد قيمة افتراضية تشير إلى أن الرقم غير موجود
        return true;
      }

      if (centralSnapshot.exists) {
        debugPrint('رقم الفاتورة $invoiceNumber موجود بالفعل في السجل المركزي');
        return false;
      }

      // التحقق من جدول المبيعات فقط (لتقليل استهلاك الذاكرة)
      // هذا يكفي في معظم الحالات لأن معظم الفواتير تكون في جدول المبيعات
      DataSnapshot salesSnapshot;
      try {
        salesSnapshot = await FirebaseDatabase.instance
            .ref("$constUserId/Sales Transition")
            .orderByChild('invoiceNumber')
            .equalTo(invoiceNumber)
            .limitToFirst(1) // تحديد النتائج لتقليل استهلاك الذاكرة
            .get()
            .timeout(timeout);
      } on TimeoutException {
        // في حالة انتهاء المهلة الزمنية، نفترض أن الرقم غير موجود
        debugPrint(
            'انتهت المهلة الزمنية للتحقق من جدول المبيعات، نفترض أن الرقم غير موجود');
        // نتحقق من الجداول الأخرى
        // لا نعيد قيمة هنا، نستمر في التنفيذ
        salesSnapshot = await FirebaseDatabase.instance
            .ref("$constUserId/Sales Transition")
            .orderByChild('invoiceNumber')
            .equalTo(
                'non_existent_value') // قيمة غير موجودة للتأكد من عدم وجود نتائج
            .get();
      }

      if (salesSnapshot.exists) {
        debugPrint('رقم الفاتورة $invoiceNumber موجود بالفعل في جدول المبيعات');

        try {
          // تسجيل الرقم في السجل المركزي للمرات القادمة
          // إضافة مهلة زمنية لتجنب الانتظار إلى ما لا نهاية
          await centralRef.child(invoiceNumber).set({
            'type': 'sales',
            'timestamp': ServerValue.timestamp,
            'status': 'active'
          }).timeout(const Duration(milliseconds: 500), onTimeout: () {
            // في حالة انتهاء المهلة الزمنية، نتجاهل التسجيل
            debugPrint(
                'انتهت المهلة الزمنية لتسجيل الرقم في السجل المركزي، تجاهل التسجيل');
            return;
          });
        } catch (e) {
          // تجاهل أخطاء التسجيل في السجل المركزي
          debugPrint('خطأ في تسجيل الرقم في السجل المركزي: $e، تجاهل التسجيل');
        }

        return false;
      }

      // إذا لم يتم العثور على الرقم في جدول المبيعات، نتحقق من الجداول الأخرى
      // ولكن بشكل متسلسل لتقليل استهلاك الذاكرة

      // التحقق من جدول المعاملات اليومية
      DataSnapshot dailySnapshot;
      try {
        dailySnapshot = await FirebaseDatabase.instance
            .ref("$constUserId/Daily Transaction")
            .orderByChild('id')
            .equalTo(invoiceNumber)
            .limitToFirst(1) // تحديد النتائج لتقليل استهلاك الذاكرة
            .get()
            .timeout(timeout);
      } on TimeoutException {
        // في حالة انتهاء المهلة الزمنية، نفترض أن الرقم غير موجود
        debugPrint(
            'انتهت المهلة الزمنية للتحقق من جدول المعاملات اليومية، نفترض أن الرقم غير موجود');
        // نتحقق من الجداول الأخرى
        // لا نعيد قيمة هنا، نستمر في التنفيذ
        dailySnapshot = await FirebaseDatabase.instance
            .ref("$constUserId/Daily Transaction")
            .orderByChild('id')
            .equalTo(
                'non_existent_value') // قيمة غير موجودة للتأكد من عدم وجود نتائج
            .get();
      }

      if (dailySnapshot.exists) {
        debugPrint(
            'رقم الفاتورة $invoiceNumber موجود بالفعل في جدول المعاملات اليومية');

        try {
          // تسجيل الرقم في السجل المركزي للمرات القادمة
          // إضافة مهلة زمنية لتجنب الانتظار إلى ما لا نهاية
          await centralRef.child(invoiceNumber).set({
            'type': 'daily',
            'timestamp': ServerValue.timestamp,
            'status': 'active'
          }).timeout(const Duration(milliseconds: 500), onTimeout: () {
            // في حالة انتهاء المهلة الزمنية، نتجاهل التسجيل
            debugPrint(
                'انتهت المهلة الزمنية لتسجيل الرقم في السجل المركزي، تجاهل التسجيل');
            return;
          });
        } catch (e) {
          // تجاهل أخطاء التسجيل في السجل المركزي
          debugPrint('خطأ في تسجيل الرقم في السجل المركزي: $e، تجاهل التسجيل');
        }

        return false;
      }

      // التحقق من جدول المعاملات الآجلة
      DataSnapshot dueSnapshot;
      try {
        dueSnapshot = await FirebaseDatabase.instance
            .ref("$constUserId/Due Transaction")
            .orderByChild('invoiceNumber')
            .equalTo(invoiceNumber)
            .limitToFirst(1) // تحديد النتائج لتقليل استهلاك الذاكرة
            .get()
            .timeout(timeout);
      } on TimeoutException {
        // في حالة انتهاء المهلة الزمنية، نفترض أن الرقم غير موجود
        debugPrint(
            'انتهت المهلة الزمنية للتحقق من جدول المعاملات الآجلة، نفترض أن الرقم غير موجود');
        // نفترض أن الرقم غير موجود
        dueSnapshot = await FirebaseDatabase.instance
            .ref("$constUserId/Due Transaction")
            .orderByChild('invoiceNumber')
            .equalTo(
                'non_existent_value') // قيمة غير موجودة للتأكد من عدم وجود نتائج
            .get();
      }

      if (dueSnapshot.exists) {
        debugPrint(
            'رقم الفاتورة $invoiceNumber موجود بالفعل في جدول المعاملات الآجلة');

        try {
          // تسجيل الرقم في السجل المركزي للمرات القادمة
          // إضافة مهلة زمنية لتجنب الانتظار إلى ما لا نهاية
          await centralRef.child(invoiceNumber).set({
            'type': 'due',
            'timestamp': ServerValue.timestamp,
            'status': 'active'
          }).timeout(const Duration(milliseconds: 500), onTimeout: () {
            // في حالة انتهاء المهلة الزمنية، نتجاهل التسجيل
            debugPrint(
                'انتهت المهلة الزمنية لتسجيل الرقم في السجل المركزي، تجاهل التسجيل');
            return;
          });
        } catch (e) {
          // تجاهل أخطاء التسجيل في السجل المركزي
          debugPrint('خطأ في تسجيل الرقم في السجل المركزي: $e، تجاهل التسجيل');
        }

        return false;
      }

      // إذا لم يتم العثور على الرقم في أي جدول، فهو فريد
      return true;
    } on TimeoutException catch (e) {
      debugPrint('انتهت المهلة الزمنية في التحقق من فرادة رقم الفاتورة: $e');
      // في حالة انتهاء المهلة الزمنية، نفترض أن الرقم غير موجود
      return true;
    } on Exception catch (e) {
      debugPrint('خطأ في التحقق من فرادة رقم الفاتورة: $e');
      // في حالة الخطأ، نفترض أن الرقم غير موجود
      return true;
    } catch (e) {
      debugPrint('خطأ غير متوقع في التحقق من فرادة رقم الفاتورة: $e');
      // في حالة الخطأ، نفترض أن الرقم غير موجود
      return true;
    }
  }

  /// تحديث مرجع فاتورة مكرر
  /// تعيد قيمة منطقية تشير إلى نجاح أو فشل العملية
  /// تم تحسين الدالة لاستخدام عمليات الدفعة وتحسين الأداء
  static Future<bool> _updateInvoiceReference(
      Map<String, dynamic> reference, String newInvoiceNumber) async {
    try {
      final DatabaseReference dbRef = reference['ref'] as DatabaseReference;
      final String path = reference['path'] as String;
      final String type = reference['type'] as String;
      final String oldInvoiceNumber =
          reference['invoiceNumber'] as String? ?? '';
      final String key = reference['key'] as String? ?? '';

      // قائمة بالفواتير المشكلة التي يجب تجاوزها
      final List<String> problematicInvoices = [
        '-OFO7DSFYFq4NbomP-IF', // الفاتورة التي تسبب مشكلة استنفاد الذاكرة
      ];

      // التحقق مما إذا كانت الفاتورة الحالية في قائمة الفواتير المشكلة
      if (problematicInvoices.contains(key)) {
        debugPrint(
            '⚠️ تجاوز الفاتورة المشكلة في _updateInvoiceReference: $key');
        return true; // نتظاهر بأن العملية نجحت لتجاوز هذه الفاتورة
      }

      debugPrint(
          'بدء تحديث الفاتورة في المسار: $path، النوع: $type، من: $oldInvoiceNumber إلى: $newInvoiceNumber');

      // تحديث السجل المركزي للفواتير
      await _updateInvoiceRegistry(oldInvoiceNumber, newInvoiceNumber);

      // قائمة لتخزين جميع عمليات التحديث
      List<Future> updateFutures = [];

      if (type == 'sales') {
        // تحديث رقم الفاتورة في جدول المبيعات
        updateFutures.add(dbRef.update({'invoiceNumber': newInvoiceNumber}));

        // تحديث رقم الفاتورة في نموذج المعاملة المضمن إذا كان موجودًا
        final snapshot = await dbRef.get();
        if (snapshot.exists && snapshot.child('saleTransactionModel').exists) {
          updateFutures.add(dbRef
              .child('saleTransactionModel')
              .update({'invoiceNumber': newInvoiceNumber}));
        }

        // تنفيذ جميع عمليات التحديث بالتوازي
        await Future.wait(updateFutures);

        // التحقق من نجاح التحديث
        final verifySnapshot = await dbRef.get();
        final updatedInvoiceNumber =
            verifySnapshot.child('invoiceNumber').value?.toString() ?? '';

        if (updatedInvoiceNumber == newInvoiceNumber) {
          debugPrint('تم التحقق من نجاح التحديث في جدول المبيعات');

          // تحديث المعاملات المرتبطة في الجداول الأخرى
          await _updateRelatedTransactions(
              oldInvoiceNumber: oldInvoiceNumber,
              newInvoiceNumber: newInvoiceNumber,
              customerName:
                  verifySnapshot.child('customerName').value?.toString() ?? '',
              customerPhone:
                  verifySnapshot.child('customerPhone').value?.toString() ?? '',
              excludeType: 'sales',
              excludePath: path);

          return true;
        } else {
          debugPrint(
              'فشل التحقق من التحديث في جدول المبيعات: $updatedInvoiceNumber != $newInvoiceNumber');

          // محاولة التراجع عن التغييرات
          await _rollbackInvoiceUpdate(newInvoiceNumber, oldInvoiceNumber);
          return false;
        }
      } else if (type == 'daily') {
        // تحديث معرف الفاتورة في جدول المعاملات اليومية
        updateFutures.add(dbRef.update(
            {'id': newInvoiceNumber, 'invoiceNumber': newInvoiceNumber}));

        // تحديث رقم الفاتورة في نماذج المعاملات المضمنة إذا كانت موجودة
        final snapshot = await dbRef.get();
        if (snapshot.exists) {
          // تحديث نموذج معاملة المبيعات المضمن إذا كان موجودًا
          if (snapshot.child('saleTransactionModel').exists) {
            updateFutures.add(dbRef
                .child('saleTransactionModel')
                .update({'invoiceNumber': newInvoiceNumber}));
          }

          // تحديث نموذج معاملة المشتريات المضمن إذا كان موجودًا
          if (snapshot.child('purchaseTransactionModel').exists) {
            updateFutures.add(dbRef
                .child('purchaseTransactionModel')
                .update({'invoiceNumber': newInvoiceNumber}));
          }

          // تحديث نموذج معاملة الدين المضمن إذا كان موجودًا
          if (snapshot.child('dueTransactionModel').exists) {
            updateFutures.add(dbRef
                .child('dueTransactionModel')
                .update({'invoiceNumber': newInvoiceNumber}));
          }
        }

        // تنفيذ جميع عمليات التحديث بالتوازي
        await Future.wait(updateFutures);

        // التحقق من نجاح التحديث
        final verifySnapshot = await dbRef.get();
        final updatedId = verifySnapshot.child('id').value?.toString() ?? '';

        if (updatedId == newInvoiceNumber) {
          debugPrint('تم التحقق من نجاح التحديث في جدول المعاملات اليومية');

          // تحديث المعاملات المرتبطة في الجداول الأخرى
          await _updateRelatedTransactions(
              oldInvoiceNumber: oldInvoiceNumber,
              newInvoiceNumber: newInvoiceNumber,
              customerName:
                  verifySnapshot.child('name').value?.toString() ?? '',
              customerPhone:
                  '', // قد لا يكون رقم الهاتف متاحًا في المعاملات اليومية
              excludeType: 'daily',
              excludePath: path);

          return true;
        } else {
          debugPrint(
              'فشل التحقق من التحديث في جدول المعاملات اليومية: $updatedId != $newInvoiceNumber');

          // محاولة التراجع عن التغييرات
          await _rollbackInvoiceUpdate(newInvoiceNumber, oldInvoiceNumber);
          return false;
        }
      } else if (type == 'due') {
        // تحديث رقم الفاتورة في جدول المعاملات الآجلة
        updateFutures.add(dbRef.update({'invoiceNumber': newInvoiceNumber}));

        // تنفيذ جميع عمليات التحديث بالتوازي
        await Future.wait(updateFutures);

        // التحقق من نجاح التحديث
        final verifySnapshot = await dbRef.get();
        final updatedInvoiceNumber =
            verifySnapshot.child('invoiceNumber').value?.toString() ?? '';

        if (updatedInvoiceNumber == newInvoiceNumber) {
          debugPrint('تم التحقق من نجاح التحديث في جدول المعاملات الآجلة');

          // تحديث المعاملات المرتبطة في الجداول الأخرى
          await _updateRelatedTransactions(
              oldInvoiceNumber: oldInvoiceNumber,
              newInvoiceNumber: newInvoiceNumber,
              customerName:
                  verifySnapshot.child('customerName').value?.toString() ?? '',
              customerPhone:
                  verifySnapshot.child('customerPhone').value?.toString() ?? '',
              excludeType: 'due',
              excludePath: path);

          return true;
        } else {
          debugPrint(
              'فشل التحقق من التحديث في جدول المعاملات الآجلة: $updatedInvoiceNumber != $newInvoiceNumber');

          // محاولة التراجع عن التغييرات
          await _rollbackInvoiceUpdate(newInvoiceNumber, oldInvoiceNumber);
          return false;
        }
      } else {
        debugPrint('نوع جدول غير معروف: $type');
        return false;
      }
    } on Exception catch (e) {
      debugPrint('خطأ في تحديث مرجع الفاتورة: $e');
      return false;
    }
  }

  /// تحديث السجل المركزي للفواتير
  static Future<void> _updateInvoiceRegistry(
      String oldInvoiceNumber, String newInvoiceNumber) async {
    try {
      final centralRef =
          FirebaseDatabase.instance.ref("$constUserId/invoice_registry");

      // الحصول على بيانات الفاتورة القديمة
      final oldSnapshot = await centralRef.child(oldInvoiceNumber).get();

      if (oldSnapshot.exists) {
        // نسخ البيانات من الفاتورة القديمة إلى الفاتورة الجديدة
        Map<String, dynamic> oldData =
            Map<String, dynamic>.from(oldSnapshot.value as Map);

        // إضافة معلومات إضافية
        oldData['originalInvoice'] = oldInvoiceNumber;
        oldData['updatedAt'] = DateTime.now().toString();
        oldData['status'] = 'updated';

        // تحديث حالة الفاتورة القديمة
        await centralRef
            .child(oldInvoiceNumber)
            .update({'status': 'replaced', 'replacedBy': newInvoiceNumber});

        // إضافة الفاتورة الجديدة
        await centralRef.child(newInvoiceNumber).set(oldData);

        debugPrint(
            'تم تحديث السجل المركزي للفواتير: $oldInvoiceNumber -> $newInvoiceNumber');
      } else {
        // إذا لم تكن الفاتورة القديمة موجودة في السجل المركزي، نضيف الفاتورة الجديدة فقط
        await centralRef.child(newInvoiceNumber).set({
          'originalInvoice': oldInvoiceNumber,
          'timestamp': ServerValue.timestamp,
          'createdAt': DateTime.now().toString(),
          'status': 'active'
        });

        debugPrint(
            'تم إضافة الفاتورة الجديدة $newInvoiceNumber إلى السجل المركزي');
      }
    } catch (e) {
      debugPrint('خطأ في تحديث السجل المركزي للفواتير: $e');
    }
  }

  /// التراجع عن تحديث الفاتورة في حالة الفشل
  static Future<void> _rollbackInvoiceUpdate(
      String newInvoiceNumber, String oldInvoiceNumber) async {
    try {
      debugPrint(
          'محاولة التراجع عن تحديث الفاتورة: $newInvoiceNumber -> $oldInvoiceNumber');

      // تحديث السجل المركزي للفواتير
      final centralRef =
          FirebaseDatabase.instance.ref("$constUserId/invoice_registry");

      // تحديث حالة الفاتورة الجديدة
      await centralRef.child(newInvoiceNumber).update({
        'status': 'rollback',
        'rollbackAt': DateTime.now().toString(),
        'rollbackTo': oldInvoiceNumber
      });

      // تحديث حالة الفاتورة القديمة
      await centralRef
          .child(oldInvoiceNumber)
          .update({'status': 'active', 'rollbackFrom': newInvoiceNumber});

      debugPrint('تم التراجع عن تحديث الفاتورة بنجاح');
    } catch (e) {
      debugPrint('خطأ في التراجع عن تحديث الفاتورة: $e');
    }
  }

  /// زيادة رقم الفاتورة بمقدار معين
  /// تم تبسيط الدالة لتعمل مع التنسيق الجديد للفواتير (6-8 أحرف)
  static String _incrementInvoiceNumber(
      String baseInvoiceNumber, int increment) {
    try {
      // نفترض أن التنسيق هو MMddXXXX حيث MMDD هو الشهر واليوم وXXXX هو رقم العداد
      if (baseInvoiceNumber.length >= 8) {
        // نفترض أن الـ 4 أحرف الأولى هي التاريخ، والباقي هو الرقم التسلسلي
        String datePart = baseInvoiceNumber.substring(0, 4);
        String counterPart = baseInvoiceNumber.substring(4);

        // محاولة تحويل الجزء الرقمي إلى عدد وزيادته
        int counterValue = int.tryParse(counterPart) ?? 0;
        counterValue += increment;

        // إعادة تكوين رقم الفاتورة
        return '$datePart${counterValue.toString().padLeft(counterPart.length, '0')}';
      } else if (baseInvoiceNumber.length >= 4) {
        // إذا كان الرقم قصيرًا، نفترض أن الـ 4 أحرف الأولى هي التاريخ
        String datePart = baseInvoiceNumber.substring(0, 4);

        // إضافة الزيادة كرقم تسلسلي
        return '$datePart${increment.toString().padLeft(2, '0')}';
      } else {
        // إذا كان التنسيق غير معروف، نضيف الزيادة في النهاية
        return '$baseInvoiceNumber${increment.toString().padLeft(2, '0')}';
      }
    } catch (e) {
      debugPrint('خطأ في زيادة رقم الفاتورة: $e');
      // في حالة الخطأ، نضيف الزيادة في النهاية
      return '$baseInvoiceNumber${increment.toString().padLeft(2, '0')}';
    }
  }

  /// تحديث المعاملات المرتبطة في الجداول الأخرى
  /// تم تحسين الدالة لتحديث السجل المركزي للفواتير وتحسين الأداء
  /// تم إضافة آلية للتراجع عن التغييرات في حالة الفشل
  static Future<void> _updateRelatedTransactions({
    required String oldInvoiceNumber,
    required String newInvoiceNumber,
    required String customerName,
    required String customerPhone,
    required String excludeType,
    required String excludePath,
  }) async {
    try {
      debugPrint(
          'البحث عن معاملات مرتبطة لتحديثها: الفاتورة القديمة=$oldInvoiceNumber، الفاتورة الجديدة=$newInvoiceNumber');

      // قائمة لتخزين جميع المراجع التي تم تحديثها (للتراجع في حالة الفشل)
      List<Map<String, dynamic>> updatedReferences = [];

      // قائمة لتخزين جميع عمليات التحديث
      List<Future> allUpdateFutures = [];

      // تحديث المعاملات المرتبطة في جدول المبيعات
      if (excludeType != 'sales') {
        final salesRef =
            FirebaseDatabase.instance.ref("$constUserId/Sales Transition");
        final salesSnapshot = await salesRef
            .orderByChild('invoiceNumber')
            .equalTo(oldInvoiceNumber)
            .get();

        if (salesSnapshot.exists) {
          List<Future> updateFutures = [];
          for (var child in salesSnapshot.children) {
            final path = "$constUserId/Sales Transition/${child.key}";
            if (path != excludePath) {
              debugPrint('تحديث معاملة مرتبطة في جدول المبيعات: $path');

              // إضافة المرجع إلى قائمة المراجع المحدثة
              updatedReferences.add({
                'type': 'sales',
                'key': child.key!,
                'path': path,
                'oldInvoiceNumber': oldInvoiceNumber,
                'ref': salesRef.child(child.key!)
              });

              // إضافة عملية التحديث إلى قائمة العمليات
              updateFutures.add(salesRef
                  .child(child.key!)
                  .update({'invoiceNumber': newInvoiceNumber}));

              // تحديث نموذج المعاملة المضمن إذا كان موجودًا
              if (child.child('saleTransactionModel').exists) {
                updateFutures.add(salesRef
                    .child(child.key!)
                    .child('saleTransactionModel')
                    .update({'invoiceNumber': newInvoiceNumber}));
              }
            }
          }

          // إضافة عمليات التحديث إلى القائمة الرئيسية
          allUpdateFutures.addAll(updateFutures);
        }
      }

      // تحديث المعاملات المرتبطة في جدول المعاملات اليومية
      if (excludeType != 'daily') {
        final dailyRef =
            FirebaseDatabase.instance.ref("$constUserId/Daily Transaction");
        final dailySnapshot =
            await dailyRef.orderByChild('id').equalTo(oldInvoiceNumber).get();

        if (dailySnapshot.exists) {
          List<Future> updateFutures = [];
          for (var child in dailySnapshot.children) {
            final path = "$constUserId/Daily Transaction/${child.key}";
            if (path != excludePath) {
              debugPrint(
                  'تحديث معاملة مرتبطة في جدول المعاملات اليومية: $path');

              // إضافة المرجع إلى قائمة المراجع المحدثة
              updatedReferences.add({
                'type': 'daily',
                'key': child.key!,
                'path': path,
                'oldInvoiceNumber': oldInvoiceNumber,
                'ref': dailyRef.child(child.key!)
              });

              // إضافة عملية التحديث إلى قائمة العمليات
              updateFutures.add(dailyRef.child(child.key!).update(
                  {'id': newInvoiceNumber, 'invoiceNumber': newInvoiceNumber}));

              // تحديث النماذج المضمنة إذا كانت موجودة
              if (child.child('saleTransactionModel').exists) {
                updateFutures.add(dailyRef
                    .child(child.key!)
                    .child('saleTransactionModel')
                    .update({'invoiceNumber': newInvoiceNumber}));
              }

              if (child.child('purchaseTransactionModel').exists) {
                updateFutures.add(dailyRef
                    .child(child.key!)
                    .child('purchaseTransactionModel')
                    .update({'invoiceNumber': newInvoiceNumber}));
              }

              if (child.child('dueTransactionModel').exists) {
                updateFutures.add(dailyRef
                    .child(child.key!)
                    .child('dueTransactionModel')
                    .update({'invoiceNumber': newInvoiceNumber}));
              }
            }
          }

          // إضافة عمليات التحديث إلى القائمة الرئيسية
          allUpdateFutures.addAll(updateFutures);
        }
      }

      // تحديث المعاملات المرتبطة في جدول المعاملات الآجلة
      if (excludeType != 'due') {
        final dueRef =
            FirebaseDatabase.instance.ref("$constUserId/Due Transaction");
        final dueSnapshot = await dueRef
            .orderByChild('invoiceNumber')
            .equalTo(oldInvoiceNumber)
            .get();

        if (dueSnapshot.exists) {
          List<Future> updateFutures = [];
          for (var child in dueSnapshot.children) {
            final path = "$constUserId/Due Transaction/${child.key}";
            if (path != excludePath) {
              debugPrint('تحديث معاملة مرتبطة في جدول المعاملات الآجلة: $path');

              // إضافة المرجع إلى قائمة المراجع المحدثة
              updatedReferences.add({
                'type': 'due',
                'key': child.key!,
                'path': path,
                'oldInvoiceNumber': oldInvoiceNumber,
                'ref': dueRef.child(child.key!)
              });

              // إضافة عملية التحديث إلى قائمة العمليات
              updateFutures.add(dueRef
                  .child(child.key!)
                  .update({'invoiceNumber': newInvoiceNumber}));
            }
          }

          // إضافة عمليات التحديث إلى القائمة الرئيسية
          allUpdateFutures.addAll(updateFutures);
        }
      }

      // تحديث سجلات المدفوعات المرتبطة
      final paymentRef =
          FirebaseDatabase.instance.ref("$constUserId/Payment History");
      final paymentSnapshot = await paymentRef
          .orderByChild('invoiceNumber')
          .equalTo(oldInvoiceNumber)
          .get();

      if (paymentSnapshot.exists) {
        List<Future> updateFutures = [];
        for (var child in paymentSnapshot.children) {
          debugPrint('تحديث سجل مدفوعات مرتبط: ${child.key}');

          // إضافة المرجع إلى قائمة المراجع المحدثة
          updatedReferences.add({
            'type': 'payment',
            'key': child.key!,
            'path': "$constUserId/Payment History/${child.key}",
            'oldInvoiceNumber': oldInvoiceNumber,
            'ref': paymentRef.child(child.key!)
          });

          // إضافة عملية التحديث إلى قائمة العمليات
          updateFutures.add(paymentRef
              .child(child.key!)
              .update({'invoiceNumber': newInvoiceNumber}));
        }

        // إضافة عمليات التحديث إلى القائمة الرئيسية
        allUpdateFutures.addAll(updateFutures);
      }

      // تنفيذ جميع عمليات التحديث بالتوازي
      if (allUpdateFutures.isNotEmpty) {
        try {
          await Future.wait(allUpdateFutures);
          debugPrint('تم تحديث ${allUpdateFutures.length} معاملة مرتبطة بنجاح');

          // تحديث السجل المركزي للفواتير
          await _updateInvoiceRegistry(oldInvoiceNumber, newInvoiceNumber);
        } catch (updateError) {
          debugPrint('حدث خطأ أثناء تحديث المعاملات المرتبطة: $updateError');

          // محاولة التراجع عن التغييرات
          await _rollbackRelatedTransactions(
              updatedReferences, newInvoiceNumber, oldInvoiceNumber);
          rethrow;
        }
      } else {
        debugPrint('لا توجد معاملات مرتبطة للتحديث');

        // تحديث السجل المركزي للفواتير
        await _updateInvoiceRegistry(oldInvoiceNumber, newInvoiceNumber);
      }

      debugPrint('تم الانتهاء من تحديث المعاملات المرتبطة');
    } on Exception catch (e) {
      debugPrint('خطأ في تحديث المعاملات المرتبطة: $e');
      rethrow;
    }
  }

  /// التراجع عن تحديث المعاملات المرتبطة في حالة الفشل
  static Future<void> _rollbackRelatedTransactions(
      List<Map<String, dynamic>> updatedReferences,
      String newInvoiceNumber,
      String oldInvoiceNumber) async {
    try {
      debugPrint(
          'محاولة التراجع عن تحديث ${updatedReferences.length} معاملة مرتبطة');

      // قائمة لتخزين جميع عمليات التراجع
      List<Future> rollbackFutures = [];

      // التراجع عن تحديث كل مرجع
      for (var reference in updatedReferences) {
        final String type = reference['type'] as String;
        final DatabaseReference ref = reference['ref'] as DatabaseReference;
        final String path = reference['path'] as String;
        final String oldInvoiceNumber = reference['oldInvoiceNumber'] as String;

        debugPrint('التراجع عن تحديث المعاملة في المسار: $path، النوع: $type');

        if (type == 'sales') {
          // التراجع عن تحديث رقم الفاتورة في جدول المبيعات
          rollbackFutures.add(ref.update({'invoiceNumber': oldInvoiceNumber}));

          // التراجع عن تحديث نموذج المعاملة المضمن إذا كان موجودًا
          final snapshot = await ref.get();
          if (snapshot.exists &&
              snapshot.child('saleTransactionModel').exists) {
            rollbackFutures.add(ref
                .child('saleTransactionModel')
                .update({'invoiceNumber': oldInvoiceNumber}));
          }
        } else if (type == 'daily') {
          // التراجع عن تحديث معرف الفاتورة في جدول المعاملات اليومية
          rollbackFutures.add(ref.update(
              {'id': oldInvoiceNumber, 'invoiceNumber': oldInvoiceNumber}));

          // التراجع عن تحديث النماذج المضمنة إذا كانت موجودة
          final snapshot = await ref.get();
          if (snapshot.exists) {
            if (snapshot.child('saleTransactionModel').exists) {
              rollbackFutures.add(ref
                  .child('saleTransactionModel')
                  .update({'invoiceNumber': oldInvoiceNumber}));
            }

            if (snapshot.child('purchaseTransactionModel').exists) {
              rollbackFutures.add(ref
                  .child('purchaseTransactionModel')
                  .update({'invoiceNumber': oldInvoiceNumber}));
            }

            if (snapshot.child('dueTransactionModel').exists) {
              rollbackFutures.add(ref
                  .child('dueTransactionModel')
                  .update({'invoiceNumber': oldInvoiceNumber}));
            }
          }
        } else if (type == 'due' || type == 'payment') {
          // التراجع عن تحديث رقم الفاتورة
          rollbackFutures.add(ref.update({'invoiceNumber': oldInvoiceNumber}));
        }
      }

      // تنفيذ جميع عمليات التراجع بالتوازي
      if (rollbackFutures.isNotEmpty) {
        await Future.wait(rollbackFutures);
        debugPrint(
            'تم التراجع عن تحديث ${rollbackFutures.length} معاملة مرتبطة بنجاح');
      }

      // تحديث السجل المركزي للفواتير
      await _rollbackInvoiceUpdate(newInvoiceNumber, oldInvoiceNumber);

      debugPrint('تم الانتهاء من التراجع عن تحديث المعاملات المرتبطة');
    } catch (e) {
      debugPrint('خطأ في التراجع عن تحديث المعاملات المرتبطة: $e');
    }
  }
}
