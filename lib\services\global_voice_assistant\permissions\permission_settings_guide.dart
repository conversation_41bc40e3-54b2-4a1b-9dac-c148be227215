import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'permission_manager.dart';

/// دليل شامل لإعدادات أذونات المساعد الذكي العالمي
class PermissionSettingsGuide extends StatefulWidget {
  const PermissionSettingsGuide({super.key});

  @override
  State<PermissionSettingsGuide> createState() =>
      _PermissionSettingsGuideState();
}

class _PermissionSettingsGuideState extends State<PermissionSettingsGuide>
    with TickerProviderStateMixin {
  final PermissionManager _permissionManager = PermissionManager();

  bool _isLoading = true;
  Map<String, dynamic>? _permissionStatus;

  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadPermissionStatus();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل حالة الأذونات
  Future<void> _loadPermissionStatus() async {
    setState(() => _isLoading = true);

    try {
      final status = await _permissionManager.checkAllPermissions();
      setState(() {
        _permissionStatus = status;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('خطأ في تحميل حالة الأذونات: $e');
    }
  }

  /// عرض رسالة خطأ
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        action: SnackBarAction(
          label: 'إعادة المحاولة',
          textColor: Colors.white,
          onPressed: _loadPermissionStatus,
        ),
      ),
    );
  }

  /// عرض رسالة نجاح
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// طلب إذن محدد
  Future<void> _requestSpecificPermission(String permissionType) async {
    HapticFeedback.lightImpact();

    try {
      final result = await _permissionManager.requestAllPermissions();

      if (result['success'] == true) {
        _showSuccessSnackBar('تم منح الأذونات بنجاح');
        await _loadPermissionStatus();
      } else {
        _showErrorSnackBar(result['message'] ?? 'فشل في منح الأذونات');
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في طلب الأذونات: $e');
    }
  }

  /// فتح إعدادات التطبيق
  Future<void> _openAppSettings() async {
    HapticFeedback.mediumImpact();

    try {
      final opened = await _permissionManager.openAppSettings();
      if (!opened) {
        _showErrorSnackBar('فشل في فتح إعدادات التطبيق');
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في فتح الإعدادات: $e');
    }
  }

  /// إعادة تعيين حالة الأذونات
  Future<void> _resetPermissionStatus() async {
    HapticFeedback.heavyImpact();

    try {
      await _permissionManager.resetUserDeniedStatus();
      _showSuccessSnackBar('تم إعادة تعيين حالة الأذونات');
      await _loadPermissionStatus();
    } catch (e) {
      _showErrorSnackBar('خطأ في إعادة التعيين: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('دليل أذونات المساعد الذكي'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadPermissionStatus,
            tooltip: 'تحديث',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'الحالة'),
            Tab(icon: Icon(Icons.help_outline), text: 'الدليل'),
            Tab(icon: Icon(Icons.settings), text: 'الإعدادات'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildStatusTab(),
                _buildGuideTab(),
                _buildSettingsTab(),
              ],
            ),
    );
  }

  /// تبويب الحالة
  Widget _buildStatusTab() {
    if (_permissionStatus == null) {
      return const Center(child: Text('خطأ في تحميل البيانات'));
    }

    final summary = _permissionStatus!['summary'] as Map<String, dynamic>;
    final permissions = _permissionStatus!['permissions'] as Map<String, bool>;
    final details = _permissionStatus!['details'] as Map<String, String>;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // بطاقة الملخص
          Card(
            elevation: 4,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Row(
                    children: [
                      Icon(
                        summary['all_granted'] == true
                            ? Icons.check_circle
                            : Icons.warning,
                        color: summary['all_granted'] == true
                            ? Colors.green
                            : Colors.orange,
                        size: 32,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              summary['all_granted'] == true
                                  ? 'جميع الأذونات ممنوحة'
                                  : 'بعض الأذونات مطلوبة',
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              '${summary['granted_count']} من ${summary['total_count']} ممنوحة',
                              style: const TextStyle(color: Colors.grey),
                            ),
                          ],
                        ),
                      ),
                      CircularProgressIndicator(
                        value: summary['percentage'] / 100.0,
                        backgroundColor: Colors.grey[300],
                        valueColor: AlwaysStoppedAnimation<Color>(
                          summary['all_granted'] == true
                              ? Colors.green
                              : Colors.orange,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: _buildStatusIndicator(
                          'أساسية',
                          summary['essential_granted'] == true,
                          Icons.mic,
                        ),
                      ),
                      Expanded(
                        child: _buildStatusIndicator(
                          'متقدمة',
                          summary['advanced_granted'] == true,
                          Icons.settings,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // قائمة الأذونات التفصيلية
          const Text(
            'تفاصيل الأذونات:',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),

          ...permissions.entries.map((entry) => _buildPermissionCard(
                entry.key,
                entry.value,
                details[entry.key] ?? '',
              )),

          const SizedBox(height: 16),

          // أزرار الإجراءات
          if (summary['all_granted'] != true) ...[
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _requestSpecificPermission('all'),
                icon: const Icon(Icons.security),
                label: const Text('طلب جميع الأذونات'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            const SizedBox(height: 8),
          ],

          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: _openAppSettings,
              icon: const Icon(Icons.settings),
              label: const Text('فتح إعدادات التطبيق'),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء مؤشر الحالة
  Widget _buildStatusIndicator(String title, bool granted, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: granted ? Colors.green[50] : Colors.red[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: granted ? Colors.green : Colors.red,
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: granted ? Colors.green : Colors.red,
            size: 24,
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: granted ? Colors.green[700] : Colors.red[700],
            ),
          ),
          Text(
            granted ? 'ممنوحة' : 'مطلوبة',
            style: TextStyle(
              fontSize: 10,
              color: granted ? Colors.green : Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة إذن
  Widget _buildPermissionCard(String permission, bool granted, String details) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: ListTile(
        leading: Icon(
          _getPermissionIcon(permission),
          color: granted ? Colors.green : Colors.orange,
        ),
        title: Text(_getPermissionDisplayName(permission)),
        subtitle: Text(details),
        trailing: Icon(
          granted ? Icons.check_circle : Icons.radio_button_unchecked,
          color: granted ? Colors.green : Colors.grey,
        ),
        onTap: granted ? null : () => _requestSpecificPermission(permission),
      ),
    );
  }

  /// تبويب الدليل
  Widget _buildGuideTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildGuideSection(
            'الأذونات الأساسية',
            Icons.mic,
            [
              _buildGuideItem(
                'الميكروفون',
                'مطلوب للاستماع لكلمة التفعيل والأوامر الصوتية',
                'ضروري لعمل المساعد الصوتي',
              ),
            ],
          ),
          _buildGuideSection(
            'الأذونات المتقدمة',
            Icons.settings,
            [
              _buildGuideItem(
                'النوافذ العائمة',
                'يسمح بإظهار المساعد فوق التطبيقات الأخرى',
                'مطلوب لعمل المساعد مع قفل الشاشة',
              ),
              _buildGuideItem(
                'تحسين البطارية',
                'يمنع النظام من إيقاف المساعد لتوفير البطارية',
                'مطلوب للعمل المستمر في الخلفية',
              ),
            ],
          ),
          _buildGuideSection(
            'الأذونات الاختيارية',
            Icons.extension,
            [
              _buildGuideItem(
                'الإشعارات',
                'لإرسال تنبيهات حول حالة المساعد',
                'اختياري - يحسن تجربة الاستخدام',
              ),
              _buildGuideItem(
                'التخزين',
                'لحفظ ملفات صوتية مؤقتة',
                'اختياري - لميزات متقدمة',
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء قسم في الدليل
  Widget _buildGuideSection(String title, IconData icon, List<Widget> items) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...items,
          ],
        ),
      ),
    );
  }

  /// بناء عنصر في الدليل
  Widget _buildGuideItem(String title, String description, String note) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          Text(
            description,
            style: const TextStyle(color: Colors.black87),
          ),
          Text(
            note,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
              fontStyle: FontStyle.italic,
            ),
          ),
          const SizedBox(height: 8),
        ],
      ),
    );
  }

  /// تبويب الإعدادات
  Widget _buildSettingsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'إعدادات الأذونات',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          ListTile(
            leading: const Icon(Icons.refresh),
            title: const Text('تحديث حالة الأذونات'),
            subtitle: const Text('فحص الأذونات الحالية مرة أخرى'),
            onTap: _loadPermissionStatus,
          ),
          ListTile(
            leading: const Icon(Icons.settings),
            title: const Text('فتح إعدادات التطبيق'),
            subtitle: const Text('منح الأذونات يدوياً من إعدادات النظام'),
            onTap: _openAppSettings,
          ),
          ListTile(
            leading: const Icon(Icons.restore),
            title: const Text('إعادة تعيين حالة الأذونات'),
            subtitle: const Text('إعادة تعيين حالة رفض الأذونات'),
            onTap: () => _showResetConfirmation(),
          ),
          const Divider(),
          const Text(
            'معلومات إضافية',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue, width: 1),
            ),
            child: const Text(
              'يمكنك منح الأذونات في أي وقت من إعدادات التطبيق. '
              'بعض الميزات قد لا تعمل بدون الأذونات المطلوبة.',
              style: TextStyle(color: Colors.blue),
            ),
          ),
        ],
      ),
    );
  }

  /// عرض تأكيد إعادة التعيين
  void _showResetConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعادة تعيين الأذونات'),
        content: const Text(
          'هل تريد إعادة تعيين حالة الأذونات؟ '
          'سيتم السماح بطلب الأذونات مرة أخرى.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _resetPermissionStatus();
            },
            child: const Text('إعادة تعيين'),
          ),
        ],
      ),
    );
  }

  /// الحصول على أيقونة الإذن
  IconData _getPermissionIcon(String permission) {
    switch (permission) {
      case 'microphone':
        return Icons.mic;
      case 'systemAlertWindow':
        return Icons.picture_in_picture;
      case 'ignoreBatteryOptimizations':
        return Icons.battery_saver;
      case 'notification':
        return Icons.notifications;
      case 'storage':
        return Icons.storage;
      default:
        return Icons.security;
    }
  }

  /// الحصول على اسم الإذن للعرض
  String _getPermissionDisplayName(String permission) {
    switch (permission) {
      case 'microphone':
        return 'الميكروفون';
      case 'systemAlertWindow':
        return 'النوافذ العائمة';
      case 'ignoreBatteryOptimizations':
        return 'تحسين البطارية';
      case 'notification':
        return 'الإشعارات';
      case 'storage':
        return 'التخزين';
      default:
        return permission;
    }
  }
}
