import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:mobile_pos/Provider/user_log_provider.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/model/user_log_model.dart';
import 'package:mobile_pos/services/loader_service.dart';
import 'package:mobile_pos/tools/log_test_tool.dart';
// لا حاجة لاستيراد nb_utils

/// شاشة عرض سجلات المستخدم
class UserLogsScreen extends ConsumerStatefulWidget {
  final String? actionType;
  final String? title;

  const UserLogsScreen({super.key, this.actionType, this.title});

  @override
  ConsumerState<UserLogsScreen> createState() => _UserLogsScreenState();
}

class _UserLogsScreenState extends ConsumerState<UserLogsScreen> {
  // فلتر البحث
  String _searchQuery = '';

  // فلتر نوع النشاط
  String? _selectedActionType;

  // فلتر التاريخ
  DateTime? _startDate;
  DateTime? _endDate;

  // عدد السجلات المعروضة
  int _limit = 100;

  @override
  void initState() {
    super.initState();

    // تعيين نوع النشاط من المعلمات المرسلة
    _selectedActionType = widget.actionType;

    // تعيين تاريخ اليوم كتاريخ البداية والنهاية افتراضيًا
    _startDate = DateTime.now();
    _endDate = DateTime.now();

    // تأخير تحميل البيانات لضمان تهيئة الشاشة بشكل كامل
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        // استخراج المعلمات المرسلة من الشاشة السابقة
        final Map<String, dynamic>? args =
            ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;

        if (args != null) {
          // تحديث نوع النشاط إذا تم تمريره
          if (args.containsKey('actionType')) {
            setState(() {
              _selectedActionType = args['actionType'] as String?;
            });
          }

          // تحديث تاريخ البداية إذا تم تمريره
          if (args.containsKey('startDate')) {
            setState(() {
              _startDate = args['startDate'] as DateTime?;
            });
          }

          // تحديث تاريخ النهاية إذا تم تمريره
          if (args.containsKey('endDate')) {
            setState(() {
              _endDate = args['endDate'] as DateTime?;
            });
          }

          // طباعة معلومات تشخيصية
          debugPrint(
              'تم استلام المعلمات: نوع النشاط: $_selectedActionType، تاريخ البداية: $_startDate، تاريخ النهاية: $_endDate');
        }

        // تحميل البيانات بعد تحديث المعلمات
        // استخدام Future.microtask لتأخير التحميل حتى اكتمال بناء الشاشة
        Future.microtask(() {
          if (mounted) {
            _loadUserLogs();
          }
        });
      }
    });
  }

  // متغير لتتبع ما إذا كان التحميل جاريًا
  bool _isLoading = false;

  // متغير لتخزين آخر فلتر تم استخدامه
  UserLogFilter? _lastFilter;

  /// تحميل سجلات المستخدم
  Future<void> _loadUserLogs() async {
    if (!mounted) return;

    // التحقق مما إذا كان التحميل جاريًا بالفعل
    if (_isLoading) {
      debugPrint('تم تجاهل طلب التحميل لأن هناك تحميل جارٍ بالفعل');
      return;
    }

    try {
      // تعيين حالة التحميل
      _isLoading = true;

      // طباعة معلومات تشخيصية
      debugPrint('بدء تحميل سجلات المستخدم...');
      debugPrint('نوع النشاط: $_selectedActionType');
      debugPrint('تاريخ البداية: ${_startDate?.toString() ?? "غير محدد"}');
      debugPrint('تاريخ النهاية: ${_endDate?.toString() ?? "غير محدد"}');
      debugPrint('الحد الأقصى للسجلات: $_limit');

      // إنشاء فلتر جديد بناءً على القيم المحددة
      final newFilter = UserLogFilter(
        actionType: _selectedActionType,
        startDate: _startDate,
        endDate: _endDate,
        limit: _limit,
      );

      // التحقق مما إذا كان الفلتر الجديد مطابقًا للفلتر السابق
      if (_lastFilter != null &&
          _lastFilter!.actionType == newFilter.actionType &&
          _lastFilter!.startDate == newFilter.startDate &&
          _lastFilter!.endDate == newFilter.endDate &&
          _lastFilter!.limit == newFilter.limit) {
        debugPrint('تم تجاهل طلب التحميل لأن الفلتر لم يتغير');
        _isLoading = false;
        return;
      }

      // تحديث آخر فلتر تم استخدامه
      _lastFilter = newFilter;

      // استخدام خدمة التحميل لتحسين تجربة المستخدم
      await LoaderService.executeWithLoading(() async {
        // تحديث البيانات
        final _ = ref.refresh(userLogsProvider(newFilter));

        // انتظار لحظة لضمان بدء التحميل
        await Future.delayed(const Duration(milliseconds: 300));
      });

      // تحديث الواجهة
      if (mounted) {
        setState(() {});
        debugPrint('تم تحديث واجهة المستخدم');
      }
    } catch (e) {
      debugPrint('خطأ في تحميل سجلات المستخدم: $e');

      // عرض رسالة خطأ
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل سجلات المستخدم: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      // إعادة تعيين حالة التحميل
      _isLoading = false;
    }
  }

  /// إنشاء سجلات اختبارية لتاريخ محدد
  Future<void> _createLogsForSpecificDate() async {
    try {
      // حفظ مرجع ScaffoldMessenger
      final scaffoldMessenger = ScaffoldMessenger.of(context);

      // اختيار تاريخ
      final date = await showDatePicker(
        context: context,
        initialDate: DateTime.now(),
        firstDate: DateTime(2020),
        lastDate: DateTime.now(),
        helpText: 'اختر تاريخ السجلات الاختبارية',
        cancelText: 'إلغاء',
        confirmText: 'تأكيد',
      );

      // التحقق من اختيار تاريخ وأن الحالة ما زالت مثبتة
      if (date == null || !mounted) return;

      // إغلاق مربع الحوار
      Navigator.of(context).pop();

      // التحقق مرة أخرى من أن الحالة ما زالت مثبتة
      if (!mounted) return;

      // استخدام دالة منفصلة لإنشاء السجلات
      await _createTestLogsForDate(date, scaffoldMessenger);
    } catch (e) {
      debugPrint('خطأ في إنشاء سجلات اختبارية لتاريخ محدد: $e');

      // عرض رسالة خطأ
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إنشاء سجلات اختبارية: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// إنشاء سجلات اختبارية لتاريخ محدد
  Future<void> _createTestLogsForDate(
      DateTime date, ScaffoldMessengerState scaffoldMessenger) async {
    // التحقق من أن الحالة ما زالت مثبتة
    if (!mounted) return;

    // إنشاء سجلات للتاريخ المحدد
    final count = await LogTestTool.logTestActionsForDate(
      ref: ref,
      context: context,
      date: date,
      count: 3,
    );

    // التحقق من أن الحالة ما زالت مثبتة
    if (!mounted) return;

    // عرض رسالة نجاح
    scaffoldMessenger.showSnackBar(
      SnackBar(
        content: Text(
            'تم تسجيل $count سجلات اختبارية لتاريخ ${date.day}/${date.month}/${date.year}'),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );

    // التحقق من أن الحالة ما زالت مثبتة
    if (!mounted) return;

    // تحديث تاريخ البحث للتاريخ المحدد
    setState(() {
      _startDate = date;
      _endDate = date;
    });

    // تحميل السجلات الجديدة
    await _loadUserLogs();
  }

  @override
  Widget build(BuildContext context) {
    // إنشاء فلتر سجلات المستخدم
    final filter = UserLogFilter(
      actionType: _selectedActionType,
      startDate: _startDate,
      endDate: _endDate,
      limit: _limit,
    );

    // تحديث آخر فلتر تم استخدامه إذا لم يكن موجودًا
    _lastFilter ??= filter;

    // الحصول على سجلات المستخدم (استخدام watch مع تعليق الاعتمادية)
    final userLogsAsyncValue = ref.watch(userLogsProvider(filter));

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(
          'سجلات النظام',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        backgroundColor: kMainColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          // زر إنشاء سجلات اختبارية
          IconButton(
            icon: const Icon(Icons.add_circle),
            tooltip: 'إنشاء سجلات اختبارية',
            onPressed: () async {
              // استيراد أداة اختبار السجلات
              await _showTestLogDialog();
            },
          ),
          // زر تحديد التاريخ
          IconButton(
            icon: const Icon(Icons.calendar_today),
            tooltip: 'تحديد التاريخ',
            onPressed: () async {
              final date = await showDatePicker(
                context: context,
                initialDate: _startDate ?? DateTime.now(),
                firstDate: DateTime(2020),
                lastDate: DateTime.now(),
                helpText: 'اختر تاريخ السجلات',
                cancelText: 'إلغاء',
                confirmText: 'تأكيد',
                builder: (context, child) {
                  return Theme(
                    data: Theme.of(context).copyWith(
                      colorScheme: const ColorScheme.light(
                        primary: kMainColor,
                        onPrimary: Colors.white,
                        surface: Colors.white,
                        onSurface: Colors.black,
                      ),
                    ),
                    child: child!,
                  );
                },
              );

              if (date != null && mounted) {
                setState(() {
                  _startDate = date;
                  _endDate = date;
                });

                // تحديث البيانات بعد تغيير التاريخ
                await _loadUserLogs();
              }
            },
          ),
          // زر تحديث السجلات
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث',
            onPressed: () async {
              // استخدام دالة تحميل السجلات
              await _loadUserLogs();
            },
          ),
          // زر الفلترة
          IconButton(
            icon: const Icon(Icons.filter_list),
            tooltip: 'فلترة',
            onPressed: () {
              _showFilterDialog();
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // عرض التاريخ الحالي
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
            color: Colors.grey.shade100,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.calendar_today, size: 16, color: kMainColor),
                const SizedBox(width: 8),
                Text(
                  'سجلات تاريخ: ${_formatDate(_startDate)}',
                  style: GoogleFonts.cairo(
                    fontWeight: FontWeight.bold,
                    color: kMainColor,
                  ),
                ),
              ],
            ),
          ),

          // حقل البحث
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              decoration: InputDecoration(
                hintText: 'بحث في السجلات...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),

          // عرض السجلات
          Expanded(
            child: userLogsAsyncValue.when(
              data: (logs) {
                // فلترة السجلات حسب البحث
                final filteredLogs = _filterLogs(logs);

                if (filteredLogs.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.search_off,
                          size: 64,
                          color: Colors.grey,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'لا توجد سجلات',
                          style: GoogleFonts.cairo(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey.shade700,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          _searchQuery.isNotEmpty
                              ? 'لا توجد نتائج تطابق بحثك: "$_searchQuery"'
                              : 'لم يتم العثور على سجلات للتاريخ المحدد',
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 24),
                        ElevatedButton.icon(
                          onPressed: () {
                            _showTestLogDialog();
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: kMainColor,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 8),
                          ),
                          icon:
                              const Icon(Icons.add_circle, color: Colors.white),
                          label: Text(
                            'إنشاء سجلات اختبارية',
                            style: GoogleFonts.cairo(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  itemCount: filteredLogs.length,
                  itemBuilder: (context, index) {
                    final log = filteredLogs[index];
                    return _buildLogItem(log);
                  },
                );
              },
              loading: () => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(kMainColor),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'جاري تحميل السجلات...',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: kMainColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'يرجى الانتظار قليلاً',
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
              error: (error, stackTrace) => Center(
                child: Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        color: Colors.red,
                        size: 48,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'حدث خطأ أثناء تحميل السجلات',
                        style: GoogleFonts.cairo(
                          color: Colors.red,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.red.withAlpha(25),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          error.toString(),
                          style: GoogleFonts.cairo(
                            color: Colors.red,
                            fontSize: 14,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          ElevatedButton.icon(
                            onPressed: () async {
                              // استخدام دالة تحميل السجلات
                              await _loadUserLogs();
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: kMainColor,
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 8),
                            ),
                            icon:
                                const Icon(Icons.refresh, color: Colors.white),
                            label: Text(
                              'إعادة المحاولة',
                              style: GoogleFonts.cairo(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          OutlinedButton.icon(
                            onPressed: () {
                              // إنشاء سجلات اختبارية
                              _showTestLogDialog();
                            },
                            style: OutlinedButton.styleFrom(
                              side: const BorderSide(color: kMainColor),
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 8),
                            ),
                            icon: const Icon(Icons.add_circle_outline,
                                color: kMainColor),
                            label: Text(
                              'إنشاء سجلات اختبارية',
                              style: GoogleFonts.cairo(
                                color: kMainColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر السجل
  Widget _buildLogItem(UserLogModel log) {
    // الحصول على لون وأيقونة نوع النشاط
    final (color, icon) = _getActionTypeColorAndIcon(log.actionType);

    // تنسيق التاريخ
    final formattedDate = DateFormat('yyyy-MM-dd HH:mm').format(log.timestamp);

    // تحديد ما إذا كان السجل من اليوم
    final isToday = _isToday(log.timestamp);

    // تحديد نوع النشاط بشكل مقروء
    final actionTypeText = _getReadableActionType(log.actionType);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      // إضافة لون خلفية فاتح للسجلات من اليوم
      color: isToday ? color.withAlpha(15) : null,
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: CircleAvatar(
          backgroundColor: color.withAlpha(51),
          child: Icon(
            icon,
            color: color,
          ),
        ),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عرض نوع النشاط
            Row(
              children: [
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: color.withAlpha(30),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    actionTypeText,
                    style: GoogleFonts.cairo(
                      fontSize: 10,
                      color: color,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                // عرض علامة "اليوم" إذا كان السجل من اليوم
                if (isToday)
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.green.withAlpha(30),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      'اليوم',
                      style: GoogleFonts.cairo(
                        fontSize: 10,
                        color: Colors.green,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 4),
            // عرض وصف النشاط
            Text(
              log.description,
              style: GoogleFonts.cairo(
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            // عرض اسم المستخدم
            Row(
              children: [
                const Icon(Icons.person, size: 12, color: Colors.grey),
                const SizedBox(width: 4),
                Text(
                  log.userName,
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.grey.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 2),
            // عرض التاريخ
            Row(
              children: [
                const Icon(Icons.access_time, size: 12, color: Colors.grey),
                const SizedBox(width: 4),
                Text(
                  formattedDate,
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.grey.shade700,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: () {
          // الانتقال إلى شاشة تفاصيل السجل
          Navigator.pushNamed(
            context,
            '/UserLogDetails',
            arguments: {
              'log': log,
            },
          );
        },
      ),
    );
  }

  /// التحقق مما إذا كان التاريخ هو اليوم
  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }

  /// الحصول على نوع النشاط بشكل مقروء
  String _getReadableActionType(String actionType) {
    if (actionType.startsWith('sales_')) {
      return 'مبيعات';
    } else if (actionType.startsWith('purchase_')) {
      return 'مشتريات';
    } else if (actionType.startsWith('due_')) {
      return 'مديونية';
    } else if (actionType.startsWith('inventory_')) {
      return 'مخزون';
    } else if (actionType.startsWith('customer_')) {
      return 'عملاء';
    } else if (actionType.startsWith('supplier_')) {
      return 'موردين';
    } else if (actionType.startsWith('user_')) {
      return 'مستخدمين';
    } else if (actionType.startsWith('system_')) {
      return 'نظام';
    } else if (actionType.startsWith('expense_')) {
      return 'مصروفات';
    } else {
      return 'أخرى';
    }
  }

  /// فلترة السجلات حسب البحث
  List<UserLogModel> _filterLogs(List<UserLogModel> logs) {
    if (_searchQuery.isEmpty) {
      return logs;
    }

    final query = _searchQuery.toLowerCase();

    return logs.where((log) {
      // البحث في الوصف
      if (log.description.toLowerCase().contains(query)) {
        return true;
      }

      // البحث في اسم المستخدم
      if (log.userName.toLowerCase().contains(query)) {
        return true;
      }

      // البحث في نوع النشاط
      if (log.actionType.toLowerCase().contains(query)) {
        return true;
      }

      // البحث في نوع النشاط المقروء
      if (_getReadableActionType(log.actionType)
          .toLowerCase()
          .contains(query)) {
        return true;
      }

      // البحث في البيانات المرتبطة بالسجل
      for (var entry in log.data.entries) {
        // تحويل القيمة إلى نص
        final value = entry.value?.toString().toLowerCase() ?? '';
        if (value.contains(query)) {
          return true;
        }
      }

      // البحث في التاريخ
      final formattedDate =
          DateFormat('yyyy-MM-dd HH:mm').format(log.timestamp);
      if (formattedDate.toLowerCase().contains(query)) {
        return true;
      }

      return false;
    }).toList();
  }

  /// تنسيق التاريخ للعرض
  String _formatDate(DateTime? date) {
    if (date == null) {
      return 'غير محدد';
    }

    // استخدام تنسيق عربي للتاريخ
    final formatter = DateFormat('yyyy/MM/dd', 'ar');
    return formatter.format(date);
  }

  /// عرض مربع حوار إنشاء سجلات اختبارية
  Future<void> _showTestLogDialog() async {
    return showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(
            'إنشاء سجلات اختبارية',
            style: GoogleFonts.cairo(
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'هذه الأداة تساعد في اختبار نظام تسجيل الأنشطة من خلال إنشاء سجلات اختبارية.',
                style: GoogleFonts.cairo(),
              ),
              const SizedBox(height: 16),
              Text(
                'اختر عدد السجلات التي تريد إنشاءها:',
                style: GoogleFonts.cairo(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Column(
                children: [
                  // صف الأزرار الأول
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      ElevatedButton(
                        onPressed: () async {
                          // حفظ مرجع ScaffoldMessenger قبل إغلاق الحوار
                          final scaffoldMessenger =
                              ScaffoldMessenger.of(context);

                          Navigator.pop(context);
                          await LogTestTool.logTestAction(
                            ref: ref,
                            context: context,
                          );

                          // عرض رسالة نجاح
                          if (mounted) {
                            scaffoldMessenger.showSnackBar(
                              const SnackBar(
                                content:
                                    Text('تم تسجيل النشاط الاختباري بنجاح'),
                                backgroundColor: Colors.green,
                                duration: Duration(seconds: 2),
                              ),
                            );
                          }

                          // تحديث البيانات
                          if (mounted) {
                            await _loadUserLogs();
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: kMainColor,
                        ),
                        child: Text(
                          'سجل واحد',
                          style: GoogleFonts.cairo(),
                        ),
                      ),
                      ElevatedButton(
                        onPressed: () async {
                          // حفظ مرجع ScaffoldMessenger قبل إغلاق الحوار
                          final scaffoldMessenger =
                              ScaffoldMessenger.of(context);

                          Navigator.pop(context);
                          await LogTestTool.logMultipleTestActions(
                            ref: ref,
                            context: context,
                            count: 5,
                          );

                          // عرض رسالة نجاح
                          if (mounted) {
                            scaffoldMessenger.showSnackBar(
                              const SnackBar(
                                content:
                                    Text('تم تسجيل 5 سجلات اختبارية بنجاح'),
                                backgroundColor: Colors.green,
                                duration: Duration(seconds: 2),
                              ),
                            );
                          }

                          // تحديث البيانات
                          if (mounted) {
                            await _loadUserLogs();
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                        ),
                        child: Text(
                          '5 سجلات',
                          style: GoogleFonts.cairo(),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // عنوان قسم سجلات لتاريخ محدد
                  Text(
                    'إنشاء سجلات لتاريخ محدد:',
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.bold,
                    ),
                  ),

                  const SizedBox(height: 8),

                  // صف الأزرار الثاني
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      ElevatedButton(
                        onPressed: () {
                          // استخدام دالة منفصلة لتجنب مشاكل BuildContext
                          _createLogsForSpecificDate();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.teal,
                        ),
                        child: Text(
                          'سجلات لتاريخ محدد',
                          style: GoogleFonts.cairo(),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: Text(
                'إلغاء',
                style: GoogleFonts.cairo(),
              ),
            ),
          ],
        );
      },
    );
  }

  /// عرض مربع حوار الفلترة
  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Text(
                'فلترة السجلات',
                style: GoogleFonts.cairo(
                  fontWeight: FontWeight.bold,
                ),
              ),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // فلتر نوع النشاط
                    DropdownButtonFormField<String?>(
                      decoration: const InputDecoration(
                        labelText: 'نوع النشاط',
                        border: OutlineInputBorder(),
                      ),
                      value: _selectedActionType,
                      items: [
                        const DropdownMenuItem<String?>(
                          value: null,
                          child: Text('الكل'),
                        ),
                        // أنشطة المبيعات
                        DropdownMenuItem<String>(
                          value: 'sales_',
                          child: Text('المبيعات', style: GoogleFonts.cairo()),
                        ),
                        // أنشطة المشتريات
                        DropdownMenuItem<String>(
                          value: 'purchase_',
                          child: Text('المشتريات', style: GoogleFonts.cairo()),
                        ),
                        // أنشطة المديونية
                        DropdownMenuItem<String>(
                          value: 'due_',
                          child: Text('المديونية', style: GoogleFonts.cairo()),
                        ),
                        // أنشطة المخزون
                        DropdownMenuItem<String>(
                          value: 'inventory_',
                          child: Text('المخزون', style: GoogleFonts.cairo()),
                        ),
                        // أنشطة العملاء
                        DropdownMenuItem<String>(
                          value: 'customer_',
                          child: Text('العملاء', style: GoogleFonts.cairo()),
                        ),
                        // أنشطة الموردين
                        DropdownMenuItem<String>(
                          value: 'supplier_',
                          child: Text('الموردين', style: GoogleFonts.cairo()),
                        ),
                        // أنشطة المستخدمين
                        DropdownMenuItem<String>(
                          value: 'user_',
                          child: Text('المستخدمين', style: GoogleFonts.cairo()),
                        ),
                        // أنشطة النظام
                        DropdownMenuItem<String>(
                          value: 'system_',
                          child: Text('النظام', style: GoogleFonts.cairo()),
                        ),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _selectedActionType = value;
                        });
                      },
                    ),

                    const SizedBox(height: 16),

                    // فلتر تاريخ البداية
                    InkWell(
                      onTap: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: _startDate ?? DateTime.now(),
                          firstDate: DateTime(2020),
                          lastDate: DateTime.now(),
                        );

                        if (date != null) {
                          setState(() {
                            _startDate = date;
                          });
                        }
                      },
                      child: InputDecorator(
                        decoration: const InputDecoration(
                          labelText: 'تاريخ البداية',
                          border: OutlineInputBorder(),
                        ),
                        child: Text(
                          _startDate != null
                              ? DateFormat('yyyy-MM-dd').format(_startDate!)
                              : 'اختر تاريخ البداية',
                          style: GoogleFonts.cairo(),
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // فلتر تاريخ النهاية
                    InkWell(
                      onTap: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: _endDate ?? DateTime.now(),
                          firstDate: DateTime(2020),
                          lastDate: DateTime.now(),
                        );

                        if (date != null) {
                          setState(() {
                            _endDate = date;
                          });
                        }
                      },
                      child: InputDecorator(
                        decoration: const InputDecoration(
                          labelText: 'تاريخ النهاية',
                          border: OutlineInputBorder(),
                        ),
                        child: Text(
                          _endDate != null
                              ? DateFormat('yyyy-MM-dd').format(_endDate!)
                              : 'اختر تاريخ النهاية',
                          style: GoogleFonts.cairo(),
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // فلتر عدد السجلات
                    DropdownButtonFormField<int>(
                      decoration: const InputDecoration(
                        labelText: 'عدد السجلات',
                        border: OutlineInputBorder(),
                      ),
                      value: _limit,
                      items: [
                        DropdownMenuItem<int>(
                          value: 50,
                          child: Text('50', style: GoogleFonts.cairo()),
                        ),
                        DropdownMenuItem<int>(
                          value: 100,
                          child: Text('100', style: GoogleFonts.cairo()),
                        ),
                        DropdownMenuItem<int>(
                          value: 200,
                          child: Text('200', style: GoogleFonts.cairo()),
                        ),
                        DropdownMenuItem<int>(
                          value: 500,
                          child: Text('500', style: GoogleFonts.cairo()),
                        ),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _limit = value!;
                        });
                      },
                    ),
                  ],
                ),
              ),
              actions: [
                // زر تنظيف السجلات القديمة
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                    _showCleanupConfirmationDialog();
                  },
                  child: Text(
                    'تنظيف السجلات القديمة',
                    style: GoogleFonts.cairo(
                      color: Colors.orange,
                    ),
                  ),
                ),
                // زر إعادة تعيين الفلتر
                TextButton(
                  onPressed: () {
                    setState(() {
                      _selectedActionType = null;
                      _startDate = DateTime.now();
                      _endDate = DateTime.now();
                      _limit = 100;
                    });
                  },
                  child: Text(
                    'إعادة تعيين',
                    style: GoogleFonts.cairo(
                      color: Colors.red,
                    ),
                  ),
                ),
                // زر إلغاء
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: Text(
                    'إلغاء',
                    style: GoogleFonts.cairo(),
                  ),
                ),
                // زر تطبيق
                TextButton(
                  onPressed: () async {
                    Navigator.pop(context);
                    // تحديث البيانات باستخدام دالة تحميل السجلات
                    await _loadUserLogs();
                    this.setState(() {});
                  },
                  child: Text(
                    'تطبيق',
                    style: GoogleFonts.cairo(
                      color: kMainColor,
                    ),
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  /// عرض مربع حوار تأكيد تنظيف السجلات القديمة
  void _showCleanupConfirmationDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(
            'تنظيف السجلات القديمة',
            style: GoogleFonts.cairo(
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Text(
            'هل أنت متأكد من رغبتك في حذف السجلات القديمة (أكثر من 30 يوم)؟\n\nلا يمكن التراجع عن هذه العملية.',
            style: GoogleFonts.cairo(),
          ),
          actions: [
            // زر إلغاء
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: Text(
                'إلغاء',
                style: GoogleFonts.cairo(),
              ),
            ),
            // زر تأكيد
            TextButton(
              onPressed: () async {
                Navigator.pop(context);

                // استخدام خدمة التحميل لتحسين تجربة المستخدم
                await LoaderService.executeWithLoadingHandleErrors(
                  () async {
                    // الحصول على خدمة سجلات المستخدم
                    final userLogService = ref.read(userLogServiceProvider);

                    // حذف السجلات القديمة
                    final success =
                        await userLogService.deleteOldLogs(olderThanDays: 30);

                    if (!success) {
                      throw Exception('فشل في حذف السجلات القديمة');
                    }

                    // تحديث البيانات بعد الحذف
                    await _loadUserLogs();

                    return success;
                  },
                  successMessage: 'تم تنظيف السجلات القديمة بنجاح',
                  errorPrefix: 'خطأ في تنظيف السجلات: ',
                );
              },
              child: Text(
                'تأكيد الحذف',
                style: GoogleFonts.cairo(
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// الحصول على لون وأيقونة نوع النشاط
  (Color, IconData) _getActionTypeColorAndIcon(String actionType) {
    if (actionType.startsWith('sales_')) {
      return (Colors.green, Icons.shopping_cart);
    } else if (actionType.startsWith('purchase_')) {
      return (Colors.blue, Icons.shopping_bag);
    } else if (actionType.startsWith('due_')) {
      return (Colors.red, Icons.money_off);
    } else if (actionType.startsWith('inventory_')) {
      return (Colors.orange, Icons.inventory);
    } else if (actionType.startsWith('customer_')) {
      return (Colors.purple, Icons.person);
    } else if (actionType.startsWith('supplier_')) {
      return (Colors.teal, Icons.business);
    } else if (actionType.startsWith('user_')) {
      return (Colors.indigo, Icons.person_pin);
    } else if (actionType.startsWith('system_')) {
      return (Colors.grey, Icons.settings);
    } else {
      return (Colors.black, Icons.info);
    }
  }
}
