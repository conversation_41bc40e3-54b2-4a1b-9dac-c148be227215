// شاشة تقرير أداء البائعين بالنسبة للتارجت الشهري
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/const_commas.dart';
import 'package:mobile_pos/empty_screen_widget.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:mobile_pos/Screens/Report/Screens/seller_target_management_screen.dart';

/// مزود حالة الشهر المختار
final selectedMonthProvider = StateProvider<DateTime>((ref) {
  // إرجاع تاريخ اليوم الحالي مع ضبط اليوم إلى 1 والوقت إلى 00:00:00
  final now = DateTime.now();
  return DateTime(now.year, now.month, 1);
});

/// مزود بيانات تقرير أداء البائعين
final sellerTargetReportProvider =
    FutureProvider.family<List<SellerTargetReport>, DateTime>(
        (ref, selectedMonth) async {
  // استخدام تنسيق yyyy_MM للشهر المختار
  final yearMonth = DateFormat('yyyy_MM').format(selectedMonth);
  final userId = await getUserID();

  // قائمة لتخزين تقارير أداء البائعين
  List<SellerTargetReport> reports = [];

  try {
    // 1. الحصول على بيانات التارجت من Firebase
    final targetsRef =
        FirebaseDatabase.instance.ref('$userId/targets/$yearMonth');
    final targetsSnapshot = await targetsRef.get();

    // خريطة لتخزين التارجت لكل بائع
    Map<String, double> targetMap = {};

    if (targetsSnapshot.exists) {
      final targetsData =
          Map<String, dynamic>.from(targetsSnapshot.value as Map);

      // استخراج التارجت لكل بائع
      targetsData.forEach((sellerName, data) {
        if (data is Map) {
          final targetAmount = (data['target_amount'] ?? 0).toDouble();
          targetMap[sellerName] = targetAmount;
        } else {
          // إذا كان الهيكل بسيط (مباشرة قيمة التارجت)
          targetMap[sellerName] = (data ?? 0).toDouble();
        }
      });
    }

    // 2. الحصول على بيانات المبيعات من Firebase
    final salesRef = FirebaseDatabase.instance.ref('$userId/Sales Transition');
    final salesSnapshot = await salesRef.get();

    // خريطة لتخزين إجمالي المبيعات لكل بائع
    Map<String, double> salesMap = {};

    if (salesSnapshot.exists) {
      for (var child in salesSnapshot.children) {
        final data = child.value as Map<dynamic, dynamic>?;
        if (data != null) {
          // التحقق من تاريخ المبيعات
          final purchaseDate = data['purchaseDate'] as String?;
          if (purchaseDate != null) {
            try {
              final date = DateTime.parse(purchaseDate);
              final saleYearMonth = DateFormat('yyyy_MM').format(date);

              // التحقق من أن المبيعات في الشهر المحدد
              if (saleYearMonth == yearMonth) {
                final sellerName = data['sellerName'] as String? ?? 'غير معروف';
                final totalAmount = (data['totalAmount'] ?? 0).toDouble();

                // إضافة المبيعات إلى الخريطة
                if (salesMap.containsKey(sellerName)) {
                  salesMap[sellerName] = salesMap[sellerName]! + totalAmount;
                } else {
                  salesMap[sellerName] = totalAmount;
                }
              }
            } catch (e) {
              // تجاهل الأخطاء في تحليل التاريخ
            }
          }
        }
      }
    }

    // 3. دمج البيانات وإنشاء تقارير أداء البائعين

    // إضافة البائعين الذين لديهم تارجت
    targetMap.forEach((sellerName, targetAmount) {
      final salesAmount = salesMap[sellerName] ?? 0;
      final achievementPercentage =
          targetAmount > 0 ? (salesAmount / targetAmount * 100) : 0.0;

      reports.add(SellerTargetReport(
        sellerName: sellerName,
        targetAmount: targetAmount,
        salesAmount: salesAmount,
        achievementPercentage: achievementPercentage,
      ));
    });

    // إضافة البائعين الذين ليس لديهم تارجت ولكن لديهم مبيعات
    salesMap.forEach((sellerName, salesAmount) {
      if (!targetMap.containsKey(sellerName)) {
        reports.add(SellerTargetReport(
          sellerName: sellerName,
          targetAmount: 0,
          salesAmount: salesAmount,
          achievementPercentage: 0.0,
        ));
      }
    });

    // ترتيب التقارير حسب نسبة الإنجاز تنازليًا
    reports.sort(
        (a, b) => b.achievementPercentage.compareTo(a.achievementPercentage));

    return reports;
  } catch (e) {
    // إرجاع قائمة فارغة في حالة حدوث خطأ
    return [];
  }
});

/// نموذج بيانات تقرير أداء البائعين
class SellerTargetReport {
  final String sellerName;
  final double targetAmount;
  final double salesAmount;
  final double achievementPercentage;

  SellerTargetReport({
    required this.sellerName,
    required this.targetAmount,
    required this.salesAmount,
    required this.achievementPercentage,
  });

  /// الحصول على حالة الإنجاز
  String get status {
    if (achievementPercentage >= 100) {
      return 'وصل التارجت';
    } else if (achievementPercentage >= 75) {
      return 'قريب';
    } else {
      return 'بعيد';
    }
  }

  /// الحصول على لون حالة الإنجاز
  Color get statusColor {
    if (achievementPercentage >= 100) {
      return Colors.green;
    } else if (achievementPercentage >= 75) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }
}

/// شاشة تقرير أداء البائعين
class SellerTargetReportScreen extends ConsumerWidget {
  const SellerTargetReportScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // الشهر المختار
    final selectedMonth = ref.watch(selectedMonthProvider);

    // بيانات التقرير
    final reportData = ref.watch(sellerTargetReportProvider(selectedMonth));

    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        title: Text(
          'تقرير أداء البائعين',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        backgroundColor: kMainColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          // زر إدارة الأهداف
          IconButton(
            icon: const Icon(Icons.edit),
            tooltip: 'إدارة الأهداف',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SellerTargetManagementScreen(),
                ),
              ).then((_) {
                // تحديث البيانات عند العودة من شاشة إدارة الأهداف
                final _ = ref.refresh(sellerTargetReportProvider(
                    ref.read(selectedMonthProvider)));
              });
            },
          ),
          // زر تصدير التقرير (يمكن إضافة وظيفة التصدير لاحقًا)
          IconButton(
            icon: const Icon(Icons.print),
            tooltip: 'تصدير التقرير',
            onPressed: () {
              // وظيفة تصدير التقرير
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('سيتم إضافة وظيفة التصدير قريبًا'),
                ),
              );
            },
          ),
        ],
      ),
      body: Container(
        alignment: Alignment.topCenter,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(30),
            topLeft: Radius.circular(30),
          ),
        ),
        child: Column(
          children: [
            // اختيار الشهر
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  Text(
                    'اختر الشهر:',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<DateTime>(
                          value: selectedMonth,
                          isExpanded: true,
                          items: _buildMonthDropdownItems(),
                          onChanged: (value) {
                            if (value != null) {
                              // تحديث القيمة المختارة مع التأكد من استخدام نفس تنسيق التاريخ
                              ref.read(selectedMonthProvider.notifier).state =
                                  DateTime(value.year, value.month, 1);
                            }
                          },
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // بيانات التقرير
            Expanded(
              child: reportData.when(
                data: (reports) {
                  if (reports.isEmpty) {
                    return const EmptyScreenWidget();
                  }

                  return SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // ملخص التقرير
                        _buildSummaryCard(reports),

                        const SizedBox(height: 16),

                        // جدول البيانات
                        Card(
                          elevation: 2,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'تفاصيل أداء البائعين',
                                  style: GoogleFonts.cairo(
                                    color: kMainColor,
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 16),

                                // رأس الجدول
                                Container(
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 8),
                                  decoration: BoxDecoration(
                                    color: Color.fromRGBO(
                                        (kMainColor.r).toInt(),
                                        (kMainColor.g).toInt(),
                                        (kMainColor.b).toInt(),
                                        0.1),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Row(
                                    children: [
                                      Expanded(
                                        flex: 3,
                                        child: Text(
                                          'اسم البائع',
                                          textAlign: TextAlign.center,
                                          style: GoogleFonts.cairo(
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                      Expanded(
                                        flex: 2,
                                        child: Text(
                                          'التارجت',
                                          textAlign: TextAlign.center,
                                          style: GoogleFonts.cairo(
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                      Expanded(
                                        flex: 2,
                                        child: Text(
                                          'المبيعات',
                                          textAlign: TextAlign.center,
                                          style: GoogleFonts.cairo(
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                      Expanded(
                                        flex: 2,
                                        child: Text(
                                          'النسبة',
                                          textAlign: TextAlign.center,
                                          style: GoogleFonts.cairo(
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                      Expanded(
                                        flex: 2,
                                        child: Text(
                                          'الحالة',
                                          textAlign: TextAlign.center,
                                          style: GoogleFonts.cairo(
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),

                                // صفوف الجدول
                                ListView.builder(
                                  shrinkWrap: true,
                                  physics: const NeverScrollableScrollPhysics(),
                                  itemCount: reports.length,
                                  itemBuilder: (context, index) {
                                    final report = reports[index];
                                    return Container(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 12),
                                      decoration: BoxDecoration(
                                        border: Border(
                                          bottom: BorderSide(
                                            color: Colors.grey.shade200,
                                          ),
                                        ),
                                      ),
                                      child: Row(
                                        children: [
                                          Expanded(
                                            flex: 3,
                                            child: Text(
                                              report.sellerName,
                                              textAlign: TextAlign.center,
                                              style: GoogleFonts.cairo(),
                                            ),
                                          ),
                                          Expanded(
                                            flex: 2,
                                            child: Text(
                                              myFormat
                                                  .format(report.targetAmount),
                                              textAlign: TextAlign.center,
                                              style: GoogleFonts.cairo(),
                                            ),
                                          ),
                                          Expanded(
                                            flex: 2,
                                            child: Text(
                                              myFormat
                                                  .format(report.salesAmount),
                                              textAlign: TextAlign.center,
                                              style: GoogleFonts.cairo(),
                                            ),
                                          ),
                                          Expanded(
                                            flex: 2,
                                            child: Text(
                                              '${report.achievementPercentage.toStringAsFixed(1)}%',
                                              textAlign: TextAlign.center,
                                              style: GoogleFonts.cairo(),
                                            ),
                                          ),
                                          Expanded(
                                            flex: 2,
                                            child: Container(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                horizontal: 8,
                                                vertical: 4,
                                              ),
                                              decoration: BoxDecoration(
                                                color: Color.fromRGBO(
                                                    (report.statusColor.r)
                                                        .toInt(),
                                                    (report.statusColor.g)
                                                        .toInt(),
                                                    (report.statusColor.b)
                                                        .toInt(),
                                                    0.2),
                                                borderRadius:
                                                    BorderRadius.circular(12),
                                              ),
                                              child: Text(
                                                report.status,
                                                textAlign: TextAlign.center,
                                                style: GoogleFonts.cairo(
                                                  color: report.statusColor,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    );
                                  },
                                ),
                              ],
                            ),
                          ),
                        ),

                        const SizedBox(height: 16),

                        // مؤشرات الأداء
                        Card(
                          elevation: 2,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'مؤشرات الأداء',
                                  style: GoogleFonts.cairo(
                                    color: kMainColor,
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 16),

                                // مؤشرات الأداء لكل بائع
                                ListView.builder(
                                  shrinkWrap: true,
                                  physics: const NeverScrollableScrollPhysics(),
                                  itemCount: reports.length,
                                  itemBuilder: (context, index) {
                                    final report = reports[index];
                                    return Padding(
                                      padding:
                                          const EdgeInsets.only(bottom: 16),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            report.sellerName,
                                            style: GoogleFonts.cairo(
                                              fontWeight: FontWeight.bold,
                                              fontSize: 16,
                                            ),
                                          ),
                                          const SizedBox(height: 8),
                                          LinearProgressIndicator(
                                            value:
                                                report.achievementPercentage /
                                                    100,
                                            backgroundColor:
                                                Colors.grey.shade200,
                                            color: report.statusColor,
                                            minHeight: 10,
                                            borderRadius:
                                                BorderRadius.circular(5),
                                          ),
                                          const SizedBox(height: 4),
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(
                                                '${report.achievementPercentage.toStringAsFixed(1)}%',
                                                style: GoogleFonts.cairo(
                                                  color: report.statusColor,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                              Text(
                                                '${myFormat.format(report.salesAmount)} / ${myFormat.format(report.targetAmount)}',
                                                style: GoogleFonts.cairo(
                                                  color: Colors.grey.shade700,
                                                ),
                                              ),
                                            ],
                                          ),
                                          if (index < reports.length - 1)
                                            const Divider(height: 24),
                                        ],
                                      ),
                                    );
                                  },
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
                loading: () => const Center(
                  child: CircularProgressIndicator(),
                ),
                error: (error, stackTrace) => Center(
                  child: Text(
                    'حدث خطأ: $error',
                    style: const TextStyle(
                      color: Colors.red,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عناصر القائمة المنسدلة للشهور
  List<DropdownMenuItem<DateTime>> _buildMonthDropdownItems() {
    final now = DateTime.now();
    final items = <DropdownMenuItem<DateTime>>[];

    // إضافة الشهر الحالي و11 شهر سابق
    for (int i = 0; i < 12; i++) {
      // إنشاء تاريخ جديد مع ضبط اليوم إلى 1 والوقت إلى 00:00:00
      final date = DateTime(now.year, now.month - i, 1);
      items.add(DropdownMenuItem(
        // استخدام تاريخ بدون وقت للمقارنة
        value: date,
        child: Text(
          DateFormat('MMMM yyyy', 'ar').format(date),
          style: GoogleFonts.cairo(),
        ),
      ));
    }

    return items;
  }

  /// بناء بطاقة ملخص التقرير
  Widget _buildSummaryCard(List<SellerTargetReport> reports) {
    // حساب إجمالي التارجت والمبيعات
    final totalTarget =
        reports.fold<double>(0, (sum, report) => sum + report.targetAmount);
    final totalSales =
        reports.fold<double>(0, (sum, report) => sum + report.salesAmount);
    final overallPercentage =
        totalTarget > 0 ? (totalSales / totalTarget * 100) : 0.0;

    // حساب عدد البائعين الذين وصلوا للتارجت
    final achievedCount =
        reports.where((report) => report.achievementPercentage >= 100).length;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ملخص الأداء',
              style: GoogleFonts.cairo(
                color: kMainColor,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    title: 'إجمالي التارجت',
                    value: myFormat.format(totalTarget),
                    icon: Icons.flag,
                    color: kMainColor,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    title: 'إجمالي المبيعات',
                    value: myFormat.format(totalSales),
                    icon: Icons.shopping_cart,
                    color: Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    title: 'نسبة الإنجاز',
                    value: '${overallPercentage.toStringAsFixed(1)}%',
                    icon: Icons.pie_chart,
                    color: _getStatusColor(overallPercentage),
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    title: 'وصلوا للتارجت',
                    value: '$achievedCount من ${reports.length}',
                    icon: Icons.people,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عنصر ملخص
  Widget _buildSummaryItem({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Color.fromRGBO(
                  (color.r).toInt(), (color.g).toInt(), (color.b).toInt(), 0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.cairo(
                    color: Colors.grey.shade700,
                    fontSize: 12,
                  ),
                ),
                Text(
                  value,
                  style: GoogleFonts.cairo(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// الحصول على لون الحالة بناءً على النسبة
  Color _getStatusColor(double percentage) {
    if (percentage >= 100) {
      return Colors.green;
    } else if (percentage >= 75) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }
}
