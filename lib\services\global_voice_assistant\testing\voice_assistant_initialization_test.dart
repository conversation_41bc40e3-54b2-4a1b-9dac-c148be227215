// بسم الله الرحمن الرحيم
// اختبار تهيئة المساعد الصوتي - AmrDevPOS

import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import '../core/voice_assistant_initialization_manager.dart';
import '../voice_assistant_exports.dart';

/// شاشة اختبار تهيئة المساعد الصوتي
class VoiceAssistantInitializationTestScreen extends StatefulWidget {
  const VoiceAssistantInitializationTestScreen({super.key});

  @override
  State<VoiceAssistantInitializationTestScreen> createState() =>
      _VoiceAssistantInitializationTestScreenState();
}

class _VoiceAssistantInitializationTestScreenState
    extends State<VoiceAssistantInitializationTestScreen> {
  final VoiceAssistantInitializationManager _initManager =
      VoiceAssistantInitializationManager();

  String _testResults = '';
  bool _isRunning = false;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _checkInitializationStatus();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('🔧 اختبار تهيئة المساعد الصوتي'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // معلومات الحالة
            Card(
              color: _isInitialized ? Colors.green.shade50 : Colors.red.shade50,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          _isInitialized ? Icons.check_circle : Icons.error,
                          color: _isInitialized ? Colors.green : Colors.red,
                          size: 24,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'حالة التهيئة',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: _isInitialized ? Colors.green : Colors.red,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 10),
                    Text(
                      _isInitialized
                          ? '✅ المساعد الصوتي مهيأ ويعمل بشكل صحيح'
                          : '❌ المساعد الصوتي غير مهيأ أو يواجه مشاكل',
                      style: const TextStyle(fontSize: 16),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // أزرار الاختبار
            Wrap(
              spacing: 10,
              runSpacing: 10,
              children: [
                ElevatedButton.icon(
                  onPressed: _isRunning ? null : _testInitialization,
                  icon: const Icon(Icons.play_arrow),
                  label: const Text('اختبار التهيئة'),
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
                ),
                ElevatedButton.icon(
                  onPressed: _isRunning ? null : _testPermissions,
                  icon: const Icon(Icons.security),
                  label: const Text('اختبار الأذونات'),
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
                ),
                ElevatedButton.icon(
                  onPressed: _isRunning ? null : _testServices,
                  icon: const Icon(Icons.settings),
                  label: const Text('اختبار الخدمات'),
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
                ),
                ElevatedButton.icon(
                  onPressed: _isRunning ? null : _resetAndTest,
                  icon: const Icon(Icons.refresh),
                  label: const Text('إعادة تعيين واختبار'),
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.purple),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // نتائج الاختبار
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Text(
                            '📋 نتائج الاختبار',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Spacer(),
                          if (_isRunning)
                            const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                        ],
                      ),
                      const SizedBox(height: 10),
                      Expanded(
                        child: SingleChildScrollView(
                          child: Text(
                            _testResults.isEmpty
                                ? 'لم يتم تشغيل أي اختبار بعد'
                                : _testResults,
                            style: const TextStyle(
                              fontFamily: 'monospace',
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _checkInitializationStatus() {
    try {
      final status = _initManager.getInitializationStatus();
      setState(() {
        _isInitialized = status['isGloballyInitialized'] ?? false;
      });
    } catch (e) {
      setState(() {
        _isInitialized = false;
      });
    }
  }

  void _addResult(String result) {
    setState(() {
      _testResults += '${DateTime.now().toIso8601String()}: $result\n';
    });
  }

  Future<void> _testInitialization() async {
    setState(() => _isRunning = true);
    _addResult('🔧 بدء اختبار التهيئة...');

    try {
      // اختبار التهيئة الأساسية
      _addResult('📝 اختبار التهيئة الأساسية...');
      final initialized = await _initManager.initializeGlobalVoiceAssistant();
      
      if (initialized) {
        _addResult('✅ نجحت التهيئة الأساسية');
        
        // فحص حالة التهيئة
        final status = _initManager.getInitializationStatus();
        _addResult('📊 حالة التهيئة: ${status.toString()}');
        
        setState(() {
          _isInitialized = true;
        });
      } else {
        _addResult('❌ فشلت التهيئة الأساسية');
        setState(() {
          _isInitialized = false;
        });
      }
    } catch (e) {
      _addResult('❌ خطأ في اختبار التهيئة: $e');
      setState(() {
        _isInitialized = false;
      });
    }

    setState(() => _isRunning = false);
  }

  Future<void> _testPermissions() async {
    setState(() => _isRunning = true);
    _addResult('🔒 بدء اختبار الأذونات...');

    try {
      // اختبار إذن الميكروفون
      final micStatus = await Permission.microphone.status;
      _addResult('🎤 إذن الميكروفون: $micStatus');
      
      if (micStatus != PermissionStatus.granted) {
        _addResult('⚠️ طلب إذن الميكروفون...');
        final newMicStatus = await Permission.microphone.request();
        _addResult('🎤 إذن الميكروفون الجديد: $newMicStatus');
      }

      // اختبار أذونات أخرى
      final storageStatus = await Permission.storage.status;
      _addResult('💾 إذن التخزين: $storageStatus');

      _addResult('✅ انتهى اختبار الأذونات');
    } catch (e) {
      _addResult('❌ خطأ في اختبار الأذونات: $e');
    }

    setState(() => _isRunning = false);
  }

  Future<void> _testServices() async {
    setState(() => _isRunning = true);
    _addResult('⚙️ بدء اختبار الخدمات...');

    try {
      // اختبار الخدمة الأساسية
      _addResult('🔧 اختبار الخدمة الأساسية...');
      final coreService = _initManager.coreService;
      final coreStatus = coreService.getStatus();
      _addResult('📊 حالة الخدمة الأساسية: ${coreStatus.toString()}');

      // اختبار الخدمة المبسطة
      _addResult('🔧 اختبار الخدمة المبسطة...');
      final simplifiedService = _initManager.simplifiedService;
      final isSimplifiedInitialized = simplifiedService.isInitialized;
      _addResult('📊 الخدمة المبسطة مهيأة: $isSimplifiedInitialized');

      // اختبار خدمة الأذونات
      _addResult('🔧 اختبار خدمة الأذونات...');
      final permissionsService = _initManager.permissionsService;
      final canRun = await permissionsService.canRunVoiceAssistant();
      _addResult('📊 يمكن تشغيل المساعد الصوتي: $canRun');

      _addResult('✅ انتهى اختبار الخدمات');
    } catch (e) {
      _addResult('❌ خطأ في اختبار الخدمات: $e');
    }

    setState(() => _isRunning = false);
  }

  Future<void> _resetAndTest() async {
    setState(() => _isRunning = true);
    _addResult('🔄 بدء إعادة التعيين والاختبار...');

    try {
      // إعادة تعيين للاختبار
      _initManager.resetForTesting();
      _addResult('🔄 تم إعادة تعيين حالة التهيئة');

      // اختبار سريع
      final quickTest = await _initManager.quickInitializeForTesting();
      _addResult('⚡ نتيجة الاختبار السريع: $quickTest');

      // تحديث الحالة
      _checkInitializationStatus();
      
      _addResult('✅ انتهى إعادة التعيين والاختبار');
    } catch (e) {
      _addResult('❌ خطأ في إعادة التعيين والاختبار: $e');
    }

    setState(() => _isRunning = false);
  }
}
