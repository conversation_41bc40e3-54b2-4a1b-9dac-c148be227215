// ignore: import_of_legacy_library_into_null_safe
// ignore_for_file: avoid_print, unused_import

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:image_picker/image_picker.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/GlobalComponents/button_global.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/generated/l10n.dart' as lang;
import 'package:mobile_pos/services/firebase_database_service.dart';
import 'package:mobile_pos/services/imgur_service.dart';

import '../../currency.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  // ignore: library_private_types_in_public_api
  _ProfileScreenState createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  File? _imageFile;
  final ImagePicker _picker = ImagePicker();
  String? _imageUrl;

  @override
  void initState() {
    super.initState();
    _loadProfileImage();
  }

  // دالة لتحميل صورة الملف الشخصي
  Future<void> _loadProfileImage() async {
    try {
      final userId = FirebaseAuth.instance.currentUser?.uid;
      if (userId == null) return;

      // الحصول على بيانات الملف الشخصي
      final snapshot = await FirebaseDatabaseService.getDataWithCaching(
          'users/$userId/Personal Information',
          cacheDuration: const Duration(minutes: 5));

      if (snapshot.exists && mounted) {
        final data = snapshot.value as Map<dynamic, dynamic>?;
        if (data != null && data['pictureUrl'] != null) {
          setState(() {
            _imageUrl = data['pictureUrl'] as String;
          });
        }
      }
    } catch (e) {
      debugPrint('خطأ في تحميل صورة الملف الشخصي: $e');
    }
  }

  // دالة لاختيار الصورة من المعرض
  Future<void> _pickImage() async {
    final XFile? pickedFile =
        await _picker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      setState(() {
        _imageFile = File(pickedFile.path);
      });
      await _uploadImageToParse();
    }
  }

  // دالة لرفع الصورة باستخدام Imgur
  Future<void> _uploadImageToParse() async {
    if (_imageFile == null) return;

    // عرض مؤشر التحميل
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      },
    );

    try {
      final userId = FirebaseAuth.instance.currentUser?.uid;

      if (userId == null) {
        // إغلاق مؤشر التحميل
        Navigator.pop(context);

        // عرض رسالة خطأ
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('لم يتم العثور على المستخدم الحالي')),
        );
        return;
      }

      // استخدام Imgur لرفع الصورة
      debugPrint('جاري رفع الصورة  ...');
      final imgurResult = await ImgurService.uploadImage(_imageFile!);

      if (imgurResult['success'] == true) {
        // إذا نجح الرفع إلى Imgur، استخدم الرابط المباشر من Imgur
        final directLink = imgurResult['directLink'] ?? '';

        debugPrint('تم رفع الصورة بنجاح  : $directLink');

        // تحديث رابط الصورة في قاعدة البيانات
        final databaseRef = FirebaseDatabaseService.getReference(
            'users/$userId/Personal Information',
            keepSynced: false);
        await databaseRef.update({
          'pictureUrl': directLink,
          'pictureSource': 'imgur',
          'lastUpdated': DateTime.now().toIso8601String(),
        });

        // تحديث حالة الواجهة
        setState(() {
          _imageUrl = directLink;
        });

        // إغلاق مؤشر التحميل
        if (mounted) {
          Navigator.pop(context);
        }

        // عرض رسالة نجاح
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم رفع الصورة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        // إذا فشل الرفع إلى Imgur
        throw Exception('فشل في رفع الصورة إلى : ${imgurResult['error']}');
      }
    } catch (e) {
      // إغلاق مؤشر التحميل
      if (mounted) {
        Navigator.pop(context);
      }

      // عرض رسالة خطأ
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في رفع الصورة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }

      debugPrint('خطأ في رفع الصورة: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        iconTheme: const IconThemeData(color: Colors.black),
        title: Text(
          'بيانات العميل',
          style: GoogleFonts.poppins(
            color: Colors.black,
          ),
        ),
        centerTitle: true,
        backgroundColor: Colors.white,
        elevation: 4.0,
        actions: const [
          Icon(
            Icons.mode_edit_outline,
            color: kGreyTextColor,
          ),
          Padding(
            padding: EdgeInsets.all(8.0),
            child: Icon(
              Icons.delete_outline,
              color: kGreyTextColor,
            ),
          ),
        ],
      ),
      body: Center(
        child: Column(
          children: [
            const SizedBox(
              height: 20.0,
            ),
            Container(
              height: 100.0,
              width: 100.0,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.0),
              ),
              child: GestureDetector(
                onTap: _pickImage,
                child: _imageFile != null
                    ? Image.file(
                        _imageFile!,
                        height: 100.0,
                        width: 100.0,
                        fit: BoxFit.cover,
                      )
                    : _imageUrl != null
                        ? Image.network(
                            _imageUrl!,
                            height: 100.0,
                            width: 100.0,
                            fit: BoxFit.cover,
                          )
                        : const Image(
                            image: AssetImage('assets/images/profileimage.png'),
                            height: 100.0,
                            width: 100.0,
                          ),
              ),
            ),
            const SizedBox(
              height: 10.0,
            ),
            Text(
              'المستخدم',
              style: GoogleFonts.poppins(
                fontWeight: FontWeight.bold,
                fontSize: 25.0,
                color: Colors.black,
              ),
            ),
            Text(
              '+20 01225999783',
              style: GoogleFonts.poppins(
                fontSize: 20.0,
                color: Colors.black,
              ),
            ),
            const SizedBox(
              height: 10.0,
            ),
            const Row(
              children: [
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.all(10.0),
                    child: IconWithText(
                      bgColor: Color(0xFFF1F7F7),
                      title: 'مكالمة',
                      iconData: Icons.call_outlined,
                      iconColor: Colors.black,
                    ),
                  ),
                ),
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.all(10.0),
                    child: IconWithText(
                      bgColor: kMainColor,
                      title: 'رسالة',
                      iconData: Icons.message,
                      iconColor: Colors.white,
                    ),
                  ),
                ),
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.all(10.0),
                    child: IconWithText(
                      bgColor: Color(0xFFF1F7F7),
                      title: 'ايميل',
                      iconData: Icons.email_outlined,
                      iconColor: Colors.black,
                    ),
                  ),
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'معاملات اليوم',
                        style: GoogleFonts.poppins(
                          fontSize: 20.0,
                          color: Colors.black,
                        ),
                      ),
                      const SizedBox(
                        height: 10.0,
                      ),
                      Transactions(
                        title: 'المبيعات',
                        amount: '$currency 1509.09',
                        tranColor: Colors.black,
                        pressed: () {},
                      ),
                      Transactions(
                        title: 'المستحق',
                        amount: '$currency 509.09',
                        tranColor: kGreyTextColor,
                        pressed: null,
                      ),
                      Transactions(
                        title: 'العروض',
                        amount: '$currency 109.09',
                        tranColor: kGreyTextColor,
                        pressed: null,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(
              height: 20.0,
            ),
            ButtonGlobalWithoutIcon(
                buttontext: 'إرسال',
                buttonDecoration: kButtonDecoration.copyWith(color: kMainColor),
                onPressed: null,
                buttonTextColor: Colors.white),
          ],
        ),
      ),
    );
  }
}

// ignore: must_be_immutable
class Transactions extends StatelessWidget {
  Transactions({
    super.key,
    required this.title,
    required this.amount,
    required this.tranColor,
    required this.pressed,
  });
  final String title;
  final String amount;
  final Color tranColor;
  // ignore: prefer_typing_uninitialized_variables
  var pressed;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: pressed,
      child: Row(
        children: [
          Text(
            title,
            style: GoogleFonts.poppins(
              fontSize: 20.0,
              color: tranColor,
            ),
          ),
          const Spacer(),
          Text(
            amount,
            style: GoogleFonts.poppins(
              fontSize: 20.0,
              color: tranColor,
            ),
          ),
          const SizedBox(
            width: 10.0,
          ),
          const Icon(
            Icons.arrow_forward_ios,
            size: 15.0,
            color: Colors.black,
          ),
        ],
      ),
    );
  }
}

class IconWithText extends StatelessWidget {
  const IconWithText({
    super.key,
    required this.iconData,
    required this.title,
    required this.bgColor,
    required this.iconColor,
  });
  final String title;
  final IconData iconData;
  final Color bgColor;
  final Color iconColor;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 100.0,
      width: 100.0,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.0),
        color: bgColor,
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              iconData,
              size: 32,
              color: iconColor,
            ),
            Text(
              title,
              maxLines: 1,
              style: GoogleFonts.poppins(
                fontSize: 20.0,
                color: iconColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
