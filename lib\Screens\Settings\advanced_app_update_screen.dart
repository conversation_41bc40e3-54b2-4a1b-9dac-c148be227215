import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:lottie/lottie.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/models/app_update_model.dart';
import 'package:mobile_pos/providers/app_update_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';

class AdvancedAppUpdateScreen extends ConsumerStatefulWidget {
  const AdvancedAppUpdateScreen({super.key});

  @override
  ConsumerState<AdvancedAppUpdateScreen> createState() =>
      _AdvancedAppUpdateScreenState();
}

class _AdvancedAppUpdateScreenState
    extends ConsumerState<AdvancedAppUpdateScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isCheckingForUpdates = false;
  bool _showChangelogHistory = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // التحقق من التحديثات عند فتح الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // طباعة الإصدار الحالي من constant.dart
      debugPrint('الإصدار الحالي من constant.dart: $appVersion');

      _checkForUpdates();

      // التحقق من وجود ملف تحديث محمل مسبقًا
      _checkForDownloadedUpdate();
    });
  }

  // التحقق من وجود ملف تحديث محمل مسبقًا
  Future<void> _checkForDownloadedUpdate() async {
    final updateCheck = ref.read(appUpdateCheckProvider);

    // التحقق فقط إذا كان هناك تحديث متاح
    if (updateCheck.value != null && updateCheck.value!.isUpdateAvailable) {
      final latestVersion = updateCheck.value!.latestVersion;

      // استخدام مزود التحقق من وجود ملف تحديث محمل مسبقًا
      final downloadedUpdatePath =
          await ref.read(downloadedUpdateProvider(latestVersion).future);

      // إذا تم العثور على ملف تحديث محمل مسبقًا
      if (downloadedUpdatePath != null && mounted) {
        // عرض مربع حوار للمستخدم
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Row(
              children: [
                const Icon(Icons.system_update, color: kMainColor),
                const SizedBox(width: 10),
                Expanded(
                  child: Text(
                    'تم اكتشاف تحديث محمل مسبقًا',
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'تم العثور على نسخة محملة بالفعل من تحديث AmrDev POS (الإصدار $latestVersion).',
                  style: GoogleFonts.cairo(),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  'هل ترغب في تثبيت التحديث الآن؟',
                  style: GoogleFonts.cairo(
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(
                  'لاحقًا',
                  style: GoogleFonts.cairo(),
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _installDownloadedUpdate(downloadedUpdatePath);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: kMainColor,
                ),
                child: Text(
                  'تثبيت الآن',
                  style: GoogleFonts.cairo(
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        );
      }
    }
  }

  // تثبيت ملف تحديث محمل مسبقًا
  Future<void> _installDownloadedUpdate(String filePath) async {
    try {
      // طلب صلاحية تثبيت التطبيقات
      final hasPermission = await _requestInstallPermission();
      if (!hasPermission) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content:
                  Text('لا يمكن تثبيت التحديث بدون صلاحية تثبيت التطبيقات'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      // التحقق من أن الشاشة لا تزال مرتبطة بالسياق
      if (!mounted) return;

      // عرض مؤشر التقدم أثناء التثبيت
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          title: Row(
            children: [
              const Icon(Icons.system_update, color: kMainColor),
              const SizedBox(width: 10),
              Text(
                'جاري تثبيت AmrDev POS',
                style: GoogleFonts.cairo(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(kMainColor),
              ),
              const SizedBox(height: 16),
              Text(
                'جاري فتح مثبت التطبيق...',
                style: GoogleFonts.cairo(),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'يرجى اتباع التعليمات على الشاشة لإكمال التثبيت',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: Colors.grey[700],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );

      // تثبيت التحديث
      final success =
          await ref.read(appUpdateRepositoryProvider).installUpdate(filePath);

      // إغلاق مربع الحوار
      if (mounted) {
        Navigator.of(context).pop();
      }

      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم بدء تثبيت تحديث AmrDev POS'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          // عرض مربع حوار للخطأ
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: Row(
                children: [
                  const Icon(Icons.error, color: Colors.red),
                  const SizedBox(width: 10),
                  Text(
                    'فشل تثبيت التحديث',
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'لم يتمكن التطبيق من تثبيت التحديث تلقائيًا.',
                    style: GoogleFonts.cairo(),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'يرجى التأكد من السماح للتطبيق بتثبيت التطبيقات من مصادر غير معروفة في إعدادات الجهاز.',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: Colors.grey[700],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(
                    'إلغاء',
                    style: GoogleFonts.cairo(),
                  ),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    openAppSettings();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: kMainColor,
                  ),
                  child: Text(
                    'فتح الإعدادات',
                    style: GoogleFonts.cairo(
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('خطأ في تثبيت التحديث المحمل مسبقًا: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تثبيت التحديث: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _checkForUpdates() async {
    setState(() {
      _isCheckingForUpdates = true;
    });

    try {
      // استخدام مزود التحقق من التحديثات مع التحديث الإجباري
      final _ = ref.refresh(appUpdateCheckForceProvider);

      // عرض رسالة نجاح
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم التحقق من التحديثات بنجاح'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      // عرض رسالة خطأ
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء التحقق من التحديثات: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
      debugPrint('خطأ في التحقق من التحديثات: $e');
    } finally {
      setState(() {
        _isCheckingForUpdates = false;
      });
    }
  }

  void _toggleChangelogHistory() {
    setState(() {
      _showChangelogHistory = !_showChangelogHistory;
    });
  }

  Future<void> _startDownload(AppUpdateModel updateInfo) async {
    debugPrint('بدء عملية التنزيل في الشاشة...');

    // التحقق من وجود رابط تنزيل صالح
    if (updateInfo.downloadUrl.isEmpty) {
      debugPrint('خطأ: رابط التنزيل فارغ');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('رابط التنزيل غير صالح'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // طباعة معلومات التنزيل للتصحيح
    debugPrint('رابط التنزيل: ${updateInfo.downloadUrl}');
    debugPrint('حجم التحديث: ${updateInfo.updateSize} ميجابايت');
    debugPrint('الإصدار الحالي: ${updateInfo.currentVersion}');
    debugPrint('الإصدار الجديد: ${updateInfo.latestVersion}');

    // عرض رسالة للمستخدم
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري بدء تنزيل التحديث...'),
        backgroundColor: Colors.blue,
        duration: Duration(seconds: 2),
      ),
    );

    // بدء التنزيل
    try {
      debugPrint('استدعاء مزود حالة التنزيل لبدء التنزيل...');
      await ref.read(downloadStatusProvider.notifier).startDownload(updateInfo);
      debugPrint('تم استدعاء مزود حالة التنزيل بنجاح');
    } catch (e) {
      debugPrint('خطأ أثناء بدء التنزيل: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل بدء التنزيل: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // طلب صلاحية تثبيت التطبيقات
  Future<bool> _requestInstallPermission() async {
    try {
      // التحقق من حالة الصلاحية
      var status = await Permission.requestInstallPackages.status;

      // إذا لم تكن الصلاحية ممنوحة، نطلبها
      if (!status.isGranted) {
        debugPrint('طلب صلاحية تثبيت التطبيقات...');
        status = await Permission.requestInstallPackages.request();
      }

      // التحقق من النتيجة
      if (status.isGranted) {
        debugPrint('تم منح صلاحية تثبيت التطبيقات');
        return true;
      } else if (status.isPermanentlyDenied) {
        debugPrint('تم رفض صلاحية تثبيت التطبيقات بشكل دائم');
        // فتح إعدادات التطبيق
        if (mounted) {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: Row(
                children: [
                  const Icon(Icons.security, color: Colors.orange),
                  const SizedBox(width: 10),
                  Text(
                    'صلاحية مطلوبة',
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'يحتاج التطبيق إلى صلاحية تثبيت التطبيقات لتثبيت التحديث.',
                    style: GoogleFonts.cairo(),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'يرجى فتح إعدادات التطبيق ومنح الصلاحية يدويًا.',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: Colors.grey[700],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(
                    'إلغاء',
                    style: GoogleFonts.cairo(),
                  ),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    openAppSettings();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: kMainColor,
                  ),
                  child: Text(
                    'فتح الإعدادات',
                    style: GoogleFonts.cairo(
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          );
        }
        return false;
      } else {
        debugPrint('تم رفض صلاحية تثبيت التطبيقات: $status');
        return false;
      }
    } catch (e) {
      debugPrint('خطأ في طلب صلاحية تثبيت التطبيقات: $e');
      return false;
    }
  }

  Future<void> _installUpdate() async {
    try {
      // طلب صلاحية تثبيت التطبيقات
      final hasPermission = await _requestInstallPermission();
      if (!hasPermission) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content:
                  Text('لا يمكن تثبيت التحديث بدون صلاحية تثبيت التطبيقات'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      // التحقق من أن الشاشة لا تزال مرتبطة بالسياق
      if (!mounted) return;

      // عرض مؤشر التقدم أثناء التثبيت
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          title: Row(
            children: [
              const Icon(Icons.system_update, color: kMainColor),
              const SizedBox(width: 10),
              Text(
                'جاري تثبيت AmrDev POS',
                style: GoogleFonts.cairo(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(kMainColor),
              ),
              const SizedBox(height: 16),
              Text(
                'جاري فتح مثبت التطبيق...',
                style: GoogleFonts.cairo(),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'يرجى اتباع التعليمات على الشاشة لإكمال التثبيت',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: Colors.grey[700],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );

      // تثبيت التحديث
      final result =
          await ref.read(downloadStatusProvider.notifier).installUpdate();

      // إغلاق مربع الحوار
      if (mounted) {
        Navigator.of(context).pop();
      }

      if (result) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم بدء تثبيت تحديث AmrDev POS'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          // عرض مربع حوار للخطأ
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: Row(
                children: [
                  const Icon(Icons.error, color: Colors.red),
                  const SizedBox(width: 10),
                  Text(
                    'فشل تثبيت التحديث',
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'لم يتمكن التطبيق من تثبيت التحديث تلقائيًا.',
                    style: GoogleFonts.cairo(),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'يرجى التأكد من السماح للتطبيق بتثبيت التطبيقات من مصادر غير معروفة في إعدادات الجهاز.',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: Colors.grey[700],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(
                    'حسنًا',
                    style: GoogleFonts.cairo(),
                  ),
                ),
              ],
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('خطأ في تثبيت التحديث: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تثبيت التحديث: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _postponeUpdate(AppUpdateModel updateInfo) async {
    final repository = ref.read(appUpdateRepositoryProvider);
    await repository.postponeUpdate(updateInfo);
    if (mounted) {
      Navigator.of(context).pop();
    }
  }

  Future<void> _ignoreUpdate(AppUpdateModel updateInfo) async {
    final repository = ref.read(appUpdateRepositoryProvider);
    await repository.ignoreUpdate(updateInfo);
    final _ = ref.refresh(appUpdateCheckForceProvider);
  }

  @override
  Widget build(BuildContext context) {
    // استخدام مزود التحقق من التحديثات مع التحديث الإجباري
    final updateCheck = ref.watch(appUpdateCheckForceProvider);
    final downloadStatus = ref.watch(downloadStatusProvider);
    final autoUpdateEnabled = ref.watch(autoUpdateEnabledProvider);

    // طباعة معلومات التحديث للتصحيح
    updateCheck.whenData((updateInfo) {
      debugPrint('معلومات التحديث:');
      debugPrint('الإصدار الحالي: ${updateInfo.currentVersion}');
      debugPrint('أحدث إصدار: ${updateInfo.latestVersion}');
      debugPrint('رابط التنزيل: ${updateInfo.downloadUrl}');
      debugPrint('ملاحظات الإصدار: ${updateInfo.releaseNotes}');
      debugPrint('تحديث إجباري: ${updateInfo.isForceUpdate}');
      debugPrint('حجم التحديث: ${updateInfo.updateSize} ميجابايت');
      debugPrint('تاريخ الإصدار: ${updateInfo.releaseDate}');
      debugPrint('أقل إصدار مطلوب: ${updateInfo.minRequiredVersion}');
      debugPrint('يوجد تحديث: ${updateInfo.isUpdateAvailable}');
    });

    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        title: Text(
          'تحديث التطبيق',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        iconTheme: const IconThemeData(color: Colors.white),
        centerTitle: true,
        backgroundColor: kMainColor,
        elevation: 0.0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _isCheckingForUpdates ? null : _checkForUpdates,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(30),
            topLeft: Radius.circular(30),
          ),
        ),
        child: updateCheck.when(
          data: (updateInfo) {
            return _buildUpdateContent(
                updateInfo, downloadStatus, autoUpdateEnabled);
          },
          loading: () => const Center(
            child: CircularProgressIndicator(),
          ),
          error: (error, stack) => Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  color: Colors.red,
                  size: 60,
                ),
                const SizedBox(height: 16),
                Text(
                  'حدث خطأ أثناء التحقق من التحديثات',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  error.toString(),
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: Colors.grey[700],
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: _checkForUpdates,
                  icon: const Icon(Icons.refresh),
                  label: Text(
                    'إعادة المحاولة',
                    style: GoogleFonts.cairo(),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: kMainColor,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUpdateContent(
    AppUpdateModel updateInfo,
    AsyncValue<DownloadStatus> downloadStatus,
    bool autoUpdateEnabled,
  ) {
    return Column(
      children: [
        // التبويبات
        TabBar(
          controller: _tabController,
          labelColor: kMainColor,
          unselectedLabelColor: Colors.grey,
          indicatorColor: kMainColor,
          tabs: [
            Tab(
              text: 'التحديثات',
              icon: Icon(
                updateInfo.isUpdateAvailable
                    ? Icons.system_update
                    : Icons.check_circle,
                color: updateInfo.isUpdateAvailable ? kMainColor : Colors.green,
              ),
            ),
            const Tab(
              text: 'الإعدادات',
              icon: Icon(Icons.settings),
            ),
          ],
        ),

        // محتوى التبويبات
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              // تبويب التحديثات
              _buildUpdatesTab(updateInfo, downloadStatus),

              // تبويب الإعدادات
              _buildSettingsTab(autoUpdateEnabled),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildUpdatesTab(
    AppUpdateModel updateInfo,
    AsyncValue<DownloadStatus> downloadStatus,
  ) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // شعار التطبيق
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(26),
                  blurRadius: 10,
                  spreadRadius: 1,
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: Image.asset(
                'assets/images/ic_launcher.ico',
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  debugPrint('خطأ في تحميل الصورة: $error');
                  return Container(
                    color: Colors.grey[200],
                    child: const Icon(
                      Icons.image_not_supported,
                      size: 60,
                      color: kMainColor,
                    ),
                  );
                },
              ),
            ),
          ),
          const SizedBox(height: 24),

          // حالة التحديث
          if (updateInfo.isUpdateAvailable) ...[
            // يوجد تحديث
            Text(
              updateInfo.isForceUpdate ? 'تحديث إلزامي!' : 'يوجد تحديث جديد!',
              style: GoogleFonts.cairo(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: updateInfo.isForceUpdate ? Colors.red : kMainColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'الإصدار الحالي: ${updateInfo.currentVersion}',
              style: GoogleFonts.cairo(
                fontSize: 16,
                color: Colors.grey[700],
              ),
            ),
            Text(
              'الإصدار الجديد: ${updateInfo.latestVersion}',
              style: GoogleFonts.cairo(
                fontSize: 16,
                color: kMainColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            // عرض حجم التحديث بشكل أكثر وضوحًا
            Container(
              margin: const EdgeInsets.symmetric(vertical: 8),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.sd_storage, color: Colors.blue[700], size: 20),
                  const SizedBox(width: 8),
                  Text(
                    updateInfo.updateSize > 0
                        ? 'حجم التحديث: ${updateInfo.updateSize} ميجابايت'
                        : 'جاري التحقق من حجم التحديث...',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue[700],
                    ),
                  ),
                ],
              ),
            ),

            Text(
              'تاريخ الإصدار: ${DateFormat('yyyy/MM/dd').format(updateInfo.releaseDate)}',
              style: GoogleFonts.cairo(
                fontSize: 16,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 16),

            // ما الجديد
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'ما الجديد:',
                    style: GoogleFonts.cairo(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    updateInfo.releaseNotes,
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // حالة التنزيل
            Builder(
              builder: (context) {
                // طباعة حالة التنزيل للتصحيح
                debugPrint(
                    'حالة التنزيل الحالية: ${downloadStatus.toString()}');

                return downloadStatus.when(
                  data: (status) {
                    debugPrint(
                        'بيانات حالة التنزيل: isDownloading=${status.isDownloading}, progress=${status.progress}, isCompleted=${status.isCompleted}, hasError=${status.hasError}');

                    if (status.isDownloading) {
                      return _buildDownloadProgress(status);
                    } else if (status.isCompleted) {
                      return _buildInstallButton();
                    } else if (status.hasError) {
                      return _buildDownloadError(status.errorMessage);
                    } else {
                      return _buildDownloadButton(updateInfo);
                    }
                  },
                  loading: () {
                    debugPrint('حالة التنزيل: جاري التحميل...');
                    return Column(
                      children: [
                        const CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(kMainColor),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'جاري تحضير التنزيل...',
                          style: GoogleFonts.cairo(
                            fontSize: 16,
                            color: Colors.grey[700],
                          ),
                        ),
                        // إضافة زر إلغاء التحميل
                        const SizedBox(height: 16),
                        TextButton.icon(
                          onPressed: () {
                            // إعادة تعيين حالة التنزيل
                            ref.read(downloadStatusProvider.notifier).reset();
                          },
                          icon: const Icon(Icons.cancel, color: Colors.red),
                          label: Text(
                            'إلغاء',
                            style: GoogleFonts.cairo(
                              color: Colors.red,
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                  error: (error, stack) {
                    debugPrint('حالة التنزيل: خطأ - $error');
                    return _buildDownloadError(error.toString());
                  },
                );
              },
            ),

            // خيارات إضافية
            if (!updateInfo.isForceUpdate &&
                downloadStatus.value != null &&
                !downloadStatus.value!.isDownloading &&
                !downloadStatus.value!.isCompleted) ...[
              const SizedBox(height: 16),
              TextButton(
                onPressed: () => _ignoreUpdate(updateInfo),
                child: Text(
                  'تجاهل هذا الإصدار',
                  style: GoogleFonts.cairo(
                    color: Colors.grey[700],
                  ),
                ),
              ),
            ],
          ] else ...[
            // لا يوجد تحديث
            Lottie.network(
              'https://assets10.lottiefiles.com/packages/lf20_ydo1amjm.json',
              width: 200,
              height: 200,
              errorBuilder: (context, error, stackTrace) {
                debugPrint('خطأ في تحميل الرسوم المتحركة: $error');
                return const Icon(
                  Icons.check_circle,
                  size: 100,
                  color: Colors.green,
                );
              },
            ),
            Text(
              'التطبيق محدث بالفعل',
              style: GoogleFonts.cairo(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'الإصدار الحالي: ${updateInfo.currentVersion}',
              style: GoogleFonts.cairo(
                fontSize: 16,
                color: Colors.grey[700],
              ),
            ),
            Text(
              'آخر إصدار متاح: ${updateInfo.latestVersion}',
              style: GoogleFonts.cairo(
                fontSize: 16,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 24),
            OutlinedButton.icon(
              onPressed: _checkForUpdates,
              icon: const Icon(Icons.refresh),
              label: Text(
                'التحقق من التحديثات',
                style: GoogleFonts.cairo(),
              ),
              style: OutlinedButton.styleFrom(
                foregroundColor: kMainColor,
                side: const BorderSide(color: kMainColor),
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],

          const SizedBox(height: 32),

          // سجل التغييرات
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 8),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: InkWell(
              onTap: _toggleChangelogHistory,
              borderRadius: BorderRadius.circular(12),
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            const Icon(
                              Icons.history,
                              color: kMainColor,
                              size: 24,
                            ),
                            const SizedBox(width: 12),
                            Text(
                              'سجل التغييرات',
                              style: GoogleFonts.cairo(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.grey[800],
                              ),
                            ),
                          ],
                        ),
                        Container(
                          decoration: BoxDecoration(
                            color: _showChangelogHistory
                                ? Colors.blue[50]
                                : Colors.grey[200],
                            borderRadius: BorderRadius.circular(20),
                          ),
                          padding: const EdgeInsets.all(4),
                          child: Icon(
                            _showChangelogHistory
                                ? Icons.keyboard_arrow_up
                                : Icons.keyboard_arrow_down,
                            color: _showChangelogHistory
                                ? kMainColor
                                : Colors.grey[700],
                            size: 24,
                          ),
                        ),
                      ],
                    ),
                    if (_showChangelogHistory) ...[
                      const Divider(height: 32),
                      _buildChangelogHistory(),
                    ],
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsTab(bool autoUpdateEnabled) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إعدادات التحديث',
            style: GoogleFonts.cairo(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // التحديث التلقائي
          SwitchListTile(
            title: Text(
              'التحديث التلقائي',
              style: GoogleFonts.cairo(
                fontSize: 16,
              ),
            ),
            subtitle: Text(
              'التحقق تلقائيًا من وجود تحديثات جديدة عند بدء التطبيق',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.grey[700],
              ),
            ),
            value: autoUpdateEnabled,
            onChanged: (value) {
              ref.read(autoUpdateEnabledProvider.notifier).setEnabled(value);
            },
            activeColor: kMainColor,
          ),

          const Divider(),

          // معلومات التطبيق
          ListTile(
            title: Text(
              'معلومات التطبيق',
              style: GoogleFonts.cairo(
                fontSize: 16,
              ),
            ),
            subtitle: Text(
              'الإصدار الحالي: ${appVersion.isNotEmpty ? appVersion : "1.0.0"}',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.grey[700],
              ),
            ),
            leading: const Icon(Icons.info_outline),
          ),

          const Divider(),

          // حول التحديثات
          ExpansionTile(
            title: Text(
              'حول التحديثات',
              style: GoogleFonts.cairo(
                fontSize: 16,
              ),
            ),
            leading: const Icon(Icons.help_outline),
            children: [
              Padding(
                padding: const EdgeInsets.all(16),
                child: Text(
                  'يقوم التطبيق بالتحقق من وجود تحديثات جديدة بشكل دوري. '
                  'يمكنك تفعيل أو تعطيل التحديث التلقائي من خلال الإعدادات. '
                  'التحديثات الإلزامية يجب تثبيتها لمواصلة استخدام التطبيق.',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: Colors.grey[700],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDownloadProgress(DownloadStatus status) {
    return Column(
      children: [
        // عنوان التنزيل
        Text(
          'جاري تنزيل التحديث...',
          style: GoogleFonts.cairo(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: kMainColor,
          ),
        ),
        const SizedBox(height: 16),

        // شريط التقدم
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            border: Border.all(color: Colors.grey[300]!),
          ),
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // النسبة المئوية مع رسم دائري
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // رسم دائري للتقدم
                  Stack(
                    alignment: Alignment.center,
                    children: [
                      SizedBox(
                        width: 80,
                        height: 80,
                        child: CircularProgressIndicator(
                          value: status.progress,
                          strokeWidth: 8,
                          backgroundColor: Colors.grey[300],
                          valueColor:
                              const AlwaysStoppedAnimation<Color>(kMainColor),
                        ),
                      ),
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            '${(status.progress * 100).toStringAsFixed(1)}%',
                            style: GoogleFonts.cairo(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: kMainColor,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(width: 24),
                  // معلومات إضافية
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'جاري التنزيل...',
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: kMainColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'يرجى عدم إغلاق التطبيق',
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: Colors.grey[700],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // شريط التقدم الخطي
              LinearProgressIndicator(
                value: status.progress,
                backgroundColor: Colors.grey[300],
                valueColor: const AlwaysStoppedAnimation<Color>(kMainColor),
                minHeight: 10,
                borderRadius: BorderRadius.circular(5),
              ),
              const SizedBox(height: 16),

              // معلومات التنزيل في بطاقات
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  children: [
                    // عنوان
                    Text(
                      'معلومات التنزيل',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey[800],
                      ),
                    ),
                    const SizedBox(height: 12),

                    // معلومات في بطاقات
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        // حجم الملف
                        _buildInfoCard(
                          icon: Icons.cloud_download,
                          title: 'الحجم',
                          value:
                              '${status.downloadedBytes} / ${status.totalBytes}',
                          color: Colors.blue,
                        ),

                        // سرعة التنزيل
                        _buildInfoCard(
                          icon: Icons.speed,
                          title: 'السرعة',
                          value: status.speed,
                          color: Colors.green,
                        ),

                        // الوقت المتبقي
                        _buildInfoCard(
                          icon: Icons.timer,
                          title: 'المتبقي',
                          value: status.remainingTime,
                          color: Colors.orange,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),

        // زر الإلغاء
        OutlinedButton.icon(
          onPressed: () {
            ref.read(downloadStatusProvider.notifier).reset();
          },
          icon: const Icon(Icons.cancel),
          label: Text(
            'إلغاء التنزيل',
            style: GoogleFonts.cairo(),
          ),
          style: OutlinedButton.styleFrom(
            foregroundColor: Colors.red,
            side: const BorderSide(color: Colors.red),
            padding: const EdgeInsets.symmetric(
              horizontal: 24,
              vertical: 12,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDownloadButton(AppUpdateModel updateInfo) {
    return Column(
      children: [
        // زر التنزيل
        Container(
          width: double.infinity,
          margin: const EdgeInsets.symmetric(horizontal: 16),
          child: ElevatedButton.icon(
            onPressed: () => _startDownload(updateInfo),
            icon: const Icon(Icons.download),
            label: Text(
              'تنزيل التحديث',
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: kMainColor,
              padding: const EdgeInsets.symmetric(
                horizontal: 24,
                vertical: 16,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          ),
        ),

        // معلومات حجم التحديث
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
          decoration: BoxDecoration(
            color: Colors.blue[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue[200]!),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.sd_storage, color: Colors.blue[700], size: 20),
              const SizedBox(width: 8),
              Text(
                updateInfo.updateSize > 0
                    ? 'حجم التحديث: ${updateInfo.updateSize} ميجابايت'
                    : 'جاري التحقق من حجم التحديث...',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue[700],
                ),
              ),
            ],
          ),
        ),

        // زر التحقق من وجود ملف تحديث محمل مسبقًا
        const SizedBox(height: 16),
        OutlinedButton.icon(
          onPressed: () => _checkForDownloadedUpdate(),
          icon: const Icon(Icons.file_download_done, size: 18),
          label: Text(
            'التحقق من وجود ملف محمل مسبقًا',
            style: GoogleFonts.cairo(
              color: Colors.blue[700],
            ),
          ),
          style: OutlinedButton.styleFrom(
            foregroundColor: Colors.blue[700],
            side: BorderSide(color: Colors.blue[700]!),
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 10,
            ),
          ),
        ),

        // زر التذكير لاحقًا
        if (!updateInfo.isForceUpdate) ...[
          const SizedBox(height: 12),
          TextButton.icon(
            onPressed: () => _postponeUpdate(updateInfo),
            icon: const Icon(Icons.schedule, size: 18),
            label: Text(
              'تذكيري لاحقًا',
              style: GoogleFonts.cairo(
                color: Colors.grey[700],
              ),
            ),
          ),
        ],
      ],
    );
  }

  // الحصول على مسار مجلد التحديث
  Future<String?> _getUpdateFolderPath() async {
    try {
      final baseDirectory = await getExternalStorageDirectory() ??
          await getApplicationDocumentsDirectory();
      return '${baseDirectory.path}/AmrDev-Update';
    } catch (e) {
      debugPrint('خطأ في الحصول على مسار مجلد التحديث: $e');
      return null;
    }
  }

  Widget _buildInstallButton() {
    return Column(
      children: [
        // رسالة نجاح التنزيل
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.green[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.green[300]!),
            boxShadow: [
              BoxShadow(
                color: Colors.green.withAlpha(25),
                blurRadius: 10,
                spreadRadius: 1,
              ),
            ],
          ),
          child: Column(
            children: [
              // أيقونة النجاح مع تأثير
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green[100],
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.check_circle,
                  color: Colors.green,
                  size: 48,
                ),
              ),
              const SizedBox(height: 16),

              // عنوان النجاح
              Text(
                'تم تنزيل التحديث بنجاح',
                style: GoogleFonts.cairo(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.green[800],
                ),
              ),
              const SizedBox(height: 8),

              // معلومات الملف
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green[200]!),
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.info_outline,
                            color: Colors.green, size: 20),
                        const SizedBox(width: 8),
                        Text(
                          'معلومات الملف',
                          style: GoogleFonts.cairo(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.green[800],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'اسم التطبيق:',
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey[700],
                          ),
                        ),
                        Text(
                          'AmrDev POS',
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue[800],
                          ),
                        ),
                      ],
                    ),
                    const Divider(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'جاهز للتثبيت:',
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey[700],
                          ),
                        ),
                        Row(
                          children: [
                            const Icon(Icons.check_circle,
                                color: Colors.green, size: 16),
                            const SizedBox(width: 4),
                            Text(
                              'نعم',
                              style: GoogleFonts.cairo(
                                fontSize: 14,
                                color: Colors.green,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),

              // تعليمات التثبيت
              Text(
                'اضغط على زر التثبيت أدناه لتثبيت التحديث',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: Colors.grey[700],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),

        // زر التثبيت
        Container(
          width: double.infinity,
          margin: const EdgeInsets.symmetric(horizontal: 16),
          child: ElevatedButton.icon(
            onPressed: _installUpdate,
            icon: const Icon(Icons.system_update_alt, size: 24),
            label: Text(
              'تثبيت التحديث الآن',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(
                horizontal: 24,
                vertical: 16,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 3,
              shadowColor: Colors.green.withAlpha(128),
            ),
          ),
        ),

        // ملاحظة تحذيرية
        const SizedBox(height: 12),
        Text(
          'سيتم إعادة تشغيل التطبيق بعد التثبيت',
          style: GoogleFonts.cairo(
            fontSize: 14,
            color: Colors.grey[600],
            fontStyle: FontStyle.italic,
          ),
          textAlign: TextAlign.center,
        ),

        // معلومات عن مجلد التحديث
        const SizedBox(height: 16),
        FutureBuilder<String?>(
          future: _getUpdateFolderPath(),
          builder: (context, snapshot) {
            if (snapshot.hasData && snapshot.data != null) {
              return Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue[200]!),
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.folder_special,
                            color: Colors.blue[700], size: 20),
                        const SizedBox(width: 8),
                        Text(
                          'مجلد التحديثات:',
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue[700],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      snapshot.data!,
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: Colors.grey[700],
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'يمكنك الوصول إلى هذا المجلد يدويًا لتثبيت التحديث',
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: Colors.grey[600],
                        fontStyle: FontStyle.italic,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              );
            }
            return const SizedBox.shrink();
          },
        ),
      ],
    );
  }

  Widget _buildDownloadError(String errorMessage) {
    return Column(
      children: [
        // رسالة الخطأ
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.red[50],
            borderRadius: BorderRadius.circular(10),
            border: Border.all(color: Colors.red[300]!),
          ),
          child: Column(
            children: [
              const Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 48,
              ),
              const SizedBox(height: 12),
              Text(
                'فشل تنزيل التحديث',
                style: GoogleFonts.cairo(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'حدث خطأ أثناء تنزيل التحديث:',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red[200]!),
                ),
                child: Text(
                  errorMessage,
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: Colors.red[700],
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                'يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى.',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: Colors.grey[700],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),

        // زر إعادة المحاولة
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ElevatedButton.icon(
              onPressed: () {
                ref.read(downloadStatusProvider.notifier).reset();
              },
              icon: const Icon(Icons.refresh),
              label: Text(
                'إعادة المحاولة',
                style: GoogleFonts.cairo(
                  fontWeight: FontWeight.bold,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: kMainColor,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            const SizedBox(width: 12),
            OutlinedButton.icon(
              onPressed: () {
                ref.read(downloadStatusProvider.notifier).reset();
                _checkForUpdates();
              },
              icon: const Icon(Icons.update),
              label: Text(
                'تحديث البيانات',
                style: GoogleFonts.cairo(),
              ),
              style: OutlinedButton.styleFrom(
                foregroundColor: kMainColor,
                side: const BorderSide(color: kMainColor),
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  // دالة مساعدة لإنشاء بطاقة معلومات
  Widget _buildInfoCard({
    required IconData icon,
    required String title,
    required String value,
    required MaterialColor color,
  }) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: color[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color[200]!),
      ),
      child: Column(
        children: [
          Icon(icon, color: color[700], size: 24),
          const SizedBox(height: 4),
          Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color[700],
            ),
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: Colors.grey[800],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildChangelogHistory() {
    return Consumer(
      builder: (context, ref, child) {
        final changelogAsync = ref.watch(changelogProvider);

        return changelogAsync.when(
          data: (changelog) {
            if (changelog.isEmpty) {
              return Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Column(
                  children: [
                    const Icon(
                      Icons.history,
                      color: Colors.grey,
                      size: 40,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'لا يوجد سجل تغييرات متاح',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey[700],
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'سيتم عرض سجل التغييرات للإصدارات السابقة هنا',
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    OutlinedButton.icon(
                      onPressed: () {
                        // تحديث سجل التغييرات
                        final _ = ref.refresh(changelogProvider);
                      },
                      icon: const Icon(Icons.refresh),
                      label: Text(
                        'تحديث',
                        style: GoogleFonts.cairo(),
                      ),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: kMainColor,
                        side: const BorderSide(color: kMainColor),
                      ),
                    ),
                  ],
                ),
              );
            }

            return ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: changelog.length,
              itemBuilder: (context, index) {
                final item = changelog[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 16),
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: ExpansionTile(
                    title: Text(
                      'الإصدار ${item.version}',
                      style: GoogleFonts.cairo(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    subtitle: Text(
                      'تاريخ الإصدار: ${DateFormat('yyyy/MM/dd').format(item.releaseDate)}',
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: Colors.grey[700],
                      ),
                    ),
                    leading: const Icon(
                      Icons.history,
                      color: kMainColor,
                    ),
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (item.updateSize > 0)
                              Padding(
                                padding: const EdgeInsets.only(bottom: 8),
                                child: Text(
                                  'حجم التحديث: ${item.updateSize} ميجابايت',
                                  style: GoogleFonts.cairo(
                                    fontSize: 14,
                                    color: Colors.grey[700],
                                  ),
                                ),
                              ),
                            Text(
                              item.releaseNotes,
                              style: GoogleFonts.cairo(
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            );
          },
          loading: () => Center(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  const CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(kMainColor),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'جاري تحميل سجل التغييرات...',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: Colors.grey[700],
                    ),
                  ),
                ],
              ),
            ),
          ),
          error: (error, stack) => Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.red[50],
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: Colors.red[200]!),
            ),
            child: Column(
              children: [
                const Icon(
                  Icons.error_outline,
                  color: Colors.red,
                  size: 32,
                ),
                const SizedBox(height: 8),
                Text(
                  'فشل تحميل سجل التغييرات',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.red[800],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: Colors.grey[700],
                  ),
                  textAlign: TextAlign.center,
                ),
                if (error.toString().isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      error.toString(),
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: Colors.red[700],
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
                const SizedBox(height: 16),
                OutlinedButton.icon(
                  onPressed: () {
                    // تحديث سجل التغييرات
                    final _ = ref.refresh(changelogProvider);
                  },
                  icon: const Icon(Icons.refresh),
                  label: Text(
                    'إعادة المحاولة',
                    style: GoogleFonts.cairo(),
                  ),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: kMainColor,
                    side: const BorderSide(color: kMainColor),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
