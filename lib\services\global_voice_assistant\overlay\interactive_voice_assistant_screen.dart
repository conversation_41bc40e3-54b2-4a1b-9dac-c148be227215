import 'package:flutter/material.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import '../voice_assistant_exports.dart';
import '../core/voice_assistant_initialization_manager.dart';

/// شاشة المساعد الصوتي التفاعلية الجديدة
class InteractiveVoiceAssistantScreen extends StatefulWidget {
  const InteractiveVoiceAssistantScreen({super.key});

  @override
  State<InteractiveVoiceAssistantScreen> createState() =>
      _InteractiveVoiceAssistantScreenState();
}

class _InteractiveVoiceAssistantScreenState
    extends State<InteractiveVoiceAssistantScreen>
    with TickerProviderStateMixin {
  // Controllers للأنيميشن
  late AnimationController _pulseController;
  late AnimationController _waveController;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  // خدمات المساعد الصوتي
  final VoiceAssistantInitializationManager _initManager =
      VoiceAssistantInitializationManager();
  final stt.SpeechToText _speechToText = stt.SpeechToText();
  final VoiceResponseEngine _responseEngine = VoiceResponseEngine();

  // نصوص وحالة المساعد
  String _displayText = 'أهلاً، كيف يمكنني مساعدتك؟';
  String _statusText = 'اضغط على الميكروفون أو اكتب رسالتك';
  String _recognizedText = '';
  bool _isListening = false;
  bool _isProcessing = false;
  bool _isContinuousMode = false; // وضع المحادثة المستمرة

  // Controller للنص المكتوب
  final TextEditingController _textController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeServices();
  }

  /// تهيئة الأنيميشن
  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _waveController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _fadeController.forward();
  }

  /// تهيئة الخدمات
  Future<void> _initializeServices() async {
    try {
      // تهيئة المدير الموحد أولاً
      await _initManager.initializeGlobalVoiceAssistant();

      await _responseEngine.initialize();
      await _speechToText.initialize();
      debugPrint('✅ تم تهيئة خدمات المساعد الصوتي التفاعلي');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة الخدمات: $e');
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _waveController.dispose();
    _fadeController.dispose();
    _textController.dispose();
    _responseEngine.dispose();
    super.dispose();
  }

  /// بدء الاستماع للصوت
  Future<void> _startListening() async {
    if (_isListening || _isProcessing) return;

    setState(() {
      _isListening = true;
      _displayText = 'أستمع إليك الآن...';
      _statusText = 'تحدث بوضوح';
      _recognizedText = '';
    });

    _pulseController.repeat();
    _waveController.repeat();

    try {
      await _speechToText.listen(
        onResult: (result) {
          setState(() {
            _recognizedText = result.recognizedWords;
            _displayText = _recognizedText.isEmpty
                ? 'أستمع إليك الآن...'
                : 'سمعت: $_recognizedText';
          });

          if (result.finalResult && _recognizedText.isNotEmpty) {
            _stopListening();
            _processCommand(_recognizedText);
          }
        },
        localeId: 'ar-SA',
        listenFor: const Duration(seconds: 10),
        pauseFor: const Duration(seconds: 3),
      );
    } catch (e) {
      debugPrint('خطأ في بدء الاستماع: $e');
      _stopListening();
      _showError('حدث خطأ في الاستماع');
    }
  }

  /// إيقاف الاستماع
  void _stopListening() {
    if (_isListening) {
      _speechToText.stop();
      setState(() {
        _isListening = false;
      });
      _pulseController.stop();
      _waveController.stop();
    }
  }

  /// معالجة الأمر (صوتي أو مكتوب)
  Future<void> _processCommand(String command) async {
    if (command.trim().isEmpty) {
      _showError('يرجى إدخال نص أو التحدث');
      return;
    }

    setState(() {
      _isProcessing = true;
      _statusText = 'جاري معالجة طلبك...';
      _displayText = 'طلبك: $command\n\nجاري المعالجة...';
    });

    try {
      // استخدام Gemini للمحادثة المباشرة
      final geminiService = GeminiVoiceChatService();
      final response = await geminiService.processVoiceRequest(command);

      if (response['success'] == true) {
        await _handleSuccessfulResponse(response);
      } else {
        await _showError(response['message'] ?? 'حدث خطأ في المعالجة');
      }
    } catch (e) {
      debugPrint('خطأ في معالجة الأمر: $e');
      await _showError('حدث خطأ في معالجة طلبك');
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  /// معالجة الاستجابة الناجحة
  Future<void> _handleSuccessfulResponse(Map<String, dynamic> response) async {
    setState(() {
      _displayText = response['message'] ?? 'تم تنفيذ طلبك بنجاح';
      _statusText = 'تم العثور على النتيجة';
    });

    // تشغيل الرد صوتياً
    try {
      await _responseEngine.speak(_displayText);
    } catch (e) {
      debugPrint('خطأ في تشغيل الصوت: $e');
    }

    // في الوضع المستمر، ابدأ الاستماع مرة أخرى
    if (_isContinuousMode) {
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted && !_isListening && !_isProcessing) {
          _startListening();
        }
      });
    } else {
      // العودة لحالة الخمول بعد فترة
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted) {
          _resetToIdle();
        }
      });
    }
  }

  /// عرض رسالة خطأ
  Future<void> _showError(String error) async {
    setState(() {
      _displayText = error;
      _statusText = 'حدث خطأ';
    });

    try {
      await _responseEngine.speak(error);
    } catch (e) {
      debugPrint('خطأ في تشغيل صوت الخطأ: $e');
    }

    // في الوضع المستمر، ابدأ الاستماع مرة أخرى
    if (_isContinuousMode) {
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted && !_isListening && !_isProcessing) {
          _startListening();
        }
      });
    } else {
      // العودة لحالة الخمول بعد فترة
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted) {
          _resetToIdle();
        }
      });
    }
  }

  /// العودة لحالة الخمول
  void _resetToIdle() {
    setState(() {
      _displayText = 'أهلاً، كيف يمكنني مساعدتك؟';
      _statusText = _isContinuousMode
          ? 'المحادثة المستمرة مفعلة - تكلم في أي وقت'
          : 'اضغط على الميكروفون أو اكتب رسالتك';
      _recognizedText = '';
    });
  }

  /// معالجة الأمر المكتوب
  void _processTextCommand() {
    final text = _textController.text.trim();
    if (text.isNotEmpty) {
      _textController.clear();
      _processCommand(text);
    }
  }

  /// إغلاق المساعد
  void _closeAssistant() {
    _responseEngine.speak('وداعاً');
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black.withOpacity(0.8),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Center(
          child: Container(
            margin: const EdgeInsets.all(20),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.blue.shade900,
                  Colors.purple.shade900,
                ],
              ),
              borderRadius: BorderRadius.circular(25),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 20,
                  spreadRadius: 5,
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildHeader(),
                const SizedBox(height: 20),
                _buildVoiceButton(),
                const SizedBox(height: 20),
                _buildDisplayArea(),
                const SizedBox(height: 20),
                _buildTextInput(),
                const SizedBox(height: 20),
                _buildQuickActions(),
                const SizedBox(height: 20),
                _buildActionButtons(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء رأس الشاشة
  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        const Text(
          '🤖 المساعد الصوتي الذكي',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        IconButton(
          onPressed: _closeAssistant,
          icon: const Icon(Icons.close, color: Colors.white),
        ),
      ],
    );
  }

  /// بناء زر الصوت
  Widget _buildVoiceButton() {
    return AnimatedBuilder(
      animation: _pulseController,
      builder: (context, child) {
        return Transform.scale(
          scale: _isListening ? 1.0 + (_pulseController.value * 0.2) : 1.0,
          child: GestureDetector(
            onTap: _isListening ? _stopListening : _startListening,
            child: Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: LinearGradient(
                  colors: _isListening
                      ? [Colors.red, Colors.red.shade700]
                      : [Colors.blue, Colors.blue.shade700],
                ),
                boxShadow: [
                  BoxShadow(
                    color: (_isListening ? Colors.red : Colors.blue)
                        .withOpacity(0.5),
                    blurRadius: 20,
                    spreadRadius: 5,
                  ),
                ],
              ),
              child: Icon(
                _isListening ? Icons.mic : Icons.mic_none,
                color: Colors.white,
                size: 40,
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء منطقة العرض
  Widget _buildDisplayArea() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(15),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.white.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            _displayText,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 10),
          Text(
            _statusText,
            style: TextStyle(
              color: Colors.white.withOpacity(0.7),
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء حقل النص
  Widget _buildTextInput() {
    return Row(
      children: [
        Expanded(
          child: TextField(
            controller: _textController,
            style: const TextStyle(color: Colors.white),
            decoration: InputDecoration(
              hintText: 'اكتب رسالتك هنا...',
              hintStyle: TextStyle(color: Colors.white.withOpacity(0.7)),
              filled: true,
              fillColor: Colors.white.withOpacity(0.1),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(25),
                borderSide: BorderSide.none,
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 20,
                vertical: 15,
              ),
            ),
            onSubmitted: (_) => _processTextCommand(),
          ),
        ),
        const SizedBox(width: 10),
        IconButton(
          onPressed: _processTextCommand,
          icon: const Icon(Icons.send, color: Colors.white),
          style: IconButton.styleFrom(
            backgroundColor: Colors.blue.withOpacity(0.3),
            shape: const CircleBorder(),
          ),
        ),
      ],
    );
  }

  /// بناء الأزرار السريعة
  Widget _buildQuickActions() {
    return Column(
      children: [
        // زر الوضع المستمر
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _isContinuousMode ? Icons.chat : Icons.chat_bubble_outline,
              color: Colors.white.withOpacity(0.7),
              size: 16,
            ),
            const SizedBox(width: 8),
            Text(
              'المحادثة المستمرة',
              style: TextStyle(
                color: Colors.white.withOpacity(0.7),
                fontSize: 12,
              ),
            ),
            const SizedBox(width: 8),
            Switch(
              value: _isContinuousMode,
              onChanged: (value) {
                setState(() {
                  _isContinuousMode = value;
                });
                if (value) {
                  _responseEngine.speak('تم تفعيل المحادثة المستمرة');
                } else {
                  _responseEngine.speak('تم إيقاف المحادثة المستمرة');
                }
              },
              activeColor: Colors.blue,
              inactiveThumbColor: Colors.white.withOpacity(0.7),
            ),
          ],
        ),
        const SizedBox(height: 15),
        // الأزرار السريعة
        Wrap(
          spacing: 10,
          runSpacing: 10,
          children: [
            _buildQuickActionButton('🔍 بحث عميل', 'ابحث عن عميل'),
            _buildQuickActionButton('🏪 بحث مورد', 'ابحث عن مورد'),
            _buildQuickActionButton('📦 بحث منتج', 'ابحث عن منتج'),
            _buildQuickActionButton('📊 تقرير مبيعات', 'اعرض تقرير المبيعات'),
          ],
        ),
      ],
    );
  }

  /// بناء زر إجراء سريع
  Widget _buildQuickActionButton(String label, String command) {
    return ElevatedButton(
      onPressed: _isProcessing ? null : () => _processCommand(command),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.white.withOpacity(0.2),
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 8),
      ),
      child: Text(
        label,
        style: const TextStyle(fontSize: 12),
      ),
    );
  }

  /// بناء أزرار الإجراءات
  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildActionButton(
          icon: Icons.refresh,
          label: 'إعادة',
          onPressed: _resetToIdle,
        ),
        _buildActionButton(
          icon: Icons.volume_up,
          label: 'إعادة النطق',
          onPressed: () => _responseEngine.speak(_displayText),
        ),
        _buildActionButton(
          icon: Icons.close,
          label: 'إغلاق',
          onPressed: _closeAssistant,
          color: Colors.red,
        ),
      ],
    );
  }

  /// بناء زر إجراء
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    Color? color,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          onPressed: onPressed,
          icon: Icon(icon, color: Colors.white),
          style: IconButton.styleFrom(
            backgroundColor: (color ?? Colors.blue).withOpacity(0.3),
            shape: const CircleBorder(),
          ),
        ),
        const SizedBox(height: 5),
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 10,
          ),
        ),
      ],
    );
  }
}

/// حالات المساعد الصوتي
enum AssistantState {
  idle,
  listening,
  processing,
  responding,
  error,
}
