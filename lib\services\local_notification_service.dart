import 'dart:async';
import 'dart:convert';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
// مكتبة flutter_local_notifications غير متوفرة، نستخدم Firebase Messaging فقط
import 'package:mobile_pos/model/notification_model.dart';
import 'package:mobile_pos/services/hidden_notifications_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LocalNotificationService {
  // قائمة الإشعارات
  static final List<NotificationModel> _notifications = [];

  // مراقب الإشعارات باستخدام ValueNotifier (للتوافق مع الكود القديم)
  static final ValueNotifier<List<NotificationModel>> notificationsNotifier =
      ValueNotifier<List<NotificationModel>>([]);

  // مراقب الإشعارات باستخدام Stream (للاستخدام الجديد)
  static final StreamController<List<NotificationModel>>
      _notificationsStreamController =
      StreamController<List<NotificationModel>>.broadcast();

  // الحصول على Stream للإشعارات
  static Stream<List<NotificationModel>> get notificationsStream =>
      _notificationsStreamController.stream;

  // تهيئة خدمة الإشعارات
  static Future<void> initialize() async {
    // تهيئة خدمة الإشعارات المخفية
    await HiddenNotificationsService.initialize();

    // تحميل الإشعارات المخزنة
    await _loadNotifications();

    // معالجة الإشعارات المعلقة التي تم استلامها في الخلفية
    await _processPendingNotifications();

    // طلب أذونات الإشعارات
    await _requestPermissions();

    // إعداد معالجة الإشعارات في الخلفية
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

    // إعداد معالجة الإشعارات في المقدمة
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      _handleForegroundMessage(message);
    });

    // إعداد معالجة النقر على الإشعار عندما يكون التطبيق في الخلفية
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      _handleNotificationOpenedApp(message);
    });

    debugPrint('تم تهيئة خدمة الإشعارات بنجاح');
  }

  // معالجة الإشعارات المعلقة التي تم استلامها في الخلفية
  static Future<void> _processPendingNotifications() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final pendingNotifications =
          prefs.getStringList('pending_notifications') ?? [];

      if (pendingNotifications.isEmpty) {
        debugPrint('لا توجد إشعارات معلقة للمعالجة');
        return;
      }

      debugPrint('معالجة ${pendingNotifications.length} إشعار معلق');

      final userId = FirebaseAuth.instance.currentUser?.uid;
      if (userId == null) {
        debugPrint('لا يوجد مستخدم حالي، لا يمكن معالجة الإشعارات المعلقة');
        return;
      }

      for (final notificationJson in pendingNotifications) {
        try {
          final notificationData =
              jsonDecode(notificationJson) as Map<String, dynamic>;

          // إضافة معرف المستلم
          notificationData['recipientId'] = userId;

          // إنشاء كائن الإشعار
          final notification = NotificationModel.fromMap(notificationData);

          // إضافة الإشعار إلى القائمة
          await addNotification(notification);

          debugPrint('تمت معالجة الإشعار المعلق: ${notification.id}');
        } catch (e) {
          debugPrint('خطأ في معالجة إشعار معلق: $e');
        }
      }

      // مسح الإشعارات المعلقة بعد معالجتها
      await prefs.remove('pending_notifications');

      debugPrint('تمت معالجة جميع الإشعارات المعلقة');
    } catch (e) {
      debugPrint('خطأ في معالجة الإشعارات المعلقة: $e');
    }
  }

  // طلب أذونات الإشعارات
  static Future<void> _requestPermissions() async {
    // طلب أذونات FCM
    await FirebaseMessaging.instance.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    // طلب أذونات FCM
    await FirebaseMessaging.instance.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );
  }

  // معالجة الرسائل في الخلفية
  static Future<void> _firebaseMessagingBackgroundHandler(
      RemoteMessage message) async {
    debugPrint('تم استلام رسالة في الخلفية: ${message.messageId}');

    try {
      // في الخلفية، نحتاج فقط لتخزين الإشعار
      // لا يمكننا استدعاء _addNotification مباشرة لأنه يعتمد على FirebaseAuth
      // سيتم معالجة الإشعار تلقائيًا بواسطة Firebase Messaging

      // يمكن تخزين الإشعار مؤقتًا لمعالجته عند فتح التطبيق
      final prefs = await SharedPreferences.getInstance();
      final pendingNotifications =
          prefs.getStringList('pending_notifications') ?? [];

      final notificationData = {
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
        'title': message.notification?.title ?? 'إشعار جديد',
        'body': message.notification?.body ?? '',
        'data': message.data,
        'timestamp': DateTime.now().toIso8601String(),
        'isRead': false,
      };

      pendingNotifications.add(jsonEncode(notificationData));
      await prefs.setStringList('pending_notifications', pendingNotifications);

      debugPrint('تم تخزين الإشعار المستلم في الخلفية للمعالجة لاحقًا');
    } catch (e) {
      debugPrint('خطأ في معالجة الإشعار في الخلفية: $e');
    }
  }

  // معالجة الرسائل في المقدمة
  static Future<void> _handleForegroundMessage(RemoteMessage message) async {
    debugPrint('تم استلام رسالة في المقدمة: ${message.messageId}');

    // إضافة الإشعار إلى القائمة
    _addNotification(
      title: message.notification?.title ?? 'إشعار جديد',
      body: message.notification?.body ?? '',
      data: message.data,
    );

    // عرض الإشعار
    await _showNotification(
      title: message.notification?.title ?? 'إشعار جديد',
      body: message.notification?.body ?? '',
      payload: jsonEncode(message.data),
    );
  }

  // معالجة النقر على الإشعار عندما يكون التطبيق في الخلفية
  static Future<void> _handleNotificationOpenedApp(
      RemoteMessage message) async {
    debugPrint(
        'تم النقر على الإشعار عندما كان التطبيق في الخلفية: ${message.messageId}');

    // يمكن إضافة منطق للتنقل إلى شاشة معينة بناءً على بيانات الإشعار
  }

  // عرض إشعار محلي باستخدام Firebase Messaging
  static Future<void> _showNotification({
    required String title,
    required String body,
    String? payload,
  }) async {
    try {
      // التحقق من أذونات الإشعارات
      final settings =
          await FirebaseMessaging.instance.getNotificationSettings();

      if (settings.authorizationStatus != AuthorizationStatus.authorized) {
        debugPrint('لا توجد أذونات لعرض الإشعارات، تم تخطي عرض الإشعار');
        return;
      }

      // نستخدم Firebase Messaging بدلاً من flutter_local_notifications
      debugPrint('عرض إشعار: $title - $body');

      // لا نحتاج لعمل شيء هنا لأن Firebase Messaging يتعامل مع الإشعارات تلقائيًا
      // في حالة الإشعارات المحلية، يمكن استخدام طرق أخرى مثل flutter_local_notifications
    } catch (e) {
      debugPrint('خطأ في عرض الإشعار: $e');
    }
  }

  // إرسال إشعار محلي
  static Future<void> sendLocalNotification({
    required String title,
    required String body,
    Map<String, dynamic>? data,
  }) async {
    // إضافة الإشعار إلى القائمة
    _addNotification(
      title: title,
      body: body,
      data: data ?? {},
    );

    // عرض الإشعار
    await _showNotification(
      title: title,
      body: body,
      payload: jsonEncode(data ?? {}),
    );
  }

  // إضافة إشعار إلى القائمة
  static void _addNotification({
    required String title,
    required String body,
    required Map<String, dynamic> data,
  }) {
    final userId = FirebaseAuth.instance.currentUser?.uid;
    if (userId == null) return;

    final notification = NotificationModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: title,
      body: body,
      data: data,
      timestamp: DateTime.now(),
      isRead: false,
      recipientId: userId,
    );

    _notifications.add(notification);

    // تحديث كل من ValueNotifier و Stream
    notificationsNotifier.value = List.from(_notifications);
    _notificationsStreamController.add(List.from(_notifications));

    // حفظ الإشعارات
    _saveNotifications();
  }

  // إضافة إشعار جاهز إلى القائمة
  static Future<void> addNotification(NotificationModel notification) async {
    // التحقق من عدم وجود الإشعار بالفعل
    final existingIndex =
        _notifications.indexWhere((n) => n.id == notification.id);
    if (existingIndex != -1) {
      // تحديث الإشعار الموجود
      _notifications[existingIndex] = notification;
    } else {
      // إضافة إشعار جديد
      _notifications.add(notification);
    }

    // تحديث كل من ValueNotifier و Stream
    notificationsNotifier.value = List.from(_notifications);
    _notificationsStreamController.add(List.from(_notifications));

    // حفظ الإشعارات
    await _saveNotifications();
  }

  // حفظ الإشعارات في التخزين المحلي
  static Future<void> _saveNotifications() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = FirebaseAuth.instance.currentUser?.uid;
      if (userId == null) return;

      final notificationsJson =
          _notifications.map((notification) => notification.toMap()).toList();

      await prefs.setString(
          'notifications_$userId', jsonEncode(notificationsJson));
    } catch (e) {
      debugPrint('خطأ في حفظ الإشعارات: $e');
    }
  }

  // تحميل الإشعارات من التخزين المحلي
  static Future<void> _loadNotifications() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = FirebaseAuth.instance.currentUser?.uid;
      if (userId == null) {
        debugPrint('لا يوجد مستخدم حالي، لا يمكن تحميل الإشعارات');
        return;
      }

      final notificationsString = prefs.getString('notifications_$userId');
      if (notificationsString == null) {
        debugPrint('لا توجد إشعارات مخزنة للمستخدم: $userId');
        return;
      }

      try {
        final notificationsJson = jsonDecode(notificationsString) as List;

        _notifications.clear();
        _notifications.addAll(
          notificationsJson
              .map((json) => NotificationModel.fromMap(json))
              .toList(),
        );

        // تحديث كل من ValueNotifier و Stream
        notificationsNotifier.value = List.from(_notifications);
        _notificationsStreamController.add(List.from(_notifications));

        debugPrint('تم تحميل ${_notifications.length} إشعار بنجاح');
      } catch (parseError) {
        debugPrint('خطأ في تحليل بيانات الإشعارات: $parseError');
        // في حالة وجود خطأ في البيانات، نقوم بمسح الإشعارات المخزنة
        await prefs.remove('notifications_$userId');
        _notifications.clear();
        notificationsNotifier.value = [];
      }
    } catch (e) {
      debugPrint('خطأ في تحميل الإشعارات: $e');
      // في حالة وجود خطأ، نقوم بتعيين قائمة فارغة
      _notifications.clear();
      notificationsNotifier.value = [];
    }
  }

  // الحصول على قائمة الإشعارات
  static List<NotificationModel> getNotifications() {
    try {
      // استبعاد الإشعارات المخفية للمستخدم الحالي
      final hiddenNotificationIds =
          HiddenNotificationsService.getHiddenNotificationIds();

      if (hiddenNotificationIds.isEmpty) {
        return List.from(_notifications);
      }

      return _notifications
          .where((notification) =>
              !hiddenNotificationIds.contains(notification.id))
          .toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على قائمة الإشعارات: $e');
      return []; // إرجاع قائمة فارغة في حالة الخطأ
    }
  }

  // تعيين إشعار كمقروء
  static Future<void> markAsRead(String notificationId) async {
    final index = _notifications.indexWhere((n) => n.id == notificationId);
    if (index != -1) {
      _notifications[index] = _notifications[index].copyWith(isRead: true);

      // تحديث كل من ValueNotifier و Stream
      notificationsNotifier.value = List.from(_notifications);
      _notificationsStreamController.add(List.from(_notifications));

      await _saveNotifications();
    }
  }

  // تعيين جميع الإشعارات كمقروءة
  static Future<void> markAllAsRead() async {
    for (int i = 0; i < _notifications.length; i++) {
      _notifications[i] = _notifications[i].copyWith(isRead: true);
    }
    // تحديث كل من ValueNotifier و Stream
    notificationsNotifier.value = List.from(_notifications);
    _notificationsStreamController.add(List.from(_notifications));

    await _saveNotifications();
  }

  // حذف إشعار
  static Future<void> deleteNotification(String notificationId) async {
    // إضافة الإشعار إلى قائمة الإشعارات المخفية
    await HiddenNotificationsService.hideNotification(notificationId);

    // تحديث قائمة الإشعارات المعروضة
    final filteredNotifications = _notifications
        .where((notification) =>
            !HiddenNotificationsService.isNotificationHidden(notification.id))
        .toList();

    // تحديث كل من ValueNotifier و Stream
    notificationsNotifier.value =
        List<NotificationModel>.from(filteredNotifications);
    _notificationsStreamController
        .add(List<NotificationModel>.from(filteredNotifications));
  }

  // حذف جميع الإشعارات
  static Future<void> deleteAllNotifications() async {
    _notifications.clear();

    // تحديث كل من ValueNotifier و Stream
    notificationsNotifier.value = [];
    _notificationsStreamController.add([]);

    await _saveNotifications();
  }

  // الحصول على عدد الإشعارات غير المقروءة
  static int getUnreadCount() {
    try {
      // استبعاد الإشعارات المخفية للمستخدم الحالي
      final hiddenNotificationIds =
          HiddenNotificationsService.getHiddenNotificationIds();

      return _notifications
          .where((notification) =>
              !notification.isRead &&
              !hiddenNotificationIds.contains(notification.id))
          .length;
    } catch (e) {
      debugPrint('خطأ في الحصول على عدد الإشعارات غير المقروءة: $e');
      return 0;
    }
  }

  // الحصول على رمز الجهاز
  static Future<String?> getDeviceToken() async {
    return await FirebaseMessaging.instance.getToken();
  }
}
