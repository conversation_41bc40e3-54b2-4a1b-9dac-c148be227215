import 'package:flutter/foundation.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// مدير شامل لجميع أذونات المساعد الذكي العالمي
class PermissionManager {
  static final PermissionManager _instance = PermissionManager._internal();
  factory PermissionManager() => _instance;
  PermissionManager._internal();

  // مفاتيح التخزين
  static const String _userDeniedKey = 'global_voice_user_denied';
  static const String _lastRequestTimeKey = 'global_voice_last_request_time';

  /// فحص جميع الأذونات المطلوبة
  Future<Map<String, dynamic>> checkAllPermissions() async {
    try {
      //debugPrint('🔍 فحص جميع أذونات المساعد الذكي العالمي...');

      final permissions = <String, bool>{};
      final details = <String, String>{};

      // 1. الأذونات الأساسية (مطلوبة)
      permissions['microphone'] = await Permission.microphone.isGranted;
      details['microphone'] =
          await _getPermissionDetails(Permission.microphone);

      // 2. الأذونات المتقدمة (مطلوبة للعمل الكامل)
      permissions['systemAlertWindow'] = await _checkSystemAlertWindow();
      details['systemAlertWindow'] = await _getSystemAlertWindowDetails();

      permissions['ignoreBatteryOptimizations'] =
          await _checkBatteryOptimization();
      details['ignoreBatteryOptimizations'] =
          await _getBatteryOptimizationDetails();

      // 3. أذونات إضافية (اختيارية)
      permissions['notification'] = await Permission.notification.isGranted;
      details['notification'] =
          await _getPermissionDetails(Permission.notification);

      permissions['storage'] = await _checkStoragePermission();
      details['storage'] = await _getStorageDetails();

      // حساب النسبة المئوية للأذونات الممنوحة
      final grantedCount =
          permissions.values.where((granted) => granted).length;
      final totalCount = permissions.length;
      final percentage = (grantedCount / totalCount * 100).round();

      //debugPrint('📊 نتائج فحص الأذونات:');
      //debugPrint('   ✅ ممنوحة: $grantedCount من $totalCount');
      //debugPrint('   📈 النسبة: $percentage%');

      return {
        'permissions': permissions,
        'details': details,
        'summary': {
          'granted_count': grantedCount,
          'total_count': totalCount,
          'percentage': percentage,
          'all_granted': grantedCount == totalCount,
          'essential_granted': permissions['microphone'] == true,
          'advanced_granted': permissions['systemAlertWindow'] == true &&
              permissions['ignoreBatteryOptimizations'] == true,
        }
      };
    } catch (e) {
      debugPrint('❌ خطأ في فحص الأذونات: $e');
      return {
        'permissions': <String, bool>{},
        'details': <String, String>{},
        'summary': {
          'granted_count': 0,
          'total_count': 0,
          'percentage': 0,
          'all_granted': false,
          'essential_granted': false,
          'advanced_granted': false,
        },
        'error': e.toString(),
      };
    }
  }

  /// طلب جميع الأذونات المطلوبة بالتسلسل
  Future<Map<String, dynamic>> requestAllPermissions() async {
    try {
      debugPrint('🚀 بدء طلب جميع الأذونات...');

      final results = <String, bool>{};
      final errors = <String, String>{};

      // التحقق من عدم طلب الأذونات مؤخراً
      if (!await _canRequestPermissions()) {
        return {
          'success': false,
          'message': 'تم طلب الأذونات مؤخراً. يرجى المحاولة لاحقاً.',
          'results': results,
        };
      }

      // 1. طلب إذن الميكروفون (أساسي)
      debugPrint('🎤 طلب إذن الميكروفون...');
      results['microphone'] = await _requestMicrophonePermission();

      if (!results['microphone']!) {
        errors['microphone'] = 'إذن الميكروفون مطلوب لعمل المساعد الصوتي';
      }

      // 2. طلب إذن النوافذ العائمة
      debugPrint('🪟 طلب إذن النوافذ العائمة...');
      results['systemAlertWindow'] = await _requestSystemAlertWindow();

      if (!results['systemAlertWindow']!) {
        errors['systemAlertWindow'] =
            'إذن النوافذ العائمة مطلوب لإظهار المساعد';
      }

      // 3. طلب إذن تحسين البطارية
      debugPrint('🔋 طلب إذن تحسين البطارية...');
      results['ignoreBatteryOptimizations'] =
          await _requestBatteryOptimization();

      if (!results['ignoreBatteryOptimizations']!) {
        errors['ignoreBatteryOptimizations'] =
            'إذن تحسين البطارية مطلوب للعمل المستمر';
      }

      // 4. طلب إذن الإشعارات (اختياري)
      debugPrint('🔔 طلب إذن الإشعارات...');
      results['notification'] = await _requestNotificationPermission();

      // 5. طلب إذن التخزين (اختياري)
      debugPrint('💾 طلب إذن التخزين...');
      results['storage'] = await _requestStoragePermission();

      // حفظ وقت آخر طلب
      await _saveLastRequestTime();

      // حساب النتائج
      final grantedCount = results.values.where((granted) => granted).length;
      final totalCount = results.length;
      final success = results['microphone'] == true; // الحد الأدنى للنجاح

      debugPrint('📊 نتائج طلب الأذونات:');
      debugPrint('   ✅ ممنوحة: $grantedCount من $totalCount');
      debugPrint('   🎯 النجاح: $success');

      return {
        'success': success,
        'message': success
            ? 'تم منح الأذونات الأساسية بنجاح'
            : 'فشل في الحصول على الأذونات الأساسية',
        'results': results,
        'errors': errors,
        'summary': {
          'granted_count': grantedCount,
          'total_count': totalCount,
          'essential_granted': results['microphone'] == true,
          'advanced_granted': results['systemAlertWindow'] == true &&
              results['ignoreBatteryOptimizations'] == true,
        }
      };
    } catch (e) {
      debugPrint('❌ خطأ في طلب الأذونات: $e');
      return {
        'success': false,
        'message': 'حدث خطأ في طلب الأذونات: $e',
        'results': <String, bool>{},
        'errors': {'general': e.toString()},
      };
    }
  }

  /// فحص إذن النوافذ العائمة
  Future<bool> _checkSystemAlertWindow() async {
    try {
      return await Permission.systemAlertWindow.isGranted;
    } catch (e) {
      debugPrint('⚠️ خطأ في فحص إذن النوافذ العائمة: $e');
      return false;
    }
  }

  /// فحص إذن تحسين البطارية
  Future<bool> _checkBatteryOptimization() async {
    try {
      return await Permission.ignoreBatteryOptimizations.isGranted;
    } catch (e) {
      debugPrint('⚠️ خطأ في فحص إذن تحسين البطارية: $e');
      return false;
    }
  }

  /// فحص إذن التخزين
  Future<bool> _checkStoragePermission() async {
    try {
      // فحص إذن التخزين العام (متوافق مع جميع الإصدارات)
      return await Permission.storage.isGranted;
    } catch (e) {
      debugPrint('⚠️ خطأ في فحص إذن التخزين: $e');
      return false;
    }
  }

  /// طلب إذن الميكروفون
  Future<bool> _requestMicrophonePermission() async {
    try {
      final status = await Permission.microphone.request();
      final granted = status == PermissionStatus.granted;

      if (granted) {
        debugPrint('✅ تم منح إذن الميكروفون');
      } else {
        debugPrint('❌ تم رفض إذن الميكروفون: $status');
        if (status == PermissionStatus.permanentlyDenied) {
          await _markUserDenied();
        }
      }

      return granted;
    } catch (e) {
      debugPrint('❌ خطأ في طلب إذن الميكروفون: $e');
      return false;
    }
  }

  /// طلب إذن النوافذ العائمة
  Future<bool> _requestSystemAlertWindow() async {
    try {
      final status = await Permission.systemAlertWindow.request();
      final granted = status == PermissionStatus.granted;

      if (granted) {
        debugPrint('✅ تم منح إذن النوافذ العائمة');
      } else {
        debugPrint('❌ تم رفض إذن النوافذ العائمة: $status');
      }

      return granted;
    } catch (e) {
      debugPrint('❌ خطأ في طلب إذن النوافذ العائمة: $e');
      return false;
    }
  }

  /// طلب إذن تحسين البطارية
  Future<bool> _requestBatteryOptimization() async {
    try {
      final status = await Permission.ignoreBatteryOptimizations.request();
      final granted = status == PermissionStatus.granted;

      if (granted) {
        debugPrint('✅ تم منح إذن تحسين البطارية');
      } else {
        debugPrint('❌ تم رفض إذن تحسين البطارية: $status');
      }

      return granted;
    } catch (e) {
      debugPrint('❌ خطأ في طلب إذن تحسين البطارية: $e');
      return false;
    }
  }

  /// طلب إذن الإشعارات
  Future<bool> _requestNotificationPermission() async {
    try {
      final status = await Permission.notification.request();
      return status == PermissionStatus.granted;
    } catch (e) {
      debugPrint('⚠️ خطأ في طلب إذن الإشعارات: $e');
      return false;
    }
  }

  /// طلب إذن التخزين
  Future<bool> _requestStoragePermission() async {
    try {
      // طلب إذن التخزين العام (متوافق مع جميع الإصدارات)
      final status = await Permission.storage.request();
      return status == PermissionStatus.granted;
    } catch (e) {
      debugPrint('⚠️ خطأ في طلب إذن التخزين: $e');
      return false;
    }
  }

  /// الحصول على تفاصيل الإذن
  Future<String> _getPermissionDetails(Permission permission) async {
    try {
      final status = await permission.status;
      switch (status) {
        case PermissionStatus.granted:
          return 'ممنوح';
        case PermissionStatus.denied:
          return 'مرفوض';
        case PermissionStatus.restricted:
          return 'مقيد';
        case PermissionStatus.limited:
          return 'محدود';
        case PermissionStatus.permanentlyDenied:
          return 'مرفوض نهائياً';
        default:
          return 'غير معروف';
      }
    } catch (e) {
      return 'خطأ: $e';
    }
  }

  /// الحصول على تفاصيل إذن النوافذ العائمة
  Future<String> _getSystemAlertWindowDetails() async {
    try {
      final granted = await _checkSystemAlertWindow();
      return granted ? 'ممنوح' : 'مطلوب للنوافذ العائمة';
    } catch (e) {
      return 'غير متاح على هذا الجهاز';
    }
  }

  /// الحصول على تفاصيل إذن تحسين البطارية
  Future<String> _getBatteryOptimizationDetails() async {
    try {
      final granted = await _checkBatteryOptimization();
      return granted ? 'ممنوح' : 'مطلوب للعمل المستمر';
    } catch (e) {
      return 'غير متاح على هذا الجهاز';
    }
  }

  /// الحصول على تفاصيل إذن التخزين
  Future<String> _getStorageDetails() async {
    try {
      final granted = await _checkStoragePermission();
      return granted ? 'ممنوح' : 'اختياري لحفظ الملفات';
    } catch (e) {
      return 'خطأ في الفحص';
    }
  }

  /// التحقق من إمكانية طلب الأذونات
  Future<bool> _canRequestPermissions() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // التحقق من رفض المستخدم السابق
      final userDenied = prefs.getBool(_userDeniedKey) ?? false;
      if (userDenied) {
        debugPrint('⚠️ المستخدم رفض الأذونات سابقاً');
        return false;
      }

      // التحقق من وقت آخر طلب (منع الطلب المتكرر)
      final lastRequestTime = prefs.getInt(_lastRequestTimeKey) ?? 0;
      final currentTime = DateTime.now().millisecondsSinceEpoch;
      final timeDifference = currentTime - lastRequestTime;
      const minInterval = 5 * 60 * 1000; // 5 دقائق

      if (timeDifference < minInterval) {
        debugPrint('⚠️ تم طلب الأذونات مؤخراً. الانتظار مطلوب.');
        return false;
      }

      return true;
    } catch (e) {
      debugPrint('❌ خطأ في فحص إمكانية طلب الأذونات: $e');
      return false;
    }
  }

  /// حفظ وقت آخر طلب للأذونات
  Future<void> _saveLastRequestTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(
          _lastRequestTimeKey, DateTime.now().millisecondsSinceEpoch);
    } catch (e) {
      debugPrint('❌ خطأ في حفظ وقت آخر طلب: $e');
    }
  }

  /// تسجيل رفض المستخدم للأذونات
  Future<void> _markUserDenied() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_userDeniedKey, true);
    } catch (e) {
      debugPrint('❌ خطأ في تسجيل رفض المستخدم: $e');
    }
  }

  /// إعادة تعيين حالة رفض المستخدم
  Future<void> resetUserDeniedStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_userDeniedKey);
      await prefs.remove(_lastRequestTimeKey);
      debugPrint('✅ تم إعادة تعيين حالة الأذونات');
    } catch (e) {
      debugPrint('❌ خطأ في إعادة تعيين حالة الأذونات: $e');
    }
  }

  /// فتح إعدادات التطبيق
  Future<bool> openAppSettings() async {
    try {
      return await openAppSettings();
    } catch (e) {
      debugPrint('❌ خطأ في فتح إعدادات التطبيق: $e');
      return false;
    }
  }
}
