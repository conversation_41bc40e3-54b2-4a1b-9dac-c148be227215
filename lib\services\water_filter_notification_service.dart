import 'package:flutter/foundation.dart';
import 'package:mobile_pos/model/water_filter_models.dart';
import 'package:mobile_pos/services/water_filter_cache_service.dart';

/// خدمة الإشعارات لنظام فلاتر المياه
class WaterFilterNotificationService {
  static final WaterFilterNotificationService _instance =
      WaterFilterNotificationService._internal();
  factory WaterFilterNotificationService() => _instance;
  WaterFilterNotificationService._internal();

  final WaterFilterCacheService _cacheService = WaterFilterCacheService();

  /// فحص وإرسال إشعارات الصيانة المستحقة
  Future<void> checkMaintenanceNotifications() async {
    try {
      debugPrint('🔔 فحص إشعارات الصيانة...');

      final systems = await _cacheService.getCachedSystems();
      final now = DateTime.now();

      // الأنظمة التي تحتاج صيانة فورية
      final urgentMaintenance = systems.where((system) {
        return system.nextMaintenanceDate.isBefore(now) &&
            system.status != FilterSystemStatus.inactive;
      }).toList();

      // الأنظمة التي تحتاج صيانة خلال 7 أيام
      final upcomingMaintenance = systems.where((system) {
        final daysUntilMaintenance =
            system.nextMaintenanceDate.difference(now).inDays;
        return daysUntilMaintenance <= 7 &&
            daysUntilMaintenance > 0 &&
            system.status != FilterSystemStatus.inactive;
      }).toList();

      // إرسال إشعارات الصيانة الفورية
      for (final system in urgentMaintenance) {
        await _sendMaintenanceNotification(
          system,
          'صيانة متأخرة',
          'النظام ${system.serialNumber} يحتاج صيانة فورية',
          isUrgent: true,
        );
      }

      // إرسال إشعارات الصيانة القادمة
      for (final system in upcomingMaintenance) {
        final daysLeft = system.nextMaintenanceDate.difference(now).inDays;
        await _sendMaintenanceNotification(
          system,
          'صيانة قادمة',
          'النظام ${system.serialNumber} يحتاج صيانة خلال $daysLeft أيام',
          isUrgent: false,
        );
      }

      debugPrint('✅ تم فحص ${systems.length} نظام');
      debugPrint('🚨 صيانة فورية: ${urgentMaintenance.length}');
      debugPrint('⏰ صيانة قادمة: ${upcomingMaintenance.length}');
    } catch (e) {
      debugPrint('❌ خطأ في فحص إشعارات الصيانة: $e');
    }
  }

  /// فحص وإرسال إشعارات الأقساط المتأخرة
  Future<void> checkInstallmentNotifications() async {
    try {
      debugPrint('🔔 فحص إشعارات الأقساط...');

      // هنا يمكن إضافة منطق فحص الأقساط المتأخرة
      // سيتم تطويرها لاحقاً عند إنشاء خدمة الأقساط المحسنة
    } catch (e) {
      debugPrint('❌ خطأ في فحص إشعارات الأقساط: $e');
    }
  }

  /// فحص وإرسال إشعارات المخزون المنخفض
  Future<void> checkInventoryNotifications() async {
    try {
      debugPrint('🔔 فحص إشعارات المخزون...');

      final lowStockProducts =
          await _cacheService.getLowStockProducts(threshold: 5);

      for (final product in lowStockProducts) {
        await _sendInventoryNotification(
          product,
          'مخزون منخفض',
          'المنتج ${product.name} - المخزون: ${product.stock}',
        );
      }

      debugPrint('📦 منتجات منخفضة المخزون: ${lowStockProducts.length}');
    } catch (e) {
      debugPrint('❌ خطأ في فحص إشعارات المخزون: $e');
    }
  }

  /// فحص وإرسال إشعارات انتهاء الضمان
  Future<void> checkWarrantyNotifications() async {
    try {
      debugPrint('🔔 فحص إشعارات الضمان...');

      final systems = await _cacheService.getCachedSystems();
      final now = DateTime.now();

      // الأنظمة التي ينتهي ضمانها خلال 30 يوم
      final expiringWarranty = systems.where((system) {
        if (!system.isUnderWarranty) return false;

        final daysUntilExpiry = system.warrantyEndDate.difference(now).inDays;
        return daysUntilExpiry <= 30 && daysUntilExpiry > 0;
      }).toList();

      for (final system in expiringWarranty) {
        final daysLeft = system.warrantyEndDate.difference(now).inDays;
        await _sendWarrantyNotification(
          system,
          'انتهاء ضمان قريب',
          'ضمان النظام ${system.serialNumber} ينتهي خلال $daysLeft يوم',
        );
      }

      debugPrint('🛡️ أنظمة ينتهي ضمانها قريباً: ${expiringWarranty.length}');
    } catch (e) {
      debugPrint('❌ خطأ في فحص إشعارات الضمان: $e');
    }
  }

  /// إرسال إشعار صيانة
  Future<void> _sendMaintenanceNotification(
    WaterFilterSystem system,
    String title,
    String body, {
    bool isUrgent = false,
  }) async {
    try {
      // : تطبيق نظام الإشعارات الفعلي
      debugPrint('📱 إشعار صيانة: $title - $body');
      debugPrint('📱 النظام: ${system.serialNumber}');
    } catch (e) {
      debugPrint('❌ خطأ في إرسال إشعار الصيانة: $e');
    }
  }

  /// إرسال إشعار مخزون
  Future<void> _sendInventoryNotification(
    WaterFilterProduct product,
    String title,
    String body,
  ) async {
    try {
      // : تطبيق نظام الإشعارات الفعلي
      debugPrint('📱 إشعار مخزون: $title - $body');
      debugPrint('📱 المنتج: ${product.name}');
    } catch (e) {
      debugPrint('❌ خطأ في إرسال إشعار المخزون: $e');
    }
  }

  /// إرسال إشعار ضمان
  Future<void> _sendWarrantyNotification(
    WaterFilterSystem system,
    String title,
    String body,
  ) async {
    try {
      // : تطبيق نظام الإشعارات الفعلي
      debugPrint('📱 إشعار ضمان: $title - $body');
      debugPrint('📱 النظام: ${system.serialNumber}');
    } catch (e) {
      debugPrint('❌ خطأ في إرسال إشعار الضمان: $e');
    }
  }

  /// فحص شامل لجميع الإشعارات
  Future<void> checkAllNotifications() async {
    debugPrint('🔔 بدء الفحص الشامل للإشعارات...');

    await Future.wait([
      checkMaintenanceNotifications(),
      checkInstallmentNotifications(),
      checkInventoryNotifications(),
      checkWarrantyNotifications(),
    ]);

    debugPrint('✅ انتهى الفحص الشامل للإشعارات');
  }

  /// جدولة الفحص الدوري للإشعارات
  void schedulePeriodicCheck() {
    // فحص كل 6 ساعات
    Future.delayed(const Duration(hours: 6), () {
      checkAllNotifications();
      schedulePeriodicCheck(); // إعادة جدولة
    });
  }

  /// إحصائيات الإشعارات
  Future<Map<String, int>> getNotificationStats() async {
    try {
      final systems = await _cacheService.getCachedSystems();
      final products = await _cacheService.getCachedProducts();
      final now = DateTime.now();

      // عدد الأنظمة التي تحتاج صيانة فورية
      final urgentMaintenance = systems.where((system) {
        return system.nextMaintenanceDate.isBefore(now) &&
            system.status != FilterSystemStatus.inactive;
      }).length;

      // عدد الأنظمة التي تحتاج صيانة خلال 7 أيام
      final upcomingMaintenance = systems.where((system) {
        final daysUntilMaintenance =
            system.nextMaintenanceDate.difference(now).inDays;
        return daysUntilMaintenance <= 7 &&
            daysUntilMaintenance > 0 &&
            system.status != FilterSystemStatus.inactive;
      }).length;

      // عدد المنتجات منخفضة المخزون
      final lowStockCount =
          products.where((product) => product.stock <= 5).length;

      // عدد الأنظمة التي ينتهي ضمانها قريباً
      final expiringWarranty = systems.where((system) {
        if (!system.isUnderWarranty) return false;
        final daysUntilExpiry = system.warrantyEndDate.difference(now).inDays;
        return daysUntilExpiry <= 30 && daysUntilExpiry > 0;
      }).length;

      return {
        'urgent_maintenance': urgentMaintenance,
        'upcoming_maintenance': upcomingMaintenance,
        'low_stock': lowStockCount,
        'expiring_warranty': expiringWarranty,
        'total_notifications': urgentMaintenance +
            upcomingMaintenance +
            lowStockCount +
            expiringWarranty,
      };
    } catch (e) {
      debugPrint('❌ خطأ في حساب إحصائيات الإشعارات: $e');
      return {};
    }
  }

  /// إرسال إشعار مخصص
  Future<void> sendCustomNotification({
    required String title,
    required String body,
    String? payload,
    String importance = 'default',
  }) async {
    try {
      // : تطبيق نظام الإشعارات الفعلي
      debugPrint('📱 إشعار مخصص: $title - $body');
    } catch (e) {
      debugPrint('❌ خطأ في إرسال الإشعار المخصص: $e');
    }
  }

  /// إرسال إشعار نجاح العملية
  Future<void> sendSuccessNotification(String message) async {
    await sendCustomNotification(
      title: '✅ نجحت العملية',
      body: message,
      importance: 'default',
    );
  }

  /// إرسال إشعار تحذير
  Future<void> sendWarningNotification(String message) async {
    await sendCustomNotification(
      title: '⚠️ تحذير',
      body: message,
      importance: 'high',
    );
  }

  /// إرسال إشعار خطأ
  Future<void> sendErrorNotification(String message) async {
    await sendCustomNotification(
      title: '❌ خطأ',
      body: message,
      importance: 'high',
    );
  }
}
