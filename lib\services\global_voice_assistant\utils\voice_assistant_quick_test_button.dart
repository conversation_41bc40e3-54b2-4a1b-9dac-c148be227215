import 'package:flutter/material.dart';
import '../overlay/interactive_voice_assistant_screen.dart';
import '../voice_assistant_exports.dart';

/// زر اختبار سريع للمساعد الصوتي
class VoiceAssistantQuickTestButton extends StatelessWidget {
  const VoiceAssistantQuickTestButton({super.key});

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton(
      onPressed: () => _showVoiceAssistant(context),
      backgroundColor: Colors.blue,
      heroTag: "voice_assistant_test",
      child: const Icon(
        Icons.mic,
        color: Colors.white,
      ),
    );
  }

  /// إظهار المساعد الصوتي للاختبار
  void _showVoiceAssistant(BuildContext context) {
    try {
      debugPrint('🎤 اختبار المساعد الصوتي - إظهار الشاشة التفاعلية');

      // محاكاة اكتشاف كلمة التفعيل
      final globalService = GlobalVoiceAssistantService();

      // إظهار الشاشة التفاعلية مباشرة
      showDialog<void>(
        context: context,
        barrierDismissible: true,
        barrierColor: Colors.black54,
        builder: (context) => Dialog(
          backgroundColor: Colors.transparent,
          insetPadding: const EdgeInsets.all(20),
          child: Container(
            width: MediaQuery.of(context).size.width * 0.9,
            height: MediaQuery.of(context).size.height * 0.7,
            decoration: BoxDecoration(
              color: Colors.black,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: Colors.blue, width: 2),
              boxShadow: [
                BoxShadow(
                  color: Colors.blue.withOpacity(0.3),
                  blurRadius: 20,
                  spreadRadius: 5,
                ),
              ],
            ),
            child: const InteractiveVoiceAssistantScreen(),
          ),
        ),
      );

      debugPrint('✅ تم إظهار الشاشة التفاعلية للاختبار');

      // إظهار رسالة للمستخدم
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم فتح المساعد الصوتي للاختبار'),
          backgroundColor: Colors.blue,
          duration: Duration(seconds: 2),
        ),
      );
    } catch (e) {
      debugPrint('❌ خطأ في اختبار المساعد الصوتي: $e');

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في فتح المساعد الصوتي: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
