import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/model/water_filter_models.dart';
import 'package:mobile_pos/services/water_filter_service.dart';

class ProductsReportScreen extends StatefulWidget {
  const ProductsReportScreen({super.key});

  @override
  State<ProductsReportScreen> createState() => _ProductsReportScreenState();
}

class _ProductsReportScreenState extends State<ProductsReportScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  List<WaterFilterProduct> _allProducts = [];
  List<WaterFilterSystem> _allSales = [];

  // إحصائيات المنتجات
  Map<String, dynamic> _stats = {};

  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadProductsData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadProductsData() async {
    setState(() => _isLoading = true);

    try {
      // تحميل المنتجات
      final productsData = await WaterFilterService.getData('Products');
      final products = <WaterFilterProduct>[];

      productsData.forEach((key, value) {
        try {
          final product = WaterFilterProduct.fromJson(
            Map<String, dynamic>.from(value),
          );
          products.add(product);
        } catch (e) {
          debugPrint('خطأ في معالجة منتج: $e');
        }
      });

      // تحميل المبيعات
      final salesData = await WaterFilterService.getData('Systems');
      final sales = <WaterFilterSystem>[];

      salesData.forEach((key, value) {
        try {
          final sale = WaterFilterSystem.fromJson(
            Map<String, dynamic>.from(value),
          );
          sales.add(sale);
        } catch (e) {
          debugPrint('خطأ في معالجة مبيعة: $e');
        }
      });

      setState(() {
        _allProducts = products;
        _allSales = sales;
        _calculateStats();
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات المنتجات: $e');
      setState(() => _isLoading = false);
    }
  }

  void _calculateStats() {
    final totalProducts = _allProducts.length;

    // إحصائيات المخزون
    final inStockProducts = _allProducts.where((p) => p.stock > 0).length;
    final outOfStockProducts = _allProducts.where((p) => p.stock == 0).length;
    final lowStockProducts =
        _allProducts.where((p) => p.stock > 0 && p.stock <= 5).length;

    // إحصائيات حسب الفئة
    final categoryStats = <WaterFilterCategory, int>{};
    for (final product in _allProducts) {
      categoryStats[product.category] =
          (categoryStats[product.category] ?? 0) + 1;
    }

    // إحصائيات المبيعات
    final productSales = <String, int>{};
    final productRevenue = <String, double>{};

    for (final sale in _allSales) {
      productSales[sale.productId] = (productSales[sale.productId] ?? 0) + 1;
      productRevenue[sale.productId] =
          (productRevenue[sale.productId] ?? 0) + sale.totalCost;
    }

    // قيمة المخزون
    final totalInventoryValue = _allProducts.fold(
        0.0, (sum, product) => sum + (product.price * product.stock));

    setState(() {
      _stats = {
        'total': totalProducts,
        'inStock': inStockProducts,
        'outOfStock': outOfStockProducts,
        'lowStock': lowStockProducts,
        'categoryStats': categoryStats,
        'productSales': productSales,
        'productRevenue': productRevenue,
        'inventoryValue': totalInventoryValue,
      };
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        title: Text(
          'تقرير المنتجات',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: _loadProductsData,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          labelStyle: GoogleFonts.cairo(fontWeight: FontWeight.bold),
          tabs: const [
            Tab(text: 'إحصائيات المخزون', icon: Icon(Icons.inventory)),
            Tab(text: 'أداء المبيعات', icon: Icon(Icons.trending_up)),
          ],
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : TabBarView(
                controller: _tabController,
                children: [
                  _buildInventoryTab(),
                  _buildSalesPerformanceTab(),
                ],
              ),
      ),
    );
  }

  Widget _buildInventoryTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('إحصائيات المخزون'),
          const SizedBox(height: 16),

          // قيمة المخزون الإجمالية
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.blue.shade400, Colors.blue.shade600],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.blue.shade200,
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.inventory,
                      color: Colors.white,
                      size: 32,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'قيمة المخزون الإجمالية',
                      style: GoogleFonts.cairo(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Text(
                  '${(_stats['inventoryValue'] ?? 0).toStringAsFixed(2)} ج.م',
                  style: GoogleFonts.cairo(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 0.9,
            children: [
              _buildStatCard(
                title: 'إجمالي المنتجات',
                value: '${_stats['total'] ?? 0}',
                icon: Icons.category,
                color: Colors.blue,
              ),
              _buildStatCard(
                title: 'متوفر في المخزن',
                value: '${_stats['inStock'] ?? 0}',
                icon: Icons.check_circle,
                color: Colors.green,
              ),
              _buildStatCard(
                title: 'نفد من المخزن',
                value: '${_stats['outOfStock'] ?? 0}',
                icon: Icons.remove_circle,
                color: Colors.red,
              ),
              _buildStatCard(
                title: 'مخزون منخفض',
                value: '${_stats['lowStock'] ?? 0}',
                icon: Icons.warning,
                color: Colors.orange,
              ),
            ],
          ),

          const SizedBox(height: 24),

          _buildSectionTitle('توزيع المنتجات حسب الفئة'),
          const SizedBox(height: 16),

          ..._buildCategoryCards(),

          const SizedBox(height: 24),

          _buildSectionTitle('المنتجات منخفضة المخزون'),
          const SizedBox(height: 16),

          ..._buildLowStockProducts(),
        ],
      ),
    );
  }

  Widget _buildSalesPerformanceTab() {
    final productSales = _stats['productSales'] as Map<String, int>? ?? {};
    final productRevenue =
        _stats['productRevenue'] as Map<String, double>? ?? {};

    final sortedBySales = productSales.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    final sortedByRevenue = productRevenue.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('أفضل المنتجات مبيعاً'),
          const SizedBox(height: 16),
          if (sortedBySales.isEmpty)
            Center(
              child: Column(
                children: [
                  Icon(
                    Icons.trending_down,
                    size: 80,
                    color: Colors.grey.shade400,
                  ),
                  const SizedBox(height: 20),
                  Text(
                    'لا توجد مبيعات بعد',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            )
          else
            ...sortedBySales.take(5).map((entry) {
              final product = _allProducts.firstWhere(
                (p) => p.id == entry.key,
                orElse: () => WaterFilterProduct(
                  id: '',
                  name: 'منتج محذوف',
                  brand: '',
                  category: WaterFilterCategory.residential,
                  price: 0,
                  stock: 0,
                  description: '',
                  specifications: [],
                  maintenanceIntervalMonths: 0,
                  maintenanceCost: 0,
                  isInstallationRequired: false,
                  installationCost: 0,
                ),
              );

              return _buildProductSalesCard(
                  product, entry.value, productRevenue[entry.key] ?? 0);
            }),
          const SizedBox(height: 24),
          _buildSectionTitle('أعلى المنتجات إيراداً'),
          const SizedBox(height: 16),
          if (sortedByRevenue.isEmpty)
            Center(
              child: Column(
                children: [
                  Icon(
                    Icons.money_off,
                    size: 80,
                    color: Colors.grey.shade400,
                  ),
                  const SizedBox(height: 20),
                  Text(
                    'لا توجد إيرادات بعد',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            )
          else
            ...sortedByRevenue.take(5).map((entry) {
              final product = _allProducts.firstWhere(
                (p) => p.id == entry.key,
                orElse: () => WaterFilterProduct(
                  id: '',
                  name: 'منتج محذوف',
                  brand: '',
                  category: WaterFilterCategory.residential,
                  price: 0,
                  stock: 0,
                  description: '',
                  specifications: [],
                  maintenanceIntervalMonths: 0,
                  maintenanceCost: 0,
                  isInstallationRequired: false,
                  installationCost: 0,
                ),
              );

              return _buildProductRevenueCard(
                  product, entry.value, productSales[entry.key] ?? 0);
            }),
        ],
      ),
    );
  }

  // Helper Methods
  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: GoogleFonts.cairo(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: kMainColor,
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 28),
          const SizedBox(height: 8),
          Flexible(
            child: Text(
              value,
              style: GoogleFonts.cairo(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(height: 4),
          Flexible(
            child: Text(
              title,
              style: GoogleFonts.cairo(
                fontSize: 11,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildCategoryCards() {
    final categoryStats =
        _stats['categoryStats'] as Map<WaterFilterCategory, int>? ?? {};

    return categoryStats.entries.map((entry) {
      return Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.shade200,
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.category,
                color: Colors.blue,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                entry.key.arabicName,
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            Text(
              '${entry.value} منتج',
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
          ],
        ),
      );
    }).toList();
  }

  List<Widget> _buildLowStockProducts() {
    final lowStockProducts =
        _allProducts.where((p) => p.stock > 0 && p.stock <= 5).toList();

    if (lowStockProducts.isEmpty) {
      return [
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.green.shade50,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.green.shade200),
          ),
          child: Row(
            children: [
              Icon(Icons.check_circle, color: Colors.green, size: 32),
              const SizedBox(width: 12),
              Text(
                'جميع المنتجات لديها مخزون كافي',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.green.shade800,
                ),
              ),
            ],
          ),
        ),
      ];
    }

    return lowStockProducts.map((product) {
      return Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.shade200,
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
          border: Border.all(color: Colors.orange.shade200),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.warning,
                color: Colors.orange,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    product.name,
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    product.brand,
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '${product.stock}',
                  style: GoogleFonts.cairo(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.orange,
                  ),
                ),
                Text(
                  'متبقي',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    }).toList();
  }

  Widget _buildProductSalesCard(
      WaterFilterProduct product, int salesCount, double revenue) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.trending_up,
                color: Colors.green,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    product.name,
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    product.brand,
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  Text(
                    'إيرادات: ${revenue.toStringAsFixed(2)} ج.م',
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: Colors.blue,
                    ),
                  ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '$salesCount',
                  style: GoogleFonts.cairo(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
                Text(
                  'مبيعة',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductRevenueCard(
      WaterFilterProduct product, double revenue, int salesCount) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.attach_money,
                color: Colors.blue,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    product.name,
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    product.brand,
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  Text(
                    'مبيعات: $salesCount وحدة',
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '${revenue.toStringAsFixed(2)} ج.م',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                  ),
                ),
                Text(
                  'إيرادات',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
