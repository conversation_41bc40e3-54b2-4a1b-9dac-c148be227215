import 'dart:io';
import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:mobile_pos/services/imgur_service.dart';

/// خدمة إدارة صور الملف الشخصي للمستخدمين
class UserProfileService {
  static final UserProfileService _instance = UserProfileService._internal();
  factory UserProfileService() => _instance;
  UserProfileService._internal();

  // الحصول على معرف المستخدم الحالي
  String get currentUserId => FirebaseAuth.instance.currentUser?.uid ?? '';

  /// رفع صورة الملف الشخصي للمستخدم
  Future<Map<String, dynamic>> uploadProfileImage(File imageFile) async {
    try {
      debugPrint('جاري رفع صورة الملف الشخصي للمستخدم...');

      // محاولة رفع الصورة إلى Imgur أولاً
      final imgurResult = await ImgurService.uploadImage(imageFile);

      if (imgurResult['success'] == true) {
        // إذا نجح الرفع إلى Imgur، استخدم الرابط المباشر من Imgur
        final directLink = imgurResult['directLink'] ?? '';

        // حفظ صورة الملف الشخصي في Firebase
        final userImagesRef = FirebaseDatabase.instance.ref('user_images');
        final newImageRef = userImagesRef.push();

        final imageData = {
          'userId': currentUserId,
          'imageUrl': directLink,
          'imageType': 'profile',
          'uploadDate': DateTime.now().millisecondsSinceEpoch,
        };

        await newImageRef.set(imageData);
        final imageId = newImageRef.key;

        // تحديث صورة الملف الشخصي في جدول المستخدمين
        await updateUserProfileImage(currentUserId, directLink);

        debugPrint('تم رفع صورة الملف الشخصي بنجاح: $directLink');
        return {
          'success': true,
          'imageUrl': directLink,
          'objectId': imageId,
        };
      } else {
        debugPrint(
            'فشل في رفع صورة الملف الشخصي إلى : ${imgurResult['error']}');
        return {
          'success': false,
          'error': 'فشل في رفع صورة الملف الشخصي',
        };
      }
    } catch (e) {
      debugPrint('خطأ في رفع صورة الملف الشخصي: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// الحصول على صورة الملف الشخصي للمستخدم
  Future<String?> getProfileImageUrl(String userId) async {
    try {
      debugPrint('جاري الحصول على صورة الملف الشخصي للمستخدم: $userId');

      // البحث عن صورة الملف الشخصي في Firebase
      final userImagesRef = FirebaseDatabase.instance.ref('user_images');
      final query =
          userImagesRef.orderByChild('userId').equalTo(userId).limitToLast(1);

      final snapshot = await query.get();

      if (snapshot.exists) {
        // تحويل البيانات إلى قائمة
        final Map<dynamic, dynamic> data =
            snapshot.value as Map<dynamic, dynamic>;

        // البحث عن أحدث صورة ملف شخصي
        String? latestImageUrl;
        int latestTimestamp = 0;

        data.forEach((key, value) {
          final imageData = value as Map<dynamic, dynamic>;
          if (imageData['imageType'] == 'profile') {
            final timestamp = imageData['uploadDate'] as int;
            if (timestamp > latestTimestamp) {
              latestTimestamp = timestamp;
              latestImageUrl = imageData['imageUrl'] as String?;
            }
          }
        });

        if (latestImageUrl != null) {
          debugPrint('تم العثور على صورة الملف الشخصي: $latestImageUrl');
          return latestImageUrl;
        }
      }

      debugPrint('لم يتم العثور على صورة الملف الشخصي للمستخدم: $userId');
      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على صورة الملف الشخصي: $e');
      return null;
    }
  }

  /// تحديث صورة الملف الشخصي في جدول المستخدمين
  Future<bool> updateUserProfileImage(String userId, String imageUrl) async {
    try {
      debugPrint('جاري تحديث صورة الملف الشخصي في جدول المستخدمين...');

      // البحث عن المستخدم في جدول chat_users
      final chatUsersRef = FirebaseDatabase.instance.ref('chat_users');
      final userRef = chatUsersRef.child(userId);

      final snapshot = await userRef.get();

      if (snapshot.exists) {
        // تحديث صورة الملف الشخصي للمستخدم
        await userRef.update({
          'profileImage': imageUrl,
          'lastUpdated': DateTime.now().millisecondsSinceEpoch,
        });

        debugPrint('تم تحديث صورة الملف الشخصي بنجاح');
        return true;
      } else {
        // إنشاء مستخدم جديد إذا لم يكن موجودًا
        final userData = {
          'userId': userId,
          'name': 'Account',
          'isOnline': true,
          'lastSeen': DateTime.now().millisecondsSinceEpoch,
          'profileImage': imageUrl,
          'createdAt': DateTime.now().millisecondsSinceEpoch,
        };

        await userRef.set(userData);

        debugPrint('تم إنشاء مستخدم جديد وتعيين صورة الملف الشخصي بنجاح');
        return true;
      }
    } catch (e) {
      debugPrint('خطأ في تحديث صورة الملف الشخصي: $e');
      return false;
    }
  }

  /// حذف صورة الملف الشخصي
  Future<bool> deleteProfileImage(String objectId) async {
    try {
      debugPrint('جاري حذف صورة الملف الشخصي...');

      // حذف صورة الملف الشخصي من Firebase
      final userImagesRef = FirebaseDatabase.instance.ref('user_images');
      final imageRef = userImagesRef.child(objectId);

      await imageRef.remove();

      debugPrint('تم حذف صورة الملف الشخصي بنجاح');
      return true;
    } catch (e) {
      debugPrint('خطأ في حذف صورة الملف الشخصي: $e');
      return false;
    }
  }
}
