import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/model/water_filter_models.dart';
import 'package:mobile_pos/services/water_filter_service.dart';
import 'package:mobile_pos/Screens/WaterFilters/Systems/add_system_screen.dart';
import 'package:mobile_pos/Screens/WaterFilters/Systems/system_details_screen.dart';
// import 'package:mobile_pos/Screens/WaterFilters/Systems/installment_plan_screen.dart';

class WaterFilterSystemsScreen extends StatefulWidget {
  const WaterFilterSystemsScreen({super.key});

  @override
  State<WaterFilterSystemsScreen> createState() =>
      _WaterFilterSystemsScreenState();
}

class _WaterFilterSystemsScreenState extends State<WaterFilterSystemsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<WaterFilterSystem> _allSystems = [];
  List<WaterFilterSystem> _activeSystems = [];
  List<WaterFilterSystem> _maintenanceDueSystems = [];
  List<WaterFilterSystem> _inactiveSystems = [];
  bool _isLoading = true;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadSystems();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadSystems() async {
    setState(() => _isLoading = true);

    try {
      final data = await WaterFilterService.getData('Systems');
      final systems = <WaterFilterSystem>[];

      data.forEach((key, value) {
        try {
          final system = WaterFilterSystem.fromJson(
            Map<String, dynamic>.from(value),
          );
          systems.add(system);
        } catch (e) {
          debugPrint('خطأ في معالجة نظام: $e');
        }
      });

      // ترتيب الأنظمة حسب تاريخ التركيب (الأحدث أولاً)
      systems.sort((a, b) => b.installationDate.compareTo(a.installationDate));

      // تصنيف الأنظمة
      final active = <WaterFilterSystem>[];
      final maintenanceDue = <WaterFilterSystem>[];
      final inactive = <WaterFilterSystem>[];

      for (final system in systems) {
        switch (system.status) {
          case FilterSystemStatus.active:
            if (system.isMaintenanceOverdue ||
                system.status == FilterSystemStatus.needsMaintenance) {
              maintenanceDue.add(system);
            } else {
              active.add(system);
            }
            break;
          case FilterSystemStatus.needsMaintenance:
            maintenanceDue.add(system);
            break;
          case FilterSystemStatus.inactive:
          case FilterSystemStatus.overdue:
            inactive.add(system);
            break;
        }
      }

      setState(() {
        _allSystems = systems;
        _activeSystems = active;
        _maintenanceDueSystems = maintenanceDue;
        _inactiveSystems = inactive;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('خطأ في تحميل الأنظمة: $e');
      setState(() => _isLoading = false);
    }
  }

  List<WaterFilterSystem> _getFilteredSystems(List<WaterFilterSystem> systems) {
    if (_searchQuery.isEmpty) return systems;

    return systems.where((system) {
      return system.serialNumber
              .toLowerCase()
              .contains(_searchQuery.toLowerCase()) ||
          system.id.toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        title: Text(
          'الأنظمة المركبة',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.add, color: Colors.white),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const AddSystemScreen(),
                ),
              ).then((result) {
                if (result == true) {
                  _loadSystems();
                }
              });
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          labelStyle: GoogleFonts.cairo(fontWeight: FontWeight.bold),
          tabs: [
            Tab(
              text: 'الكل (${_allSystems.length})',
              icon: const Icon(Icons.list_alt),
            ),
            Tab(
              text: 'نشطة (${_activeSystems.length})',
              icon: const Icon(Icons.check_circle),
            ),
            Tab(
              text: 'صيانة (${_maintenanceDueSystems.length})',
              icon: const Icon(Icons.warning_amber),
            ),
            Tab(
              text: 'غير نشطة (${_inactiveSystems.length})',
              icon: const Icon(Icons.cancel),
            ),
          ],
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        child: Column(
          children: [
            // شريط البحث
            Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: TextField(
                onChanged: (value) => setState(() => _searchQuery = value),
                decoration: InputDecoration(
                  hintText: 'البحث في الأنظمة...',
                  hintStyle: GoogleFonts.cairo(color: Colors.grey.shade600),
                  prefixIcon: Icon(Icons.search, color: Colors.grey.shade600),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
                style: GoogleFonts.cairo(),
              ),
            ),

            // المحتوى
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : TabBarView(
                      controller: _tabController,
                      children: [
                        _buildSystemsList(_getFilteredSystems(_allSystems)),
                        _buildSystemsList(_getFilteredSystems(_activeSystems)),
                        _buildSystemsList(
                            _getFilteredSystems(_maintenanceDueSystems)),
                        _buildSystemsList(
                            _getFilteredSystems(_inactiveSystems)),
                      ],
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSystemsList(List<WaterFilterSystem> systems) {
    if (systems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.settings_outlined,
              size: 80,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 20),
            Text(
              'لا توجد أنظمة',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 10),
            Text(
              'لم يتم العثور على أنظمة في هذه الفئة',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.grey.shade500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: systems.length,
      itemBuilder: (context, index) {
        final system = systems[index];
        return _buildSystemCard(system);
      },
    );
  }

  Widget _buildSystemCard(WaterFilterSystem system) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: system.status.color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => SystemDetailsScreen(system: system),
            ),
          ).then((result) {
            if (result == true) {
              _loadSystems();
            }
          });
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // الصف الأول - الرقم التسلسلي والحالة
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'نظام ${system.serialNumber}',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: kMainColor,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: system.status.color.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: system.status.color.withOpacity(0.3),
                      ),
                    ),
                    child: Text(
                      system.status.arabicName,
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: system.status.color,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // معلومات النظام
              Row(
                children: [
                  Expanded(
                    child: _buildInfoItem(
                      icon: Icons.attach_money,
                      label: 'إجمالي التكلفة',
                      value: '${system.totalCost.toStringAsFixed(2)} ج.م',
                      color: Colors.blue,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildInfoItem(
                      icon: Icons.payment,
                      label: 'المدفوع',
                      value: '${system.paidAmount.toStringAsFixed(2)} ج.م',
                      color: Colors.green,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 8),

              Row(
                children: [
                  Expanded(
                    child: _buildInfoItem(
                      icon: Icons.money_off,
                      label: 'المتبقي',
                      value: '${system.remainingAmount.toStringAsFixed(2)} ج.م',
                      color: system.remainingAmount > 0
                          ? Colors.red
                          : Colors.green,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildInfoItem(
                      icon: Icons.percent,
                      label: 'نسبة الدفع',
                      value: '${system.paymentPercentage.toStringAsFixed(1)}%',
                      color: Colors.purple,
                    ),
                  ),
                ],
              ),

              // شريط التقدم
              const SizedBox(height: 12),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'تقدم الدفع',
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  LinearProgressIndicator(
                    value: system.paymentPercentage / 100,
                    backgroundColor: Colors.grey.shade200,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      system.paymentPercentage == 100
                          ? Colors.green
                          : system.paymentPercentage > 50
                              ? Colors.blue
                              : Colors.orange,
                    ),
                  ),
                ],
              ),

              // تنبيهات
              if (system.isMaintenanceOverdue) ...[
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.orange.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.orange.shade200),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.warning_amber,
                        color: Colors.orange.shade600,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'يحتاج صيانة',
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.orange.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              if (system.remainingAmount > 0) ...[
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.payment,
                        color: Colors.blue.shade600,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'يحتاج دفع أقساط',
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              // أزرار الإجراءات
              const SizedBox(height: 12),
              Row(
                children: [
                  if (system.remainingAmount > 0) ...[
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () {
                          _showInstallmentPlanDialog(system);
                        },
                        icon: const Icon(Icons.schedule, size: 16),
                        label: Text(
                          'خطة أقساط',
                          style: GoogleFonts.cairo(fontSize: 12),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                  ],
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) =>
                                SystemDetailsScreen(system: system),
                          ),
                        ).then((result) {
                          if (result == true) {
                            _loadSystems();
                          }
                        });
                      },
                      icon: const Icon(Icons.info, size: 16),
                      label: Text(
                        'التفاصيل',
                        style: GoogleFonts.cairo(fontSize: 12),
                      ),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: kMainColor,
                        side: BorderSide(color: kMainColor),
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Row(
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(width: 6),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: GoogleFonts.cairo(
                  fontSize: 10,
                  color: Colors.grey.shade600,
                ),
              ),
              Text(
                value,
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _showInstallmentPlanDialog(WaterFilterSystem system) {
    showDialog(
      context: context,
      builder: (context) => InstallmentPlanDialog(system: system),
    ).then((result) {
      if (result == true) {
        _loadSystems();
      }
    });
  }
}

class InstallmentPlanDialog extends StatefulWidget {
  final WaterFilterSystem system;

  const InstallmentPlanDialog({super.key, required this.system});

  @override
  State<InstallmentPlanDialog> createState() => _InstallmentPlanDialogState();
}

class _InstallmentPlanDialogState extends State<InstallmentPlanDialog> {
  final _formKey = GlobalKey<FormState>();
  final _numberOfInstallmentsController = TextEditingController();
  final _firstInstallmentController = TextEditingController();

  int _numberOfInstallments = 3;
  double _firstInstallmentAmount = 0;
  double _remainingAmount = 0;
  double _installmentAmount = 0;
  final List<Map<String, dynamic>> _installmentPlan = [];

  @override
  void initState() {
    super.initState();
    _remainingAmount = widget.system.remainingAmount;
    _numberOfInstallmentsController.text = _numberOfInstallments.toString();
    _calculateInstallments();
  }

  @override
  void dispose() {
    _numberOfInstallmentsController.dispose();
    _firstInstallmentController.dispose();
    super.dispose();
  }

  void _calculateInstallments() {
    if (_numberOfInstallments <= 0) return;

    final remainingAfterFirst = _remainingAmount - _firstInstallmentAmount;
    _installmentAmount = remainingAfterFirst / _numberOfInstallments;

    _installmentPlan.clear();

    // القسط الأول (إذا كان موجود)
    if (_firstInstallmentAmount > 0) {
      _installmentPlan.add({
        'number': 0,
        'amount': _firstInstallmentAmount,
        'dueDate': DateTime.now().add(const Duration(days: 7)),
        'isFirstPayment': true,
      });
    }

    // باقي الأقساط
    for (int i = 1; i <= _numberOfInstallments; i++) {
      _installmentPlan.add({
        'number': i,
        'amount': _installmentAmount,
        'dueDate': DateTime.now().add(Duration(days: 30 * i)),
        'isFirstPayment': false,
      });
    }

    setState(() {});
  }

  Future<void> _createInstallmentPlan() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      // إنشاء الأقساط في قاعدة البيانات
      for (final installmentData in _installmentPlan) {
        final installment = WaterFilterInstallment(
          id: WaterFilterService.generateId(),
          systemId: widget.system.id,
          installmentNumber: installmentData['number'],
          amount: installmentData['amount'],
          dueDate: installmentData['dueDate'],
          status: InstallmentStatus.pending,
          notes: installmentData['isFirstPayment']
              ? 'دفعة أولى'
              : 'قسط رقم ${installmentData['number']}',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await WaterFilterService.saveData(
          'Installments/${installment.id}',
          installment.toJson(),
        );
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إنشاء خطة الأقساط بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context, true);
      }
    } catch (e) {
      debugPrint('خطأ في إنشاء خطة الأقساط: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إنشاء خطة الأقساط: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        'خطة الأقساط',
        style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // معلومات النظام
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'نظام ${widget.system.serialNumber}',
                      style: GoogleFonts.cairo(
                        fontWeight: FontWeight.bold,
                        color: kMainColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'المبلغ المتبقي: ${_remainingAmount.toStringAsFixed(2)} ج.م',
                      style: GoogleFonts.cairo(
                        color: Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // دفعة أولى (اختيارية)
              TextFormField(
                controller: _firstInstallmentController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'دفعة أولى (اختياري)',
                  hintText: '0',
                  border: const OutlineInputBorder(),
                  suffixText: 'ج.م',
                ),
                onChanged: (value) {
                  _firstInstallmentAmount = double.tryParse(value) ?? 0;
                  _calculateInstallments();
                },
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    final amount = double.tryParse(value);
                    if (amount == null || amount < 0) {
                      return 'يرجى إدخال مبلغ صحيح';
                    }
                    if (amount >= _remainingAmount) {
                      return 'الدفعة الأولى يجب أن تكون أقل من المبلغ المتبقي';
                    }
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // عدد الأقساط
              TextFormField(
                controller: _numberOfInstallmentsController,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: 'عدد الأقساط',
                  border: OutlineInputBorder(),
                ),
                onChanged: (value) {
                  _numberOfInstallments = int.tryParse(value) ?? 3;
                  _calculateInstallments();
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال عدد الأقساط';
                  }
                  final number = int.tryParse(value);
                  if (number == null || number <= 0 || number > 24) {
                    return 'يرجى إدخال عدد صحيح بين 1 و 24';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // عرض خطة الأقساط
              if (_installmentPlan.isNotEmpty) ...[
                Text(
                  'خطة الأقساط المقترحة',
                  style: GoogleFonts.cairo(
                    fontWeight: FontWeight.bold,
                    color: kMainColor,
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  height: 200,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: ListView.builder(
                    itemCount: _installmentPlan.length,
                    itemBuilder: (context, index) {
                      final installment = _installmentPlan[index];
                      return ListTile(
                        dense: true,
                        leading: CircleAvatar(
                          radius: 12,
                          backgroundColor: installment['isFirstPayment']
                              ? Colors.green
                              : kMainColor,
                          child: Text(
                            installment['isFirstPayment']
                                ? 'أ'
                                : '${installment['number']}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        title: Text(
                          '${installment['amount'].toStringAsFixed(2)} ج.م',
                          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
                        ),
                        subtitle: Text(
                          '${(installment['dueDate'] as DateTime).day}/${(installment['dueDate'] as DateTime).month}/${(installment['dueDate'] as DateTime).year}',
                          style: GoogleFonts.cairo(fontSize: 12),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text(
            'إلغاء',
            style: GoogleFonts.cairo(),
          ),
        ),
        ElevatedButton(
          onPressed: _createInstallmentPlan,
          style: ElevatedButton.styleFrom(
            backgroundColor: kMainColor,
            foregroundColor: Colors.white,
          ),
          child: Text(
            'إنشاء خطة الأقساط',
            style: GoogleFonts.cairo(),
          ),
        ),
      ],
    );
  }
}
