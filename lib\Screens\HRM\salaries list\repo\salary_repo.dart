import 'package:firebase_database/firebase_database.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

import 'package:nb_utils/nb_utils.dart';
import '../model/pay_salary_model.dart';

class SalaryRepository {
  final DatabaseReference _dbRef = FirebaseDatabase.instance.ref();

  // Get user ID
  Future<String> getUserID() async {
    return await SharedPreferences.getInstance()
        .then((prefs) => prefs.getString('userId') ?? '');
  }

  // Get all paid salaries
  Future<List<PaySalaryModel>> getAllPaidSalary() async {
    List<PaySalaryModel> salaries = [];

    try {
      final snapshot = await _dbRef.child('HRM/Salaries').get();

      if (snapshot.exists) {
        Map<dynamic, dynamic> data = snapshot.value as Map<dynamic, dynamic>;
        data.forEach((key, value) {
          Map<String, dynamic> salaryMap = {};
          (value as Map<dynamic, dynamic>).forEach((k, v) {
            salaryMap[k.toString()] = v;
          });
          salaries.add(PaySalaryModel.fromMap(salaryMap, key.toString()));
        });
      }
    } catch (e) {
      // Error fetching paid Salary: $e
    }

    return salaries;
  }

  // Pay salary
  Future<bool> paySalary(PaySalaryModel salary) async {
    try {
      EasyLoading.show(status: 'جاري الحفظ...', dismissOnTap: false);

      await _dbRef.child('HRM/Salaries/${salary.id}').set(salary.toMap());

      EasyLoading.showSuccess('تم الدفع بنجاح');
      return true;
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception('فشل دفع الراتب: ${e.toString()}');
    }
  }

  // Update salary
  Future<bool> updateSalary(PaySalaryModel salary) async {
    try {
      EasyLoading.show(status: 'جاري التحديث...', dismissOnTap: false);

      await _dbRef.child('HRM/Salaries/${salary.id}').update(salary.toMap());

      EasyLoading.showSuccess('تم التحديث بنجاح');
      return true;
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception('فشل تحديث الراتب: ${e.toString()}');
    }
  }

  // Delete salary
  Future<bool> deletePaidSalary({required String id}) async {
    try {
      EasyLoading.show(status: 'جاري الحذف...');

      await _dbRef.child('HRM/Salaries/$id').remove();

      EasyLoading.showSuccess('تم الحذف بنجاح');
      return true;
    } catch (e) {
      EasyLoading.showError('خطأ: ${e.toString()}');
      return false;
    }
  }
}
