import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/models/hrm_models.dart';
// import 'provider/designation_provider.dart'; // غير مستخدم

class AddDesignationScreen extends StatefulWidget {
  const AddDesignationScreen(
      {super.key, required this.listOfDesignations, this.designationModel});

  final List<DesignationModel> listOfDesignations;
  final DesignationModel? designationModel;

  @override
  State<AddDesignationScreen> createState() => _AddDesignationScreenState();
}

class _AddDesignationScreenState extends State<AddDesignationScreen> {
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _departmentController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();

    if (widget.designationModel != null) {
      _titleController.text = widget.designationModel?.title ?? '';
      _descriptionController.text = widget.designationModel?.description ?? '';
      _departmentController.text = widget.designationModel?.department ?? '';
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _departmentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    List<String> existingTitles = widget.listOfDesignations
        .map((element) => element.title.toLowerCase().trim())
        .toList();

    return Consumer(
      builder: (context, ref, child) {
        return SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: SingleChildScrollView(
            child: Container(
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(20)),
                color: Colors.white,
              ),
              width: 600,
              child: Form(
                key: _formKey,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(20.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              const Text(
                                'إضافة مسمى وظيفي',
                                style: TextStyle(
                                    color: kMainColor,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 21.0),
                              ),
                              const Spacer(),
                              const Icon(Icons.close,
                                      color: kMainColor, size: 30.0)
                                  .onTap(() => Navigator.pop(context)),
                            ],
                          ),
                          const SizedBox(height: 20.0),
                          _buildTextField(
                            controller: _titleController,
                            label: 'المسمى الوظيفي',
                            hint: 'أدخل المسمى الوظيفي',
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return 'يرجى إدخال المسمى الوظيفي';
                              }
                              String normalizedValue =
                                  value.toLowerCase().trim();
                              if (widget.designationModel != null) {
                                // تحقق إذا كان الاسم موجود بالفعل ولكن ليس نفس المسمى الحالي
                                if (existingTitles.contains(normalizedValue) &&
                                    normalizedValue !=
                                        widget.designationModel!.title
                                            .toLowerCase()
                                            .trim()) {
                                  return 'هذا المسمى الوظيفي موجود بالفعل';
                                }
                              } else {
                                // تحقق إذا كان الاسم موجود بالفعل
                                if (existingTitles.contains(normalizedValue)) {
                                  return 'هذا المسمى الوظيفي موجود بالفعل';
                                }
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 20.0),
                          _buildTextField(
                            controller: _departmentController,
                            label: 'القسم',
                            hint: 'أدخل القسم',
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return 'يرجى إدخال القسم';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 20.0),
                          _buildTextField(
                            controller: _descriptionController,
                            label: 'الوصف',
                            hint: 'أدخل وصف المسمى الوظيفي',
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return 'يرجى إدخال الوصف';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 20.0),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              _buildButton(
                                label: 'إلغاء',
                                color: Colors.red,
                                onTap: () => Navigator.pop(context),
                              ),
                              const SizedBox(width: 20),
                              _buildButton(
                                label: 'حفظ ونشر',
                                color: Colors.green,
                                onTap: () async {
                                  if (_formKey.currentState?.validate() ??
                                      false) {
                                    // إظهار رسالة تحميل
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(
                                          content: Text('جاري الحفظ...')),
                                    );

                                    try {
                                      // إنشاء كائن المسمى الوظيفي
                                      final newDesignation = DesignationModel(
                                        id: widget.designationModel?.id ??
                                            DateTime.now()
                                                .millisecondsSinceEpoch
                                                .toString(),
                                        title: _titleController.text.trim(),
                                        description:
                                            _descriptionController.text.trim(),
                                        department:
                                            _departmentController.text.trim(),
                                        createdAt: widget
                                                .designationModel?.createdAt ??
                                            DateTime.now(),
                                      );

                                      // حفظ البيانات (هذا مجرد مثال، يجب تنفيذ الوظيفة الفعلية)
                                      // تنفيذ وظيفة الحفظ الفعلية
                                      if (widget.designationModel == null) {
                                        // إضافة مسمى وظيفي جديد
                                        // يمكن استخدام ref.read(designationNotifierProvider.notifier).addDesignation(newDesignation);
                                        // استخدم السجل بدلاً من print
                                        debugPrint(
                                            'Adding new designation: ${newDesignation.title}');
                                      } else {
                                        // تحديث مسمى وظيفي موجود
                                        // يمكن استخدام ref.read(designationNotifierProvider.notifier).updateDesignation(newDesignation);
                                        // استخدم السجل بدلاً من print
                                        debugPrint(
                                            'Updating designation: ${newDesignation.title}');
                                      }

                                      // إظهار رسالة نجاح
                                      if (mounted) {
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          const SnackBar(
                                              content: Text('تم الحفظ بنجاح')),
                                        );
                                        Navigator.pop(context);
                                      }
                                    } catch (e) {
                                      // إظهار رسالة خطأ
                                      if (mounted) {
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          SnackBar(
                                              content: Text('حدث خطأ: $e')),
                                        );
                                      }
                                    }
                                  }
                                },
                              ),
                            ],
                          )
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required String? Function(String?) validator,
  }) {
    return SizedBox(
      width: 580,
      child: AppTextField(
        controller: controller,
        showCursor: true,
        cursorColor: kMainColor,
        textFieldType: TextFieldType.NAME,
        decoration: InputDecoration(
          labelText: label,
          labelStyle: const TextStyle(color: kMainColor),
          hintText: hint,
          hintStyle: const TextStyle(color: kGreyTextColor),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(color: kBorderColorTextField),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(color: kMainColor),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(color: kBorderColorTextField),
          ),
        ),
        validator: validator,
      ),
    );
  }

  Widget _buildButton(
      {required String label,
      required Color color,
      required VoidCallback onTap}) {
    return Container(
      padding: const EdgeInsets.all(10.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5.0),
        color: color,
      ),
      width: 150,
      child: Center(
        child: Text(
          label,
          style: const TextStyle(color: Colors.white),
        ),
      ),
    ).onTap(onTap);
  }
}
