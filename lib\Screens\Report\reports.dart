// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/Screens/Report/Screens/daily_sales_report_screen.dart';
import 'package:mobile_pos/Screens/Report/Screens/due_report_screen';
//import 'package:mobile_pos/Screens/Report/Screens/inventory_report.dart';
import 'package:mobile_pos/Screens/Report/Screens/product_report_screen.dart';
//import 'package:mobile_pos/Screens/Report/Screens/item_details_screen.dart';
//import 'package:mobile_pos/Screens/Report/Screens/product_quantity_report.dart';
//import 'package:mobile_pos/Screens/Report/Screens/product_report_screen2';
import 'package:mobile_pos/Screens/Report/Screens/purchase_report.dart';
import 'package:mobile_pos/Screens/Report/Screens/sales_report_screen.dart';
import 'package:mobile_pos/Screens/Report/Screens/seller_due_report_screen.dart';
import 'package:mobile_pos/Screens/SalesTargets/sales_targets_report_screen.dart';
import 'package:mobile_pos/Screens/Report/Screens/financial_report_screen.dart';
//import 'package:mobile_pos/Screens/Report/Screens/advanced_inventory_report.dart';
//import 'package:mobile_pos/Screens/Report/Screens/sales_performance_report.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/generated/l10n.dart' as lang;
//import 'package:mobile_pos/model/item_model.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:mobile_pos/features/trial_balance/screens/trial_balance_screen.dart';

import '../sale return/sale_return_report.dart';
import 'Purchase Return Report/purchase_return_report_screen.dart';
import 'Screens/loss profit report/loss_profit_report.dart';
//import 'Screens/item_report_screen.dart'; // Add this import
// Add this import

class Reports extends StatefulWidget {
  const Reports({super.key, required this.isFromHome});

  final bool isFromHome;

  @override
  // ignore: library_private_types_in_public_api
  _ReportsState createState() => _ReportsState();
}

class _ReportsState extends State<Reports> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        title: Text(
          lang.S.of(context).reports,
          style: GoogleFonts.poppins(
            color: Colors.white,
            fontSize: 20.0,
          ),
        ),
        centerTitle: true,
        backgroundColor: kMainColor,
        elevation: 0.0,
        iconTheme: const IconThemeData(color: kWhite),
        automaticallyImplyLeading: widget.isFromHome ? false : true,
      ),
      body: Container(
        alignment: Alignment.topCenter,
        decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
                topRight: Radius.circular(30), topLeft: Radius.circular(30))),
        child: SingleChildScrollView(
          child: Column(
            children: [
              const SizedBox(height: 20),
              ReportCard(
                  pressed: () {
                    const PurchaseReportScreen().launch(context);
                  },
                  iconPath: 'assets/images/purchase.png',
                  title: lang.S.of(context).purchaseReportss),
              ReportCard(
                  pressed: () {
                    const SalesReportScreen().launch(context);
                  },
                  iconPath: 'assets/images/sales.png',
                  title: lang.S.of(context).saleReportss),
              ReportCard(
                  pressed: () {
                    const DailySalesReportScreen().launch(context);
                  },
                  iconPath: 'assets/images/Sales Report.png',
                  title: 'تقرير المبيعات اليومي'),
              ReportCard(
                  pressed: () {
                    const DueReportScreen().launch(context);
                  },
                  iconPath: 'assets/images/duelist.png',
                  title: lang.S.of(context).dueCollectionReports),
              ReportCard(
                  pressed: () {
                    const LossProfitReport().launch(context);
                  },
                  iconPath: 'assets/images/lossprofit.png',
                  title: 'تقرير الربح والخسارة'),
              ReportCard(
                  pressed: () {
                    const SaleReturnReport().launch(context);
                  },
                  iconPath: 'assets/images/duelist.png',
                  title: ' مبيعات تقرير المرتجع'),
              ReportCard(
                  pressed: () {
                    const PurchaseReturnReport().launch(context);
                  },
                  iconPath: 'assets/images/Purchase_Return_Report.png',
                  title: 'تقرير المرتجع مشترايات'),
              ReportCard(
                  pressed: () {
                    const ProductReportScreen()
                        .launch(context); // Add this report card
                  },
                  iconPath: 'assets/images/Inventory_Trial.png',
                  title: 'تقرير الاصناف'),
              ReportCard(
                  pressed: () {
                    const SalesTargetsReportScreen().launch(context);
                  },
                  iconPath: 'assets/images/Sales_Performance_Report.png',
                  title: 'أهداف البائعين'),
              ReportCard(
                  pressed: () {
                    const SellerDueReportScreen().launch(context);
                  },
                  iconPath: 'assets/images/duelist.png',
                  title: 'مديونيات البائع الحالي'),
              ReportCard(
                  pressed: () {
                    const FinancialReportScreen().launch(context);
                  },
                  iconPath: 'assets/images/lossprofit.png',
                  title: 'التقرير المالي'),
              ReportCard(
                  pressed: () {
                    const TrialBalanceScreen().launch(context);
                  },
                  iconPath: 'assets/images/Financial Reconciliation.png',
                  title: 'ميزان المراجعة'),

              // ReportCard(
              //     pressed: () {
              //       const InventoryReport()
              //           .launch(context); // Add this report card
              //     },
              //     iconPath: 'assets/images/Advanced_Inventory_Report.png',
              //     title: 'جرد المخزن تجريبي'),
              // ReportCard(
              //     pressed: () {
              //       const AdvancedInventoryReport().launch(context);
              //     },
              //     iconPath: 'assets/images/stock.png',
              //     title: 'تقرير المخزون المتقدم'),
              // ReportCard(
              //     pressed: () {
              //       const SalesPerformanceReport().launch(context);
              //     },
              //     iconPath: 'assets/images/Sales_Performance_Report.png',
              //     title: 'تقرير أداء المندوبين'),
            ],
          ),
        ),
      ),
    );
  }
}

// ignore: must_be_immutable
class ReportCard extends StatelessWidget {
  ReportCard({
    super.key,
    required this.pressed,
    required this.iconPath,
    required this.title,
  });

  // ignore: prefer_typing_uninitialized_variables
  var pressed;
  String iconPath, title;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16, bottom: 15),
      child: GestureDetector(
        onTap: pressed,
        child: Container(
          decoration: BoxDecoration(
              color: kWhite,
              borderRadius: BorderRadius.circular(6),
              boxShadow: [
                BoxShadow(
                    color: const Color(0xff0C1A4B).withOpacity(0.24),
                    offset: const Offset(0, 0),
                    blurRadius: 1,
                    spreadRadius: 0),
                BoxShadow(
                    color: const Color(0xff473232).withOpacity(0.05),
                    offset: const Offset(0, 3),
                    blurRadius: 8,
                    spreadRadius: -1),
              ]),
          child: Row(
            children: [
              Padding(
                padding: const EdgeInsets.all(10.0),
                child: Image(
                  height: 40,
                  width: 40,
                  image: AssetImage(iconPath),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(10.0),
                child: Text(
                  title,
                  style: GoogleFonts.poppins(
                    color: Colors.black,
                  ),
                ),
              ),
              const Spacer(),
              const Padding(
                padding: EdgeInsets.all(10.0),
                child: Icon(
                  Icons.arrow_forward_ios,
                  color: kMainColor,
                  size: 18,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
