import 'package:flutter/material.dart';
import 'package:mobile_pos/Screens/HRM/Designation/designation_screen.dart';
import 'package:mobile_pos/Screens/HRM/employees/employees_screen.dart';
import 'package:mobile_pos/Screens/HRM/salaries list/salaries_list_screen.dart';
import 'package:mobile_pos/constant.dart';

class HRMScreen extends StatelessWidget {
  const HRMScreen({super.key});

  static const String route = '/hrm';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('إدارة الموارد البشرية'),
        backgroundColor: kMainColor,
        centerTitle: true,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        titleTextStyle: const TextStyle(
            color: Colors.white, fontSize: 20, fontWeight: FontWeight.bold),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'مرحبًا بك في نظام إدارة الموارد البشرية',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'يمكنك إدارة الموظفين والمسميات الوظيفية والرواتب من هنا',
              style: TextStyle(
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 32),
            Expanded(
              child: GridView.count(
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                children: [
                  _buildMenuCard(
                    context,
                    title: 'الموظفين',
                    icon: Icons.people,
                    color: Colors.blue,
                    onTap: () {
                      Navigator.pushNamed(context, EmployeesScreen.route);
                    },
                  ),
                  _buildMenuCard(
                    context,
                    title: 'المسميات الوظيفية',
                    icon: Icons.work,
                    color: Colors.orange,
                    onTap: () {
                      Navigator.pushNamed(context, DesignationScreen.route);
                    },
                  ),
                  _buildMenuCard(
                    context,
                    title: 'الرواتب',
                    icon: Icons.attach_money,
                    color: Colors.green,
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const SalariesListScreen(),
                        ),
                      );
                    },
                  ),
                  _buildMenuCard(
                    context,
                    title: 'التقارير',
                    icon: Icons.bar_chart,
                    color: Colors.purple,
                    onTap: () {
                      Navigator.pushNamed(context, '/hrm/reports');
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuCard(
    BuildContext context, {
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Card(
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                color.withAlpha(179),
                color,
              ],
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 48,
                color: Colors.white,
              ),
              const SizedBox(height: 16),
              Text(
                title,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
