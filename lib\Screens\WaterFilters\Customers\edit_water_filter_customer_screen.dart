import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/model/water_filter_models.dart';
import 'package:mobile_pos/services/water_filter_customer_service.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

class EditWaterFilterCustomerScreen extends StatefulWidget {
  final WaterFilterCustomer customer;

  const EditWaterFilterCustomerScreen({
    super.key,
    required this.customer,
  });

  @override
  State<EditWaterFilterCustomerScreen> createState() =>
      _EditWaterFilterCustomerScreenState();
}

class _EditWaterFilterCustomerScreenState
    extends State<EditWaterFilterCustomerScreen> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _nameController;
  late final TextEditingController _phoneController;
  late final TextEditingController _emailController;
  late final TextEditingController _addressController;
  late final TextEditingController _areaController;
  late final TextEditingController _cityController;
  late final TextEditingController _notesController;

  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    _nameController = TextEditingController(text: widget.customer.name);
    _phoneController = TextEditingController(text: widget.customer.phone);
    _emailController = TextEditingController(text: widget.customer.email);
    _addressController = TextEditingController(text: widget.customer.address);
    _areaController = TextEditingController(text: widget.customer.area);
    _cityController = TextEditingController(text: widget.customer.city);
    _notesController = TextEditingController(text: widget.customer.notes ?? '');
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _addressController.dispose();
    _areaController.dispose();
    _cityController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _updateCustomer() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() => _isLoading = true);
    EasyLoading.show(status: 'جاري تحديث العميل...', dismissOnTap: false);

    try {
      // تحديث العميل
      final updatedCustomer = WaterFilterCustomer(
        id: widget.customer.id,
        name: _nameController.text.trim(),
        phone: _phoneController.text.trim(),
        email: _emailController.text.trim(),
        address: _addressController.text.trim(),
        area: _areaController.text.trim(),
        city: _cityController.text.trim(),
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
        createdAt: widget.customer.createdAt,
      );

      // حفظ التحديثات
      final success =
          await WaterFilterCustomerService.updateCustomer(updatedCustomer);

      EasyLoading.dismiss();
      setState(() => _isLoading = false);

      if (success) {
        EasyLoading.showSuccess('تم تحديث العميل بنجاح');
        if (mounted) {
          Navigator.pop(context, true);
        }
      } else {
        EasyLoading.showError('فشل في تحديث العميل');
      }
    } catch (e) {
      EasyLoading.dismiss();
      setState(() => _isLoading = false);
      EasyLoading.showError('خطأ في تحديث العميل: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        elevation: 0,
        title: Text(
          'تعديل العميل',
          style: GoogleFonts.cairo(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        child: Form(
          key: _formKey,
          child: ListView(
            padding: const EdgeInsets.all(20),
            children: [
              // المعلومات الأساسية
              _buildBasicInfoSection(),
              const SizedBox(height: 20),

              // معلومات الاتصال
              _buildContactInfoSection(),
              const SizedBox(height: 20),

              // معلومات العنوان
              _buildAddressInfoSection(),
              const SizedBox(height: 20),

              // الملاحظات
              _buildNotesSection(),
              const SizedBox(height: 30),

              // زر التحديث
              _buildUpdateButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المعلومات الأساسية',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: kMainColor,
          ),
        ),
        const SizedBox(height: 15),

        // اسم العميل
        TextFormField(
          controller: _nameController,
          decoration: InputDecoration(
            labelText: 'اسم العميل *',
            labelStyle: GoogleFonts.cairo(),
            prefixIcon: const Icon(Icons.person, color: kMainColor),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
              borderSide: const BorderSide(color: kMainColor),
            ),
          ),
          style: GoogleFonts.cairo(),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'اسم العميل مطلوب';
            }
            if (value.trim().length < 2) {
              return 'اسم العميل يجب أن يكون أكثر من حرفين';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildContactInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'معلومات الاتصال',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: kMainColor,
          ),
        ),
        const SizedBox(height: 15),

        // رقم الهاتف
        TextFormField(
          controller: _phoneController,
          keyboardType: TextInputType.phone,
          decoration: InputDecoration(
            labelText: 'رقم الهاتف *',
            labelStyle: GoogleFonts.cairo(),
            prefixIcon: const Icon(Icons.phone, color: kMainColor),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
              borderSide: const BorderSide(color: kMainColor),
            ),
          ),
          style: GoogleFonts.cairo(),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'رقم الهاتف مطلوب';
            }
            if (!RegExp(r'^[0-9+\-\s()]+$').hasMatch(value)) {
              return 'رقم الهاتف غير صحيح';
            }
            return null;
          },
        ),
        const SizedBox(height: 15),

        // البريد الإلكتروني
        TextFormField(
          controller: _emailController,
          keyboardType: TextInputType.emailAddress,
          decoration: InputDecoration(
            labelText: 'البريد الإلكتروني (اختياري)',
            labelStyle: GoogleFonts.cairo(),
            prefixIcon: const Icon(Icons.email, color: kMainColor),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
              borderSide: const BorderSide(color: kMainColor),
            ),
          ),
          style: GoogleFonts.cairo(),
          validator: (value) {
            if (value != null && value.isNotEmpty) {
              if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                  .hasMatch(value)) {
                return 'البريد الإلكتروني غير صحيح';
              }
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildAddressInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'معلومات العنوان',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: kMainColor,
          ),
        ),
        const SizedBox(height: 15),

        // المدينة والمنطقة
        Row(
          children: [
            // المدينة
            Expanded(
              child: TextFormField(
                controller: _cityController,
                decoration: InputDecoration(
                  labelText: 'المدينة *',
                  labelStyle: GoogleFonts.cairo(),
                  prefixIcon:
                      const Icon(Icons.location_city, color: kMainColor),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(15),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(15),
                    borderSide: const BorderSide(color: kMainColor),
                  ),
                ),
                style: GoogleFonts.cairo(),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'المدينة مطلوبة';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 15),

            // المنطقة
            Expanded(
              child: TextFormField(
                controller: _areaController,
                decoration: InputDecoration(
                  labelText: 'المنطقة *',
                  labelStyle: GoogleFonts.cairo(),
                  prefixIcon: const Icon(Icons.location_on, color: kMainColor),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(15),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(15),
                    borderSide: const BorderSide(color: kMainColor),
                  ),
                ),
                style: GoogleFonts.cairo(),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'المنطقة مطلوبة';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 15),

        // العنوان التفصيلي
        TextFormField(
          controller: _addressController,
          maxLines: 3,
          decoration: InputDecoration(
            labelText: 'العنوان التفصيلي *',
            labelStyle: GoogleFonts.cairo(),
            prefixIcon: const Icon(Icons.home, color: kMainColor),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
              borderSide: const BorderSide(color: kMainColor),
            ),
          ),
          style: GoogleFonts.cairo(),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'العنوان التفصيلي مطلوب';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildNotesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'ملاحظات إضافية',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: kMainColor,
          ),
        ),
        const SizedBox(height: 15),
        TextFormField(
          controller: _notesController,
          maxLines: 4,
          decoration: InputDecoration(
            labelText: 'ملاحظات (اختياري)',
            labelStyle: GoogleFonts.cairo(),
            prefixIcon: const Icon(Icons.note, color: kMainColor),
            hintText: 'أي ملاحظات إضافية عن العميل...',
            hintStyle: GoogleFonts.cairo(color: Colors.grey.shade500),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
              borderSide: const BorderSide(color: kMainColor),
            ),
          ),
          style: GoogleFonts.cairo(),
        ),
      ],
    );
  }

  Widget _buildUpdateButton() {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _updateCustomer,
        style: ElevatedButton.styleFrom(
          backgroundColor: kMainColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
        ),
        child: _isLoading
            ? const CircularProgressIndicator(color: Colors.white)
            : Text(
                'تحديث العميل',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
      ),
    );
  }
}
