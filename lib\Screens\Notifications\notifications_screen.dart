import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/Screens/Home/home.dart';
import 'package:mobile_pos/Screens/stock_list/stock_list.dart';
import 'package:mobile_pos/Screens/subscription/package_screen.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/model/unified_notification_model.dart';
import 'package:mobile_pos/models/system_notification_model.dart';
import 'package:mobile_pos/services/loader_service.dart';
import 'package:mobile_pos/utils/lifecycle_manager.dart';
import 'package:mobile_pos/utils/notification_helper.dart';
import 'package:mobile_pos/widgets/notification_card.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen>
    with SafeStateMixin {
  List<UnifiedNotificationModel> _notifications = [];
  bool _isLoading = true;

  // إنشاء نسخة من خدمة التحميل
  final loaderService = LoaderService();

  @override
  void initState() {
    super.initState();

    // تأخير تحميل الإشعارات لضمان تهيئة الشاشة بشكل كامل
    WidgetsBinding.instance.addPostFrameCallback((_) {
      executeSafely(
        operation: () async {
          await _loadNotifications();
          return true;
        },
        onError: (e) {
          debugPrint('خطأ في تحميل الإشعارات: $e');
        },
      );
    });
  }

  // تحميل الإشعارات - تم تحسينه باستخدام NotificationHelper
  Future<void> _loadNotifications() async {
    if (!mounted) return;

    // عرض شاشة التحميل
    await loaderService.showLoader();

    setState(() {
      _isLoading = true;
    });

    try {
      // استخدام NotificationHelper لتحميل الإشعارات بشكل آمن
      _notifications = await NotificationHelper.loadNotifications();

      if (!mounted) {
        loaderService.hideLoader();
        return;
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('خطأ في تحميل الإشعارات: $e');

      if (!mounted) {
        loaderService.hideLoader();
        return;
      }

      setState(() {
        _isLoading = false;
        _notifications = []; // تعيين قائمة فارغة في حالة الخطأ
      });

      // عرض رسالة خطأ للمستخدم
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ أثناء تحميل الإشعارات: $e')),
        );
      }
    } finally {
      // إخفاء شاشة التحميل
      loaderService.hideLoader();
    }
  }

  // إخفاء جميع الإشعارات للمستخدم الحالي فقط
  Future<void> _deleteAllNotifications() async {
    // عرض مربع حوار للتأكيد
    final shouldDelete = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إخفاء جميع الإشعارات'),
        content: const Text(
            'هل أنت متأكد من إخفاء جميع الإشعارات؟ سيتم إخفاؤها من جهازك فقط ولن تؤثر على المستخدمين الآخرين.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('إخفاء', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );

    // إذا لم يتم التأكيد، لا تفعل شيئًا
    if (shouldDelete != true) return;

    // عرض شاشة التحميل
    await loaderService.showLoader();

    setState(() {
      _isLoading = true;
    });

    try {
      // إخفاء جميع الإشعارات محلياً للمستخدم الحالي فقط
      for (final notification in _notifications) {
        await NotificationHelper.hideNotification(notification);
        debugPrint('تم إخفاء الإشعار: ${notification.id} للمستخدم الحالي فقط');
      }

      if (!mounted) {
        loaderService.hideLoader();
        return;
      }

      setState(() {
        _notifications.clear();
        _isLoading = false;
      });

      // عرض رسالة نجاح
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم إخفاء جميع الإشعارات بنجاح')),
        );
      }
    } catch (e) {
      debugPrint('خطأ في حذف جميع الإشعارات: $e');

      if (!mounted) {
        loaderService.hideLoader();
        return;
      }

      setState(() {
        _isLoading = false;
      });

      // عرض رسالة خطأ
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ أثناء حذف الإشعارات: $e')),
        );
      }
    } finally {
      // إخفاء شاشة التحميل
      loaderService.hideLoader();
    }
  }

  // التنقل إلى الشاشة المناسبة بناءً على نوع الإشعار - تم تحسينه باستخدام NotificationHelper
  Future<void> _navigateToNotificationScreen(
      UnifiedNotificationModel notification) async {
    // عرض شاشة التحميل
    await loaderService.showLoader();

    setState(() {
      _isLoading = true;
    });

    try {
      // تعيين الإشعار كمقروء باستخدام NotificationHelper
      await NotificationHelper.markAsRead(notification);

      // إعادة تحميل الإشعارات
      await _loadNotifications();

      if (!mounted) {
        loaderService.hideLoader();
        return;
      }

      setState(() {
        _isLoading = false;
      });

      // إخفاء شاشة التحميل قبل الانتقال
      loaderService.hideLoader();

      // الحصول على نوع الشاشة ورقم الفاتورة بشكل آمن
      final screen = NotificationHelper.getScreenType(notification);
      final invoiceNumber = NotificationHelper.getInvoiceNumber(notification);

      // إذا كان الإشعار يحتوي على رقم فاتورة، عرض تفاصيل الفاتورة
      if (invoiceNumber.isNotEmpty &&
          (screen == 'sale' ||
              screen == 'purchase' ||
              screen == 'due' ||
              screen == 'due_payment')) {
        // إنشاء نموذج إشعار نظام وهمي لعرض تفاصيل الفاتورة
        final type = screen == 'sale'
            ? SystemNotificationTypes.creditSale
            : screen == 'purchase'
                ? SystemNotificationTypes.creditPurchase
                : SystemNotificationTypes.due;

        final systemNotification =
            NotificationHelper.createDummySystemNotification(
          notification,
          type,
        );

        _showInvoiceDetailsDialog(systemNotification, screen);
        return;
      }

      // التنقل بناءً على نوع الإشعار النظامي
      if (notification.isSystemNotification) {
        switch (notification.type) {
          case SystemNotificationTypes.creditSale:
            // التنقل إلى شاشة المبيعات
            _showInvoiceDetailsDialog(
              NotificationHelper.createDummySystemNotification(
                notification,
                notification.type,
              ),
              'sale',
            );
            break;
          case SystemNotificationTypes.creditPurchase:
            // التنقل إلى شاشة المشتريات
            _showInvoiceDetailsDialog(
              NotificationHelper.createDummySystemNotification(
                notification,
                notification.type,
              ),
              'purchase',
            );
            break;
          case SystemNotificationTypes.expense:
            // التنقل إلى شاشة المصروفات
            if (mounted) {
              Navigator.pushNamed(context, '/Expense');
            }
            break;
          case SystemNotificationTypes.due:
          case SystemNotificationTypes.duePayment:
            // التنقل إلى شاشة المديونية
            _showInvoiceDetailsDialog(
              NotificationHelper.createDummySystemNotification(
                notification,
                notification.type,
              ),
              'due',
            );
            break;
          default:
            // التنقل بناءً على نوع الشاشة
            if (mounted) {
              _navigateBasedOnScreen(screen);
            }
            break;
        }
      } else {
        // التنقل بناءً على نوع الشاشة
        if (mounted) {
          _navigateBasedOnScreen(screen);
        }
      }

      // تحديث الإشعارات بعد العودة من الشاشة
      if (mounted) {
        await _loadNotifications();
      }
    } catch (e) {
      debugPrint('خطأ في التنقل إلى الشاشة: $e');

      if (!mounted) {
        loaderService.hideLoader();
        return;
      }

      setState(() {
        _isLoading = false;
      });

      // عرض رسالة خطأ للمستخدم
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ أثناء الانتقال إلى الشاشة: $e')),
        );
      }

      // إخفاء شاشة التحميل
      loaderService.hideLoader();
    }
  }

  // التنقل بناءً على نوع الشاشة
  Future<void> _navigateBasedOnScreen(String screen) async {
    if (screen == 'inventory') {
      // التنقل إلى شاشة المخزون
      await Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const StockList()),
      );
    } else if (screen == 'due_payments') {
      // التنقل إلى شاشة المدفوعات المستحقة
      await Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const Home()),
      );
    } else if (screen == 'subscription') {
      // التنقل إلى شاشة الاشتراك
      await Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const PackageScreen()),
      );
    } else {
      // التنقل إلى الشاشة الرئيسية
      await Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const Home()),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kDarkWhite,
      appBar: AppBar(
        title: Text(
          'الإشعارات',
          style: GoogleFonts.poppins(
            color: Colors.white,
            fontSize: 20.0,
          ),
        ),
        iconTheme: const IconThemeData(color: Colors.white),
        centerTitle: true,
        backgroundColor: kMainColor,
        elevation: 2.0,
        actions: [
          // زر حذف جميع الإشعارات
          if (_notifications.isNotEmpty)
            IconButton(
              icon: const Icon(Icons.visibility_off),
              onPressed: _isLoading ? null : _deleteAllNotifications,
              tooltip: 'إخفاء الكل',
            ),
          // زر التحديث
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _isLoading ? null : _loadNotifications,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _notifications.isEmpty
              ? _buildEmptyState('لا توجد إشعارات')
              : _buildNotificationsList(),
    );
  }

  // بناء قائمة الإشعارات الموحدة
  Widget _buildNotificationsList() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: ListView.builder(
        itemCount: _notifications.length,
        itemBuilder: (context, index) {
          final notification = _notifications[index];
          return _buildNotificationItem(notification);
        },
      ),
    );
  }

  // بناء حالة عدم وجود إشعارات
  Widget _buildEmptyState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: kMainColor.withAlpha(15),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.notifications_off,
              size: 60,
              color: kMainColor,
            ),
          ),
          const SizedBox(height: 20),
          Text(
            message,
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: kGreyTextColor,
            ),
          ),
          const SizedBox(height: 10),
          Text(
            'ستظهر الإشعارات هنا عند وصولها',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: kGreyTextColor,
            ),
          ),
        ],
      ),
    );
  }

  // بناء عنصر إشعار موحد - تم تحسينه باستخدام NotificationHelper
  Widget _buildNotificationItem(UnifiedNotificationModel notification) {
    return CustomNotificationCard(
      notification: notification,
      onTap: () => _navigateToNotificationScreen(notification),
      onDelete: () async {
        try {
          // إخفاء الإشعار باستخدام NotificationHelper
          final success =
              await NotificationHelper.hideNotification(notification);

          if (success && mounted) {
            setState(() {
              _notifications.remove(notification);
            });

            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('تم إخفاء الإشعار')),
            );
          } else if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                  content: Text('فشل في إخفاء الإشعار، حاول مرة أخرى')),
            );
          }
        } catch (e) {
          debugPrint('خطأ في إخفاء الإشعار: $e');
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('حدث خطأ أثناء إخفاء الإشعار: $e')),
            );
          }
        }
      },
    );
  }

  // عرض حوار تفاصيل الفاتورة - تم تحسينه باستخدام NotificationHelper
  void _showInvoiceDetailsDialog(
      SystemNotificationModel notification, String invoiceType) {
    if (!mounted) return;

    try {
      // الحصول على البيانات بشكل آمن
      final invoiceNumber = NotificationHelper.getDataValue<String>(
          notification.data, 'invoiceNumber',
          defaultValue: 'غير متوفر');

      final customerName = NotificationHelper.getDataValue<String>(
          notification.data, 'customerName',
          defaultValue: 'غير متوفر');

      // عرض حوار يخبر المستخدم أن هذه الميزة قيد التطوير
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('عرض تفاصيل الفاتورة'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('نوع الفاتورة: ${_getInvoiceTypeName(invoiceType)}'),
              const SizedBox(height: 8),
              Text('رقم الفاتورة: $invoiceNumber'),
              const SizedBox(height: 8),
              Text('العميل: $customerName'),
              const SizedBox(height: 16),
              const Text(
                'سيتم تطوير هذه الميزة قريبًا لعرض تفاصيل الفاتورة مباشرة من شاشة الإشعارات.',
                style: TextStyle(fontStyle: FontStyle.italic),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إغلاق'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _navigateToInvoiceList(invoiceType);
              },
              child: const Text('الذهاب إلى قائمة الفواتير'),
            ),
          ],
        ),
      );
    } catch (e) {
      debugPrint('خطأ في عرض حوار تفاصيل الفاتورة: $e');
      // عرض رسالة خطأ للمستخدم
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ أثناء عرض تفاصيل الفاتورة: $e')),
        );
      }
    }
  }

  // الحصول على اسم نوع الفاتورة
  String _getInvoiceTypeName(String invoiceType) {
    switch (invoiceType) {
      case 'sale':
        return 'مبيعات';
      case 'purchase':
        return 'مشتريات';
      case 'due':
      case 'due_payment':
        return 'مديونية';
      default:
        return 'غير معروف';
    }
  }

  // الانتقال إلى قائمة الفواتير المناسبة
  void _navigateToInvoiceList(String invoiceType) {
    switch (invoiceType) {
      case 'sale':
        Navigator.pushNamed(context, '/Sales List');
        break;
      case 'purchase':
        Navigator.pushNamed(context, '/Purchase List');
        break;
      case 'due':
      case 'due_payment':
        Navigator.pushNamed(context, '/Due List');
        break;
      default:
        Navigator.pushNamed(context, '/Home');
        break;
    }
  }
}
