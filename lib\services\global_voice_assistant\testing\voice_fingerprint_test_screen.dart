import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../voice_assistant_exports.dart';

/// شاشة اختبار شاملة لنظام بصمة الصوت
class VoiceFingerprintTestScreen extends StatefulWidget {
  const VoiceFingerprintTestScreen({super.key});

  @override
  State<VoiceFingerprintTestScreen> createState() => _VoiceFingerprintTestScreenState();
}

class _VoiceFingerprintTestScreenState extends State<VoiceFingerprintTestScreen> {
  final VoiceFingerprintService _fingerprintService = VoiceFingerprintService();
  
  bool _isLoading = false;
  bool _isRegistering = false;
  bool _isVerifying = false;
  String _statusMessage = 'جاهز للاختبار';
  String _registrationProgress = '';
  Map<String, dynamic>? _lastVerificationResult;
  Map<String, dynamic>? _fingerprintInfo;

  @override
  void initState() {
    super.initState();
    _initializeService();
  }

  /// تهيئة الخدمة
  Future<void> _initializeService() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'تهيئة خدمة بصمة الصوت...';
    });

    try {
      await _fingerprintService.initialize();
      await _loadFingerprintInfo();
      
      setState(() {
        _statusMessage = 'تم تهيئة خدمة بصمة الصوت بنجاح';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'خطأ في تهيئة الخدمة: $e';
        _isLoading = false;
      });
    }
  }

  /// تحميل معلومات بصمة الصوت
  Future<void> _loadFingerprintInfo() async {
    try {
      final info = _fingerprintService.getVoiceFingerprintInfo();
      setState(() {
        _fingerprintInfo = info;
      });
    } catch (e) {
      _showSnackBar('خطأ في تحميل معلومات بصمة الصوت: $e', Colors.red);
    }
  }

  /// تسجيل بصمة الصوت
  Future<void> _registerFingerprint() async {
    setState(() {
      _isRegistering = true;
      _registrationProgress = 'بدء التسجيل...';
    });

    try {
      final trainingPhrases = [
        'مرحباً، أنا أسجل بصمة صوتي',
        'هذا هو صوتي الطبيعي',
        'أريد تسجيل بصمة صوتي للأمان',
        'المساعد الصوتي الذكي',
        'أهلاً وسهلاً بك في التطبيق',
      ];

      final result = await _fingerprintService.registerVoiceFingerprint(
        trainingPhrases: trainingPhrases,
        onProgress: (progress) {
          setState(() => _registrationProgress = progress);
        },
      );

      setState(() {
        _isRegistering = false;
        _statusMessage = result['success'] == true 
            ? 'تم تسجيل بصمة الصوت بنجاح!'
            : 'فشل في تسجيل بصمة الصوت: ${result['message']}';
      });

      if (result['success'] == true) {
        HapticFeedback.heavyImpact();
        _showSnackBar('تم تسجيل بصمة الصوت بنجاح!', Colors.green);
        await _loadFingerprintInfo();
      } else {
        HapticFeedback.vibrate();
        _showSnackBar(result['message'] ?? 'فشل في التسجيل', Colors.red);
      }
    } catch (e) {
      setState(() {
        _isRegistering = false;
        _statusMessage = 'خطأ في تسجيل بصمة الصوت: $e';
      });
      _showSnackBar('خطأ في التسجيل: $e', Colors.red);
    }
  }

  /// اختبار التحقق من بصمة الصوت
  Future<void> _testVerification() async {
    if (!_fingerprintService.isRegistered) {
      _showSnackBar('يجب تسجيل بصمة الصوت أولاً', Colors.orange);
      return;
    }

    setState(() {
      _isVerifying = true;
      _statusMessage = 'اختبار التحقق من بصمة الصوت...';
    });

    try {
      // محاكاة نص منطوق للاختبار
      final testPhrases = [
        'مرحباً، هذا اختبار للتحقق من صوتي',
        'أريد الوصول إلى حسابي',
        'المساعد الصوتي الذكي يعمل بشكل ممتاز',
      ];

      final randomPhrase = testPhrases[DateTime.now().millisecond % testPhrases.length];
      
      final result = await _fingerprintService.verifyVoice(randomPhrase);
      
      setState(() {
        _isVerifying = false;
        _lastVerificationResult = result;
        _statusMessage = result['success'] == true 
            ? 'تم التحقق من بصمة الصوت بنجاح!'
            : 'فشل في التحقق من بصمة الصوت';
      });

      if (result['success'] == true) {
        HapticFeedback.lightImpact();
        _showSnackBar('تم التحقق من بصمة الصوت!', Colors.green);
      } else {
        HapticFeedback.mediumImpact();
        _showSnackBar('فشل في التحقق من بصمة الصوت', Colors.orange);
      }
    } catch (e) {
      setState(() {
        _isVerifying = false;
        _statusMessage = 'خطأ في اختبار التحقق: $e';
      });
      _showSnackBar('خطأ في الاختبار: $e', Colors.red);
    }
  }

  /// حذف بصمة الصوت
  Future<void> _deleteFingerprint() async {
    final confirmed = await _showDeleteConfirmation();
    if (confirmed != true) return;

    setState(() {
      _isLoading = true;
      _statusMessage = 'حذف بصمة الصوت...';
    });

    try {
      await _fingerprintService.deleteVoiceFingerprint();
      await _loadFingerprintInfo();
      
      setState(() {
        _isLoading = false;
        _statusMessage = 'تم حذف بصمة الصوت بنجاح';
        _lastVerificationResult = null;
      });

      HapticFeedback.mediumImpact();
      _showSnackBar('تم حذف بصمة الصوت', Colors.orange);
    } catch (e) {
      setState(() {
        _isLoading = false;
        _statusMessage = 'خطأ في حذف بصمة الصوت: $e';
      });
      _showSnackBar('خطأ في الحذف: $e', Colors.red);
    }
  }

  /// عرض تأكيد الحذف
  Future<bool?> _showDeleteConfirmation() {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف بصمة الصوت'),
        content: const Text(
          'هل أنت متأكد من حذف بصمة الصوت؟\n'
          'ستحتاج لإعادة تسجيلها مرة أخرى.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  /// عرض رسالة
  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار بصمة الصوت'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _isLoading ? null : _initializeService,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // بطاقة الحالة
            _buildStatusCard(),
            
            const SizedBox(height: 16),
            
            // معلومات بصمة الصوت
            if (_fingerprintInfo != null) _buildFingerprintInfoCard(),
            
            const SizedBox(height: 16),
            
            // أزرار التحكم
            _buildControlButtons(),
            
            const SizedBox(height: 16),
            
            // نتائج التحقق
            if (_lastVerificationResult != null) _buildVerificationResultCard(),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة الحالة
  Widget _buildStatusCard() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _isLoading || _isRegistering || _isVerifying 
                      ? Icons.hourglass_empty 
                      : Icons.fingerprint,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                const Text(
                  'حالة بصمة الصوت',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            if (_isLoading || _isRegistering || _isVerifying) ...[
              const LinearProgressIndicator(),
              const SizedBox(height: 8),
            ],
            
            Text(
              _statusMessage,
              style: TextStyle(
                color: _isLoading || _isRegistering || _isVerifying 
                    ? Colors.orange 
                    : Colors.green,
                fontWeight: FontWeight.w500,
              ),
            ),
            
            if (_isRegistering && _registrationProgress.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                _registrationProgress,
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.blue,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة معلومات بصمة الصوت
  Widget _buildFingerprintInfoCard() {
    final info = _fingerprintInfo!;
    final isRegistered = info['isRegistered'] as bool;

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isRegistered ? Icons.check_circle : Icons.radio_button_unchecked,
                  color: isRegistered ? Colors.green : Colors.grey,
                ),
                const SizedBox(width: 8),
                Text(
                  isRegistered ? 'بصمة الصوت مسجلة' : 'بصمة الصوت غير مسجلة',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: isRegistered ? Colors.green[700] : Colors.grey[700],
                  ),
                ),
              ],
            ),
            
            if (isRegistered) ...[
              const SizedBox(height: 12),
              _buildInfoRow('معرف البصمة', info['profileId']?.toString() ?? 'غير متاح'),
              _buildInfoRow('تاريخ الإنشاء', _formatDate(info['createdAt'])),
              _buildInfoRow('عدد العينات', info['samplesCount']?.toString() ?? '0'),
              _buildInfoRow('عتبة الثقة', '${((info['confidenceThreshold'] ?? 0.0) * 100).toInt()}%'),
              
              if (info['qualityMetrics'] != null) ...[
                const SizedBox(height: 8),
                const Text(
                  'مقاييس الجودة:',
                  style: TextStyle(fontWeight: FontWeight.w500),
                ),
                const SizedBox(height: 4),
                _buildQualityMetrics(info['qualityMetrics']),
              ],
            ],
          ],
        ),
      ),
    );
  }

  /// بناء صف معلومات
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء مقاييس الجودة
  Widget _buildQualityMetrics(Map<String, dynamic> metrics) {
    final overallScore = metrics['overall_score'] as double? ?? 0.0;
    final avgQuality = metrics['average_quality'] as double? ?? 0.0;
    final consistency = metrics['consistency_score'] as double? ?? 0.0;

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(6),
      ),
      child: Column(
        children: [
          _buildQualityBar('الجودة الإجمالية', overallScore),
          _buildQualityBar('متوسط الجودة', avgQuality),
          _buildQualityBar('الثبات', consistency),
        ],
      ),
    );
  }

  /// بناء شريط الجودة
  Widget _buildQualityBar(String label, double value) {
    final percentage = (value * 100).toInt();
    final color = value >= 0.8 ? Colors.green : value >= 0.6 ? Colors.orange : Colors.red;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: const TextStyle(fontSize: 10),
            ),
          ),
          Expanded(
            child: LinearProgressIndicator(
              value: value,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(color),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            '$percentage%',
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء أزرار التحكم
  Widget _buildControlButtons() {
    final isRegistered = _fingerprintInfo?['isRegistered'] as bool? ?? false;

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'أدوات الاختبار',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            
            // الصف الأول
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isLoading || _isRegistering ? null : _registerFingerprint,
                    icon: _isRegistering 
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.mic),
                    label: Text(isRegistered ? 'إعادة التسجيل' : 'تسجيل بصمة الصوت'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 8),
            
            // الصف الثاني
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: (!isRegistered || _isLoading || _isVerifying) ? null : _testVerification,
                    icon: _isVerifying 
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.verified_user),
                    label: const Text('اختبار التحقق'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: (!isRegistered || _isLoading) ? null : _deleteFingerprint,
                    icon: const Icon(Icons.delete),
                    label: const Text('حذف البصمة'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.red,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة نتائج التحقق
  Widget _buildVerificationResultCard() {
    final result = _lastVerificationResult!;
    final success = result['success'] as bool;
    final confidence = result['confidence'] as double;
    final threshold = result['threshold'] as double;

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  success ? Icons.check_circle : Icons.cancel,
                  color: success ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 8),
                Text(
                  'نتيجة التحقق',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: success ? Colors.green[700] : Colors.red[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            Text(
              result['message'] as String,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
            
            const SizedBox(height: 8),
            
            _buildInfoRow('درجة الثقة', '${(confidence * 100).toInt()}%'),
            _buildInfoRow('العتبة المطلوبة', '${(threshold * 100).toInt()}%'),
            _buildInfoRow('معرف البصمة', result['voice_profile_id']?.toString() ?? 'غير متاح'),
            
            const SizedBox(height: 8),
            
            // شريط الثقة
            Row(
              children: [
                const Text('مستوى الثقة: ', style: TextStyle(fontSize: 12)),
                Expanded(
                  child: LinearProgressIndicator(
                    value: confidence,
                    backgroundColor: Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(
                      confidence >= threshold ? Colors.green : Colors.red,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  '${(confidence * 100).toInt()}%',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: confidence >= threshold ? Colors.green : Colors.red,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// تنسيق التاريخ
  String _formatDate(String? dateString) {
    if (dateString == null) return 'غير متاح';
    
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return 'غير متاح';
    }
  }
}
