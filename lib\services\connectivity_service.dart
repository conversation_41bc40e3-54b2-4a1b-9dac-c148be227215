import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';

/// خدمة التحقق من الاتصال بالإنترنت
class ConnectivityService {
  static final ConnectivityService _instance = ConnectivityService._internal();
  factory ConnectivityService() => _instance;
  ConnectivityService._internal();

  // مستمع لتغييرات الاتصال
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  
  // حالة الاتصال الحالية
  bool _isConnected = false;
  
  // الحصول على حالة الاتصال الحالية
  bool get isConnected => _isConnected;
  
  // تدفق لمراقبة حالة الاتصال
  final _connectivityStreamController = StreamController<bool>.broadcast();
  Stream<bool> get connectivityStream => _connectivityStreamController.stream;
  
  /// بدء مراقبة الاتصال
  void initialize() {
    debugPrint('بدء مراقبة الاتصال بالإنترنت');
    
    // إلغاء الاشتراك السابق إذا كان موجودًا
    _connectivitySubscription?.cancel();
    
    // التحقق من حالة الاتصال الأولية
    checkConnectivity();
    
    // الاستماع لتغييرات الاتصال
    _connectivitySubscription = Connectivity()
        .onConnectivityChanged
        .listen(_handleConnectivityChange);
  }
  
  /// التحقق من حالة الاتصال
  Future<bool> checkConnectivity() async {
    try {
      _isConnected = await InternetConnectionChecker().hasConnection;
      _connectivityStreamController.add(_isConnected);
      debugPrint('حالة الاتصال بالإنترنت: $_isConnected');
      return _isConnected;
    } catch (e) {
      debugPrint('خطأ في التحقق من الاتصال بالإنترنت: $e');
      _isConnected = false;
      _connectivityStreamController.add(false);
      return false;
    }
  }
  
  /// معالجة تغييرات الاتصال
  Future<void> _handleConnectivityChange(List<ConnectivityResult> results) async {
    if (results.isEmpty) return;
    
    // التحقق من الاتصال الفعلي بالإنترنت
    final hasConnection = await InternetConnectionChecker().hasConnection;
    
    // تحديث الحالة فقط إذا تغيرت
    if (_isConnected != hasConnection) {
      _isConnected = hasConnection;
      _connectivityStreamController.add(_isConnected);
      debugPrint('تغيرت حالة الاتصال بالإنترنت: $_isConnected');
    }
  }
  
  /// إيقاف مراقبة الاتصال
  void dispose() {
    _connectivitySubscription?.cancel();
    _connectivityStreamController.close();
    debugPrint('تم إيقاف مراقبة الاتصال بالإنترنت');
  }
}
