import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import '../voice_assistant_exports.dart';

/// خدمة أساسية موحدة للمساعد الصوتي - تجمع جميع الوظائف المكررة
class VoiceAssistantCoreService {
  static final VoiceAssistantCoreService _instance =
      VoiceAssistantCoreService._internal();
  factory VoiceAssistantCoreService() => _instance;
  VoiceAssistantCoreService._internal();

  // حالة الخدمة
  bool _isInitialized = false;
  bool _isRunning = false;
  bool _isEnabled = true;
  bool _isInitializing = false; // منع التهيئة المتعددة

  // إعدادات الخصوصية والأمان
  bool _enableDataCollection = false;
  bool _enableVoiceRecording = false;
  bool _enableCloudSync = false;
  bool _enableLocalProcessing = true;

  // مفاتيح التخزين
  static const String _enabledKey = 'voice_assistant_enabled';
  static const String _dataCollectionKey = 'voice_data_collection_enabled';
  static const String _voiceRecordingKey = 'voice_recording_enabled';
  static const String _cloudSyncKey = 'voice_cloud_sync_enabled';
  static const String _localProcessingKey = 'voice_local_processing_enabled';

  // الخدمات الأساسية
  late WakeWordDetectionService _wakeWordService;
  late VoiceFingerprintService _voiceFingerprintService;
  late VoiceResponseEngine _responseEngine;
  late stt.SpeechToText _speechToText;

  /// تهيئة الخدمة الأساسية مع منع التكرار
  Future<bool> initialize() async {
    // إذا كانت مهيأة بالفعل، إرجاع true
    if (_isInitialized) return true;

    // إذا كانت قيد التهيئة، انتظار انتهاء التهيئة
    if (_isInitializing) {
      while (_isInitializing && !_isInitialized) {
        await Future.delayed(const Duration(milliseconds: 100));
      }
      return _isInitialized;
    }

    // بدء التهيئة
    _isInitializing = true;

    try {
      // debugPrint('🚀 تهيئة الخدمة الأساسية للمساعد الصوتي...');

      // تحميل إعدادات الخصوصية
      await _loadPrivacySettings();

      // التحقق من الأذونات الأساسية فقط
      if (!await _checkEssentialPermissions()) {
        debugPrint('❌ الأذونات الأساسية غير متاحة');
        _isInitializing = false;
        return false;
      }

      // تهيئة الخدمات الأساسية
      await _initializeCoreServices();

      _isInitialized = true;
      _isInitializing = false;
      debugPrint('✅ تم تهيئة الخدمة الأساسية بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة الخدمة الأساسية: $e');
      _isInitializing = false;
      return false;
    }
  }

  /// تحميل إعدادات الخصوصية
  Future<void> _loadPrivacySettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      _isEnabled = prefs.getBool(_enabledKey) ?? false; // افتراضياً معطل
      _enableDataCollection = prefs.getBool(_dataCollectionKey) ?? false;
      _enableVoiceRecording = prefs.getBool(_voiceRecordingKey) ?? false;
      _enableCloudSync = prefs.getBool(_cloudSyncKey) ?? false;
      _enableLocalProcessing = prefs.getBool(_localProcessingKey) ?? true;

      // debugPrint('📋 إعدادات الخصوصية:');
      // debugPrint('   - المساعد مُفعل: $_isEnabled');
      // debugPrint('   - جمع البيانات: $_enableDataCollection');
      // debugPrint('   - تسجيل الصوت: $_enableVoiceRecording');
      // debugPrint('   - المزامنة السحابية: $_enableCloudSync');
      // debugPrint('   - المعالجة المحلية: $_enableLocalProcessing');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل إعدادات الخصوصية: $e');
    }
  }

  /// فحص الأذونات الأساسية فقط
  Future<bool> _checkEssentialPermissions() async {
    try {
      // فحص إذن الميكروفون فقط (الأساسي)
      final micStatus = await Permission.microphone.status;
      if (micStatus != PermissionStatus.granted) {
        debugPrint('❌ إذن الميكروفون مطلوب للمساعد الصوتي');
        return false;
      }

      // debugPrint('✅ الأذونات الأساسية متاحة');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في فحص الأذونات: $e');
      return false;
    }
  }

  /// تهيئة الخدمات الأساسية
  Future<void> _initializeCoreServices() async {
    try {
      _wakeWordService = WakeWordDetectionService();
      _voiceFingerprintService = VoiceFingerprintService();
      _responseEngine = VoiceResponseEngine();
      _speechToText = stt.SpeechToText();

      // تهيئة الخدمات مع معالجة الأخطاء
      await _wakeWordService.initialize();
      await _voiceFingerprintService.initialize();
      await _responseEngine.initialize();

      // تهيئة Speech-to-Text
      await _initializeSpeechToText();

      debugPrint('✅ تم تهيئة الخدمات الأساسية');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة الخدمات الأساسية: $e');
      rethrow;
    }
  }

  /// تهيئة Speech-to-Text مع معالجة الأخطاء
  Future<void> _initializeSpeechToText() async {
    try {
      final available = await _speechToText.initialize(
        onError: (error) {
          debugPrint('خطأ في Speech-to-Text: $error');
        },
        onStatus: (status) {
          debugPrint('حالة Speech-to-Text: $status');
        },
      );

      if (available) {
        debugPrint('✅ تم تهيئة Speech-to-Text بنجاح');
      } else {
        debugPrint('⚠️ Speech-to-Text غير متاح');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة Speech-to-Text: $e');
      // لا نرمي الخطأ هنا لأن Speech-to-Text اختياري
    }
  }

  /// بدء الخدمة
  Future<bool> start() async {
    if (!_isInitialized || _isRunning || !_isEnabled) {
      debugPrint('⚠️ لا يمكن بدء الخدمة');
      return false;
    }

    try {
      debugPrint('▶️ بدء المساعد الصوتي...');

      // بدء خدمة اكتشاف كلمة التفعيل
      await _wakeWordService.startListening();

      _isRunning = true;
      debugPrint('✅ تم بدء المساعد الصوتي بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في بدء المساعد الصوتي: $e');
      return false;
    }
  }

  /// إيقاف الخدمة
  Future<void> stop() async {
    if (!_isRunning) return;

    try {
      debugPrint('⏹️ إيقاف المساعد الصوتي...');

      await _wakeWordService.stopListening();

      _isRunning = false;
      debugPrint('✅ تم إيقاف المساعد الصوتي');
    } catch (e) {
      debugPrint('❌ خطأ في إيقاف المساعد الصوتي: $e');
    }
  }

  /// تفعيل/إلغاء تفعيل المساعد الصوتي
  Future<void> setEnabled(bool enabled) async {
    try {
      _isEnabled = enabled;

      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_enabledKey, enabled);

      if (enabled && !_isRunning) {
        await start();
      } else if (!enabled && _isRunning) {
        await stop();
      }

      debugPrint('✅ تم ${enabled ? 'تفعيل' : 'إلغاء تفعيل'} المساعد الصوتي');
    } catch (e) {
      debugPrint('❌ خطأ في تغيير حالة المساعد الصوتي: $e');
    }
  }

  /// تحديث إعدادات الخصوصية
  Future<void> updatePrivacySettings({
    bool? enableDataCollection,
    bool? enableVoiceRecording,
    bool? enableCloudSync,
    bool? enableLocalProcessing,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      if (enableDataCollection != null) {
        _enableDataCollection = enableDataCollection;
        await prefs.setBool(_dataCollectionKey, enableDataCollection);
      }

      if (enableVoiceRecording != null) {
        _enableVoiceRecording = enableVoiceRecording;
        await prefs.setBool(_voiceRecordingKey, enableVoiceRecording);
      }

      if (enableCloudSync != null) {
        _enableCloudSync = enableCloudSync;
        await prefs.setBool(_cloudSyncKey, enableCloudSync);
      }

      if (enableLocalProcessing != null) {
        _enableLocalProcessing = enableLocalProcessing;
        await prefs.setBool(_localProcessingKey, enableLocalProcessing);
      }

      debugPrint('✅ تم تحديث إعدادات الخصوصية');
    } catch (e) {
      debugPrint('❌ خطأ في تحديث إعدادات الخصوصية: $e');
    }
  }

  /// الحصول على حالة الخدمة
  Map<String, dynamic> getStatus() {
    return {
      'isInitialized': _isInitialized,
      'isRunning': _isRunning,
      'isEnabled': _isEnabled,
      'privacySettings': {
        'dataCollection': _enableDataCollection,
        'voiceRecording': _enableVoiceRecording,
        'cloudSync': _enableCloudSync,
        'localProcessing': _enableLocalProcessing,
      },
    };
  }

  /// تنظيف الموارد
  Future<void> dispose() async {
    await stop();
    _wakeWordService.dispose();
    _voiceFingerprintService.dispose();
    _responseEngine.dispose();
    _isInitialized = false;
    debugPrint('🧹 تم تنظيف موارد المساعد الصوتي');
  }

  // Getters للإعدادات
  bool get isEnabled => _isEnabled;
  bool get isRunning => _isRunning;
  bool get isInitialized => _isInitialized;
  bool get enableDataCollection => _enableDataCollection;
  bool get enableVoiceRecording => _enableVoiceRecording;
  bool get enableCloudSync => _enableCloudSync;
  bool get enableLocalProcessing => _enableLocalProcessing;

  // Getters للخدمات
  stt.SpeechToText get speechToText => _speechToText;
}
