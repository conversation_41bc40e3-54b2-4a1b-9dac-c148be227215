import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';
import 'package:intl/intl.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/currency.dart';
import 'package:mobile_pos/const_commas.dart';
import 'package:nb_utils/nb_utils.dart';

import '../providers/treasury_provider.dart';
import '../widgets/treasury_card_widget.dart';
import '../widgets/balance_summary_widget.dart';
import '../widgets/transaction_item_widget.dart';
import 'add_treasury_transaction_screen.dart';
import 'treasury_reports_screen.dart';
import 'treasury_history_screen.dart';

class TreasuryMainScreen extends ConsumerStatefulWidget {
  static const String routeName = '/treasury';

  const TreasuryMainScreen({super.key});

  @override
  ConsumerState<TreasuryMainScreen> createState() => _TreasuryMainScreenState();
}

class _TreasuryMainScreenState extends ConsumerState<TreasuryMainScreen> {
  String _selectedFilter = 'all'; // 'all', 'income', 'expense'
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // بدء المزامنة التلقائية مع المبيعات والمشتريات والمصروفات
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final treasuryService = ref.read(treasuryServiceProvider);
      treasuryService.startAutoSync();
      treasuryService.syncAllData();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final balanceAsync = ref.watch(treasuryBalanceProvider);
    final statisticsAsync = ref.watch(treasuryStatisticsProvider);

    // Create filters for transactions
    final filters = {
      'type': _selectedFilter == 'all' ? null : _selectedFilter,
      'searchQuery': _searchQuery,
    };

    final filteredTransactions =
        ref.watch(filteredTransactionsProvider(filters));

    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        title: Text(
          'إدارة الخزينة',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontSize: 20.0,
            fontWeight: FontWeight.bold,
          ),
        ),
        iconTheme: const IconThemeData(color: Colors.white),
        centerTitle: true,
        elevation: 0.0,
        actions: [
          IconButton(
            icon: const Icon(FeatherIcons.barChart2),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const TreasuryReportsScreen(),
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(FeatherIcons.clock),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const TreasuryHistoryScreen(),
                ),
              );
            },
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(30),
            topLeft: Radius.circular(30),
          ),
        ),
        child: Column(
          children: [
            // Balance Summary Section
            Container(
              padding: const EdgeInsets.all(20),
              child: balanceAsync.when(
                data: (balance) => BalanceSummaryWidget(balance: balance),
                loading: () => const Center(
                  child: CircularProgressIndicator(),
                ),
                error: (error, stack) => Center(
                  child: Text(
                    'خطأ في تحميل الرصيد: $error',
                    style: const TextStyle(color: Colors.red),
                  ),
                ),
              ),
            ),

            // Statistics Cards
            Container(
              height: 120,
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: statisticsAsync.isNotEmpty
                  ? Row(
                      children: [
                        Expanded(
                          child: TreasuryCardWidget(
                            title: 'اليوم',
                            amount:
                                statisticsAsync['todayNet']?.toString() ?? '0',
                            subtitle:
                                '${statisticsAsync['todayTransactions'] ?? 0} معاملة',
                            color: (statisticsAsync['todayNet'] ?? 0) >= 0
                                ? Colors.green
                                : Colors.red,
                            icon: FeatherIcons.calendar,
                          ),
                        ),
                        const SizedBox(width: 10),
                        Expanded(
                          child: TreasuryCardWidget(
                            title: 'هذا الشهر',
                            amount:
                                statisticsAsync['monthNet']?.toString() ?? '0',
                            subtitle:
                                '${statisticsAsync['monthTransactions'] ?? 0} معاملة',
                            color: (statisticsAsync['monthNet'] ?? 0) >= 0
                                ? Colors.green
                                : Colors.red,
                            icon: FeatherIcons.trendingUp,
                          ),
                        ),
                      ],
                    )
                  : const Center(child: CircularProgressIndicator()),
            ),

            const SizedBox(height: 10),

            // Due Amounts Cards (المديونية)
            Container(
              height: 120,
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: statisticsAsync.isNotEmpty
                  ? Row(
                      children: [
                        Expanded(
                          child: TreasuryCardWidget(
                            title: 'مديونية العملاء',
                            amount:
                                statisticsAsync['customerDue']?.toString() ??
                                    '0',
                            subtitle: 'لنا عند العملاء',
                            color: Colors.blue,
                            icon: FeatherIcons.users,
                          ),
                        ),
                        const SizedBox(width: 10),
                        Expanded(
                          child: TreasuryCardWidget(
                            title: 'مديونية الموردين',
                            amount:
                                statisticsAsync['supplierDue']?.toString() ??
                                    '0',
                            subtitle: 'علينا للموردين',
                            color: Colors.orange,
                            icon: FeatherIcons.truck,
                          ),
                        ),
                      ],
                    )
                  : const Center(child: CircularProgressIndicator()),
            ),

            const SizedBox(height: 20),

            // Search and Filter Section
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: [
                  // Search Bar
                  TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'البحث في المعاملات...',
                      hintStyle: GoogleFonts.cairo(color: Colors.grey),
                      prefixIcon: const Icon(FeatherIcons.search),
                      suffixIcon: _searchQuery.isNotEmpty
                          ? IconButton(
                              icon: const Icon(FeatherIcons.x),
                              onPressed: () {
                                _searchController.clear();
                                setState(() {
                                  _searchQuery = '';
                                });
                              },
                            )
                          : null,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10),
                        borderSide:
                            const BorderSide(color: kBorderColorTextField),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10),
                        borderSide: const BorderSide(color: kMainColor),
                      ),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                      });
                    },
                  ),

                  const SizedBox(height: 10),

                  // Filter Chips
                  Row(
                    children: [
                      _buildFilterChip('الكل', 'all'),
                      const SizedBox(width: 8),
                      _buildFilterChip('الإيرادات', 'income'),
                      const SizedBox(width: 8),
                      _buildFilterChip('المصروفات', 'expense'),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 20),

            // Transactions List Header
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'المعاملات الأخيرة',
                    style: GoogleFonts.cairo(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: kTitleColor,
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const TreasuryHistoryScreen(),
                        ),
                      );
                    },
                    child: Text(
                      'عرض الكل',
                      style: GoogleFonts.cairo(
                        color: kMainColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Transactions List
            Expanded(
              child: filteredTransactions.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            FeatherIcons.inbox,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'لا توجد معاملات',
                            style: GoogleFonts.cairo(
                              fontSize: 18,
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'ابدأ بإضافة معاملة جديدة',
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              color: Colors.grey[500],
                            ),
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      itemCount: filteredTransactions.length > 10
                          ? 10
                          : filteredTransactions.length,
                      itemBuilder: (context, index) {
                        final transaction = filteredTransactions[index];
                        return TransactionItemWidget(
                          transaction: transaction,
                          onTap: () {
                            // Navigate to transaction details or edit
                            _showTransactionDetails(transaction);
                          },
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const AddTreasuryTransactionScreen(),
            ),
          );
        },
        backgroundColor: kMainColor,
        icon: const Icon(FeatherIcons.plus, color: Colors.white),
        label: Text(
          'إضافة معاملة',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _buildFilterChip(String label, String value) {
    final isSelected = _selectedFilter == value;

    return FilterChip(
      label: Text(
        label,
        style: GoogleFonts.cairo(
          color: isSelected ? Colors.white : kMainColor,
          fontWeight: FontWeight.w600,
        ),
      ),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedFilter = value;
        });
      },
      backgroundColor: Colors.white,
      selectedColor: kMainColor,
      checkmarkColor: Colors.white,
      side: BorderSide(
        color: isSelected ? kMainColor : kBorderColorTextField,
      ),
    );
  }

  void _showTransactionDetails(transaction) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(top: 10),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'تفاصيل المعاملة',
                      style: GoogleFonts.cairo(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: kTitleColor,
                      ),
                    ),
                    const SizedBox(height: 20),
                    _buildDetailRow(
                        'النوع', transaction.isIncome ? 'إيراد' : 'مصروف'),
                    _buildDetailRow('الفئة', transaction.category),
                    _buildDetailRow('الوصف', transaction.description),
                    _buildDetailRow('المبلغ',
                        '$currency${myFormat.format(transaction.amountAsDouble)}'),
                    _buildDetailRow('طريقة الدفع', transaction.paymentMethod),
                    _buildDetailRow('رقم المرجع', transaction.referenceNumber),
                    _buildDetailRow(
                        'التاريخ',
                        DateFormat('yyyy/MM/dd')
                            .format(transaction.dateAsDateTime)),
                    if (transaction.notes.isNotEmpty)
                      _buildDetailRow('ملاحظات', transaction.notes),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: GoogleFonts.cairo(
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: GoogleFonts.cairo(
                color: kTitleColor,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
