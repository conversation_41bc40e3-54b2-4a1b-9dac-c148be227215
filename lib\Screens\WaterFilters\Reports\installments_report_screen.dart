import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/model/water_filter_models.dart';
import 'package:mobile_pos/services/water_filter_service.dart';

class InstallmentsReportScreen extends StatefulWidget {
  const InstallmentsReportScreen({super.key});

  @override
  State<InstallmentsReportScreen> createState() =>
      _InstallmentsReportScreenState();
}

class _InstallmentsReportScreenState extends State<InstallmentsReportScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  List<WaterFilterInstallment> _allInstallments = [];
  List<WaterFilterCustomer> _allCustomers = [];
  List<WaterFilterSystem> _allSystems = [];

  // إحصائيات الأقساط
  Map<String, dynamic> _stats = {};

  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadInstallmentsData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadInstallmentsData() async {
    setState(() => _isLoading = true);

    try {
      // تحميل الأقساط
      final installmentsData = await WaterFilterService.getData('Installments');
      final installments = <WaterFilterInstallment>[];

      installmentsData.forEach((key, value) {
        try {
          final installment = WaterFilterInstallment.fromJson(
            Map<String, dynamic>.from(value),
          );
          installments.add(installment);
        } catch (e) {
          debugPrint('خطأ في معالجة قسط: $e');
        }
      });

      // تحميل العملاء
      final customersData = await WaterFilterService.getData('Customers');
      final customers = <WaterFilterCustomer>[];

      customersData.forEach((key, value) {
        try {
          final customer = WaterFilterCustomer.fromJson(
            Map<String, dynamic>.from(value),
          );
          customers.add(customer);
        } catch (e) {
          debugPrint('خطأ في معالجة عميل: $e');
        }
      });

      // تحميل الأنظمة
      final systemsData = await WaterFilterService.getData('Systems');
      final systems = <WaterFilterSystem>[];

      systemsData.forEach((key, value) {
        try {
          final system = WaterFilterSystem.fromJson(
            Map<String, dynamic>.from(value),
          );
          systems.add(system);
        } catch (e) {
          debugPrint('خطأ في معالجة نظام: $e');
        }
      });

      setState(() {
        _allInstallments = installments;
        _allCustomers = customers;
        _allSystems = systems;
        _calculateStats();
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات الأقساط: $e');
      setState(() => _isLoading = false);
    }
  }

  void _calculateStats() {
    final totalInstallments = _allInstallments.length;

    // إحصائيات حسب الحالة
    final paidInstallments = _allInstallments
        .where((i) => i.status == InstallmentStatus.paid)
        .length;
    final unpaidInstallments = totalInstallments - paidInstallments;
    final overdueInstallments = _allInstallments
        .where((i) => i.status == InstallmentStatus.overdue)
        .length;

    // إحصائيات مالية
    final totalAmount = _allInstallments.fold(
        0.0, (sum, installment) => sum + installment.amount);
    final paidAmount = _allInstallments
        .where((i) => i.status == InstallmentStatus.paid)
        .fold(0.0, (sum, installment) => sum + installment.amount);
    final unpaidAmount = totalAmount - paidAmount;
    final overdueAmount = _allInstallments
        .where((i) => i.status == InstallmentStatus.overdue)
        .fold(0.0, (sum, installment) => sum + installment.amount);

    // الأقساط المستحقة هذا الشهر
    final now = DateTime.now();
    final thisMonthInstallments = _allInstallments
        .where((i) =>
            i.status != InstallmentStatus.paid &&
            i.dueDate.year == now.year &&
            i.dueDate.month == now.month)
        .length;

    // الأقساط المستحقة الأسبوع القادم
    final nextWeek = now.add(const Duration(days: 7));
    final nextWeekInstallments = _allInstallments
        .where((i) =>
            i.status != InstallmentStatus.paid &&
            i.dueDate.isAfter(now) &&
            i.dueDate.isBefore(nextWeek))
        .length;

    setState(() {
      _stats = {
        'total': totalInstallments,
        'paid': paidInstallments,
        'unpaid': unpaidInstallments,
        'overdue': overdueInstallments,
        'totalAmount': totalAmount,
        'paidAmount': paidAmount,
        'unpaidAmount': unpaidAmount,
        'overdueAmount': overdueAmount,
        'thisMonth': thisMonthInstallments,
        'nextWeek': nextWeekInstallments,
      };
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        title: Text(
          'تقرير الأقساط',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: _loadInstallmentsData,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          labelStyle: GoogleFonts.cairo(fontWeight: FontWeight.bold),
          tabs: const [
            Tab(text: 'الإحصائيات', icon: Icon(Icons.analytics)),
            Tab(text: 'الأقساط المستحقة', icon: Icon(Icons.schedule)),
            Tab(text: 'التحليل المالي', icon: Icon(Icons.attach_money)),
          ],
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : TabBarView(
                controller: _tabController,
                children: [
                  _buildStatsTab(),
                  _buildDueInstallmentsTab(),
                  _buildFinancialTab(),
                ],
              ),
      ),
    );
  }

  Widget _buildStatsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('إحصائيات الأقساط'),
          const SizedBox(height: 16),
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 0.9,
            children: [
              _buildStatCard(
                title: 'إجمالي الأقساط',
                value: '${_stats['total'] ?? 0}',
                icon: Icons.receipt_long,
                color: Colors.blue,
              ),
              _buildStatCard(
                title: 'أقساط مدفوعة',
                value: '${_stats['paid'] ?? 0}',
                icon: Icons.check_circle,
                color: Colors.green,
              ),
              _buildStatCard(
                title: 'أقساط غير مدفوعة',
                value: '${_stats['unpaid'] ?? 0}',
                icon: Icons.pending,
                color: Colors.orange,
              ),
              _buildStatCard(
                title: 'أقساط متأخرة',
                value: '${_stats['overdue'] ?? 0}',
                icon: Icons.warning,
                color: Colors.red,
              ),
            ],
          ),
          const SizedBox(height: 24),
          _buildSectionTitle('الأقساط المستحقة'),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildTypeCard(
                  title: 'هذا الشهر',
                  subtitle: 'أقساط مستحقة',
                  value: '${_stats['thisMonth'] ?? 0}',
                  color: Colors.purple,
                  icon: Icons.calendar_month,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildTypeCard(
                  title: 'الأسبوع القادم',
                  subtitle: 'أقساط مستحقة',
                  value: '${_stats['nextWeek'] ?? 0}',
                  color: Colors.teal,
                  icon: Icons.schedule,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDueInstallmentsTab() {
    final overdueInstallments = _allInstallments
        .where((i) => i.status == InstallmentStatus.overdue)
        .toList();

    final upcomingInstallments = _allInstallments
        .where((i) =>
            i.status == InstallmentStatus.pending &&
            i.dueDate.isAfter(DateTime.now()))
        .toList();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('الأقساط المتأخرة'),
          const SizedBox(height: 16),
          if (overdueInstallments.isEmpty)
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.green.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.green, size: 32),
                  const SizedBox(width: 12),
                  Text(
                    'لا توجد أقساط متأخرة',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.green.shade800,
                    ),
                  ),
                ],
              ),
            )
          else
            ...overdueInstallments.map((installment) =>
                _buildInstallmentCard(installment, isOverdue: true)),
          const SizedBox(height: 24),
          _buildSectionTitle('الأقساط المستحقة قريباً'),
          const SizedBox(height: 16),
          if (upcomingInstallments.isEmpty)
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.info, color: Colors.blue, size: 32),
                  const SizedBox(width: 12),
                  Text(
                    'لا توجد أقساط مستحقة قريباً',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue.shade800,
                    ),
                  ),
                ],
              ),
            )
          else
            ...upcomingInstallments
                .take(10)
                .map((installment) => _buildInstallmentCard(installment)),
        ],
      ),
    );
  }

  Widget _buildFinancialTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('التحليل المالي للأقساط'),
          const SizedBox(height: 16),

          // إجمالي المبالغ
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.blue.shade400, Colors.blue.shade600],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.blue.shade200,
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.account_balance_wallet,
                      color: Colors.white,
                      size: 32,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'إجمالي قيمة الأقساط',
                      style: GoogleFonts.cairo(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Text(
                  '${(_stats['totalAmount'] ?? 0).toStringAsFixed(2)} ج.م',
                  style: GoogleFonts.cairo(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // تفاصيل المدفوعات
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 0.8,
            children: [
              _buildFinancialCard(
                title: 'إجمالي المدفوع',
                value: '${(_stats['paidAmount'] ?? 0).toStringAsFixed(2)} ج.م',
                icon: Icons.check_circle,
                color: Colors.green,
              ),
              _buildFinancialCard(
                title: 'إجمالي المتبقي',
                value:
                    '${(_stats['unpaidAmount'] ?? 0).toStringAsFixed(2)} ج.م',
                icon: Icons.pending,
                color: Colors.orange,
              ),
              _buildFinancialCard(
                title: 'المبالغ المتأخرة',
                value:
                    '${(_stats['overdueAmount'] ?? 0).toStringAsFixed(2)} ج.م',
                icon: Icons.warning,
                color: Colors.red,
              ),
              _buildFinancialCard(
                title: 'نسبة التحصيل',
                value:
                    '${((_stats['paidAmount'] ?? 0) / (_stats['totalAmount'] ?? 1) * 100).toStringAsFixed(1)}%',
                icon: Icons.trending_up,
                color: Colors.blue,
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Helper Methods
  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: GoogleFonts.cairo(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: kMainColor,
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 28),
          const SizedBox(height: 8),
          Flexible(
            child: Text(
              value,
              style: GoogleFonts.cairo(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(height: 4),
          Flexible(
            child: Text(
              title,
              style: GoogleFonts.cairo(
                fontSize: 11,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTypeCard({
    required String title,
    required String subtitle,
    required String value,
    required Color color,
    required IconData icon,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(width: 8),
              Text(
                title,
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInstallmentCard(WaterFilterInstallment installment,
      {bool isOverdue = false}) {
    // البحث عن النظام أولاً
    final system = _allSystems.firstWhere(
      (s) => s.id == installment.systemId,
      orElse: () => WaterFilterSystem(
        id: '',
        customerId: '',
        productId: '',
        serialNumber: '',
        installationDate: DateTime.now(),
        status: FilterSystemStatus.inactive,
        nextMaintenanceDate: DateTime.now(),
        totalCost: 0,
        paidAmount: 0,
        remainingAmount: 0,
        isUnderWarranty: false,
        warrantyEndDate: DateTime.now(),
      ),
    );

    // ثم البحث عن العميل
    final customer = _allCustomers.firstWhere(
      (c) => c.id == system.customerId,
      orElse: () => WaterFilterCustomer(
        id: '',
        name: 'غير محدد',
        phone: '',
        address: '',
        email: '',
        area: '',
        city: '',
      ),
    );

    final cardColor = isOverdue ? Colors.red : Colors.orange;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: cardColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: cardColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        isOverdue ? Icons.warning : Icons.schedule,
                        color: cardColor,
                        size: 16,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      customer.name,
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: cardColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    isOverdue ? 'متأخر' : 'مستحق',
                    style: GoogleFonts.cairo(
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      color: cardColor,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.attach_money, size: 14, color: Colors.grey.shade600),
                const SizedBox(width: 4),
                Text(
                  'المبلغ: ${installment.amount.toStringAsFixed(2)} ج.م',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: Colors.grey.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(Icons.calendar_today,
                    size: 14, color: Colors.grey.shade600),
                const SizedBox(width: 4),
                Text(
                  'تاريخ الاستحقاق: ${installment.dueDate.day}/${installment.dueDate.month}/${installment.dueDate.year}',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
            if (customer.phone.isNotEmpty) ...[
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(Icons.phone, size: 14, color: Colors.grey.shade600),
                  const SizedBox(width: 4),
                  Text(
                    customer.phone,
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 6),
          Flexible(
            child: Text(
              value,
              style: GoogleFonts.cairo(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
          const SizedBox(height: 4),
          Flexible(
            child: Text(
              title,
              style: GoogleFonts.cairo(
                fontSize: 10,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
        ],
      ),
    );
  }
}
