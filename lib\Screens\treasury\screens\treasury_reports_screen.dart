import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';
import 'package:intl/intl.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/currency.dart';
import 'package:mobile_pos/const_commas.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:nb_utils/nb_utils.dart';

import '../providers/treasury_report_provider.dart';
import '../models/treasury_report_model.dart';

class TreasuryReportsScreen extends ConsumerStatefulWidget {
  const TreasuryReportsScreen({super.key});

  @override
  ConsumerState<TreasuryReportsScreen> createState() =>
      _TreasuryReportsScreenState();
}

class _TreasuryReportsScreenState extends ConsumerState<TreasuryReportsScreen> {
  @override
  Widget build(BuildContext context) {
    final quickReports = ref.watch(quickReportsProvider);
    final generateReportAsync = ref.watch(generateReportProvider);

    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        title: Text(
          'تقارير الخزينة',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontSize: 20.0,
            fontWeight: FontWeight.bold,
          ),
        ),
        iconTheme: const IconThemeData(color: Colors.white),
        centerTitle: true,
        elevation: 0.0,
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(30),
            topLeft: Radius.circular(30),
          ),
        ),
        child: Column(
          children: [
            // Quick Reports Section
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'التقارير السريعة',
                    style: GoogleFonts.cairo(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: kTitleColor,
                    ),
                  ),
                  const SizedBox(height: 15),
                  GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      crossAxisSpacing: 15,
                      mainAxisSpacing: 15,
                      childAspectRatio: 1.2,
                    ),
                    itemCount: quickReports.length,
                    itemBuilder: (context, index) {
                      final report = quickReports[index];
                      return _buildQuickReportCard(report);
                    },
                  ),
                ],
              ),
            ),

            // Generated Report Section
            Expanded(
              child: generateReportAsync.when(
                data: (report) {
                  if (report == null) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            FeatherIcons.barChart2,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'اختر نوع التقرير لعرضه',
                            style: GoogleFonts.cairo(
                              fontSize: 16,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  return _buildReportDetails(report);
                },
                loading: () => const Center(
                  child: CircularProgressIndicator(),
                ),
                error: (error, stack) => Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        FeatherIcons.alertCircle,
                        size: 64,
                        color: Colors.red[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'خطأ في إنشاء التقرير',
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          color: Colors.red[600],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        error.toString(),
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showCustomReportDialog();
        },
        backgroundColor: kMainColor,
        child: const Icon(FeatherIcons.plus, color: Colors.white),
      ),
    );
  }

  Widget _buildQuickReportCard(Map<String, dynamic> report) {
    IconData icon;
    switch (report['icon']) {
      case 'today':
        icon = FeatherIcons.calendar;
        break;
      case 'week':
        icon = FeatherIcons.clock;
        break;
      case 'month':
        icon = FeatherIcons.calendar;
        break;
      case 'year':
        icon = FeatherIcons.calendar;
        break;
      case 'last_month':
        icon = FeatherIcons.arrowLeft;
        break;
      case 'last_year':
        icon = FeatherIcons.arrowLeft;
        break;
      default:
        icon = FeatherIcons.barChart2;
    }

    return GestureDetector(
      onTap: () => _generateReport(report['type'], report['date']),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            color: kMainColor.withOpacity(0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: kMainColor.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: kMainColor,
              size: 32,
            ),
            const SizedBox(height: 12),
            Text(
              report['title'],
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: kTitleColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              report['subtitle'],
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReportDetails(TreasuryReportModel report) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Report Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'تقرير ${_getReportTypeInArabic(report.reportType)}',
                    style: GoogleFonts.cairo(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: kTitleColor,
                    ),
                  ),
                  Text(
                    '${DateFormat('yyyy/MM/dd').format(report.startDateAsDateTime)} - ${DateFormat('yyyy/MM/dd').format(report.endDateAsDateTime)}',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
              PopupMenuButton<String>(
                icon: const Icon(FeatherIcons.moreVertical),
                onSelected: (value) {
                  if (value == 'export_csv') {
                    _exportReport(report, 'csv');
                  } else if (value == 'export_json') {
                    _exportReport(report, 'json');
                  }
                },
                itemBuilder: (context) => [
                  PopupMenuItem(
                    value: 'export_csv',
                    child: Row(
                      children: [
                        const Icon(FeatherIcons.download),
                        const SizedBox(width: 8),
                        Text('تصدير CSV', style: GoogleFonts.cairo()),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'export_json',
                    child: Row(
                      children: [
                        const Icon(FeatherIcons.download),
                        const SizedBox(width: 8),
                        Text('تصدير JSON', style: GoogleFonts.cairo()),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Summary Cards
          Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  'إجمالي الإيرادات',
                  report.totalIncome,
                  Colors.green,
                  FeatherIcons.trendingUp,
                ),
              ),
              const SizedBox(width: 15),
              Expanded(
                child: _buildSummaryCard(
                  'إجمالي المصروفات',
                  report.totalExpense,
                  Colors.red,
                  FeatherIcons.trendingDown,
                ),
              ),
            ],
          ),

          const SizedBox(height: 15),

          Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  report.isProfit ? 'صافي الربح' : 'صافي الخسارة',
                  report.netAmount,
                  report.isProfit ? Colors.green : Colors.red,
                  report.isProfit
                      ? FeatherIcons.trendingUp
                      : FeatherIcons.trendingDown,
                ),
              ),
              const SizedBox(width: 15),
              Expanded(
                child: _buildSummaryCard(
                  'عدد المعاملات',
                  report.transactionCount,
                  kMainColor,
                  FeatherIcons.list,
                ),
              ),
            ],
          ),

          const SizedBox(height: 30),

          // Category Breakdown
          if (report.items.isNotEmpty) ...[
            Text(
              'التفصيل حسب الفئة',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: kTitleColor,
              ),
            ),
            const SizedBox(height: 15),
            ...report.items.map((item) => _buildCategoryItem(item)),
          ],
        ],
      ),
    );
  }

  Widget _buildSummaryCard(
      String title, String value, Color color, IconData icon) {
    final numValue = double.tryParse(value) ?? 0.0;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: Colors.grey[700],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            title.contains('عدد')
                ? value
                : '$currency${myFormat.format(numValue)}',
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryItem(TreasuryReportItem item) {
    final color = item.type == 'income' ? Colors.green : Colors.red;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.grey[200]!,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              item.type == 'income'
                  ? FeatherIcons.trendingUp
                  : FeatherIcons.trendingDown,
              color: color,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.category,
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: kTitleColor,
                  ),
                ),
                Text(
                  '${item.count} معاملة • ${item.percentage}%',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Text(
            '$currency${myFormat.format(item.amountAsDouble)}',
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  void _generateReport(String type, DateTime date) async {
    final notifier = ref.read(generateReportProvider.notifier);

    switch (type) {
      case 'daily':
        await notifier.generateDailyReport(date);
        break;
      case 'weekly':
        await notifier.generateWeeklyReport(date);
        break;
      case 'monthly':
        await notifier.generateMonthlyReport(date);
        break;
      case 'yearly':
        await notifier.generateYearlyReport(date);
        break;
    }
  }

  void _exportReport(TreasuryReportModel report, String format) async {
    EasyLoading.show(status: 'جاري التصدير...');

    final notifier = ref.read(exportReportProvider.notifier);

    if (format == 'csv') {
      await notifier.exportToCsv(report);
    } else {
      await notifier.exportToJson(report);
    }

    final result = ref.read(exportReportProvider);

    result.when(
      data: (filePath) {
        if (filePath != null) {
          EasyLoading.showSuccess('تم تصدير التقرير بنجاح');
          // يمكن إضافة مشاركة الملف هنا
        } else {
          EasyLoading.showError('فشل في تصدير التقرير');
        }
      },
      loading: () {},
      error: (error, stack) {
        EasyLoading.showError('خطأ في التصدير: $error');
      },
    );
  }

  void _showCustomReportDialog() {
    DateTime startDate = DateTime.now().subtract(const Duration(days: 30));
    DateTime endDate = DateTime.now();

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text(
            'تقرير مخصص',
            style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                title: Text('من تاريخ', style: GoogleFonts.cairo()),
                subtitle: Text(
                  DateFormat('yyyy/MM/dd').format(startDate),
                  style: GoogleFonts.cairo(),
                ),
                trailing: const Icon(FeatherIcons.calendar),
                onTap: () async {
                  final date = await showDatePicker(
                    context: context,
                    initialDate: startDate,
                    firstDate: DateTime(2020),
                    lastDate: DateTime.now(),
                  );
                  if (date != null) {
                    setState(() => startDate = date);
                  }
                },
              ),
              ListTile(
                title: Text('إلى تاريخ', style: GoogleFonts.cairo()),
                subtitle: Text(
                  DateFormat('yyyy/MM/dd').format(endDate),
                  style: GoogleFonts.cairo(),
                ),
                trailing: const Icon(FeatherIcons.calendar),
                onTap: () async {
                  final date = await showDatePicker(
                    context: context,
                    initialDate: endDate,
                    firstDate: startDate,
                    lastDate: DateTime.now(),
                  );
                  if (date != null) {
                    setState(() => endDate = date);
                  }
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('إلغاء', style: GoogleFonts.cairo()),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                ref
                    .read(generateReportProvider.notifier)
                    .generateCustomReport(startDate, endDate);
              },
              style: ElevatedButton.styleFrom(backgroundColor: kMainColor),
              child: Text('إنشاء التقرير',
                  style: GoogleFonts.cairo(color: Colors.white)),
            ),
          ],
        ),
      ),
    );
  }

  String _getReportTypeInArabic(String reportType) {
    switch (reportType) {
      case 'daily':
        return 'يومي';
      case 'weekly':
        return 'أسبوعي';
      case 'monthly':
        return 'شهري';
      case 'yearly':
        return 'سنوي';
      case 'custom':
        return 'مخصص';
      default:
        return reportType;
    }
  }
}
