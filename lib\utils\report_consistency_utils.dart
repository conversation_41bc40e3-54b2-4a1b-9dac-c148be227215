// دوال مساعدة للتحقق من اتساق البيانات في التقارير
import 'dart:convert';

import 'package:firebase_database/firebase_database.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/currency.dart';
import 'package:mobile_pos/model/transition_model.dart';
import 'package:mobile_pos/model/product_model.dart';

/// التحقق من اتساق بيانات المبيعات
Future<Map<String, dynamic>> verifySalesData(SalesTransitionModel sale) async {
  try {
    // التحقق من اتساق المبلغ الإجمالي
    double calculatedTotal = 0;

    if (sale.productList != null) {
      for (var product in sale.productList!) {
        // تحويل آمن لـ subTotal
        double price = 0.0;
        if (product.subTotal != null) {
          if (product.subTotal is double) {
            price = product.subTotal;
          } else if (product.subTotal is String) {
            price = double.tryParse(product.subTotal) ?? 0.0;
          } else if (product.subTotal is num) {
            price = product.subTotal.toDouble();
          }
        }
        calculatedTotal += price;
      }
    }

    double storedTotal = sale.totalAmount?.toDouble() ?? 0;

    // التحقق من اتساق المبلغ الإجمالي
    bool isTotalConsistent = (calculatedTotal - storedTotal).abs() <= 0.01;

    // التحقق من اتساق المخزون
    bool isStockConsistent = await verifyProductStockConsistency(sale);

    return {
      'isConsistent': isTotalConsistent && isStockConsistent,
      'totalConsistency': {
        'isConsistent': isTotalConsistent,
        'calculatedTotal': calculatedTotal,
        'storedTotal': storedTotal,
      },
      'stockConsistency': {
        'isConsistent': isStockConsistent,
      },
    };
  } catch (e) {
    // خطأ في التحقق من اتساق بيانات المبيعات
    return {
      'isConsistent': false,
      'error': e.toString(),
    };
  }
}

/// التحقق من اتساق المخزون مع حركات البيع
Future<bool> verifyProductStockConsistency(SalesTransitionModel sale) async {
  try {
    if (sale.productList == null || sale.productList!.isEmpty) {
      return true;
    }

    for (var product in sale.productList!) {
      // الحصول على المخزون الحالي
      final productRef = FirebaseDatabase.instance.ref('$constUserId/Products');
      final productSnapshot = await productRef
          .orderByChild('productCode')
          .equalTo(product.productId)
          .get();

      if (!productSnapshot.exists) {
        continue;
      }

      for (var child in productSnapshot.children) {
        var data = jsonDecode(jsonEncode(child.value));
        int currentStock = int.tryParse(data['productStock'].toString()) ?? 0;

        // حساب المخزون المتوقع
        int expectedStock =
            await calculateExpectedStock(product.productId.toString());

        // التحقق من اتساق المخزون
        if (currentStock != expectedStock) {
          return false;
        }
      }
    }

    return true;
  } catch (e) {
    // خطأ في التحقق من اتساق المخزون
    return false;
  }
}

/// حساب المخزون المتوقع للمنتج
Future<int> calculateExpectedStock(String productCode) async {
  try {
    // الحصول على المخزون الأولي
    int initialStock = 0;

    // حساب المشتريات
    final purchaseRef =
        FirebaseDatabase.instance.ref('$constUserId/Purchase Transition');
    final purchaseSnapshot = await purchaseRef.get();

    if (purchaseSnapshot.exists) {
      for (var child in purchaseSnapshot.children) {
        var data = jsonDecode(jsonEncode(child.value));

        if (data['productList'] != null) {
          for (var product in data['productList']) {
            if (product['productCode'] == productCode) {
              initialStock += int.tryParse(product['quantity'].toString()) ?? 0;
            }
          }
        }
      }
    }

    // حساب المبيعات
    final salesRef =
        FirebaseDatabase.instance.ref('$constUserId/Sales Transition');
    final salesSnapshot = await salesRef.get();

    if (salesSnapshot.exists) {
      for (var child in salesSnapshot.children) {
        var data = jsonDecode(jsonEncode(child.value));

        if (data['productList'] != null) {
          for (var product in data['productList']) {
            if (product['productCode'] == productCode) {
              initialStock -= int.tryParse(product['quantity'].toString()) ?? 0;
            }
          }
        }
      }
    }

    return initialStock;
  } catch (e) {
    // خطأ في حساب المخزون المتوقع
    return 0;
  }
}

/// التحقق من اتساق بيانات المخزون
Future<Map<String, dynamic>> verifyInventoryData(ProductModel product) async {
  try {
    // حساب المخزون المتوقع
    int expectedStock = await calculateExpectedStock(product.productCode);
    int currentStock = int.tryParse(product.productStock) ?? 0;

    // التحقق من اتساق المخزون
    bool isStockConsistent = currentStock == expectedStock;

    return {
      'isConsistent': isStockConsistent,
      'stockConsistency': {
        'isConsistent': isStockConsistent,
        'expectedStock': expectedStock,
        'currentStock': currentStock,
      },
    };
  } catch (e) {
    // خطأ في التحقق من اتساق بيانات المخزون
    return {
      'isConsistent': false,
      'error': e.toString(),
    };
  }
}

/// تصحيح بيانات المخزون
Future<bool> fixInventoryData(ProductModel product) async {
  try {
    // حساب المخزون المتوقع
    int expectedStock = await calculateExpectedStock(product.productCode);

    // تحديث المخزون
    final productRef = FirebaseDatabase.instance.ref('$constUserId/Products');
    final productSnapshot = await productRef
        .orderByChild('productCode')
        .equalTo(product.productCode)
        .get();

    if (!productSnapshot.exists) {
      return false;
    }

    for (var child in productSnapshot.children) {
      await productRef.child(child.key!).update({
        'productStock': expectedStock.toString(),
      });
    }

    return true;
  } catch (e) {
    // خطأ في تصحيح بيانات المخزون
    return false;
  }
}

/// تصحيح بيانات المبيعات
Future<bool> fixSalesData(SalesTransitionModel sale) async {
  try {
    // حساب المبلغ الإجمالي
    double calculatedTotal = 0;

    if (sale.productList != null) {
      for (var product in sale.productList!) {
        double price = double.tryParse(product.subTotal) ?? 0;
        calculatedTotal += price;
      }
    }

    // تحديث المبلغ الإجمالي
    final salesRef =
        FirebaseDatabase.instance.ref('$constUserId/Sales Transition');
    final salesSnapshot = await salesRef
        .orderByChild('invoiceNumber')
        .equalTo(sale.invoiceNumber)
        .get();

    if (!salesSnapshot.exists) {
      return false;
    }

    for (var child in salesSnapshot.children) {
      await salesRef.child(child.key!).update({
        'totalAmount': calculatedTotal,
      });
    }

    return true;
  } catch (e) {
    // خطأ في تصحيح بيانات المبيعات
    return false;
  }
}

/// حساب مؤشر دقة البيانات
double calculateDataAccuracy(int totalItems, int inconsistentItems) {
  if (totalItems == 0) {
    return 100.0;
  }

  return ((totalItems - inconsistentItems) / totalItems) * 100;
}
