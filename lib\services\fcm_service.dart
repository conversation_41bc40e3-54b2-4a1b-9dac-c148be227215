import 'package:flutter/foundation.dart';
import 'package:firebase_messaging/firebase_messaging.dart';

/// خدمة إدارة Firebase Cloud Messaging (FCM) باستخدام API v1
class FCMService {
  static final FCMService _instance = FCMService._internal();
  factory FCMService() => _instance;
  FCMService._internal();

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;

  /// تهيئة خدمة الإشعارات
  Future<void> initialize() async {
    // طلب أذونات الإشعارات
    await _requestPermissions();

    // تهيئة إشعارات Firebase
    await _initializeFirebaseMessaging();
  }

  /// طلب أذونات الإشعارات
  Future<void> _requestPermissions() async {
    await _firebaseMessaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
    );
  }

  /// تهيئة إشعارات Firebase
  Future<void> _initializeFirebaseMessaging() async {
    // الاستماع للإشعارات عندما يكون التطبيق في الخلفية
    FirebaseMessaging.onMessageOpenedApp.listen(_handleMessageOpenedApp);

    // الاستماع للإشعارات عندما يكون التطبيق مفتوحًا
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // التعامل مع الإشعارات التي تم النقر عليها عندما كان التطبيق مغلقًا
    final RemoteMessage? initialMessage =
        await _firebaseMessaging.getInitialMessage();
    if (initialMessage != null) {
      _handleMessageOpenedApp(initialMessage);
    }
  }

  /// التعامل مع الإشعارات في الواجهة الأمامية
  /// هذه الدالة تعمل عندما يكون التطبيق مفتوحًا ويصل إشعار جديد
  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    debugPrint(
        'تم استلام إشعار في الواجهة الأمامية: ${message.notification?.title}');
    debugPrint('محتوى الإشعار: ${message.notification?.body}');
    debugPrint('بيانات الإشعار: ${message.data}');

    // لعرض الإشعارات المحلية عندما يكون التطبيق في الواجهة الأمامية
    // يجب إعادة تفعيل مكتبة flutter_local_notifications وإضافة الكود المناسب

    // ملاحظة: في الوضع الحالي، لن تظهر الإشعارات عندما يكون التطبيق مفتوحًا
    // لكن ستظهر عندما يكون التطبيق في الخلفية أو مغلقًا
  }

  /// التعامل مع النقر على الإشعار لفتح التطبيق
  /// هذه الدالة تعمل عندما ينقر المستخدم على إشعار والتطبيق في الخلفية أو مغلق
  void _handleMessageOpenedApp(RemoteMessage message) {
    debugPrint('تم النقر على إشعار: ${message.notification?.title}');
    debugPrint('محتوى الإشعار: ${message.notification?.body}');
    debugPrint('بيانات الإشعار: ${message.data}');

    // هنا يمكن إضافة منطق للتنقل إلى شاشة معينة بناءً على بيانات الإشعار
    final data = message.data;
    if (data.containsKey('screen')) {
      // التنقل إلى الشاشة المحددة
      // مثال: navigatorKey.currentState?.pushNamed(data['screen']);

      // مثال: إذا كان الإشعار لرسالة دردشة، يمكن فتح شاشة الدردشة مع المرسل
      // if (data['screen'] == 'Chat' && data.containsKey('senderId')) {
      //   navigatorKey.currentState?.pushNamed('/Chat', arguments: data['senderId']);
      // }
    }
  }

  /// الحصول على رمز الجهاز
  Future<String?> getDeviceToken() async {
    return await _firebaseMessaging.getToken();
  }

  /// تخزين رمز الجهاز للمستخدم
  Future<void> saveUserDeviceToken(String userId, String token) async {
    // يمكن تنفيذ هذه الوظيفة حسب احتياجات التطبيق
    // مثال: تخزين الرمز في قاعدة البيانات
    debugPrint('تم تخزين رمز الجهاز للمستخدم: $userId');
  }

  /// إرسال إشعار باستخدام FCM API v1 (يتطلب خادم خارجي)
  /// ملاحظة: هذه الوظيفة لا تعمل مباشرة من التطبيق، بل تحتاج إلى خادم خارجي
  /// يستخدم Firebase Admin SDK للحصول على Access Token وإرسال الإشعارات
  Future<void> sendNotification({
    required String token,
    required String title,
    required String body,
    Map<String, dynamic>? data,
  }) async {
    debugPrint('''
لا يمكن إرسال إشعارات مباشرة من التطبيق باستخدام FCM API v1.
يجب استخدام خادم خارجي مع Firebase Admin SDK.

معلومات الإشعار:
- رمز الجهاز: $token
- العنوان: $title
- المحتوى: $body
- البيانات: $data
''');
  }

  /// اختبار إرسال إشعار للجهاز الحالي (للتوضيح فقط)
  Future<bool> sendTestNotification() async {
    try {
      final token = await getDeviceToken();
      if (token == null) {
        debugPrint('لم يتم العثور على رمز الجهاز');
        return false;
      }

      debugPrint('''
تم الحصول على رمز الجهاز: $token

لإرسال إشعار اختبار، يجب استخدام خادم خارجي مع Firebase Admin SDK.
راجع الوثائق: https://firebase.google.com/docs/cloud-messaging/migrate-v1
''');

      return true;
    } catch (e) {
      debugPrint('خطأ في اختبار الإشعارات: $e');
      return false;
    }
  }

  /// الاشتراك في موضوع
  Future<void> subscribeToTopic(String topic) async {
    await _firebaseMessaging.subscribeToTopic(topic);
    debugPrint('تم الاشتراك في الموضوع: $topic');
  }

  /// إلغاء الاشتراك من موضوع
  Future<void> unsubscribeFromTopic(String topic) async {
    await _firebaseMessaging.unsubscribeFromTopic(topic);
    debugPrint('تم إلغاء الاشتراك من الموضوع: $topic');
  }
}
