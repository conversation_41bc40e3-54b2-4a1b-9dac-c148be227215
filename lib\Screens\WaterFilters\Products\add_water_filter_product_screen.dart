import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/model/water_filter_models.dart';
import 'package:mobile_pos/services/water_filter_product_service.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

class AddWaterFilterProductScreen extends StatefulWidget {
  const AddWaterFilterProductScreen({super.key});

  @override
  State<AddWaterFilterProductScreen> createState() =>
      _AddWaterFilterProductScreenState();
}

class _AddWaterFilterProductScreenState
    extends State<AddWaterFilterProductScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _brandController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _stockController = TextEditingController();
  final _maintenanceIntervalController = TextEditingController();
  final _maintenanceCostController = TextEditingController();
  final _installationCostController = TextEditingController();
  final _specificationsController = TextEditingController();

  WaterFilterCategory _selectedCategory = WaterFilterCategory.residential;
  bool _isInstallationRequired = false;
  File? _selectedImage;
  bool _isLoading = false;

  @override
  void dispose() {
    _nameController.dispose();
    _brandController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _stockController.dispose();
    _maintenanceIntervalController.dispose();
    _maintenanceCostController.dispose();
    _installationCostController.dispose();
    _specificationsController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 80,
      );

      if (image != null) {
        setState(() {
          _selectedImage = File(image.path);
        });
      }
    } catch (e) {
      EasyLoading.showError('خطأ في اختيار الصورة: $e');
    }
  }

  Future<void> _saveProduct() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() => _isLoading = true);
    EasyLoading.show(status: 'جاري حفظ المنتج...', dismissOnTap: false);

    try {
      // رفع الصورة إذا تم اختيارها
      String? imageUrl;
      if (_selectedImage != null) {
        imageUrl =
            await WaterFilterProductService.uploadProductImage(_selectedImage!);
        if (imageUrl == null) {
          EasyLoading.showError('فشل في رفع الصورة');
          setState(() => _isLoading = false);
          return;
        }
      }

      // تحضير قائمة المواصفات
      final specifications = _specificationsController.text
          .split('\n')
          .where((spec) => spec.trim().isNotEmpty)
          .map((spec) => spec.trim())
          .toList();

      // إنشاء المنتج
      final product = WaterFilterProduct(
        id: '', // سيتم إنشاؤه تلقائياً
        name: _nameController.text.trim(),
        brand: _brandController.text.trim(),
        category: _selectedCategory,
        price: double.parse(_priceController.text),
        stock: int.parse(_stockController.text),
        description: _descriptionController.text.trim(),
        specifications: specifications,
        maintenanceIntervalMonths:
            int.parse(_maintenanceIntervalController.text),
        maintenanceCost: double.parse(_maintenanceCostController.text),
        isInstallationRequired: _isInstallationRequired,
        installationCost: double.parse(_installationCostController.text),
        imageUrl: imageUrl,
      );

      // حفظ المنتج
      final success = await WaterFilterProductService.addProduct(product);

      EasyLoading.dismiss();
      setState(() => _isLoading = false);

      if (success) {
        EasyLoading.showSuccess('تم إضافة المنتج بنجاح');
        if (mounted) {
          Navigator.pop(context, true);
        }
      } else {
        EasyLoading.showError('فشل في إضافة المنتج');
      }
    } catch (e) {
      EasyLoading.dismiss();
      setState(() => _isLoading = false);
      EasyLoading.showError('خطأ في حفظ المنتج: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        elevation: 0,
        title: Text(
          'إضافة منتج جديد',
          style: GoogleFonts.cairo(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        child: Form(
          key: _formKey,
          child: ListView(
            padding: const EdgeInsets.all(20),
            children: [
              // صورة المنتج
              _buildImageSection(),
              const SizedBox(height: 20),

              // المعلومات الأساسية
              _buildBasicInfoSection(),
              const SizedBox(height: 20),

              // معلومات السعر والمخزون
              _buildPriceStockSection(),
              const SizedBox(height: 20),

              // معلومات الصيانة
              _buildMaintenanceSection(),
              const SizedBox(height: 20),

              // المواصفات
              _buildSpecificationsSection(),
              const SizedBox(height: 30),

              // زر الحفظ
              _buildSaveButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'صورة المنتج',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: kMainColor,
          ),
        ),
        const SizedBox(height: 10),
        GestureDetector(
          onTap: _pickImage,
          child: Container(
            height: 150,
            width: double.infinity,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300, width: 2),
              borderRadius: BorderRadius.circular(15),
              color: Colors.grey.shade50,
            ),
            child: _selectedImage != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(15),
                    child: Image.file(
                      _selectedImage!,
                      fit: BoxFit.cover,
                    ),
                  )
                : Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.add_photo_alternate,
                        size: 50,
                        color: Colors.grey.shade400,
                      ),
                      const SizedBox(height: 10),
                      Text(
                        'اضغط لإضافة صورة',
                        style: GoogleFonts.cairo(
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
          ),
        ),
      ],
    );
  }

  Widget _buildBasicInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المعلومات الأساسية',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: kMainColor,
          ),
        ),
        const SizedBox(height: 15),

        // اسم المنتج
        TextFormField(
          controller: _nameController,
          decoration: InputDecoration(
            labelText: 'اسم المنتج *',
            labelStyle: GoogleFonts.cairo(),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
              borderSide: const BorderSide(color: kMainColor),
            ),
          ),
          style: GoogleFonts.cairo(),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'اسم المنتج مطلوب';
            }
            return null;
          },
        ),
        const SizedBox(height: 15),

        // العلامة التجارية
        TextFormField(
          controller: _brandController,
          decoration: InputDecoration(
            labelText: 'العلامة التجارية *',
            labelStyle: GoogleFonts.cairo(),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
              borderSide: const BorderSide(color: kMainColor),
            ),
          ),
          style: GoogleFonts.cairo(),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'العلامة التجارية مطلوبة';
            }
            return null;
          },
        ),
        const SizedBox(height: 15),

        // الفئة
        DropdownButtonFormField<WaterFilterCategory>(
          value: _selectedCategory,
          onChanged: (value) {
            if (value != null) {
              setState(() => _selectedCategory = value);
            }
          },
          decoration: InputDecoration(
            labelText: 'فئة المنتج *',
            labelStyle: GoogleFonts.cairo(),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
              borderSide: const BorderSide(color: kMainColor),
            ),
          ),
          items: WaterFilterCategory.values
              .map(
                (category) => DropdownMenuItem<WaterFilterCategory>(
                  value: category,
                  child: Text(category.arabicName, style: GoogleFonts.cairo()),
                ),
              )
              .toList(),
        ),
        const SizedBox(height: 15),

        // الوصف
        TextFormField(
          controller: _descriptionController,
          maxLines: 3,
          decoration: InputDecoration(
            labelText: 'وصف المنتج',
            labelStyle: GoogleFonts.cairo(),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
              borderSide: const BorderSide(color: kMainColor),
            ),
          ),
          style: GoogleFonts.cairo(),
        ),
      ],
    );
  }

  Widget _buildPriceStockSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'السعر والمخزون',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: kMainColor,
          ),
        ),
        const SizedBox(height: 15),
        Row(
          children: [
            // السعر
            Expanded(
              child: TextFormField(
                controller: _priceController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'السعر (ج.م) *',
                  labelStyle: GoogleFonts.cairo(),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(15),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(15),
                    borderSide: const BorderSide(color: kMainColor),
                  ),
                ),
                style: GoogleFonts.cairo(),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'السعر مطلوب';
                  }
                  if (double.tryParse(value) == null ||
                      double.parse(value) <= 0) {
                    return 'السعر غير صحيح';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 15),

            // المخزون
            Expanded(
              child: TextFormField(
                controller: _stockController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'المخزون *',
                  labelStyle: GoogleFonts.cairo(),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(15),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(15),
                    borderSide: const BorderSide(color: kMainColor),
                  ),
                ),
                style: GoogleFonts.cairo(),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'المخزون مطلوب';
                  }
                  if (int.tryParse(value) == null || int.parse(value) < 0) {
                    return 'المخزون غير صحيح';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMaintenanceSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'معلومات الصيانة والتركيب',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: kMainColor,
          ),
        ),
        const SizedBox(height: 15),

        Row(
          children: [
            // فترة الصيانة
            Expanded(
              child: TextFormField(
                controller: _maintenanceIntervalController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'فترة الصيانة (شهر) *',
                  labelStyle: GoogleFonts.cairo(),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(15),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(15),
                    borderSide: const BorderSide(color: kMainColor),
                  ),
                ),
                style: GoogleFonts.cairo(),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'فترة الصيانة مطلوبة';
                  }
                  if (int.tryParse(value) == null || int.parse(value) <= 0) {
                    return 'فترة الصيانة غير صحيحة';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 15),

            // تكلفة الصيانة
            Expanded(
              child: TextFormField(
                controller: _maintenanceCostController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'تكلفة الصيانة (ج.م) *',
                  labelStyle: GoogleFonts.cairo(),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(15),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(15),
                    borderSide: const BorderSide(color: kMainColor),
                  ),
                ),
                style: GoogleFonts.cairo(),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'تكلفة الصيانة مطلوبة';
                  }
                  if (double.tryParse(value) == null ||
                      double.parse(value) < 0) {
                    return 'تكلفة الصيانة غير صحيحة';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 15),

        // يحتاج تركيب
        CheckboxListTile(
          title: Text(
            'يحتاج تركيب',
            style: GoogleFonts.cairo(),
          ),
          value: _isInstallationRequired,
          onChanged: (value) {
            setState(() => _isInstallationRequired = value ?? false);
          },
          activeColor: kMainColor,
        ),

        // تكلفة التركيب
        if (_isInstallationRequired)
          TextFormField(
            controller: _installationCostController,
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              labelText: 'تكلفة التركيب (ج.م) *',
              labelStyle: GoogleFonts.cairo(),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(15),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(15),
                borderSide: const BorderSide(color: kMainColor),
              ),
            ),
            style: GoogleFonts.cairo(),
            validator: (value) {
              if (_isInstallationRequired) {
                if (value == null || value.trim().isEmpty) {
                  return 'تكلفة التركيب مطلوبة';
                }
                if (double.tryParse(value) == null || double.parse(value) < 0) {
                  return 'تكلفة التركيب غير صحيحة';
                }
              }
              return null;
            },
          ),
      ],
    );
  }

  Widget _buildSpecificationsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المواصفات',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: kMainColor,
          ),
        ),
        const SizedBox(height: 15),
        TextFormField(
          controller: _specificationsController,
          maxLines: 5,
          decoration: InputDecoration(
            labelText: 'المواصفات (كل مواصفة في سطر منفصل)',
            labelStyle: GoogleFonts.cairo(),
            hintText:
                'مثال:\n- عدد المراحل: 7\n- معدل التدفق: 2 لتر/دقيقة\n- ضغط التشغيل: 1-6 بار',
            hintStyle: GoogleFonts.cairo(color: Colors.grey.shade500),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
              borderSide: const BorderSide(color: kMainColor),
            ),
          ),
          style: GoogleFonts.cairo(),
        ),
      ],
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _saveProduct,
        style: ElevatedButton.styleFrom(
          backgroundColor: kMainColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
        ),
        child: _isLoading
            ? const CircularProgressIndicator(color: Colors.white)
            : Text(
                'حفظ المنتج',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
      ),
    );
  }
}
