import 'dart:async';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/foundation.dart';

/// مدير مستمعي Firebase - يمنع الاستماع المزدوج ويدير دورة حياة المستمعين
class FirebaseListenerManager {
  // نمط Singleton للتأكد من وجود نسخة واحدة فقط
  static final FirebaseListenerManager _instance = FirebaseListenerManager._internal();
  factory FirebaseListenerManager() => _instance;
  FirebaseListenerManager._internal();

  // قاموس لتخزين المستمعين النشطين مع معرفات فريدة
  // المفتاح هو مسار قاعدة البيانات + معرف المالك (الشاشة أو الكائن)
  final Map<String, _ListenerInfo> _activeListeners = {};

  // قاموس لتخزين المستمعين حسب المالك
  // يسمح بإلغاء جميع المستمعين لمالك معين (مثل شاشة)
  final Map<String, Set<String>> _listenersByOwner = {};

  // حالة التهيئة
  bool _isInitialized = false;

  /// تهيئة مدير المستمعين
  void initialize() {
    if (!_isInitialized) {
      // debugPrint('تهيئة مدير مستمعي Firebase...');
      _isInitialized = true;
    }
  }

  /// إنشاء معرف فريد للمستمع
  String _createListenerId(String path, String? ownerId, String? eventType, String? queryType) {
    final baseId = '${path}_${ownerId ?? 'global'}';
    if (eventType != null) {
      return '${baseId}_$eventType${queryType != null ? '_$queryType' : ''}';
    }
    return baseId;
  }

  /// الاستماع للتغييرات في مسار معين مع منع الاستماع المزدوج
  Stream<DatabaseEvent> listenToPath(
    String path, {
    String? ownerId,
    String? eventType,
    String? queryType,
    Query? query,
    bool keepSynced = false,
  }) {
    // التأكد من تهيئة المدير
    if (!_isInitialized) {
      initialize();
    }

    // تنظيف المسار
    final cleanPath = path.trim();

    // إنشاء معرف فريد للمستمع
    final listenerId = _createListenerId(cleanPath, ownerId, eventType, queryType);

    // التحقق من وجود مستمع نشط بنفس المعرف
    if (_activeListeners.containsKey(listenerId)) {
      debugPrint('استخدام مستمع موجود للمسار: $cleanPath (المالك: ${ownerId ?? 'global'})');

      // إرجاع المجرى الموجود
      return _activeListeners[listenerId]!.stream;
    }

    // إنشاء مرجع لقاعدة البيانات
    final DatabaseReference reference = FirebaseDatabase.instance.ref(cleanPath);

    // تعيين المزامنة إذا كان مطلوبًا
    if (keepSynced) {
      try {
        reference.keepSynced(true);
      } catch (e) {
        debugPrint('خطأ في تعيين المزامنة للمسار $cleanPath: $e');
      }
    }

    // إنشاء مجرى للبيانات
    final controller = StreamController<DatabaseEvent>.broadcast(
      onCancel: () {
        _removeListener(listenerId, ownerId);
      },
    );

    // تحديد المرجع أو الاستعلام المناسب
    final dbRef = query ?? reference;

    // تحديد نوع الحدث المطلوب الاستماع له
    Stream<DatabaseEvent> eventStream;

    if (eventType == 'child_added') {
      eventStream = dbRef.onChildAdded;
    } else if (eventType == 'child_changed') {
      eventStream = dbRef.onChildChanged;
    } else if (eventType == 'child_removed') {
      eventStream = dbRef.onChildRemoved;
    } else if (eventType == 'child_moved') {
      eventStream = dbRef.onChildMoved;
    } else {
      // الافتراضي هو onValue
      eventStream = dbRef.onValue;
    }

    // إنشاء اشتراك في الأحداث
    final subscription = eventStream.listen(
      (event) {
        // إرسال الحدث إلى المجرى إذا كان لا يزال مفتوحًا
        if (!controller.isClosed) {
          controller.add(event);
        }
      },
      onError: (error) {
        debugPrint('خطأ في الاستماع للمسار $cleanPath: $error');
        if (!controller.isClosed) {
          controller.addError(error);
        }
      },
      onDone: () {
        debugPrint('انتهى الاستماع للمسار: $cleanPath');
        if (!controller.isClosed) {
          controller.close();
        }
        _removeListener(listenerId, ownerId);
      },
    );

    // تخزين معلومات المستمع
    final listenerInfo = _ListenerInfo(
      path: cleanPath,
      ownerId: ownerId,
      subscription: subscription,
      stream: controller.stream,
      keepSynced: keepSynced,
    );

    _activeListeners[listenerId] = listenerInfo;

    // إضافة المستمع إلى قائمة المستمعين للمالك
    if (ownerId != null) {
      _listenersByOwner.putIfAbsent(ownerId, () => {}).add(listenerId);
    }

    debugPrint('تم إنشاء مستمع جديد للمسار: $cleanPath (المالك: ${ownerId ?? 'global'})');

    return controller.stream;
  }

  /// إلغاء مستمع معين
  Future<void> cancelListener(String path, {String? ownerId, String? eventType, String? queryType}) async {
    final listenerId = _createListenerId(path, ownerId, eventType, queryType);

    await _removeListener(listenerId, ownerId);
  }

  /// إلغاء جميع المستمعين لمالك معين
  Future<void> cancelListenersByOwner(String ownerId) async {
    if (_listenersByOwner.containsKey(ownerId)) {
      debugPrint('إلغاء جميع المستمعين للمالك: $ownerId');

      final listenerIds = List<String>.from(_listenersByOwner[ownerId] ?? {});

      for (final listenerId in listenerIds) {
        await _removeListener(listenerId, ownerId);
      }

      _listenersByOwner.remove(ownerId);
    }
  }

  /// إلغاء جميع المستمعين
  Future<void> cancelAllListeners() async {
    debugPrint('إلغاء جميع المستمعين...');

    final listenerIds = List<String>.from(_activeListeners.keys);

    for (final listenerId in listenerIds) {
      final listenerInfo = _activeListeners[listenerId];
      await _removeListener(listenerId, listenerInfo?.ownerId);
    }

    _listenersByOwner.clear();
  }

  /// إزالة مستمع داخليًا
  Future<void> _removeListener(String listenerId, String? ownerId) async {
    if (_activeListeners.containsKey(listenerId)) {
      final listenerInfo = _activeListeners[listenerId]!;

      debugPrint('إلغاء المستمع للمسار: ${listenerInfo.path}');

      // إلغاء الاشتراك
      await listenerInfo.subscription.cancel();

      // إلغاء المزامنة إذا كانت مفعلة
      if (listenerInfo.keepSynced) {
        try {
          FirebaseDatabase.instance.ref(listenerInfo.path).keepSynced(false);
        } catch (e) {
          debugPrint('خطأ في إلغاء المزامنة للمسار ${listenerInfo.path}: $e');
        }
      }

      // إزالة المستمع من القاموس
      _activeListeners.remove(listenerId);

      // إزالة المستمع من قائمة المستمعين للمالك
      if (ownerId != null && _listenersByOwner.containsKey(ownerId)) {
        _listenersByOwner[ownerId]?.remove(listenerId);

        // إزالة المالك إذا لم يعد لديه مستمعين
        if (_listenersByOwner[ownerId]?.isEmpty ?? true) {
          _listenersByOwner.remove(ownerId);
        }
      }
    }
  }

  /// الحصول على عدد المستمعين النشطين
  int get activeListenersCount => _activeListeners.length;

  /// الحصول على قائمة بمسارات المستمعين النشطين
  List<String> get activeListenerPaths =>
      _activeListeners.values.map((info) => info.path).toList();
}

/// فئة لتخزين معلومات المستمع
class _ListenerInfo {
  final String path;
  final String? ownerId;
  final StreamSubscription<DatabaseEvent> subscription;
  final Stream<DatabaseEvent> stream;
  final bool keepSynced;

  _ListenerInfo({
    required this.path,
    this.ownerId,
    required this.subscription,
    required this.stream,
    this.keepSynced = false,
  });
}
