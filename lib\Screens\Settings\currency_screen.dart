// ignore_for_file: unused_import, library_private_types_in_public_api, avoid_print, use_build_context_synchronously, unused_result

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:firebase_database/firebase_database.dart';
import '../../Provider/profile_provider.dart';
import '../../constant.dart';
import '../../currency.dart';
import '../Home/home.dart';

class CurrencyScreen extends StatefulWidget {
  const CurrencyScreen({super.key});

  @override
  _CurrencyScreenState createState() => _CurrencyScreenState();
}

class _CurrencyScreenState extends State<CurrencyScreen> {
  String? selectedCountry = 'جنيه (جنية)';
  String currency = "جنيه";

  final List<String> countryList = [
    'جنيه (جنية)',
    'جنية (Egyptian Pound)',

    // Add more currencies as needed
  ];

  final Map<String, String> currencySymbols = {
    'جنيه (جنية)': 'جنيه',
    'جنية (Egyptian Pound)': 'جنية',
    // Add more currencies as needed
  };

  @override
  void initState() {
    super.initState();
    print('-----selected $selectedCountry-----');
    getCurrency();
  }

  Future<void> getCurrency() async {
    final prefs = await SharedPreferences.getInstance();
    String? data = prefs.getString('currency');
    print('-------------get currency $data----------');
    if (!data.isEmptyOrNull) {
      for (var element in countryList) {
        if (currencySymbols[element] == data) {
          setState(() {
            selectedCountry = element;
          });
          break;
        }
      }
    } else {
      setState(() {
        selectedCountry = countryList[0];
      });
    }
  }

  Future<void> _updateCurrency(String newCurrency) async {
    final prefs = await SharedPreferences.getInstance();
    final DatabaseReference personalInformationRef = FirebaseDatabase.instance
        .ref()
        .child(await getUserID())
        .child('Personal Information');
    personalInformationRef.keepSynced(true);
    if (currencySymbols.containsKey(newCurrency)) {
      currency = currencySymbols[newCurrency]!;
      personalInformationRef.update({'currency': currency});
      await prefs.setString('currency', currency);
    } else {
      currency = " جنية";
      await prefs.setString('currency', currency);
    }
    setState(() {
      currency;
      selectedCountry = newCurrency;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer(builder: (context, ref, __) {
      return Scaffold(
        backgroundColor: kMainColor,
        appBar: AppBar(
          iconTheme: const IconThemeData(color: kWhite),
          title: Text(
            'Currency',
            style: kTextStyle.copyWith(color: kWhite),
          ),
          backgroundColor: Colors.transparent,
        ),
        bottomNavigationBar: Container(
          height: 95,
          color: Colors.white,
          child: Padding(
            padding:
                const EdgeInsets.only(top: 25, left: 20, right: 20, bottom: 15),
            child: InkWell(
              onTap: () async {
                await _updateCurrency(selectedCountry ?? '');
                ref.refresh(profileDetailsProvider);
                // final prefs = await SharedPreferences.getInstance();
                // String? data = prefs.getString('currency');
                // if (!data.isEmptyOrNull) {
                //   currency = data!;
                // }
                // Navigator.pop(context);
                Navigator.pushReplacement(context,
                    MaterialPageRoute(builder: (context) => const Home()));
              },
              child: Container(
                alignment: Alignment.center,
                width: double.infinity,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10), color: kMainColor),
                child: const Text(
                  'Save',
                  style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold),
                ),
              ),
            ),
          ),
        ),
        body: Padding(
          padding: const EdgeInsets.only(top: 10),
          child: Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(30),
                topRight: Radius.circular(30),
              ),
            ),
            child: ListView.builder(
              itemCount: currencySymbols.length,
              itemBuilder: (_, index) {
                String country = currencySymbols.keys.elementAt(index);
                // String country = currencySymbols[index]??'';
                return Padding(
                  padding: const EdgeInsets.only(top: 20, left: 20, right: 20),
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: Colors.white,
                      border: Border.all(color: Colors.grey, width: 0.7),
                    ),
                    child: ListTile(
                      horizontalTitleGap: 5,
                      onTap: () async {
                        setState(() {
                          selectedCountry = country;
                        });

                        // await _updateCurrency(country);
                        // ref.refresh(profileDetailsProvider);

                        // ref.refresh(profileDetailsProvider);
                      },
                      title: Text(country),
                      trailing: selectedCountry == country
                          ? const Icon(Icons.radio_button_checked,
                              color: Colors.blue)
                          : const Icon(Icons.radio_button_off,
                              color: Color(0xff9F9F9F)),
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      );
    });
  }
}
