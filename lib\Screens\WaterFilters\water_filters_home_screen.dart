import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/model/water_filter_models.dart';
import 'package:mobile_pos/Screens/WaterFilters/Products/water_filter_products_screen.dart';
import 'package:mobile_pos/Screens/WaterFilters/Customers/water_filter_customers_screen.dart';
import 'package:mobile_pos/Screens/WaterFilters/Systems/water_filter_systems_screen.dart';
import 'package:mobile_pos/Screens/WaterFilters/Maintenance/maintenance_schedule_screen.dart';
import 'package:mobile_pos/Screens/WaterFilters/Installments/installments_tracking_screen.dart';
import 'package:mobile_pos/Screens/WaterFilters/Reports/water_filter_reports_screen.dart';
import 'package:mobile_pos/Screens/WaterFilters/Expenses/water_filter_expenses_screen.dart';
import 'package:mobile_pos/Screens/WaterFilters/dashboard_screen.dart';
import 'package:mobile_pos/services/water_filter_service.dart';
import 'package:mobile_pos/Screens/WaterFilters/test_data_helper.dart';

class WaterFiltersHomeScreen extends StatefulWidget {
  const WaterFiltersHomeScreen({super.key});

  @override
  State<WaterFiltersHomeScreen> createState() => _WaterFiltersHomeScreenState();
}

class _WaterFiltersHomeScreenState extends State<WaterFiltersHomeScreen> {
  Map<String, dynamic> _stats = {
    'activeSystems': 0,
    'maintenanceDue': 0,
    'overdueInstallments': 0,
  };
  bool _isLoadingStats = true;

  @override
  void initState() {
    super.initState();
    _loadStats();
  }

  Future<void> _loadStats() async {
    try {
      setState(() => _isLoadingStats = true);
      final stats = await WaterFilterService.getQuickStats();
      if (mounted) {
        setState(() {
          _stats = stats;
          _isLoadingStats = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoadingStats = false);
        debugPrint('خطأ في تحميل الإحصائيات: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        elevation: 0,
        title: Text(
          'فلاتر المياه',
          style: GoogleFonts.cairo(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert, color: Colors.white),
            onSelected: (value) {
              if (value == 'add_test_data') {
                _handleAddTestData();
              } else if (value == 'clear_test_data') {
                _handleClearTestData();
              }
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'add_test_data',
                child: Row(
                  children: [
                    const Icon(Icons.add_circle, color: Colors.green),
                    const SizedBox(width: 8),
                    Text(
                      'إضافة بيانات تجريبية',
                      style: GoogleFonts.cairo(),
                    ),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'clear_test_data',
                child: Row(
                  children: [
                    const Icon(Icons.delete, color: Colors.red),
                    const SizedBox(width: 8),
                    Text(
                      'حذف البيانات التجريبية',
                      style: GoogleFonts.cairo(),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            children: [
              // إحصائيات سريعة
              _buildQuickStats(),
              const SizedBox(height: 20),

              // شبكة الخدمات
              Expanded(
                child: GridView.count(
                  crossAxisCount: 2,
                  crossAxisSpacing: 15,
                  mainAxisSpacing: 15,
                  childAspectRatio: 1.1,
                  children: [
                    _buildServiceCard(
                      title: 'لوحة التحكم',
                      subtitle: 'نظرة شاملة على النظام',
                      icon: Icons.dashboard_outlined,
                      color: Colors.purple,
                      onTap: () {
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  const WaterFilterDashboardScreen(),
                            ));
                      },
                    ),
                    _buildServiceCard(
                      title: 'إدارة المنتجات',
                      subtitle: 'فلاتر وقطع غيار',
                      icon: Icons.inventory_2_outlined,
                      color: Colors.blue,
                      onTap: () {
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  const WaterFilterProductsScreen(),
                            ));
                      },
                    ),
                    _buildServiceCard(
                      title: 'إدارة العملاء',
                      subtitle: 'عملاء فلاتر المياه',
                      icon: Icons.people_outline,
                      color: Colors.green,
                      onTap: () {
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  const WaterFilterCustomersScreen(),
                            ));
                      },
                    ),
                    _buildServiceCard(
                      title: 'الأنظمة المركبة',
                      subtitle: 'فلاتر مركبة لدى العملاء',
                      icon: Icons.settings_outlined,
                      color: Colors.orange,
                      onTap: () {
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  const WaterFilterSystemsScreen(),
                            ));
                      },
                    ),
                    _buildServiceCard(
                      title: 'جدولة الصيانة',
                      subtitle: 'مواعيد الصيانة الدورية',
                      icon: Icons.schedule_outlined,
                      color: Colors.purple,
                      onTap: () {
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  const MaintenanceScheduleScreen(),
                            ));
                      },
                    ),
                    _buildServiceCard(
                      title: 'متابعة الأقساط',
                      subtitle: 'أقساط العملاء والمدفوعات',
                      icon: Icons.payment_outlined,
                      color: Colors.teal,
                      onTap: () {
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  const InstallmentsTrackingScreen(),
                            ));
                      },
                    ),
                    _buildServiceCard(
                      title: 'إدارة المصروفات',
                      subtitle: 'تسجيل ومتابعة المصروفات',
                      icon: Icons.receipt_long_outlined,
                      color: Colors.brown,
                      onTap: () {
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  const WaterFilterExpensesScreen(),
                            ));
                      },
                    ),
                    _buildServiceCard(
                      title: 'التقارير',
                      subtitle: 'تقارير المبيعات والصيانة',
                      icon: Icons.analytics_outlined,
                      color: Colors.indigo,
                      onTap: () {
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  const WaterFilterReportsScreen(),
                            ));
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickStats() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [kMainColor.withOpacity(0.1), kMainColor.withOpacity(0.05)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: kMainColor.withOpacity(0.2)),
      ),
      child: Column(
        children: [
          Text(
            'نظرة عامة',
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: kMainColor,
            ),
          ),
          const SizedBox(height: 15),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  title: 'أنظمة نشطة',
                  value: _isLoadingStats
                      ? '...'
                      : '${_stats['activeSystems'] ?? 0}',
                  icon: Icons.check_circle,
                  color: Colors.green,
                ),
              ),
              const SizedBox(width: 15),
              Expanded(
                child: _buildStatItem(
                  title: 'صيانة مستحقة',
                  value: _isLoadingStats
                      ? '...'
                      : '${_stats['maintenanceDue'] ?? 0}',
                  icon: Icons.warning,
                  color: Colors.orange,
                ),
              ),
              const SizedBox(width: 15),
              Expanded(
                child: _buildStatItem(
                  title: 'أقساط متأخرة',
                  value: _isLoadingStats
                      ? '...'
                      : '${_stats['overdueInstallments'] ?? 0}',
                  icon: Icons.error,
                  color: Colors.red,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildServiceCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(15),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 2,
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(15),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  size: 30,
                  color: color,
                ),
              ),
              const SizedBox(height: 15),
              Text(
                title,
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 5),
              Text(
                subtitle,
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _handleAddTestData() async {
    _showLoadingDialog('إضافة البيانات التجريبية...');
    try {
      await WaterFilterTestDataHelper.addTestData();
      if (mounted) {
        Navigator.pop(context); // إغلاق dialog
        _loadStats(); // تحديث الإحصائيات
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إضافة البيانات التجريبية بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إضافة البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _handleClearTestData() async {
    _showLoadingDialog('حذف البيانات التجريبية...');
    try {
      await WaterFilterTestDataHelper.clearTestData();
      if (mounted) {
        Navigator.pop(context); // إغلاق dialog
        _loadStats(); // تحديث الإحصائيات
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حذف البيانات التجريبية بنجاح'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حذف البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showLoadingDialog(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(width: 20),
            Expanded(
              child: Text(
                message,
                style: GoogleFonts.cairo(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
