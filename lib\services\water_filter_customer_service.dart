import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:mobile_pos/model/water_filter_models.dart';
import 'package:mobile_pos/services/water_filter_service.dart';
import 'package:mobile_pos/services/gofile_service.dart';
import 'dart:io';

/// خدمة إدارة عملاء فلاتر المياه
class WaterFilterCustomerService {
  // نمط Singleton
  static final WaterFilterCustomerService _instance = WaterFilterCustomerService._internal();
  factory WaterFilterCustomerService() => _instance;
  WaterFilterCustomerService._internal();

  /// إضافة عميل جديد
  static Future<bool> addCustomer(WaterFilterCustomer customer) async {
    try {
      debugPrint('➕ إضافة عميل فلتر مياه جديد: ${customer.name}');

      // التحقق من صحة البيانات
      if (!_validateCustomer(customer)) {
        debugPrint('❌ بيانات العميل غير صحيحة');
        return false;
      }

      // التحقق من عدم تكرار رقم الهاتف
      if (await _isPhoneNumberExists(customer.phone, customer.id)) {
        debugPrint('❌ رقم الهاتف موجود مسبقاً');
        return false;
      }

      // إنشاء معرف فريد إذا لم يكن موجوداً
      if (customer.id.isEmpty) {
        customer.id = WaterFilterService.generateId();
      }

      // إضافة الطوابع الزمنية
      customer.createdAt = DateTime.now();
      customer.updatedAt = DateTime.now();

      // حفظ العميل في قاعدة البيانات
      await WaterFilterService.customersRef
          .child(customer.id)
          .set(customer.toJson());

      debugPrint('✅ تم إضافة العميل بنجاح: ${customer.id}');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في إضافة العميل: $e');
      return false;
    }
  }

  /// تحديث عميل موجود
  static Future<bool> updateCustomer(WaterFilterCustomer customer) async {
    try {
      debugPrint('🔄 تحديث عميل فلتر المياه: ${customer.name}');

      // التحقق من وجود العميل
      final snapshot = await WaterFilterService.customersRef
          .child(customer.id)
          .get();

      if (!snapshot.exists) {
        debugPrint('❌ العميل غير موجود: ${customer.id}');
        return false;
      }

      // التحقق من صحة البيانات
      if (!_validateCustomer(customer)) {
        debugPrint('❌ بيانات العميل غير صحيحة');
        return false;
      }

      // التحقق من عدم تكرار رقم الهاتف (باستثناء العميل الحالي)
      if (await _isPhoneNumberExists(customer.phone, customer.id)) {
        debugPrint('❌ رقم الهاتف موجود مسبقاً');
        return false;
      }

      // تحديث الطابع الزمني
      customer.updatedAt = DateTime.now();

      // تحديث العميل في قاعدة البيانات
      await WaterFilterService.customersRef
          .child(customer.id)
          .update(customer.toJson());

      debugPrint('✅ تم تحديث العميل بنجاح: ${customer.id}');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في تحديث العميل: $e');
      return false;
    }
  }

  /// حذف عميل
  static Future<bool> deleteCustomer(String customerId) async {
    try {
      debugPrint('🗑️ حذف عميل فلتر المياه: $customerId');

      // التحقق من وجود العميل
      final snapshot = await WaterFilterService.customersRef
          .child(customerId)
          .get();

      if (!snapshot.exists) {
        debugPrint('❌ العميل غير موجود: $customerId');
        return false;
      }

      // التحقق من عدم وجود أنظمة مركبة للعميل
      final systemsSnapshot = await WaterFilterService.systemsRef
          .orderByChild('customerId')
          .equalTo(customerId)
          .get();

      if (systemsSnapshot.exists) {
        debugPrint('❌ لا يمكن حذف العميل - لديه أنظمة مركبة');
        return false;
      }

      // حذف العميل
      await WaterFilterService.customersRef.child(customerId).remove();

      debugPrint('✅ تم حذف العميل بنجاح: $customerId');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في حذف العميل: $e');
      return false;
    }
  }

  /// الحصول على عميل بالمعرف
  static Future<WaterFilterCustomer?> getCustomer(String customerId) async {
    try {
      debugPrint('🔍 جلب عميل فلتر المياه: $customerId');

      final snapshot = await WaterFilterService.customersRef
          .child(customerId)
          .get();

      if (!snapshot.exists) {
        debugPrint('❌ العميل غير موجود: $customerId');
        return null;
      }

      final data = Map<String, dynamic>.from(snapshot.value as Map);
      final customer = WaterFilterCustomer.fromJson(data);

      debugPrint('✅ تم جلب العميل بنجاح: ${customer.name}');
      return customer;
    } catch (e) {
      debugPrint('❌ خطأ في جلب العميل: $e');
      return null;
    }
  }

  /// الحصول على جميع العملاء
  static Future<List<WaterFilterCustomer>> getAllCustomers() async {
    try {
      debugPrint('📋 جلب جميع عملاء فلاتر المياه...');

      final snapshot = await WaterFilterService.customersRef.get();

      if (!snapshot.exists) {
        debugPrint('ℹ️ لا يوجد عملاء');
        return [];
      }

      final customers = <WaterFilterCustomer>[];

      for (final child in snapshot.children) {
        try {
          final data = Map<String, dynamic>.from(child.value as Map);
          final customer = WaterFilterCustomer.fromJson(data);
          customers.add(customer);
        } catch (e) {
          debugPrint('⚠️ خطأ في معالجة عميل: $e');
        }
      }

      // ترتيب العملاء حسب التاريخ (الأحدث أولاً)
      customers.sort((a, b) {
        if (a.createdAt == null || b.createdAt == null) return 0;
        return b.createdAt!.compareTo(a.createdAt!);
      });

      debugPrint('✅ تم جلب ${customers.length} عميل');
      return customers;
    } catch (e) {
      debugPrint('❌ خطأ في جلب العملاء: $e');
      return [];
    }
  }

  /// البحث في العملاء
  static Future<List<WaterFilterCustomer>> searchCustomers(String query) async {
    try {
      debugPrint('🔍 البحث في العملاء: $query');

      final allCustomers = await getAllCustomers();
      
      if (query.isEmpty) {
        return allCustomers;
      }

      final searchQuery = query.toLowerCase().trim();
      final filteredCustomers = allCustomers.where((customer) {
        return customer.name.toLowerCase().contains(searchQuery) ||
               customer.phone.contains(searchQuery) ||
               customer.address.toLowerCase().contains(searchQuery) ||
               customer.area.toLowerCase().contains(searchQuery) ||
               customer.city.toLowerCase().contains(searchQuery) ||
               customer.email.toLowerCase().contains(searchQuery);
      }).toList();

      debugPrint('✅ تم العثور على ${filteredCustomers.length} عميل');
      return filteredCustomers;
    } catch (e) {
      debugPrint('❌ خطأ في البحث: $e');
      return [];
    }
  }

  /// الحصول على العملاء حسب المنطقة
  static Future<List<WaterFilterCustomer>> getCustomersByArea(String area) async {
    try {
      debugPrint('📍 جلب العملاء حسب المنطقة: $area');

      final allCustomers = await getAllCustomers();
      final areaCustomers = allCustomers
          .where((customer) => customer.area.toLowerCase() == area.toLowerCase())
          .toList();

      debugPrint('✅ تم العثور على ${areaCustomers.length} عميل في المنطقة');
      return areaCustomers;
    } catch (e) {
      debugPrint('❌ خطأ في جلب العملاء حسب المنطقة: $e');
      return [];
    }
  }

  /// الحصول على العملاء حسب المدينة
  static Future<List<WaterFilterCustomer>> getCustomersByCity(String city) async {
    try {
      debugPrint('🏙️ جلب العملاء حسب المدينة: $city');

      final allCustomers = await getAllCustomers();
      final cityCustomers = allCustomers
          .where((customer) => customer.city.toLowerCase() == city.toLowerCase())
          .toList();

      debugPrint('✅ تم العثور على ${cityCustomers.length} عميل في المدينة');
      return cityCustomers;
    } catch (e) {
      debugPrint('❌ خطأ في جلب العملاء حسب المدينة: $e');
      return [];
    }
  }

  /// الاستماع للتغييرات في العملاء
  static Stream<DatabaseEvent> listenToCustomers({String? ownerId}) {
    return WaterFilterService.listenToPath(
      'Customers',
      ownerId: ownerId ?? 'water_filter_customers',
    );
  }

  /// إلغاء الاستماع للعملاء
  static void cancelCustomersListener({String? ownerId}) {
    WaterFilterService.cancelListener(
      'Customers',
      ownerId: ownerId ?? 'water_filter_customers',
    );
  }

  /// التحقق من صحة بيانات العميل
  static bool _validateCustomer(WaterFilterCustomer customer) {
    if (customer.name.trim().isEmpty) {
      debugPrint('❌ اسم العميل مطلوب');
      return false;
    }

    if (customer.phone.trim().isEmpty) {
      debugPrint('❌ رقم الهاتف مطلوب');
      return false;
    }

    // التحقق من صحة رقم الهاتف (يجب أن يحتوي على أرقام فقط)
    if (!RegExp(r'^[0-9+\-\s()]+$').hasMatch(customer.phone)) {
      debugPrint('❌ رقم الهاتف غير صحيح');
      return false;
    }

    if (customer.address.trim().isEmpty) {
      debugPrint('❌ العنوان مطلوب');
      return false;
    }

    if (customer.area.trim().isEmpty) {
      debugPrint('❌ المنطقة مطلوبة');
      return false;
    }

    if (customer.city.trim().isEmpty) {
      debugPrint('❌ المدينة مطلوبة');
      return false;
    }

    // التحقق من صحة البريد الإلكتروني إذا تم إدخاله
    if (customer.email.isNotEmpty && 
        !RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(customer.email)) {
      debugPrint('❌ البريد الإلكتروني غير صحيح');
      return false;
    }

    return true;
  }

  /// التحقق من وجود رقم الهاتف
  static Future<bool> _isPhoneNumberExists(String phone, String excludeId) async {
    try {
      final snapshot = await WaterFilterService.customersRef
          .orderByChild('phone')
          .equalTo(phone)
          .get();

      if (!snapshot.exists) {
        return false;
      }

      // التحقق من أن رقم الهاتف لا ينتمي للعميل المستثنى
      for (final child in snapshot.children) {
        if (child.key != excludeId) {
          return true;
        }
      }

      return false;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من رقم الهاتف: $e');
      return false;
    }
  }
}
