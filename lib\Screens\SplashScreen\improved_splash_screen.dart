// // ignore_for_file: use_build_context_synchronously, unused_import, library_private_types_in_public_api, unused_field, prefer_const_constructors, prefer_const_literals_to_create_immutables, deprecated_member_use

// import 'package:firebase_auth/firebase_auth.dart';
// import 'package:flutter/material.dart';
// import 'package:google_fonts/google_fonts.dart';
// import 'package:internet_connection_checker/internet_connection_checker.dart';
// import 'package:mobile_pos/GlobalComponents/loading_indicator.dart';
// import 'package:mobile_pos/Screens/SplashScreen/on_board.dart';
// import 'package:mobile_pos/constant.dart';
// import 'package:nb_utils/nb_utils.dart';
// import 'package:permission_handler/permission_handler.dart';
// import 'package:provider/provider.dart';

// import '../../Language/language_provider.dart';
// import '../../currency.dart';
// import '../../services/user_state_service.dart';
// import '../Home/home.dart';

// class ImprovedSplashScreen extends StatefulWidget {
//   const ImprovedSplashScreen({super.key});

//   @override
//   _ImprovedSplashScreenState createState() => _ImprovedSplashScreenState();
// }

// class _ImprovedSplashScreenState extends State<ImprovedSplashScreen> {
//   final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey();
//   final UserStateService _userStateService = UserStateService();
  
//   // State variables
//   // ignore: prefer_final_fields
//   bool _isLoading = true;
//   bool _hasInternetConnection = true;
//   String _loadingMessage = "جاري التحقق من حالة المستخدم...";

//   @override
//   void initState() {
//     super.initState();
//     _initializeApp();
//   }

//   /// Initialize the app by checking permissions, language, and user state
//   Future<void> _initializeApp() async {
//     try {
//       // Request necessary permissions
//       await _requestPermissions();
      
//       // Set language preference
//       await _setLanguagePreference();
      
//       // Check internet connection
//       _hasInternetConnection = await InternetConnectionChecker().hasConnection;
//       if (!_hasInternetConnection) {
//         setState(() {
//           _loadingMessage = "لا يوجد اتصال بالإنترنت";
//         });
//       }
      
//       // Delay for splash screen visibility
//       await Future.delayed(const Duration(seconds: 2));
      
//       // Check user authentication state
//       await _checkUserAuthState();
//     } catch (e) {
//       debugPrint('Error during app initialization: $e');
//       _showSnackBar('حدث خطأ أثناء تهيئة التطبيق');
      
//       // Navigate to onboarding as fallback
//       await Future.delayed(const Duration(seconds: 1));
//       _navigateToOnboarding();
//     }
//   }

//   /// Request necessary app permissions
//   Future<void> _requestPermissions() async {
//     setState(() {
//       _loadingMessage = "جاري طلب الأذونات اللازمة...";
//     });
    
//     await [  
//       Permission.bluetoothScan,
//       Permission.bluetoothConnect,
//       Permission.notification,
//       Permission.storage
//     ].request();
//   }

//   /// Set language preference based on saved settings
//   Future<void> _setLanguagePreference() async {
//     setState(() {
//       _loadingMessage = "جاري تحميل إعدادات اللغة...";
//     });
    
//     final prefs = await SharedPreferences.getInstance();
//     String selectedLanguage = prefs.getString('savedLanguage') ?? 'Arabic';
    
//     if (mounted) {
//       selectedLanguage == 'Arabic'
//           ? context.read<LanguageChangeProvider>().changeLocale("ar")
//           : context.read<LanguageChangeProvider>().changeLocale("en");
//     }
    
//     // Set RTL direction
//     isRtl = prefs.getBool('isRtl') ?? false;
    
//     // Set print enable preference
//     isPrintEnable = prefs.getBool('isPrintEnable') ?? false;
//   }

//   /// Check if user is authenticated and profile exists
//   Future<void> _checkUserAuthState() async {
//     setState(() {
//       _loadingMessage = "جاري التحقق من حالة المستخدم...";
//     });
    
//     try {
//       bool isLoggedIn = await _userStateService.isUserLoggedIn();
//       if (!isLoggedIn) {
//         _navigateToOnboarding();
//         return;
//       }
    
//       final user = _userStateService.getCurrentFirebaseUser();
//       final userRole = await _userStateService.checkUserRole(user?.email);
    
//       if (!userRole['isFound']) {
//         _navigateToOnboarding();
//         return;
//       }
    
//       // Store user data
//       _userStateService.storeUserData(
//         userId: userRole['userId'],
//         subUser: userRole['isSubUser'],
//         title: userRole['title'],
//         email: userRole['email'],
//       );
    
//       // Check if user profile exists
//       bool hasProfile = await _userStateService.userProfileExists();
//       if (!hasProfile) {
//         _navigateToOnboarding();
//         return;
//       }
    
//       // Navigate to home if everything is valid
//       _navigateToHome();
    
//     } catch (e) {
//       debugPrint('Error checking user state: $e');
//       _navigateToOnboarding();
//     }
//   }

//   void _navigateToHome() {
//     if (mounted) {
//       const Home().launch(context, isNewTask: true);
//     }
//   }

//   void _navigateToOnboarding() {
//     if (mounted) {
//       const OnBoard().launch(context, isNewTask: true);
//     }
//   }

//   void _showSnackBar(String text) {
//     if (_scaffoldKey.currentContext != null) {
//       ScaffoldMessenger.of(_scaffoldKey.currentContext!)
//           .showSnackBar(SnackBar(content: Text(text)));
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return SafeArea(
//       child: Directionality(
//         textDirection: isRtl ? TextDirection.rtl : TextDirection.ltr,
//         child: Scaffold(
//           key: _scaffoldKey,
//           backgroundColor: kMainColor,
//           body: Stack(
//             children: [
//               Center(
//                 child: Column(
//                   mainAxisAlignment: MainAxisAlignment.center,
//                   crossAxisAlignment: CrossAxisAlignment.center,
//                   children: [
//                     Image.asset(
//                       'assets/images/logoPos.png',
//                       width: 150.0,
//                       height: 150.0,
//                     ),
//                     const SizedBox(height: 20.0),
//                     Text(
//                       'AmrDev',
//                       style: GoogleFonts.poppins(
//                         fontSize: 30.0,
//                         fontWeight: FontWeight.bold,
//                         color: Colors.white,
//                       ),
//                     ),
//                     const SizedBox(height: 10.0),
//                     Text(
//                       'نظام نقاط البيع الأكثر تطوراً',
//                       style: GoogleFonts.poppins(
//                         fontSize: 16.0,
//                         color: Colors.white,
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//               // Loading indicator at the bottom
//               Positioned(
//                 bottom: 50,
//                 left: 0,
//                 right: 0,
//                 child: LoadingIndicatorWidget(
//                   color: Colors.white,
//                   message: _loadingMessage,
//                 ),
//               ),
//               // Internet connection warning
//               if (!_hasInternetConnection)
//                 Positioned(
//                   top: 20,
//                   left: 20,
//                   right: 20,
//                   child: Container(
//                     padding: const EdgeInsets.all(10),
//                     decoration: BoxDecoration(
//                       color: Colors.red.withOpacity(0.8),
//                       borderRadius: BorderRadius.circular(8),
//                     ),
//                     child: Row(
//                       children: [
//                         const Icon(Icons.wifi_off, color: Colors.white),
//                         const SizedBox(width: 10),
//                         Expanded(
//                           child: Text(
//                             'لا يوجد اتصال بالإنترنت. بعض الميزات قد لا تعمل بشكل صحيح.',
//                             style: TextStyle(color: Colors.white),
//                           ),
//                         ),
//                       ],
//                     ),
//                   ),
//                 ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }