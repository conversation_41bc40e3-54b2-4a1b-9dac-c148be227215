import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;
import 'package:mobile_pos/services/imgur_service.dart';
import 'package:mobile_pos/services/gofile_service.dart';

/// خدمة مركزية لإدارة رفع الملفات
/// تستخدم Imgur لرفع الصور وGoFile.io لرفع أنواع الملفات الأخرى
class FileUploadService {
  static final FileUploadService _instance = FileUploadService._internal();
  factory FileUploadService() => _instance;
  FileUploadService._internal();

  /// رفع ملف إلى الخدمة المناسبة
  ///
  /// **قواعد الرفع:**
  /// - الصور: Imgur (سريع ومجاني)
  /// - الصوت (Voice Notes): GoFile.io (يدعم جميع أنواع الصوت)
  /// - المستندات والملفات الأخرى: GoFile.io (يدعم ملفات كبيرة)
  ///
  /// [file] الملف المراد رفعه
  /// [fileType] نوع الملف (اختياري، سيتم تحديده تلقائيًا إذا لم يتم تحديده)
  /// [forceGoFile] إجبار استخدام GoFile.io حتى للصور (اختياري)
  ///
  /// يعيد خريطة تحتوي على معلومات الملف المرفوع:
  /// - success: نجاح العملية أم لا
  /// - directLink: الرابط المباشر للملف
  /// - downloadPage: رابط صفحة التحميل (لـ GoFile.io)
  /// - fileName: اسم الملف
  /// - fileType: نوع الملف
  /// - source: مصدر الملف (imgur أو gofile)
  /// - deleteHash: رمز حذف الصورة (لـ Imgur فقط)
  Future<Map<String, dynamic>> uploadFile(
    File file, {
    String? fileType,
    bool forceGoFile = false,
  }) async {
    try {
      if (kDebugMode) {
        debugPrint('جاري تحديد نوع الملف وخدمة الرفع المناسبة...');
      }

      // تحديد نوع الملف إذا لم يتم تحديده
      fileType ??= _getFileType(file.path);

      // تحديد ما إذا كان الملف صورة
      final isImage = fileType == 'image';
      final isAudio = fileType == 'audio';
      final isDocument = fileType == 'document';

      // قواعد الرفع:
      // 1. الصور -> Imgur (إلا إذا تم إجبار GoFile)
      // 2. الصوت -> GoFile دائماً (مهم للدردشة)
      // 3. المستندات -> GoFile دائماً
      // 4. باقي الملفات -> GoFile

      if (isImage && !forceGoFile) {
        if (kDebugMode) {
          debugPrint('رفع الصورة باستخدام Imgur...');
        }
        return await _uploadImageToImgur(file);
      } else {
        if (kDebugMode) {
          if (isAudio) {
            debugPrint('رفع الملف الصوتي باستخدام GoFile.io...');
          } else if (isDocument) {
            debugPrint('رفع المستند باستخدام GoFile.io...');
          } else {
            debugPrint('رفع الملف باستخدام GoFile.io...');
          }
        }
        return await _uploadFileToGoFile(file, fileType);
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('خطأ في رفع الملف: $e');
      }
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// رفع صورة إلى Imgur
  Future<Map<String, dynamic>> _uploadImageToImgur(File imageFile) async {
    try {
      final imgurResult = await ImgurService.uploadImage(imageFile);

      if (imgurResult['success'] == true) {
        return {
          'success': true,
          'directLink': imgurResult['directLink'],
          'downloadPage': imgurResult['directLink'], // نفس الرابط المباشر
          'fileName': imgurResult['fileName'] ?? path.basename(imageFile.path),
          'fileType': 'image',
          'source': 'imgur',
          'deleteHash': imgurResult['deleteHash'],
        };
      } else {
        // إذا فشل الرفع إلى Imgur، حاول استخدام GoFile كخيار احتياطي
        if (kDebugMode) {
          debugPrint('فشل رفع الصورة إلى Imgur، جاري المحاولة مع GoFile...');
        }
        return await _uploadFileToGoFile(imageFile, 'image');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('خطأ في رفع الصورة إلى Imgur: $e');
      }
      // محاولة استخدام GoFile كخيار احتياطي
      return await _uploadFileToGoFile(imageFile, 'image');
    }
  }

  /// رفع ملف إلى GoFile.io
  Future<Map<String, dynamic>> _uploadFileToGoFile(
      File file, String fileType) async {
    try {
      final goFileService = GoFileService();
      final fileInfo = await goFileService.uploadFile(file);

      // التحقق من نجاح الرفع
      if (fileInfo['success'] == true) {
        return {
          'success': true,
          'directLink': fileInfo['directLink'],
          'downloadPage': fileInfo['downloadPage'],
          'fileName': fileInfo['fileName'] ?? path.basename(file.path),
          'fileType': fileType,
          'source': 'gofile',
          'fileId': fileInfo['fileId'],
          'server': fileInfo['server'],
        };
      } else {
        return {
          'success': false,
          'error': fileInfo['error'] ?? 'فشل في رفع الملف إلى GoFile',
        };
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('خطأ في رفع الملف إلى GoFile: $e');
      }
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// تحديد نوع الملف بناءً على امتداده
  String _getFileType(String filePath) {
    final extension = path.extension(filePath).toLowerCase();

    // الصور
    if (['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg']
        .contains(extension)) {
      return 'image';
    }

    // الفيديو
    if (['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv', '.webm']
        .contains(extension)) {
      return 'video';
    }

    // الصوت (مهم للدردشة - Voice Notes)
    if ([
      '.mp3',
      '.wav',
      '.ogg',
      '.m4a',
      '.aac',
      '.flac',
      '.wma',
      '.amr',
      '.3ga'
    ].contains(extension)) {
      return 'audio';
    }

    // المستندات
    if ([
      '.pdf',
      '.doc',
      '.docx',
      '.xls',
      '.xlsx',
      '.ppt',
      '.pptx',
      '.txt',
      '.rtf',
      '.odt',
      '.ods',
      '.odp',
      '.csv'
    ].contains(extension)) {
      return 'document';
    }

    // ملفات مضغوطة
    if (['.zip', '.rar', '.7z', '.tar', '.gz', '.bz2'].contains(extension)) {
      return 'archive';
    }

    // أنواع أخرى
    return 'other';
  }

  /// رفع ملف صوتي (Voice Note) إلى GoFile
  /// دالة مخصصة للملفات الصوتية في الدردشة
  Future<Map<String, dynamic>> uploadVoiceNote(File audioFile) async {
    if (kDebugMode) {
      debugPrint('رفع ملف صوتي (Voice Note) إلى GoFile...');
    }

    // إجبار استخدام GoFile للملفات الصوتية
    return await uploadFile(audioFile, fileType: 'audio', forceGoFile: true);
  }

  /// رفع مستند إلى GoFile
  /// دالة مخصصة للمستندات
  Future<Map<String, dynamic>> uploadDocument(File documentFile) async {
    if (kDebugMode) {
      debugPrint('رفع مستند إلى GoFile...');
    }

    // إجبار استخدام GoFile للمستندات
    return await uploadFile(documentFile,
        fileType: 'document', forceGoFile: true);
  }
}
