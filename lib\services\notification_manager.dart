import 'dart:async';
import 'package:flutter/material.dart';
import 'package:mobile_pos/services/inventory_notification_service.dart';
import 'package:mobile_pos/services/local_notification_service.dart';
import 'package:mobile_pos/services/payment_notification_service.dart';
import 'package:mobile_pos/services/subscription_notification_service.dart';

/// مدير الإشعارات المركزي للتطبيق
class NotificationManager {
  static final NotificationManager _instance = NotificationManager._internal();

  factory NotificationManager() => _instance;

  NotificationManager._internal();

  Timer? _periodicCheckTimer;

  /// تهيئة مدير الإشعارات
  Future<void> initialize() async {
    // تهيئة خدمة الإشعارات المحلية
    await LocalNotificationService.initialize();

    // بدء فحص دوري للإشعارات
    _startPeriodicChecks();

    debugPrint('تم تهيئة مدير الإشعارات بنجاح');
  }

  /// بدء فحص دوري للإشعارات
  void _startPeriodicChecks() {
    // إلغاء المؤقت الحالي إذا كان موجودًا
    _periodicCheckTimer?.cancel();

    // إنشاء مؤقت جديد للفحص كل ساعة
    _periodicCheckTimer = Timer.periodic(const Duration(hours: 1), (timer) {
      _checkAllNotifications();
    });

    // فحص فوري عند بدء التشغيل
    _checkAllNotifications();
  }

  /// فحص جميع أنواع الإشعارات
  Future<void> _checkAllNotifications() async {
    try {
      // فحص المخزون المنخفض
      await InventoryNotificationService.checkLowInventory();

      // فحص المدفوعات المتأخرة
      await PaymentNotificationService.checkOverduePayments();

      // فحص حالة الاشتراك
      await SubscriptionNotificationService.checkSubscriptionStatus();

      debugPrint('تم إكمال فحص جميع الإشعارات');
    } catch (e) {
      debugPrint('خطأ في فحص الإشعارات: $e');
    }
  }

  /// إيقاف مدير الإشعارات
  void dispose() {
    _periodicCheckTimer?.cancel();
    _periodicCheckTimer = null;
  }

  /// فحص الإشعارات يدويًا
  Future<void> checkNotificationsManually() async {
    await _checkAllNotifications();
  }

  /// الحصول على قائمة الإشعارات
  static Future<List<Map<String, dynamic>>> getNotifications() async {
    try {
      // الحصول على الإشعارات من خدمة الإشعارات المحلية
      final notifications = LocalNotificationService.getNotifications();

      // تحويل الإشعارات إلى التنسيق المطلوب
      final formattedNotifications = notifications.map((notification) {
        String type = 'general';
        if (notification.data.containsKey('type')) {
          type = notification.data['type'] as String? ?? 'general';
        }

        return {
          'id': notification.id,
          'type': type,
          'title': notification.title,
          'message': notification.body,
          'date': notification.timestamp,
          'isRead': notification.isRead,
          'data': notification.data,
        };
      }).toList();

      // ترتيب الإشعارات حسب التاريخ (الأحدث أولاً)
      formattedNotifications.sort(
          (a, b) => (b['date'] as DateTime).compareTo(a['date'] as DateTime));

      return formattedNotifications;
    } catch (e) {
      debugPrint('خطأ في الحصول على الإشعارات: $e');
      return [];
    }
  }

  /// تحديد إشعار كمقروء
  static Future<void> markAsRead(String notificationId) async {
    try {
      // تحديد الإشعار كمقروء في خدمة الإشعارات المحلية
      LocalNotificationService.markAsRead(notificationId);

      // تحديث المستمعين (يتم استدعاء notifyListeners داخل الدالة markAsRead)

      debugPrint('تم تحديد الإشعار كمقروء: $notificationId');
    } catch (e) {
      debugPrint('خطأ في تحديد الإشعار كمقروء: $e');
    }
  }
}
