// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/Screens/Due%20Calculation/advanced_due_management/screens/due_notification_screen.dart';
import 'package:mobile_pos/generated/l10n.dart' as lang;

import '../../constant.dart';

class NotificationScreen extends StatefulWidget {
  const NotificationScreen({super.key});

  @override
  // ignore: library_private_types_in_public_api
  _NotificationScreenState createState() => _NotificationScreenState();
}

class _NotificationScreenState extends State<NotificationScreen> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          lang.S.of(context).notification,
          style: GoogleFonts.poppins(
            color: Colors.black,
            fontSize: 20.0,
          ),
        ),
        iconTheme: const IconThemeData(color: Colors.black),
        centerTitle: true,
        backgroundColor: Colors.white,
        elevation: 0.0,
      ),
      body: Padding(
        padding: const EdgeInsets.all(10.0),
        child: Column(
          children: [
            // إشعارات المديونية
            GestureDetector(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const DueNotificationScreen(),
                  ),
                );
              },
              child: const NotificationCard(
                title: 'إشعارات المديونية',
                iconColor: Colors.red,
                icons: Icons.warning,
                time: 'اليوم',
                description:
                    'لديك إشعارات جديدة متعلقة بالمديونية. اضغط هنا للاطلاع عليها.',
              ),
            ),

            // إشعارات أخرى
            NotificationCard(
                title: lang.S.of(context).purchaseAlarm,
                iconColor: Colors.orange,
                icons: Icons.alarm,
                time: 'June 23, 2021',
                description:
                    'في الوقت ده حصل تنبيه مهم وعايزين نتأكد إنك شوفته. الموضوع ده مهم جداً ومحتاج اهتمامك.'),
            NotificationCard(
                title: lang.S.of(context).purchaseConfirmed,
                iconColor: Colors.purple,
                icons: Icons.notifications_none_outlined,
                time: '23 يونيو 2021',
                description:
                    'تم تأكيد عملية الشراء بتاعتك وكل حاجة تمام. ممكن تتابع التفاصيل من هنا.'),
          ],
        ),
      ),
    );
  }
}

class NotificationCard extends StatelessWidget {
  const NotificationCard({
    super.key,
    required this.icons,
    required this.title,
    required this.description,
    required this.time,
    required this.iconColor,
  });

  final IconData icons;
  final String title;
  final String description;
  final String time;
  final Color iconColor;

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 0.0,
      child: Column(
        children: [
          Row(
            children: [
              Container(
                height: 40.0,
                width: 40.0,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10.0),
                  color: iconColor.withOpacity(0.2),
                ),
                child: Center(
                  child: Icon(
                    icons,
                    color: iconColor,
                  ),
                ),
              ),
              const SizedBox(
                width: 20.0,
              ),
              Column(
                children: [
                  Text(
                    title,
                    style: GoogleFonts.poppins(
                      fontSize: 18.0,
                      color: Colors.black,
                    ),
                  ),
                ],
              ),
              const Spacer(),
              Text(
                time,
                style: GoogleFonts.poppins(
                  color: kGreyTextColor,
                  fontSize: 12.0,
                ),
              ),
              const SizedBox(
                width: 20,
              ),
            ],
          ),
          Padding(
            padding: const EdgeInsets.only(left: 60.0),
            child: Text(
              description,
              style: GoogleFonts.poppins(
                color: kGreyTextColor,
                fontSize: 14.0,
              ),
              maxLines: 3,
            ),
          ),
        ],
      ),
    );
  }
}
