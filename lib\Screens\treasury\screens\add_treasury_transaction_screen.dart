import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';
import 'package:intl/intl.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/GlobalComponents/button_global.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

import '../models/treasury_transaction_model.dart';
import '../providers/treasury_provider.dart';

class AddTreasuryTransactionScreen extends ConsumerStatefulWidget {
  final TreasuryTransactionModel? transaction; // For editing
  
  const AddTreasuryTransactionScreen({
    super.key,
    this.transaction,
  });

  @override
  ConsumerState<AddTreasuryTransactionScreen> createState() => _AddTreasuryTransactionScreenState();
}

class _AddTreasuryTransactionScreenState extends ConsumerState<AddTreasuryTransactionScreen> {
  final _formKey = GlobalKey<FormState>();
  
  // Controllers
  final _descriptionController = TextEditingController();
  final _amountController = TextEditingController();
  final _referenceController = TextEditingController();
  final _notesController = TextEditingController();
  final _dateController = TextEditingController();
  
  // Form values
  String _selectedType = 'income';
  String _selectedCategory = '';
  String _selectedPaymentMethod = '';
  DateTime _selectedDate = DateTime.now();
  
  bool get _isEditing => widget.transaction != null;

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    if (_isEditing && widget.transaction != null) {
      final transaction = widget.transaction!;
      _descriptionController.text = transaction.description;
      _amountController.text = transaction.amount;
      _referenceController.text = transaction.referenceNumber;
      _notesController.text = transaction.notes;
      _selectedType = transaction.type;
      _selectedCategory = transaction.category;
      _selectedPaymentMethod = transaction.paymentMethod;
      _selectedDate = transaction.dateAsDateTime;
    }
    
    _dateController.text = DateFormat('yyyy/MM/dd').format(_selectedDate);
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    _amountController.dispose();
    _referenceController.dispose();
    _notesController.dispose();
    _dateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final incomeCategories = ref.watch(incomeCategoriesProvider);
    final expenseCategories = ref.watch(expenseCategoriesProvider);
    final paymentMethods = ref.watch(paymentMethodsProvider);
    
    final currentCategories = _selectedType == 'income' ? incomeCategories : expenseCategories;
    
    // Set default category if not selected
    if (_selectedCategory.isEmpty && currentCategories.isNotEmpty) {
      _selectedCategory = currentCategories.first;
    }
    
    if (_selectedPaymentMethod.isEmpty && paymentMethods.isNotEmpty) {
      _selectedPaymentMethod = paymentMethods.first;
    }

    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        title: Text(
          _isEditing ? 'تعديل المعاملة' : 'إضافة معاملة جديدة',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontSize: 20.0,
            fontWeight: FontWeight.bold,
          ),
        ),
        iconTheme: const IconThemeData(color: Colors.white),
        centerTitle: true,
        elevation: 0.0,
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(30),
            topLeft: Radius.circular(30),
          ),
        ),
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Transaction Type Selection
                Text(
                  'نوع المعاملة',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: kTitleColor,
                  ),
                ),
                const SizedBox(height: 10),
                Row(
                  children: [
                    Expanded(
                      child: _buildTypeCard(
                        'إيراد',
                        'income',
                        FeatherIcons.trendingUp,
                        Colors.green,
                      ),
                    ),
                    const SizedBox(width: 10),
                    Expanded(
                      child: _buildTypeCard(
                        'مصروف',
                        'expense',
                        FeatherIcons.trendingDown,
                        Colors.red,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // Category Selection
                Text(
                  'الفئة',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: kTitleColor,
                  ),
                ),
                const SizedBox(height: 10),
                DropdownButtonFormField<String>(
                  value: currentCategories.contains(_selectedCategory) ? _selectedCategory : null,
                  decoration: InputDecoration(
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 15, vertical: 12),
                  ),
                  items: currentCategories.map((category) {
                    return DropdownMenuItem(
                      value: category,
                      child: Text(
                        category,
                        style: GoogleFonts.cairo(),
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedCategory = value ?? '';
                    });
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى اختيار الفئة';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 20),

                // Description Field
                Text(
                  'الوصف',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: kTitleColor,
                  ),
                ),
                const SizedBox(height: 10),
                TextFormField(
                  controller: _descriptionController,
                  decoration: InputDecoration(
                    hintText: 'أدخل وصف المعاملة',
                    hintStyle: GoogleFonts.cairo(color: Colors.grey),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 15, vertical: 12),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال وصف المعاملة';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 20),

                // Amount Field
                Text(
                  'المبلغ',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: kTitleColor,
                  ),
                ),
                const SizedBox(height: 10),
                TextFormField(
                  controller: _amountController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    hintText: '0.00',
                    hintStyle: GoogleFonts.cairo(color: Colors.grey),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 15, vertical: 12),
                    prefixIcon: Icon(
                      _selectedType == 'income' ? FeatherIcons.plus : FeatherIcons.minus,
                      color: _selectedType == 'income' ? Colors.green : Colors.red,
                    ),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال المبلغ';
                    }
                    if (double.tryParse(value) == null || double.parse(value) <= 0) {
                      return 'يرجى إدخال مبلغ صحيح';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 20),

                // Payment Method
                Text(
                  'طريقة الدفع',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: kTitleColor,
                  ),
                ),
                const SizedBox(height: 10),
                DropdownButtonFormField<String>(
                  value: paymentMethods.contains(_selectedPaymentMethod) ? _selectedPaymentMethod : null,
                  decoration: InputDecoration(
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 15, vertical: 12),
                  ),
                  items: paymentMethods.map((method) {
                    return DropdownMenuItem(
                      value: method,
                      child: Text(
                        method,
                        style: GoogleFonts.cairo(),
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedPaymentMethod = value ?? '';
                    });
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى اختيار طريقة الدفع';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 20),

                // Date Field
                Text(
                  'التاريخ',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: kTitleColor,
                  ),
                ),
                const SizedBox(height: 10),
                TextFormField(
                  controller: _dateController,
                  readOnly: true,
                  decoration: InputDecoration(
                    hintText: 'اختر التاريخ',
                    hintStyle: GoogleFonts.cairo(color: Colors.grey),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 15, vertical: 12),
                    suffixIcon: const Icon(FeatherIcons.calendar),
                  ),
                  onTap: () async {
                    final date = await showDatePicker(
                      context: context,
                      initialDate: _selectedDate,
                      firstDate: DateTime(2020),
                      lastDate: DateTime.now().add(const Duration(days: 365)),
                    );
                    
                    if (date != null) {
                      setState(() {
                        _selectedDate = date;
                        _dateController.text = DateFormat('yyyy/MM/dd').format(date);
                      });
                    }
                  },
                ),

                const SizedBox(height: 20),

                // Reference Number Field
                Text(
                  'رقم المرجع (اختياري)',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: kTitleColor,
                  ),
                ),
                const SizedBox(height: 10),
                TextFormField(
                  controller: _referenceController,
                  decoration: InputDecoration(
                    hintText: 'رقم الفاتورة أو المرجع',
                    hintStyle: GoogleFonts.cairo(color: Colors.grey),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 15, vertical: 12),
                  ),
                ),

                const SizedBox(height: 20),

                // Notes Field
                Text(
                  'ملاحظات (اختياري)',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: kTitleColor,
                  ),
                ),
                const SizedBox(height: 10),
                TextFormField(
                  controller: _notesController,
                  maxLines: 3,
                  decoration: InputDecoration(
                    hintText: 'أضف ملاحظات إضافية',
                    hintStyle: GoogleFonts.cairo(color: Colors.grey),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 15, vertical: 12),
                  ),
                ),

                const SizedBox(height: 30),

                // Save Button
                ButtonGlobalWithoutIcon(
                  buttontext: _isEditing ? 'تحديث المعاملة' : 'حفظ المعاملة',
                  buttonDecoration: kButtonDecoration.copyWith(
                    color: kMainColor,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  onPressed: _saveTransaction,
                  buttonTextColor: Colors.white,
                ),

                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTypeCard(String title, String value, IconData icon, Color color) {
    final isSelected = _selectedType == value;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedType = value;
          _selectedCategory = ''; // Reset category when type changes
        });
      },
      child: Container(
        padding: const EdgeInsets.all(15),
        decoration: BoxDecoration(
          color: isSelected ? color.withOpacity(0.1) : Colors.grey[100],
          border: Border.all(
            color: isSelected ? color : Colors.grey[300]!,
            width: 2,
          ),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected ? color : Colors.grey[600],
              size: 30,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: GoogleFonts.cairo(
                color: isSelected ? color : Colors.grey[600],
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _saveTransaction() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    EasyLoading.show(status: _isEditing ? 'جاري التحديث...' : 'جاري الحفظ...');

    try {
      final transaction = TreasuryTransactionModel(
        id: _isEditing ? widget.transaction!.id : '',
        date: _selectedDate.toIso8601String(),
        type: _selectedType,
        category: _selectedCategory,
        description: _descriptionController.text.trim(),
        amount: _amountController.text.trim(),
        paymentMethod: _selectedPaymentMethod,
        referenceNumber: _referenceController.text.trim(),
        notes: _notesController.text.trim(),
        createdBy: 'current_user', // يمكن تحديثه لاحقاً
        createdAt: _isEditing ? widget.transaction!.createdAt : DateTime.now().toIso8601String(),
        updatedAt: DateTime.now().toIso8601String(),
      );

      final addTransactionNotifier = ref.read(addTransactionProvider.notifier);
      
      if (_isEditing) {
        await addTransactionNotifier.updateTransaction(transaction);
      } else {
        await addTransactionNotifier.addTransaction(transaction);
      }

      final result = ref.read(addTransactionProvider);
      
      result.when(
        data: (success) {
          if (success) {
            EasyLoading.showSuccess(_isEditing ? 'تم تحديث المعاملة بنجاح' : 'تم حفظ المعاملة بنجاح');
            Navigator.pop(context);
          } else {
            EasyLoading.showError('فشل في حفظ المعاملة');
          }
        },
        loading: () {},
        error: (error, stack) {
          EasyLoading.showError('خطأ: $error');
        },
      );
    } catch (e) {
      EasyLoading.showError('خطأ غير متوقع: $e');
    }
  }
}
