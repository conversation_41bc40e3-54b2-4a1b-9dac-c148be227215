import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/model/water_filter_models.dart';
import 'package:mobile_pos/services/water_filter_expense_service.dart';

class AddExpenseScreen extends StatefulWidget {
  final WaterFilterExpense? expense; // للتعديل

  const AddExpenseScreen({super.key, this.expense});

  @override
  State<AddExpenseScreen> createState() => _AddExpenseScreenState();
}

class _AddExpenseScreenState extends State<AddExpenseScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _amountController = TextEditingController();
  final _vendorController = TextEditingController();
  final _invoiceController = TextEditingController();
  final _notesController = TextEditingController();

  ExpenseCategory _selectedCategory = ExpenseCategory.other;
  ExpensePaymentMethod _selectedPaymentMethod = ExpensePaymentMethod.cash;
  DateTime _selectedDate = DateTime.now();
  bool _isRecurring = false;
  int? _recurringDays;
  bool _isLoading = false;

  bool get _isEditing => widget.expense != null;

  @override
  void initState() {
    super.initState();
    if (_isEditing) {
      _loadExpenseData();
    }
  }

  void _loadExpenseData() {
    final expense = widget.expense!;
    _titleController.text = expense.title;
    _descriptionController.text = expense.description;
    _amountController.text = expense.amount.toString();
    _vendorController.text = expense.vendorName ?? '';
    _invoiceController.text = expense.invoiceNumber ?? '';
    _notesController.text = expense.notes ?? '';
    _selectedCategory = expense.category;
    _selectedPaymentMethod = expense.paymentMethod;
    _selectedDate = expense.date;
    _isRecurring = expense.isRecurring;
    _recurringDays = expense.recurringIntervalDays;
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _amountController.dispose();
    _vendorController.dispose();
    _invoiceController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date != null) {
      setState(() => _selectedDate = date);
    }
  }

  Future<void> _saveExpense() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final expense = WaterFilterExpense(
        id: _isEditing ? widget.expense!.id : '',
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        category: _selectedCategory,
        amount: double.parse(_amountController.text),
        date: _selectedDate,
        paymentMethod: _selectedPaymentMethod,
        vendorName: _vendorController.text.trim().isEmpty
            ? null
            : _vendorController.text.trim(),
        invoiceNumber: _invoiceController.text.trim().isEmpty
            ? null
            : _invoiceController.text.trim(),
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
        isRecurring: _isRecurring,
        recurringIntervalDays: _isRecurring ? _recurringDays : null,
      );

      bool success;
      if (_isEditing) {
        success = await WaterFilterExpenseService.updateExpense(expense);
      } else {
        success = await WaterFilterExpenseService.addExpense(expense);
      }

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              _isEditing ? 'تم تحديث المصروف بنجاح' : 'تم إضافة المصروف بنجاح',
            ),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context, true);
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              _isEditing ? 'فشل في تحديث المصروف' : 'فشل في إضافة المصروف',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        title: Text(
          _isEditing ? 'تعديل المصروف' : 'إضافة مصروف جديد',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // عنوان المصروف
                _buildTextField(
                  controller: _titleController,
                  label: 'عنوان المصروف',
                  hint: 'مثال: شراء قطع غيار',
                  icon: Icons.title,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'عنوان المصروف مطلوب';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 16),

                // وصف المصروف
                _buildTextField(
                  controller: _descriptionController,
                  label: 'وصف المصروف',
                  hint: 'تفاصيل المصروف...',
                  icon: Icons.description,
                  maxLines: 3,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'وصف المصروف مطلوب';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 16),

                // المبلغ
                _buildTextField(
                  controller: _amountController,
                  label: 'المبلغ (ج.م)',
                  hint: '0.00',
                  icon: Icons.attach_money,
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'المبلغ مطلوب';
                    }
                    final amount = double.tryParse(value);
                    if (amount == null || amount <= 0) {
                      return 'يرجى إدخال مبلغ صحيح';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 16),

                // فئة المصروف
                _buildDropdownField<ExpenseCategory>(
                  label: 'فئة المصروف',
                  value: _selectedCategory,
                  items: ExpenseCategory.values.map((category) {
                    return DropdownMenuItem(
                      value: category,
                      child: Text(
                        category.arabicName,
                        style: GoogleFonts.cairo(),
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() => _selectedCategory = value!);
                  },
                  icon: Icons.category,
                ),

                const SizedBox(height: 16),

                // طريقة الدفع
                _buildDropdownField<ExpensePaymentMethod>(
                  label: 'طريقة الدفع',
                  value: _selectedPaymentMethod,
                  items: ExpensePaymentMethod.values.map((method) {
                    return DropdownMenuItem(
                      value: method,
                      child: Text(
                        method.arabicName,
                        style: GoogleFonts.cairo(),
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() => _selectedPaymentMethod = value!);
                  },
                  icon: Icons.payment,
                ),

                const SizedBox(height: 16),

                // تاريخ المصروف
                _buildDateField(),

                const SizedBox(height: 16),

                // اسم المورد (اختياري)
                _buildTextField(
                  controller: _vendorController,
                  label: 'اسم المورد (اختياري)',
                  hint: 'اسم الشركة أو المورد',
                  icon: Icons.business,
                ),

                const SizedBox(height: 16),

                // رقم الفاتورة (اختياري)
                _buildTextField(
                  controller: _invoiceController,
                  label: 'رقم الفاتورة (اختياري)',
                  hint: 'رقم الفاتورة أو الإيصال',
                  icon: Icons.receipt,
                ),

                const SizedBox(height: 16),

                // مصروف متكرر
                _buildRecurringSection(),

                const SizedBox(height: 16),

                // ملاحظات (اختياري)
                _buildTextField(
                  controller: _notesController,
                  label: 'ملاحظات (اختياري)',
                  hint: 'أي ملاحظات إضافية...',
                  icon: Icons.note,
                  maxLines: 3,
                ),

                const SizedBox(height: 32),

                // زر الحفظ
                SizedBox(
                  width: double.infinity,
                  height: 50,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _saveExpense,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: kMainColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: _isLoading
                        ? const CircularProgressIndicator(color: Colors.white)
                        : Text(
                            _isEditing ? 'تحديث المصروف' : 'إضافة المصروف',
                            style: GoogleFonts.cairo(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: GoogleFonts.cairo(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: kTitleColor,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          maxLines: maxLines,
          validator: validator,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: GoogleFonts.cairo(color: Colors.grey.shade600),
            prefixIcon: Icon(icon, color: kMainColor),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: kMainColor),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.red),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDropdownField<T>({
    required String label,
    required T value,
    required List<DropdownMenuItem<T>> items,
    required void Function(T?) onChanged,
    required IconData icon,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: GoogleFonts.cairo(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: kTitleColor,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<T>(
          value: value,
          items: items,
          onChanged: onChanged,
          decoration: InputDecoration(
            prefixIcon: Icon(icon, color: kMainColor),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: kMainColor),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDateField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'تاريخ المصروف',
          style: GoogleFonts.cairo(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: kTitleColor,
          ),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: _selectDate,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                const Icon(Icons.calendar_today, color: kMainColor),
                const SizedBox(width: 12),
                Text(
                  '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                  style: GoogleFonts.cairo(fontSize: 16),
                ),
                const Spacer(),
                const Icon(Icons.arrow_drop_down, color: Colors.grey),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRecurringSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Checkbox(
              value: _isRecurring,
              onChanged: (value) {
                setState(() {
                  _isRecurring = value ?? false;
                  if (!_isRecurring) {
                    _recurringDays = null;
                  }
                });
              },
              activeColor: kMainColor,
            ),
            Text(
              'مصروف متكرر',
              style: GoogleFonts.cairo(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: kTitleColor,
              ),
            ),
          ],
        ),
        if (_isRecurring) ...[
          const SizedBox(height: 8),
          Row(
            children: [
              Text(
                'كل',
                style: GoogleFonts.cairo(fontSize: 14),
              ),
              const SizedBox(width: 8),
              SizedBox(
                width: 80,
                child: TextFormField(
                  initialValue: _recurringDays?.toString() ?? '30',
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    _recurringDays = int.tryParse(value);
                  },
                  decoration: InputDecoration(
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 8,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                'يوم',
                style: GoogleFonts.cairo(fontSize: 14),
              ),
            ],
          ),
        ],
      ],
    );
  }
}
