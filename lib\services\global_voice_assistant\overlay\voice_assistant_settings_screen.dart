import 'dart:async';
import 'package:flutter/material.dart';
import '../voice_assistant_exports.dart';
import '../core/voice_assistant_initialization_manager.dart';
import '../permissions/permission_manager.dart';
import '../testing/interactive_voice_assistant_test_screen.dart';
import '../../../constant.dart';

/// شاشة إعدادات المساعد الصوتي العالمي
class VoiceAssistantSettingsScreen extends StatefulWidget {
  const VoiceAssistantSettingsScreen({super.key});

  @override
  State<VoiceAssistantSettingsScreen> createState() =>
      _VoiceAssistantSettingsScreenState();
}

class _VoiceAssistantSettingsScreenState
    extends State<VoiceAssistantSettingsScreen> with TickerProviderStateMixin {
  // الخدمات الأساسية
  final VoiceAssistantInitializationManager _initManager =
      VoiceAssistantInitializationManager();
  final VoiceFingerprintService _fingerprintService = VoiceFingerprintService();
  final PermissionManager _permissionManager = PermissionManager();
  final WakeWordDetectionService _wakeWordService = WakeWordDetectionService();
  final VoiceResponseEngine _responseEngine = VoiceResponseEngine();

  // حالة الخدمات
  bool _isServiceEnabled = false;
  bool _isVoiceFingerprintRegistered = false;
  Map<String, dynamic>? _permissionStatus;
  Map<String, dynamic>? _serviceStatus;

  // إعدادات كلمة التفعيل
  String _currentWakeWord = 'hey app';
  double _sensitivity = 0.7;

  // إعدادات الصوت
  double _speechRate = 0.5;
  double _volume = 0.8;
  double _pitch = 1.0;

  // حالة التحميل والعمليات
  bool _isLoading = true;
  bool _isRegistering = false;
  bool _isTogglingService = false;
  String _registrationProgress = '';

  // التحكم في الرسوم المتحركة
  late TabController _tabController;
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadCurrentSettings();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  /// تهيئة الرسوم المتحركة
  void _initializeAnimations() {
    _tabController = TabController(length: 4, vsync: this);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animationController.forward();
  }

  /// تحميل الإعدادات الحالية
  Future<void> _loadCurrentSettings() async {
    try {
      debugPrint('🔄 بدء تحميل إعدادات المساعد الصوتي...');

      setState(() {
        _isLoading = true;
      });

      // تحديد timeout لتجنب الانتظار اللانهائي
      await Future.wait([
        _initializeServices(),
        _loadServiceStatus(),
        _loadPermissionStatus(),
        _loadFingerprintStatus(),
        _loadWakeWordSettings(),
        _loadVoiceSettings(),
      ]).timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          debugPrint('⏰ انتهت مهلة تحميل الإعدادات');
          throw TimeoutException(
              'انتهت مهلة تحميل الإعدادات', const Duration(seconds: 10));
        },
      );

      setState(() {
        _isLoading = false;
      });

      debugPrint('✅ تم تحميل جميع إعدادات المساعد الصوتي بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الإعدادات: $e');
      setState(() {
        _isLoading = false;
      });

      // عرض رسالة خطأ للمستخدم
      if (mounted) {
        _showSnackBar('خطأ في تحميل الإعدادات: ${e.toString()}', Colors.red);
      }
    }
  }

  /// تهيئة الخدمات
  Future<void> _initializeServices() async {
    try {
      debugPrint('🔧 بدء تهيئة خدمات شاشة الإعدادات...');

      // تهيئة خدمة بصمة الصوت
      await _fingerprintService.initialize();
      debugPrint('✅ تم تهيئة خدمة بصمة الصوت');

      // فحص الأذونات
      await _permissionManager.checkAllPermissions();
      debugPrint('✅ تم فحص الأذونات');

      debugPrint('✅ تم تهيئة جميع خدمات شاشة الإعدادات بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمات شاشة الإعدادات: $e');
    }
  }

  /// تحميل حالة الخدمة
  Future<void> _loadServiceStatus() async {
    try {
      debugPrint('📊 تحميل حالة خدمة المساعد الصوتي...');

      final status = _initManager.getInitializationStatus();
      debugPrint('📋 حالة الخدمة: $status');

      setState(() {
        _serviceStatus = status;
        _isServiceEnabled = status['isGloballyInitialized'] ?? false;
      });

      debugPrint('✅ تم تحميل حالة الخدمة: مُفعل=$_isServiceEnabled');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل حالة الخدمة: $e');
      // تعيين قيم افتراضية في حالة الخطأ
      setState(() {
        _serviceStatus = {
          'isGloballyInitialized': false,
          'error': e.toString()
        };
        _isServiceEnabled = false;
      });
    }
  }

  /// تحميل حالة الأذونات
  Future<void> _loadPermissionStatus() async {
    try {
      debugPrint('🔐 تحميل حالة الأذونات...');

      final status = await _permissionManager.checkAllPermissions();
      debugPrint('📋 حالة الأذونات: $status');

      setState(() {
        _permissionStatus = status;
      });

      debugPrint('✅ تم تحميل حالة الأذونات بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل حالة الأذونات: $e');
      // تعيين قيم افتراضية في حالة الخطأ
      setState(() {
        _permissionStatus = {
          'permissions': <String, bool>{},
          'error': e.toString(),
        };
      });
    }
  }

  /// تحميل حالة بصمة الصوت
  Future<void> _loadFingerprintStatus() async {
    try {
      debugPrint('🔐 تحميل حالة بصمة الصوت...');

      final info = _fingerprintService.getVoiceFingerprintInfo();
      debugPrint('📋 معلومات بصمة الصوت: $info');

      setState(() {
        _isVoiceFingerprintRegistered = info['isRegistered'] ?? false;
      });

      debugPrint(
          '✅ تم تحميل حالة بصمة الصوت: مسجلة=$_isVoiceFingerprintRegistered');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل حالة بصمة الصوت: $e');
      // تعيين قيم افتراضية في حالة الخطأ
      setState(() {
        _isVoiceFingerprintRegistered = false;
      });
    }
  }

  /// تحميل إعدادات كلمة التفعيل
  Future<void> _loadWakeWordSettings() async {
    try {
      debugPrint('🎤 تحميل إعدادات كلمة التفعيل...');

      _currentWakeWord = _wakeWordService.currentWakeWord;
      _sensitivity = _wakeWordService.sensitivity;

      debugPrint(
          '✅ تم تحميل إعدادات كلمة التفعيل: كلمة=$_currentWakeWord، حساسية=$_sensitivity');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل إعدادات كلمة التفعيل: $e');
      // تعيين قيم افتراضية
      _currentWakeWord = 'hey app';
      _sensitivity = 0.7;
    }
  }

  /// تحميل إعدادات الصوت
  Future<void> _loadVoiceSettings() async {
    try {
      debugPrint('🔊 تحميل إعدادات الصوت...');

      final speechStatus = _responseEngine.getSpeechStatus();
      setState(() {
        _speechRate = speechStatus['speechRate'] ?? 0.5;
        _volume = speechStatus['volume'] ?? 0.8;
        _pitch = speechStatus['pitch'] ?? 1.0;
      });

      debugPrint(
          '✅ تم تحميل إعدادات الصوت: سرعة=$_speechRate، صوت=$_volume، طبقة=$_pitch');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل إعدادات الصوت: $e');
      // تعيين قيم افتراضية
      setState(() {
        _speechRate = 0.5;
        _volume = 0.8;
        _pitch = 1.0;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        elevation: 0,
        title: const Text(
          'إعدادات المساعد الصوتي',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: _isLoading ? null : _refreshSettings,
            tooltip: 'تحديث الإعدادات',
          ),
          IconButton(
            icon: const Icon(Icons.help_outline, color: Colors.white),
            onPressed: () => _showHelpDialog(),
            tooltip: 'المساعدة',
          ),
        ],
      ),
      body: _isLoading
          ? _buildLoadingScreen()
          : Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(30),
                  topRight: Radius.circular(30),
                ),
              ),
              child: ListView(
                padding: const EdgeInsets.all(12),
                children: [
                  _buildServiceToggleSection(),
                  const SizedBox(height: 12),
                  _buildVoiceFingerprintSection(),
                  const SizedBox(height: 12),
                  _buildWakeWordSection(),
                  const SizedBox(height: 12),
                  _buildVoiceSettingsSection(),
                  const SizedBox(height: 12),
                  _buildTestSection(),
                  const SizedBox(height: 12),
                  _buildDiagnosticsSection(),
                ],
              ),
            ),
      floatingActionButton: _isServiceEnabled
          ? FloatingActionButton.extended(
              onPressed: () => _testVoiceAssistant(),
              backgroundColor: Colors.green,
              icon: const Icon(Icons.play_arrow),
              label: const Text('اختبار المساعد'),
            )
          : null,
    );
  }

  /// بناء شاشة التحميل
  Widget _buildLoadingScreen() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFF1E3A8A),
            Color(0xFF3B82F6),
          ],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // أيقونة المساعد الصوتي
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(60),
                border: Border.all(
                  color: Colors.white.withOpacity(0.3),
                  width: 2,
                ),
              ),
              child: const Icon(
                Icons.settings_voice,
                size: 60,
                color: Colors.white,
              ),
            ),

            const SizedBox(height: 30),

            // مؤشر التحميل
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              strokeWidth: 3,
            ),

            const SizedBox(height: 20),

            // نص التحميل
            const Text(
              'جاري تحميل إعدادات المساعد الصوتي...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 10),

            // نص فرعي
            Text(
              'يرجى الانتظار بينما نقوم بتحميل جميع الإعدادات',
              style: TextStyle(
                color: Colors.white.withOpacity(0.8),
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// قسم تفعيل/إلغاء تفعيل الخدمة
  Widget _buildServiceToggleSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.mic, color: kMainColor, size: 24),
                const SizedBox(width: 10),
                const Text(
                  'تفعيل المساعد الصوتي',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 15),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'تفعيل الخدمة',
                  style: TextStyle(fontSize: 16),
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (_isTogglingService) ...[
                      const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                      const SizedBox(width: 10),
                    ],
                    Switch(
                      value: _isServiceEnabled,
                      onChanged: _isTogglingService ? null : _toggleService,
                      activeColor: kMainColor,
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// قسم بصمة الصوت
  Widget _buildVoiceFingerprintSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.fingerprint, color: kMainColor, size: 24),
                const SizedBox(width: 10),
                const Text(
                  'بصمة الصوت',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 15),
            Row(
              children: [
                Icon(
                  _isVoiceFingerprintRegistered
                      ? Icons.check_circle
                      : Icons.warning,
                  color: _isVoiceFingerprintRegistered
                      ? Colors.green
                      : Colors.orange,
                ),
                const SizedBox(width: 10),
                Text(
                  _isVoiceFingerprintRegistered
                      ? 'تم تسجيل بصمة الصوت'
                      : 'لم يتم تسجيل بصمة الصوت',
                  style: TextStyle(
                    fontSize: 16,
                    color: _isVoiceFingerprintRegistered
                        ? Colors.green
                        : Colors.orange,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 15),
            if (_isRegistering) ...[
              LinearProgressIndicator(
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(kMainColor),
              ),
              const SizedBox(height: 10),
              Text(
                _registrationProgress,
                style: const TextStyle(fontSize: 14),
                textAlign: TextAlign.center,
              ),
            ] else ...[
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _registerVoiceFingerprint,
                      icon: const Icon(Icons.mic),
                      label: Text(_isVoiceFingerprintRegistered
                          ? 'إعادة تسجيل'
                          : 'تسجيل بصمة الصوت'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: kMainColor,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                    ),
                  ),
                  if (_isVoiceFingerprintRegistered) ...[
                    const SizedBox(width: 10),
                    ElevatedButton.icon(
                      onPressed: _deleteVoiceFingerprint,
                      icon: const Icon(Icons.delete),
                      label: const Text('حذف'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// قسم إعدادات كلمة التفعيل
  Widget _buildWakeWordSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.record_voice_over, color: kMainColor, size: 24),
                const SizedBox(width: 10),
                const Text(
                  'كلمة التفعيل',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 15),
            TextFormField(
              initialValue: _currentWakeWord,
              decoration: InputDecoration(
                labelText: 'كلمة التفعيل',
                hintText: 'مثال: hey app',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                prefixIcon: const Icon(Icons.keyboard_voice),
              ),
              onChanged: (value) {
                _currentWakeWord = value;
              },
              onFieldSubmitted: (value) {
                _updateWakeWord(value);
              },
            ),
            const SizedBox(height: 15),
            Text(
              'حساسية الكشف: ${(_sensitivity * 100).round()}%',
              style: const TextStyle(fontSize: 16),
            ),
            Slider(
              value: _sensitivity,
              min: 0.1,
              max: 1.0,
              divisions: 9,
              activeColor: kMainColor,
              onChanged: (value) {
                setState(() => _sensitivity = value);
              },
              onChangeEnd: (value) {
                _updateSensitivity(value);
              },
            ),
          ],
        ),
      ),
    );
  }

  /// قسم إعدادات الصوت
  Widget _buildVoiceSettingsSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.volume_up, color: kMainColor, size: 24),
                const SizedBox(width: 10),
                const Text(
                  'إعدادات الصوت',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 15),
            _buildSliderSetting(
              'سرعة الكلام',
              _speechRate,
              0.1,
              2.0,
              (value) => setState(() => _speechRate = value),
              (value) => _updateSpeechRate(value),
            ),
            _buildSliderSetting(
              'مستوى الصوت',
              _volume,
              0.0,
              1.0,
              (value) => setState(() => _volume = value),
              (value) => _updateVolume(value),
            ),
            _buildSliderSetting(
              'طبقة الصوت',
              _pitch,
              0.5,
              2.0,
              (value) => setState(() => _pitch = value),
              (value) => _updatePitch(value),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء شريط التمرير للإعدادات
  Widget _buildSliderSetting(
    String title,
    double value,
    double min,
    double max,
    Function(double) onChanged,
    Function(double) onChangeEnd,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '$title: ${(value * 100).round()}%',
          style: const TextStyle(fontSize: 16),
        ),
        Slider(
          value: value,
          min: min,
          max: max,
          divisions: 20,
          activeColor: kMainColor,
          onChanged: onChanged,
          onChangeEnd: onChangeEnd,
        ),
        const SizedBox(height: 10),
      ],
    );
  }

  /// قسم الاختبار
  Widget _buildTestSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.play_circle, color: kMainColor, size: 24),
                const SizedBox(width: 10),
                const Text(
                  'اختبار الإعدادات',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 15),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _testVoiceResponse,
                    icon: const Icon(Icons.volume_up),
                    label: const Text('اختبار الصوت'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: kMainColor,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _testWakeWordDetection,
                    icon: const Icon(Icons.mic),
                    label: const Text('اختبار كلمة التفعيل'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 15),
            // زر اختبار المساعد الصوتي السريع
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _testInteractiveAssistant,
                icon: const Icon(Icons.chat),
                label: const Text('اختبار المساعد التفاعلي'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// قسم التشخيص والإصلاح
  Widget _buildDiagnosticsSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.medical_services, color: kMainColor, size: 24),
                const SizedBox(width: 10),
                const Text(
                  'التشخيص والإصلاح',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 10),
            const Text(
              'تشخيص المشاكل وإصلاح الأعطال في المساعد الصوتي',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 15),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _openDiagnosticsScreen,
                icon: const Icon(Icons.search),
                label: const Text('فتح أدوات التشخيص'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// تفعيل/إلغاء تفعيل الخدمة
  Future<void> _toggleService(bool enabled) async {
    if (_isTogglingService) {
      debugPrint('⚠️ عملية تفعيل/إلغاء تفعيل قيد التنفيذ بالفعل');
      return;
    }

    setState(() => _isTogglingService = true);

    debugPrint(
        '🔄 محاولة ${enabled ? 'تفعيل' : 'إلغاء تفعيل'} المساعد الصوتي...');

    // عرض مؤشر تحميل
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                    strokeWidth: 2, color: Colors.white),
              ),
              const SizedBox(width: 10),
              Text(enabled
                  ? 'جاري تفعيل المساعد الصوتي...'
                  : 'جاري إلغاء تفعيل المساعد الصوتي...'),
            ],
          ),
          backgroundColor: Colors.blue,
          duration: const Duration(seconds: 10),
        ),
      );
    }

    try {
      if (enabled) {
        debugPrint('🚀 بدء تفعيل المساعد الصوتي...');

        // تفعيل الخدمة مع timeout
        final success = await _initManager
            .initializeGlobalVoiceAssistant()
            .timeout(const Duration(seconds: 8));

        debugPrint('📊 نتيجة التفعيل: $success');

        if (success) {
          setState(() => _isServiceEnabled = true);
          _showSnackBar('✅ تم تفعيل المساعد الصوتي بنجاح', Colors.green);
          debugPrint('✅ تم تفعيل المساعد الصوتي بنجاح');
        } else {
          setState(() => _isServiceEnabled = false);
          _showSnackBar('❌ فشل في تفعيل المساعد الصوتي', Colors.red);
          debugPrint('❌ فشل في تفعيل المساعد الصوتي');
        }
      } else {
        debugPrint('🛑 بدء إلغاء تفعيل المساعد الصوتي...');

        // إلغاء تفعيل الخدمة
        await _initManager.decreaseReference();
        setState(() => _isServiceEnabled = false);
        _showSnackBar('🔶 تم إلغاء تفعيل المساعد الصوتي', Colors.orange);
        debugPrint('🔶 تم إلغاء تفعيل المساعد الصوتي');
      }

      // إعادة تحميل حالة الخدمة
      await _loadServiceStatus();
      debugPrint('🔄 تم تحديث حالة الخدمة');
    } on TimeoutException catch (e) {
      debugPrint('⏰ انتهت مهلة تفعيل المساعد الصوتي: $e');

      // إعادة تعيين فورية عند انتهاء المهلة
      await _initManager.forceReset();

      setState(() => _isServiceEnabled = false);
      _showSnackBar('⏰ انتهت مهلة التفعيل - تم إعادة تعيين الخدمة', Colors.red);
    } catch (e) {
      debugPrint('❌ خطأ في تفعيل/إلغاء تفعيل الخدمة: $e');

      // إعادة تعيين في حالة الخطأ
      await _initManager.forceReset();

      setState(() => _isServiceEnabled = false);
      _showSnackBar(
          '❌ خطأ في تغيير حالة الخدمة - تم إعادة التعيين', Colors.red);
    } finally {
      // إعادة تعيين حالة التفعيل
      setState(() => _isTogglingService = false);

      // إخفاء مؤشر التحميل
      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
      }
    }
  }

  /// عرض رسالة SnackBar
  void _showSnackBar(String message, Color color) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: color,
          duration: const Duration(seconds: 3),
          behavior: SnackBarBehavior.floating,
          action: SnackBarAction(
            label: 'إغلاق',
            textColor: Colors.white,
            onPressed: () {
              ScaffoldMessenger.of(context).hideCurrentSnackBar();
            },
          ),
        ),
      );
    }
  }

  /// تحديث الإعدادات
  Future<void> _refreshSettings() async {
    await _loadCurrentSettings();
  }

  /// إعادة تعيين فورية للمساعد الصوتي
  Future<void> _forceResetService() async {
    try {
      debugPrint('🔄 بدء إعادة التعيين الفورية...');

      _showSnackBar('🔄 جاري إعادة تعيين المساعد الصوتي...', Colors.blue);

      // إعادة تعيين فورية
      await _initManager.forceReset();

      // تحديث حالة الواجهة
      setState(() {
        _isServiceEnabled = false;
        _isTogglingService = false;
      });

      // إعادة تحميل الإعدادات
      await _loadCurrentSettings();

      _showSnackBar('✅ تم إعادة تعيين المساعد الصوتي بنجاح', Colors.green);
      debugPrint('✅ تم إعادة التعيين الفورية بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في إعادة التعيين الفورية: $e');
      _showSnackBar('❌ خطأ في إعادة التعيين: $e', Colors.red);
    }
  }

  /// تسجيل بصمة الصوت
  Future<void> _registerVoiceFingerprint() async {
    setState(() {
      _isRegistering = true;
      _registrationProgress = 'جاري التحضير...';
    });

    try {
      final trainingPhrases = [
        'مرحباً، أنا أسجل بصمة صوتي',
        'هذا هو صوتي الطبيعي',
        'أريد تسجيل بصمة صوتي للأمان',
        'المساعد الصوتي الذكي',
        'أهلاً وسهلاً بك في التطبيق',
      ];

      final result = await _fingerprintService.registerVoiceFingerprint(
        trainingPhrases: trainingPhrases,
        onProgress: (progress) {
          setState(() => _registrationProgress = progress);
        },
      );

      if (result['success']) {
        setState(() {
          _isVoiceFingerprintRegistered = true;
          _isRegistering = false;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم تسجيل بصمة الصوت بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        throw Exception(result['message']);
      }
    } catch (e) {
      setState(() => _isRegistering = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في تسجيل بصمة الصوت: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// حذف بصمة الصوت
  Future<void> _deleteVoiceFingerprint() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف بصمة الصوت؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _fingerprintService.deleteVoiceFingerprint();
        setState(() => _isVoiceFingerprintRegistered = false);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف بصمة الصوت'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('فشل في حذف بصمة الصوت'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  /// تحديث كلمة التفعيل
  Future<void> _updateWakeWord(String wakeWord) async {
    try {
      await _wakeWordService.setCustomWakeWord(wakeWord);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث كلمة التفعيل'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('فشل في تحديث كلمة التفعيل'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// تحديث حساسية الكشف
  Future<void> _updateSensitivity(double sensitivity) async {
    try {
      await _wakeWordService.setSensitivity(sensitivity);
    } catch (e) {
      debugPrint('خطأ في تحديث الحساسية: $e');
    }
  }

  /// تحديث سرعة الكلام
  Future<void> _updateSpeechRate(double rate) async {
    try {
      await _responseEngine.setSpeechRate(rate);
    } catch (e) {
      debugPrint('خطأ في تحديث سرعة الكلام: $e');
    }
  }

  /// تحديث مستوى الصوت
  Future<void> _updateVolume(double volume) async {
    try {
      await _responseEngine.setVolume(volume);
    } catch (e) {
      debugPrint('خطأ في تحديث مستوى الصوت: $e');
    }
  }

  /// تحديث طبقة الصوت
  Future<void> _updatePitch(double pitch) async {
    try {
      await _responseEngine.setPitch(pitch);
    } catch (e) {
      debugPrint('خطأ في تحديث طبقة الصوت: $e');
    }
  }

  /// اختبار الرد الصوتي
  Future<void> _testVoiceResponse() async {
    try {
      debugPrint('🔊 بدء اختبار الصوت...');

      _showSnackBar('🔊 جاري اختبار الصوت...', Colors.blue);

      // تطبيق الإعدادات أولاً
      await _responseEngine.setSpeechRate(_speechRate);
      await _responseEngine.setVolume(_volume);
      await _responseEngine.setPitch(_pitch);

      // اختبار نطق نص تجريبي
      await _responseEngine
          .speak('اختبار الصوت - المساعد الذكي يعمل بشكل صحيح');

      debugPrint('✅ تم اختبار الصوت بنجاح');
      _showSnackBar('✅ تم اختبار الصوت بنجاح', Colors.green);
    } catch (e) {
      debugPrint('❌ خطأ في اختبار الصوت: $e');
      _showSnackBar('❌ فشل في اختبار الصوت: $e', Colors.red);
    }
  }

  /// اختبار كشف كلمة التفعيل
  Future<void> _testWakeWordDetection() async {
    try {
      debugPrint('🎤 بدء اختبار كشف كلمة التفعيل...');

      if (!_isServiceEnabled) {
        _showSnackBar('⚠️ يجب تفعيل المساعد الصوتي أولاً', Colors.orange);
        return;
      }

      _showSnackBar('🎤 جاري تهيئة اختبار كلمة التفعيل...', Colors.blue);

      // تهيئة خدمة كشف كلمة التفعيل
      await _wakeWordService.initialize();
      await _wakeWordService.setCustomWakeWord(_currentWakeWord);
      await _wakeWordService.setSensitivity(_sensitivity);

      // بدء الاستماع لكلمة التفعيل
      await _wakeWordService.startListening();

      _showSnackBar(
        '🎤 قل "$_currentWakeWord" الآن لاختبار الكشف (مدة الاختبار: 10 ثوانٍ)',
        Colors.green,
      );

      // انتظار 10 ثوانٍ للاختبار
      await Future.delayed(const Duration(seconds: 10));

      // إيقاف الاستماع
      await _wakeWordService.stopListening();

      _showSnackBar('✅ انتهى اختبار كلمة التفعيل', Colors.blue);
      debugPrint('✅ تم انتهاء اختبار كلمة التفعيل');
    } catch (e) {
      debugPrint('❌ خطأ في اختبار كلمة التفعيل: $e');
      _showSnackBar('❌ فشل في اختبار كلمة التفعيل: $e', Colors.red);
    }
  }

  /// اختبار المساعد التفاعلي
  void _testInteractiveAssistant() {
    try {
      debugPrint('🎤 اختبار المساعد التفاعلي من الإعدادات');

      if (!_isServiceEnabled) {
        _showSnackBar('⚠️ يجب تفعيل المساعد الصوتي أولاً', Colors.orange);
        return;
      }

      // فتح شاشة اختبار المساعد التفاعلي
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const InteractiveVoiceAssistantTestScreen(),
        ),
      );

      _showSnackBar('🎤 تم فتح المساعد التفاعلي للاختبار', Colors.blue);
    } catch (e) {
      debugPrint('❌ خطأ في فتح المساعد التفاعلي: $e');
      _showSnackBar('❌ خطأ في فتح المساعد التفاعلي: $e', Colors.red);
    }
  }

  /// فتح شاشة التشخيص
  void _openDiagnosticsScreen() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildDiagnosticsBottomSheet(),
    );
  }

  /// بناء شاشة التشخيص السفلية
  Widget _buildDiagnosticsBottomSheet() {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // مقبض السحب
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 10),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          // العنوان
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(Icons.medical_services, color: kMainColor),
                const SizedBox(width: 10),
                const Text(
                  'أدوات التشخيص والإصلاح',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
          ),
          const Divider(),
          // المحتوى
          Expanded(
            child: ListView(
              padding: const EdgeInsets.all(16),
              children: [
                _buildDiagnosticCard(
                  'فحص الأذونات',
                  'التحقق من جميع الأذونات المطلوبة',
                  Icons.security,
                  () => Navigator.pushNamed(context, '/PermissionTest'),
                ),
                _buildDiagnosticCard(
                  'اختبار بصمة الصوت',
                  'اختبار تسجيل والتحقق من بصمة الصوت',
                  Icons.fingerprint,
                  () => Navigator.pushNamed(context, '/VoiceFingerprintTest'),
                ),
                _buildDiagnosticCard(
                  'اختبار المساعد السريع',
                  'اختبار QuickAI Assistant',
                  Icons.chat,
                  () => Navigator.pushNamed(context, '/QuickAITest'),
                ),
                _buildDiagnosticCard(
                  'حالة الخدمات',
                  'عرض حالة جميع الخدمات',
                  Icons.info,
                  () => _showServiceStatusDialog(),
                ),
                _buildDiagnosticCard(
                  'إعادة تعيين فورية',
                  'إعادة تعيين المساعد الصوتي فوراً',
                  Icons.refresh,
                  () => _forceResetService(),
                ),
                _buildDiagnosticCard(
                  'إعادة تعيين الإعدادات',
                  'إعادة تعيين جميع الإعدادات للافتراضي',
                  Icons.restore,
                  () => _showResetConfirmation(),
                ),
                _buildDiagnosticCard(
                  'تنظيف البيانات',
                  'حذف البيانات المؤقتة والملفات المحفوظة',
                  Icons.cleaning_services,
                  () => _cleanupData(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة تشخيص
  Widget _buildDiagnosticCard(
    String title,
    String description,
    IconData icon,
    VoidCallback onTap,
  ) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: ListTile(
        leading: Icon(icon, color: kMainColor),
        title: Text(title),
        subtitle: Text(description),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: onTap,
      ),
    );
  }

  /// عرض حوار حالة الخدمات
  void _showServiceStatusDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حالة الخدمات'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildStatusItem('المساعد الصوتي', _isServiceEnabled),
                _buildStatusItem('بصمة الصوت', _isVoiceFingerprintRegistered),
                _buildStatusItem('كلمة التفعيل', _currentWakeWord.isNotEmpty),
                if (_permissionStatus != null) ...[
                  const Divider(),
                  const Text('الأذونات:',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                  ...(_permissionStatus!['permissions'] as Map<String, bool>)
                      .entries
                      .map((e) =>
                          _buildStatusItem(_getPermissionName(e.key), e.value)),
                ],
                if (_serviceStatus != null) ...[
                  const Divider(),
                  const Text('تفاصيل الخدمة:',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                  Text('مهيأة: ${_serviceStatus!['isGloballyInitialized']}'),
                  Text('قيد التهيئة: ${_serviceStatus!['isInitializing']}'),
                  Text('عداد المراجع: ${_serviceStatus!['referenceCount']}'),
                ],
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _refreshSettings();
            },
            child: const Text('تحديث'),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر حالة
  Widget _buildStatusItem(String title, bool status) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Icon(
            status ? Icons.check_circle : Icons.cancel,
            color: status ? Colors.green : Colors.red,
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(title),
          const Spacer(),
          Text(
            status ? 'نشط' : 'غير نشط',
            style: TextStyle(
              color: status ? Colors.green : Colors.red,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  /// الحصول على اسم الإذن
  String _getPermissionName(String permission) {
    switch (permission) {
      case 'microphone':
        return 'الميكروفون';
      case 'systemAlertWindow':
        return 'النوافذ العائمة';
      case 'ignoreBatteryOptimizations':
        return 'تحسين البطارية';
      case 'notification':
        return 'الإشعارات';
      case 'storage':
        return 'التخزين';
      default:
        return permission;
    }
  }

  /// عرض تأكيد إعادة التعيين
  void _showResetConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعادة تعيين الإعدادات'),
        content: const Text(
          'هل أنت متأكد من إعادة تعيين جميع الإعدادات؟\n'
          'سيتم فقدان جميع الإعدادات المخصصة.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _resetAllSettings();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('إعادة تعيين'),
          ),
        ],
      ),
    );
  }

  /// إعادة تعيين جميع الإعدادات
  Future<void> _resetAllSettings() async {
    try {
      // إعادة تعيين إعدادات كلمة التفعيل
      await _wakeWordService.setCustomWakeWord('hey app');
      await _wakeWordService.setSensitivity(0.7);

      // إعادة تعيين إعدادات الصوت
      await _responseEngine.setSpeechRate(0.5);
      await _responseEngine.setVolume(0.8);
      await _responseEngine.setPitch(1.0);

      // إعادة تحميل الإعدادات
      await _loadCurrentSettings();

      _showSnackBar('تم إعادة تعيين جميع الإعدادات', Colors.green);
    } catch (e) {
      _showSnackBar('خطأ في إعادة التعيين: $e', Colors.red);
    }
  }

  /// تنظيف البيانات
  Future<void> _cleanupData() async {
    try {
      // حذف بيانات مؤقتة (إذا وجدت)
      // يمكن إضافة المزيد من عمليات التنظيف هنا

      _showSnackBar('تم تنظيف البيانات بنجاح', Colors.green);
    } catch (e) {
      _showSnackBar('خطأ في تنظيف البيانات: $e', Colors.red);
    }
  }

  /// عرض حوار المساعدة
  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مساعدة المساعد الصوتي'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'كيفية استخدام المساعد الصوتي:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 10),
              Text('1. فعّل المساعد الصوتي من الإعدادات'),
              Text('2. سجّل بصمة صوتك للأمان'),
              Text('3. اختر كلمة التفعيل المناسبة'),
              Text('4. اضبط إعدادات الصوت حسب تفضيلك'),
              Text('5. اختبر المساعد للتأكد من عمله'),
              SizedBox(height: 15),
              Text(
                'نصائح:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 5),
              Text('• تحدث بوضوح عند تسجيل بصمة الصوت'),
              Text('• استخدم كلمة تفعيل سهلة النطق'),
              Text('• تأكد من منح جميع الأذونات المطلوبة'),
              Text('• اختبر المساعد في بيئة هادئة'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('فهمت'),
          ),
        ],
      ),
    );
  }

  /// اختبار المساعد الصوتي
  void _testVoiceAssistant() {
    if (!_isServiceEnabled) {
      _showSnackBar('يجب تفعيل المساعد الصوتي أولاً', Colors.orange);
      return;
    }

    Navigator.pushNamed(context, '/InteractiveVoiceAssistant');
  }
}
