import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// نموذج لتخزين بيانات حساب المستخدم
class UserAccount {
  final String email;
  final String userId;
  final String displayName;
  final String? photoUrl;
  final DateTime lastLogin;

  UserAccount({
    required this.email,
    required this.userId,
    required this.displayName,
    this.photoUrl,
    required this.lastLogin,
  });

  factory UserAccount.fromJson(Map<String, dynamic> json) {
    return UserAccount(
      email: json['email'] as String,
      userId: json['userId'] as String,
      displayName: json['displayName'] as String,
      photoUrl: json['photoUrl'] as String?,
      lastLogin: DateTime.parse(json['lastLogin'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'userId': userId,
      'displayName': displayName,
      'photoUrl': photoUrl,
      'lastLogin': lastLogin.toIso8601String(),
    };
  }
}

/// خدمة إدارة حسابات المستخدمين
class UserAccountsService {
  static const String _storageKey = 'saved_user_accounts';
  
  /// الحصول على قائمة الحسابات المحفوظة
  static Future<List<UserAccount>> getSavedAccounts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String? accountsJson = prefs.getString(_storageKey);
      
      if (accountsJson == null || accountsJson.isEmpty) {
        return [];
      }
      
      final List<dynamic> accountsList = jsonDecode(accountsJson);
      return accountsList
          .map((account) => UserAccount.fromJson(account))
          .toList();
    } catch (e) {
      debugPrint('خطأ في استرجاع الحسابات المحفوظة: $e');
      return [];
    }
  }
  
  /// حفظ حساب مستخدم جديد أو تحديث حساب موجود
  static Future<bool> saveAccount(UserAccount account) async {
    try {
      final accounts = await getSavedAccounts();
      
      // البحث عن الحساب بنفس البريد الإلكتروني
      final existingIndex = accounts.indexWhere((a) => a.userId == account.userId);
      
      if (existingIndex >= 0) {
        // تحديث الحساب الموجود
        accounts[existingIndex] = account;
      } else {
        // إضافة حساب جديد
        accounts.add(account);
      }
      
      // ترتيب الحسابات حسب آخر تسجيل دخول
      accounts.sort((a, b) => b.lastLogin.compareTo(a.lastLogin));
      
      // حفظ القائمة المحدثة
      final prefs = await SharedPreferences.getInstance();
      final accountsJson = jsonEncode(accounts.map((a) => a.toJson()).toList());
      return await prefs.setString(_storageKey, accountsJson);
    } catch (e) {
      debugPrint('خطأ في حفظ الحساب: $e');
      return false;
    }
  }
  
  /// حذف حساب مستخدم
  static Future<bool> removeAccount(String userId) async {
    try {
      final accounts = await getSavedAccounts();
      accounts.removeWhere((account) => account.userId == userId);
      
      final prefs = await SharedPreferences.getInstance();
      final accountsJson = jsonEncode(accounts.map((a) => a.toJson()).toList());
      return await prefs.setString(_storageKey, accountsJson);
    } catch (e) {
      debugPrint('خطأ في حذف الحساب: $e');
      return false;
    }
  }
  
  /// الحصول على الحساب الحالي
  static Future<UserAccount?> getCurrentAccount() async {
    try {
      final accounts = await getSavedAccounts();
      if (accounts.isEmpty) {
        return null;
      }
      
      // نفترض أن أول حساب في القائمة هو الحساب الحالي (الأحدث تسجيل دخول)
      return accounts.first;
    } catch (e) {
      debugPrint('خطأ في الحصول على الحساب الحالي: $e');
      return null;
    }
  }
}
