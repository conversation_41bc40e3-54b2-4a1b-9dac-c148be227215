// ignore_for_file: use_build_context_synchronously, deprecated_member_use

import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/GlobalComponents/button_global.dart';
import 'package:mobile_pos/Screens/Authentication/login_form.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/generated/l10n.dart' as lang;
import 'package:mobile_pos/model/user_role_model.dart';
import 'package:nb_utils/nb_utils.dart';

class AddUserRole extends StatefulWidget {
  const AddUserRole({super.key});

  @override
  // ignore: library_private_types_in_public_api
  _AddUserRoleState createState() => _AddUserRoleState();
}

class _AddUserRoleState extends State<AddUserRole> {
  GlobalKey<FormState> globalKey = GlobalKey<FormState>();
  bool validateAndSave() {
    final form = globalKey.currentState;
    if (form!.validate()) {
      form.save();
      return true;
    }
    return false;
  }

  bool allPermissions = false;
  bool salePermission = false;
  bool partiesPermission = false;
  bool purchasePermission = false;
  bool productPermission = false;
  bool settlementPermission = false;
  //settlementPermission
  bool profileEditPermission = false;
  bool addExpensePermission = false;
  bool lossProfitPermission = false;
  bool dueListPermission = false;
  bool stockPermission = false;
  bool reportsPermission = false;
  bool salesListPermission = false;
  bool purchaseListPermission = false;
  // الصلاحيات الجديدة - الذكاء الاصطناعي
  bool aiChatPermission = false;
  bool aiAssistantPermission = false;
  bool voiceAssistantPermission = false;
  // الخزينة
  bool treasuryPermission = false;
  bool cashBoxPermission = false;
  // إدارة التوصيل
  bool deliveryManagementPermission = false;
  // الموارد البشرية
  bool hrmPermission = false;
  bool employeesPermission = false;
  bool designationPermission = false;
  bool salariesPermission = false;
  // التقارير المتقدمة
  bool financialReportsPermission = false;
  bool salesTargetsPermission = false;
  bool taxReportsPermission = false;
  // الإعدادات المتقدمة
  bool userLogsPermission = false;
  bool notificationsPermission = false;
  bool warrantyPermission = false;
  bool settingsPermission = false;
  bool userManagementPermission = false;
  // صلاحيات إضافية
  bool ledgerPermission = false;
  // فلاتر المياه
  bool waterFiltersPermission = false;
  // فلاتر المياه - صلاحيات مفصلة
  bool waterFilterProductsPermission = false;
  bool waterFilterCustomersPermission = false;
  bool waterFilterSystemsPermission = false;
  bool waterFilterMaintenancePermission = false;
  bool waterFilterInstallmentsPermission = false;
  bool waterFilterReportsPermission = false;
  // حالة التفعيل
  bool isActive = true;

  // نظام الرولز الجاهزة
  String selectedRole = 'Custom';
  List<String> availableRoles = [
    'Custom',
    'General Manager',
    'Sales Manager',
    'Cashier',
    'Warehouse Employee',
    'Accountant',
  ];

  // أسماء الرولز بالعربية للعرض
  Map<String, String> roleDisplayNames = {
    'Custom': 'مخصص',
    'General Manager': 'مدير عام',
    'Sales Manager': 'مدير مبيعات',
    'Cashier': 'مندوب مبيعات',
    'Warehouse Employee': 'موظف مخزن',
    'Accountant': 'محاسب',
  };
  TextEditingController emailController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  TextEditingController confirmPasswordController = TextEditingController();
  TextEditingController titleController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Consumer(builder: (context, ref, __) {
      return Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          backgroundColor: Colors.white,
          title: Text(
            lang.S.of(context).addUserRole,
            style: GoogleFonts.poppins(
              color: Colors.black,
            ),
          ),
          centerTitle: true,
          iconTheme: const IconThemeData(color: Colors.black),
          elevation: 0.0,
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(10.0),
            child: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(width: 0.5, color: kGreyTextColor),
                      borderRadius: const BorderRadius.all(Radius.circular(10)),
                    ),
                    child: Column(
                      children: [
                        ///_______Role Selection_________________________________________
                        Padding(
                          padding: const EdgeInsets.all(10.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'اختر الدور الوظيفي:',
                                style: GoogleFonts.cairo(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: kMainColor,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Container(
                                width: double.infinity,
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 12),
                                decoration: BoxDecoration(
                                  border:
                                      Border.all(color: kBorderColorTextField),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: DropdownButtonHideUnderline(
                                  child: DropdownButton<String>(
                                    value: selectedRole,
                                    isExpanded: true,
                                    items: availableRoles.map((String role) {
                                      return DropdownMenuItem<String>(
                                        value: role,
                                        child: Text(
                                          roleDisplayNames[role] ?? role,
                                          style:
                                              GoogleFonts.cairo(fontSize: 14),
                                        ),
                                      );
                                    }).toList(),
                                    onChanged: (String? newValue) {
                                      if (newValue != null) {
                                        setState(() {
                                          selectedRole = newValue;
                                          _applyRolePermissions(newValue);
                                        });
                                      }
                                    },
                                  ),
                                ),
                              ),
                              const SizedBox(height: 16),
                            ],
                          ),
                        ),

                        ///_______all_&_sale____________________________________________
                        Row(
                          children: [
                            ///_______all__________________________
                            SizedBox(
                              width: context.width() / 2 - 20,
                              child: CheckboxListTile(
                                value: allPermissions,
                                onChanged: (value) {
                                  if (value == true) {
                                    setState(() {
                                      allPermissions = value!;
                                      // الصلاحيات الأساسية
                                      salePermission = true;
                                      partiesPermission = true;
                                      purchasePermission = true;
                                      productPermission = true;
                                      profileEditPermission = true;
                                      addExpensePermission = true;
                                      lossProfitPermission = true;
                                      dueListPermission = true;
                                      stockPermission = true;
                                      reportsPermission = true;
                                      salesListPermission = true;
                                      purchaseListPermission = true;
                                      // الصلاحيات الجديدة - الذكاء الاصطناعي
                                      aiChatPermission = true;
                                      aiAssistantPermission = true;
                                      voiceAssistantPermission = true;
                                      // الخزينة
                                      treasuryPermission = true;
                                      cashBoxPermission = true;
                                      // إدارة التوصيل
                                      deliveryManagementPermission = true;
                                      // الموارد البشرية
                                      hrmPermission = true;
                                      employeesPermission = true;
                                      designationPermission = true;
                                      salariesPermission = true;
                                      // التقارير المتقدمة
                                      financialReportsPermission = true;
                                      salesTargetsPermission = true;
                                      taxReportsPermission = true;
                                      // الإعدادات المتقدمة
                                      userLogsPermission = true;
                                      notificationsPermission = true;
                                      warrantyPermission = true;
                                      settingsPermission = true;
                                      userManagementPermission = true;
                                      // صلاحيات إضافية
                                      ledgerPermission = true;
                                      // فلاتر المياه
                                      waterFiltersPermission = true;
                                      // فلاتر المياه - صلاحيات مفصلة
                                      waterFilterProductsPermission = true;
                                      waterFilterCustomersPermission = true;
                                      waterFilterSystemsPermission = true;
                                      waterFilterMaintenancePermission = true;
                                      waterFilterInstallmentsPermission = true;
                                      waterFilterReportsPermission = true;
                                    });
                                  } else {
                                    setState(() {
                                      allPermissions = value!;
                                      salePermission = false;
                                      partiesPermission = false;
                                      purchasePermission = false;
                                      productPermission = false;
                                      profileEditPermission = false;
                                      addExpensePermission = false;
                                      lossProfitPermission = false;
                                      dueListPermission = false;
                                      stockPermission = false;
                                      reportsPermission = false;
                                      salesListPermission = false;
                                      purchaseListPermission = false;
                                      // الصلاحيات الجديدة - الذكاء الاصطناعي
                                      aiChatPermission = false;
                                      aiAssistantPermission = false;
                                      voiceAssistantPermission = false;
                                      // الخزينة
                                      treasuryPermission = false;
                                      cashBoxPermission = false;
                                      // إدارة التوصيل
                                      deliveryManagementPermission = false;
                                      // الموارد البشرية
                                      hrmPermission = false;
                                      employeesPermission = false;
                                      designationPermission = false;
                                      salariesPermission = false;
                                      // التقارير المتقدمة
                                      financialReportsPermission = false;
                                      salesTargetsPermission = false;
                                      taxReportsPermission = false;
                                      // الإعدادات المتقدمة
                                      userLogsPermission = false;
                                      notificationsPermission = false;
                                      warrantyPermission = false;
                                      settingsPermission = false;
                                      userManagementPermission = false;
                                      // صلاحيات إضافية
                                      ledgerPermission = false;
                                      // فلاتر المياه
                                      waterFiltersPermission = false;
                                      // فلاتر المياه - صلاحيات مفصلة
                                      waterFilterProductsPermission = false;
                                      waterFilterCustomersPermission = false;
                                      waterFilterSystemsPermission = false;
                                      waterFilterMaintenancePermission = false;
                                      waterFilterInstallmentsPermission = false;
                                      waterFilterReportsPermission = false;
                                    });
                                  }
                                },
                                title: Text(lang.S.of(context).all),
                              ),
                            ),
                          ],
                        ),

                        ///_______Edit Profile_&_sale____________________________________________
                        Row(
                          children: [
                            ///_______Edit_Profile_________________________
                            Expanded(
                              child: CheckboxListTile(
                                value: profileEditPermission,
                                onChanged: (value) {
                                  setState(() {
                                    profileEditPermission = value!;
                                  });
                                },
                                title: Text(lang.S.of(context).profileEdit),
                              ),
                            ),

                            ///______sales____________________________
                            Expanded(
                              child: CheckboxListTile(
                                value: salePermission,
                                onChanged: (value) {
                                  setState(() {
                                    salePermission = value!;
                                  });
                                },
                                title: Text(lang.S.of(context).sales),
                              ),
                            ),
                          ],
                        ),

                        ///_____parties_&_Purchase_________________________________________
                        Row(
                          children: [
                            Expanded(
                              child: CheckboxListTile(
                                value: partiesPermission,
                                onChanged: (value) {
                                  setState(() {
                                    partiesPermission = value!;
                                  });
                                },
                                title: Text(lang.S.of(context).parties),
                              ),
                            ),
                            Expanded(
                              child: CheckboxListTile(
                                value: purchasePermission,
                                onChanged: (value) {
                                  setState(() {
                                    purchasePermission = value!;
                                  });
                                },
                                title: Text(lang.S.of(context).purchase),
                              ),
                            ),
                          ],
                        ),

                        ///_____Product_&_DueList_________________________________________
                        Row(
                          children: [
                            Expanded(
                              child: CheckboxListTile(
                                value: productPermission,
                                onChanged: (value) {
                                  setState(() {
                                    productPermission = value!;
                                  });
                                },
                                title: Text(lang.S.of(context).product),
                              ),
                            ),
                            Expanded(
                              child: CheckboxListTile(
                                value: dueListPermission,
                                onChanged: (value) {
                                  setState(() {
                                    dueListPermission = value!;
                                  });
                                },
                                title: Text(lang.S.of(context).dueList),
                              ),
                            ),
                          ],
                        ),

                        ///_____Stock_&_Reports_________________________________________
                        Row(
                          children: [
                            Expanded(
                              child: CheckboxListTile(
                                value: stockPermission,
                                onChanged: (value) {
                                  setState(() {
                                    stockPermission = value!;
                                  });
                                },
                                title: Text(lang.S.of(context).stocks),
                              ),
                            ),
                            Expanded(
                              child: CheckboxListTile(
                                value: reportsPermission,
                                onChanged: (value) {
                                  setState(() {
                                    reportsPermission = value!;
                                  });
                                },
                                title: Text(lang.S.of(context).reports),
                              ),
                            ),
                          ],
                        ),

                        ///_____SalesList_&_Purchase List_________________________________________
                        Row(
                          children: [
                            Expanded(
                              child: CheckboxListTile(
                                value: salesListPermission,
                                onChanged: (value) {
                                  setState(() {
                                    salesListPermission = value!;
                                  });
                                },
                                title: Text(lang.S.of(context).salesList),
                              ),
                            ),
                            Expanded(
                              child: CheckboxListTile(
                                value: purchaseListPermission,
                                onChanged: (value) {
                                  setState(() {
                                    purchaseListPermission = value!;
                                  });
                                },
                                title: Text(lang.S.of(context).purchaseList),
                              ),
                            ),
                          ],
                        ),

                        ///_____LossProfit_&_Expense_________________________________________
                        Row(
                          children: [
                            Expanded(
                              child: CheckboxListTile(
                                value: lossProfitPermission,
                                onChanged: (value) {
                                  setState(() {
                                    lossProfitPermission = value!;
                                  });
                                },
                                title: Text(lang.S.of(context).lossOrProfit),
                              ),
                            ),
                            Expanded(
                              child: CheckboxListTile(
                                value: addExpensePermission,
                                onChanged: (value) {
                                  setState(() {
                                    addExpensePermission = value!;
                                  });
                                },
                                title: Text(lang.S.of(context).expense),
                              ),
                            ),
                          ],
                        ),

                        ///_____Ledger_&_AI Chat_________________________________________
                        Row(
                          children: [
                            Expanded(
                              child: CheckboxListTile(
                                value: ledgerPermission,
                                onChanged: (value) {
                                  setState(() {
                                    ledgerPermission = value!;
                                  });
                                },
                                title: const Text('دفتر الأستاذ'),
                              ),
                            ),
                            Expanded(
                              child: CheckboxListTile(
                                value: aiChatPermission,
                                onChanged: (value) {
                                  setState(() {
                                    aiChatPermission = value!;
                                  });
                                },
                                title: const Text('الدردشة الذكية'),
                              ),
                            ),
                          ],
                        ),

                        ///_____AI Assistant_&_Voice Assistant_________________________________________
                        Row(
                          children: [
                            Expanded(
                              child: CheckboxListTile(
                                value: aiAssistantPermission,
                                onChanged: (value) {
                                  setState(() {
                                    aiAssistantPermission = value!;
                                  });
                                },
                                title: const Text('المساعد الذكي'),
                              ),
                            ),
                            Expanded(
                              child: CheckboxListTile(
                                value: voiceAssistantPermission,
                                onChanged: (value) {
                                  setState(() {
                                    voiceAssistantPermission = value!;
                                  });
                                },
                                title: const Text('المساعد الصوتي'),
                              ),
                            ),
                          ],
                        ),

                        ///_____Treasury_&_Cash Box_________________________________________
                        Row(
                          children: [
                            Expanded(
                              child: CheckboxListTile(
                                value: treasuryPermission,
                                onChanged: (value) {
                                  setState(() {
                                    treasuryPermission = value!;
                                  });
                                },
                                title: const Text('الخزينة'),
                              ),
                            ),
                            Expanded(
                              child: CheckboxListTile(
                                value: cashBoxPermission,
                                onChanged: (value) {
                                  setState(() {
                                    cashBoxPermission = value!;
                                  });
                                },
                                title: const Text('صندوق النقدية'),
                              ),
                            ),
                          ],
                        ),

                        ///_____Delivery Management_&_HRM_________________________________________
                        Row(
                          children: [
                            Expanded(
                              child: CheckboxListTile(
                                value: deliveryManagementPermission,
                                onChanged: (value) {
                                  setState(() {
                                    deliveryManagementPermission = value!;
                                  });
                                },
                                title: const Text('إدارة التوصيل'),
                              ),
                            ),
                            Expanded(
                              child: CheckboxListTile(
                                value: hrmPermission,
                                onChanged: (value) {
                                  setState(() {
                                    hrmPermission = value!;
                                  });
                                },
                                title: const Text('الموارد البشرية'),
                              ),
                            ),
                          ],
                        ),

                        ///_____Employees_&_Designation_________________________________________
                        Row(
                          children: [
                            Expanded(
                              child: CheckboxListTile(
                                value: employeesPermission,
                                onChanged: (value) {
                                  setState(() {
                                    employeesPermission = value!;
                                  });
                                },
                                title: const Text('الموظفين'),
                              ),
                            ),
                            Expanded(
                              child: CheckboxListTile(
                                value: designationPermission,
                                onChanged: (value) {
                                  setState(() {
                                    designationPermission = value!;
                                  });
                                },
                                title: const Text('المناصب'),
                              ),
                            ),
                          ],
                        ),

                        ///_____Salaries_&_Financial Reports_________________________________________
                        Row(
                          children: [
                            Expanded(
                              child: CheckboxListTile(
                                value: salariesPermission,
                                onChanged: (value) {
                                  setState(() {
                                    salariesPermission = value!;
                                  });
                                },
                                title: const Text('الرواتب'),
                              ),
                            ),
                            Expanded(
                              child: CheckboxListTile(
                                value: financialReportsPermission,
                                onChanged: (value) {
                                  setState(() {
                                    financialReportsPermission = value!;
                                  });
                                },
                                title: const Text('التقارير المالية'),
                              ),
                            ),
                          ],
                        ),

                        ///_____Sales Targets_&_Tax Reports_________________________________________
                        Row(
                          children: [
                            Expanded(
                              child: CheckboxListTile(
                                value: salesTargetsPermission,
                                onChanged: (value) {
                                  setState(() {
                                    salesTargetsPermission = value!;
                                  });
                                },
                                title: const Text('أهداف المبيعات'),
                              ),
                            ),
                            Expanded(
                              child: CheckboxListTile(
                                value: taxReportsPermission,
                                onChanged: (value) {
                                  setState(() {
                                    taxReportsPermission = value!;
                                  });
                                },
                                title: const Text('تقارير الضرائب'),
                              ),
                            ),
                          ],
                        ),

                        ///_____User Logs_&_Notifications_________________________________________
                        Row(
                          children: [
                            Expanded(
                              child: CheckboxListTile(
                                value: userLogsPermission,
                                onChanged: (value) {
                                  setState(() {
                                    userLogsPermission = value!;
                                  });
                                },
                                title: const Text('سجلات المستخدمين'),
                              ),
                            ),
                            Expanded(
                              child: CheckboxListTile(
                                value: notificationsPermission,
                                onChanged: (value) {
                                  setState(() {
                                    notificationsPermission = value!;
                                  });
                                },
                                title: const Text('الإشعارات'),
                              ),
                            ),
                          ],
                        ),

                        ///_____Warranty_&_Settings_________________________________________
                        Row(
                          children: [
                            Expanded(
                              child: CheckboxListTile(
                                value: warrantyPermission,
                                onChanged: (value) {
                                  setState(() {
                                    warrantyPermission = value!;
                                  });
                                },
                                title: const Text('الضمان'),
                              ),
                            ),
                            Expanded(
                              child: CheckboxListTile(
                                value: settingsPermission,
                                onChanged: (value) {
                                  setState(() {
                                    settingsPermission = value!;
                                  });
                                },
                                title: const Text('الإعدادات'),
                              ),
                            ),
                          ],
                        ),

                        ///_____User Management_&_Water Filters_________________________________________
                        Row(
                          children: [
                            Expanded(
                              child: CheckboxListTile(
                                value: userManagementPermission,
                                onChanged: (value) {
                                  setState(() {
                                    userManagementPermission = value!;
                                  });
                                },
                                title: const Text('إدارة المستخدمين'),
                              ),
                            ),
                            Expanded(
                              child: CheckboxListTile(
                                value: waterFiltersPermission,
                                onChanged: (value) {
                                  setState(() {
                                    waterFiltersPermission = value!;
                                  });
                                },
                                title: const Text('فلاتر المياه (عام)'),
                              ),
                            ),
                          ],
                        ),

                        // قسم فلاتر المياه المفصل
                        const Padding(
                          padding: EdgeInsets.all(8.0),
                          child: Text(
                            'فلاتر المياه - صلاحيات مفصلة',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.blue,
                            ),
                          ),
                        ),

                        ///_____Water Filter Products_&_Customers_________________________________________
                        Row(
                          children: [
                            Expanded(
                              child: CheckboxListTile(
                                value: waterFilterProductsPermission,
                                onChanged: (value) {
                                  setState(() {
                                    waterFilterProductsPermission = value!;
                                  });
                                },
                                title: const Text('منتجات فلاتر المياه'),
                              ),
                            ),
                            Expanded(
                              child: CheckboxListTile(
                                value: waterFilterCustomersPermission,
                                onChanged: (value) {
                                  setState(() {
                                    waterFilterCustomersPermission = value!;
                                  });
                                },
                                title: const Text('عملاء فلاتر المياه'),
                              ),
                            ),
                          ],
                        ),

                        ///_____Water Filter Systems_&_Maintenance_________________________________________
                        Row(
                          children: [
                            Expanded(
                              child: CheckboxListTile(
                                value: waterFilterSystemsPermission,
                                onChanged: (value) {
                                  setState(() {
                                    waterFilterSystemsPermission = value!;
                                  });
                                },
                                title: const Text('أنظمة فلاتر المياه'),
                              ),
                            ),
                            Expanded(
                              child: CheckboxListTile(
                                value: waterFilterMaintenancePermission,
                                onChanged: (value) {
                                  setState(() {
                                    waterFilterMaintenancePermission = value!;
                                  });
                                },
                                title: const Text('صيانة فلاتر المياه'),
                              ),
                            ),
                          ],
                        ),

                        ///_____Water Filter Installments_&_Reports_________________________________________
                        Row(
                          children: [
                            Expanded(
                              child: CheckboxListTile(
                                value: waterFilterInstallmentsPermission,
                                onChanged: (value) {
                                  setState(() {
                                    waterFilterInstallmentsPermission = value!;
                                  });
                                },
                                title: const Text('أقساط فلاتر المياه'),
                              ),
                            ),
                            Expanded(
                              child: CheckboxListTile(
                                value: waterFilterReportsPermission,
                                onChanged: (value) {
                                  setState(() {
                                    waterFilterReportsPermission = value!;
                                  });
                                },
                                title: const Text('تقارير فلاتر المياه'),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),

                ///___________Text_fields_____________________________________________
                Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: Form(
                    key: globalKey,
                    child: Column(
                      children: [
                        ///__________username_________________________________________________________
                        AppTextField(
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'اسم المستخدم لا يمكن أن يكون فارغًا';
                            } else if (value.contains('@')) {
                              return 'الرجاء إدخال اسم المستخدم فقط بدون @amrdev.com';
                            } else if (value.contains(' ')) {
                              return 'اسم المستخدم لا يمكن أن يحتوي على مسافات';
                            }
                            return null;
                          },
                          showCursor: true,
                          controller: emailController,
                          // cursorColor: kTitleColor,
                          decoration: kInputDecoration.copyWith(
                            labelText: 'اسم المستخدم',
                            // labelStyle: kTextStyle.copyWith(color: kTitleColor),
                            hintText: 'أدخل اسم المستخدم',
                            // hintStyle: kTextStyle.copyWith(color: kLitGreyColor),
                            contentPadding: const EdgeInsets.all(10.0),
                            enabledBorder: const OutlineInputBorder(
                              borderRadius: BorderRadius.all(
                                Radius.circular(4.0),
                              ),
                              borderSide: BorderSide(
                                  color: kBorderColorTextField, width: 1),
                            ),
                            errorBorder: const OutlineInputBorder(
                                borderSide: BorderSide(color: Colors.red)),
                            focusedBorder: const OutlineInputBorder(
                              borderRadius:
                                  BorderRadius.all(Radius.circular(4.0)),
                              borderSide: BorderSide(
                                  color: kBorderColorTextField, width: 2),
                            ),
                          ),
                          textFieldType: TextFieldType.NAME,
                        ),
                        const SizedBox(height: 20.0),

                        ///______password___________________________________________________________
                        AppTextField(
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Password can\'t be empty';
                            } else if (value.length < 4) {
                              return 'Please enter a bigger password';
                            }
                            return null;
                          },
                          controller: passwordController,
                          showCursor: true,
                          // cursorColor: kTitleColor,
                          decoration: kInputDecoration.copyWith(
                            labelText: lang.S.of(context).password,
                            floatingLabelAlignment:
                                FloatingLabelAlignment.start,
                            // labelStyle: kTextStyle.copyWith(color: kTitleColor),
                            hintText: lang.S.of(context).enterYourPassword,
                            // hintStyle: kTextStyle.copyWith(color: kLitGreyColor),
                            contentPadding: const EdgeInsets.all(10.0),
                            enabledBorder: const OutlineInputBorder(
                              borderRadius: BorderRadius.all(
                                Radius.circular(4.0),
                              ),
                              borderSide: BorderSide(
                                  color: kBorderColorTextField, width: 1),
                            ),
                            errorBorder: const OutlineInputBorder(
                                borderSide: BorderSide(color: Colors.red)),
                            focusedBorder: const OutlineInputBorder(
                              borderRadius:
                                  BorderRadius.all(Radius.circular(4.0)),
                              borderSide: BorderSide(
                                  color: kBorderColorTextField, width: 2),
                            ),
                          ),
                          textFieldType: TextFieldType.PASSWORD,
                        ),

                        ///________retype_email____________________________________________________
                        const SizedBox(height: 20.0),
                        AppTextField(
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Password can\'t be empty';
                            } else if (value != passwordController.text) {
                              return 'Password and confirm password does not match';
                            } else if (value.length < 4) {
                              return 'Please enter a bigger password';
                            }
                            return null;
                          },
                          controller: confirmPasswordController,
                          showCursor: true,
                          // cursorColor: kTitleColor,
                          decoration: kInputDecoration.copyWith(
                            labelText: lang.S.of(context).password,
                            floatingLabelAlignment:
                                FloatingLabelAlignment.start,
                            // labelStyle: kTextStyle.copyWith(color: kTitleColor),
                            hintText: lang.S.of(context).enterYourPassword,
                            // hintStyle: kTextStyle.copyWith(color: kLitGreyColor),
                            contentPadding: const EdgeInsets.all(10.0),
                            errorBorder: const OutlineInputBorder(
                                borderSide: BorderSide(color: Colors.red)),
                            enabledBorder: const OutlineInputBorder(
                              borderRadius: BorderRadius.all(
                                Radius.circular(4.0),
                              ),
                              borderSide: BorderSide(
                                  color: kBorderColorTextField, width: 1),
                            ),
                            focusedBorder: const OutlineInputBorder(
                              borderRadius:
                                  BorderRadius.all(Radius.circular(4.0)),
                              borderSide: BorderSide(
                                  color: kBorderColorTextField, width: 2),
                            ),
                          ),
                          textFieldType: TextFieldType.PASSWORD,
                        ),

                        ///__________Title_________________________________________________________
                        const SizedBox(height: 20.0),
                        AppTextField(
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'User title can\'n be empty';
                            }
                            return null;
                          },
                          showCursor: true,
                          controller: titleController,
                          decoration: kInputDecoration.copyWith(
                            labelText: lang.S.of(context).userTitle,
                            hintText: lang.S.of(context).enterUserTitle,
                            contentPadding: const EdgeInsets.all(10.0),
                            errorBorder: const OutlineInputBorder(
                                borderSide: BorderSide(color: Colors.red)),
                            enabledBorder: const OutlineInputBorder(
                              borderRadius: BorderRadius.all(
                                Radius.circular(4.0),
                              ),
                              borderSide: BorderSide(
                                  color: kBorderColorTextField, width: 1),
                            ),
                            focusedBorder: const OutlineInputBorder(
                              borderRadius:
                                  BorderRadius.all(Radius.circular(4.0)),
                              borderSide: BorderSide(
                                  color: kBorderColorTextField, width: 2),
                            ),
                          ),
                          textFieldType: TextFieldType.EMAIL,
                        ),
                        const SizedBox(height: 20.0),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        bottomNavigationBar: Padding(
          padding: const EdgeInsets.all(10.0),
          child: ButtonGlobalWithoutIcon(
              buttontext: lang.S.of(context).create,
              buttonDecoration: kButtonDecoration.copyWith(color: kMainColor),
              onPressed: (() {
                if (salePermission ||
                    partiesPermission ||
                    purchasePermission ||
                    productPermission ||
                    profileEditPermission ||
                    addExpensePermission ||
                    lossProfitPermission ||
                    dueListPermission ||
                    stockPermission ||
                    reportsPermission ||
                    salesListPermission ||
                    purchaseListPermission ||
                    ledgerPermission ||
                    aiChatPermission ||
                    aiAssistantPermission ||
                    voiceAssistantPermission ||
                    treasuryPermission ||
                    cashBoxPermission ||
                    deliveryManagementPermission ||
                    hrmPermission ||
                    employeesPermission ||
                    designationPermission ||
                    salariesPermission ||
                    financialReportsPermission ||
                    salesTargetsPermission ||
                    taxReportsPermission ||
                    userLogsPermission ||
                    notificationsPermission ||
                    warrantyPermission ||
                    settingsPermission ||
                    userManagementPermission ||
                    waterFiltersPermission ||
                    waterFilterProductsPermission ||
                    waterFilterCustomersPermission ||
                    waterFilterSystemsPermission ||
                    waterFilterMaintenancePermission ||
                    waterFilterInstallmentsPermission ||
                    waterFilterReportsPermission) {
                  if (validateAndSave()) {
                    // إضافة @amrdev.com إلى اسم المستخدم
                    String fullEmail =
                        "${emailController.text.trim()}@amrdev.com";

                    debugPrint('💾 Creating user with permissions:');
                    debugPrint(
                        'Sale: $salePermission, Settings: $settingsPermission');
                    debugPrint('User Management: $userManagementPermission');

                    UserRoleModel userRoleData = UserRoleModel(
                      email: fullEmail,
                      userTitle: titleController.text,
                      databaseId: FirebaseAuth.instance.currentUser!.uid,
                      salePermission: salePermission,
                      partiesPermission: partiesPermission,
                      purchasePermission: purchasePermission,
                      productPermission: productPermission,
                      //settlementPermission: settlementPermission,
                      profileEditPermission: profileEditPermission,
                      addExpensePermission: addExpensePermission,
                      lossProfitPermission: lossProfitPermission,
                      dueListPermission: dueListPermission,
                      stockPermission: stockPermission,
                      reportsPermission: reportsPermission,
                      salesListPermission: salesListPermission,
                      purchaseListPermission: purchaseListPermission,
                      // الصلاحيات الجديدة - الذكاء الاصطناعي
                      aiChatPermission: aiChatPermission,
                      aiAssistantPermission: aiAssistantPermission,
                      voiceAssistantPermission: voiceAssistantPermission,
                      // الخزينة
                      treasuryPermission: treasuryPermission,
                      cashBoxPermission: cashBoxPermission,
                      // إدارة التوصيل
                      deliveryManagementPermission:
                          deliveryManagementPermission,
                      // الموارد البشرية
                      hrmPermission: hrmPermission,
                      employeesPermission: employeesPermission,
                      designationPermission: designationPermission,
                      salariesPermission: salariesPermission,
                      // التقارير المتقدمة
                      financialReportsPermission: financialReportsPermission,
                      salesTargetsPermission: salesTargetsPermission,
                      taxReportsPermission: taxReportsPermission,
                      // الإعدادات المتقدمة
                      userLogsPermission: userLogsPermission,
                      notificationsPermission: notificationsPermission,
                      warrantyPermission: warrantyPermission,
                      settingsPermission: settingsPermission,
                      userManagementPermission: userManagementPermission,
                      // صلاحيات إضافية
                      ledgerPermission: ledgerPermission,
                      // فلاتر المياه
                      waterFiltersPermission: waterFiltersPermission,
                      // فلاتر المياه - صلاحيات مفصلة
                      waterFilterProductsPermission:
                          waterFilterProductsPermission,
                      waterFilterCustomersPermission:
                          waterFilterCustomersPermission,
                      waterFilterSystemsPermission:
                          waterFilterSystemsPermission,
                      waterFilterMaintenancePermission:
                          waterFilterMaintenancePermission,
                      waterFilterInstallmentsPermission:
                          waterFilterInstallmentsPermission,
                      waterFilterReportsPermission:
                          waterFilterReportsPermission,
                      // حالة التفعيل
                      isActive: isActive,
                    );
                    // print(FirebaseAuth.instance.currentUser!.uid);
                    signUp(
                      context: context,
                      email: fullEmail,
                      password: passwordController.text,
                      ref: ref,
                      userRoleModel: userRoleData,
                    );
                  }
                } else {
                  EasyLoading.showError('You Have To Give Permission');
                }
              }),
              buttonTextColor: Colors.white),
        ),
      );
    });
  }

  /// تطبيق صلاحيات الدور المحدد
  void _applyRolePermissions(String role) {
    debugPrint('🎭 Applying role permissions for: $role');
    // إعادة تعيين جميع الصلاحيات إلى false أولاً
    _resetAllPermissions();

    switch (role) {
      case 'General Manager':
        _setAllPermissions(true);
        debugPrint('✅ Applied General Manager permissions');
        break;

      case 'Sales Manager':
        salePermission = true;
        partiesPermission = true;
        salesListPermission = true;
        reportsPermission = true;
        dueListPermission = true;
        addExpensePermission = true;
        lossProfitPermission = true;
        aiChatPermission = true;
        debugPrint('✅ Applied Sales Manager permissions');
        break;

      case 'Cashier':
        salePermission = true;
        partiesPermission = true;
        dueListPermission = true;
        addExpensePermission = true;
        treasuryPermission = true;
        cashBoxPermission = true;
        debugPrint('✅ Applied Cashier permissions');
        break;

      case 'Warehouse Employee':
        productPermission = true;
        stockPermission = true;
        purchasePermission = true;
        purchaseListPermission = true;
        deliveryManagementPermission = true;
        debugPrint('✅ Applied Warehouse Employee permissions');
        break;

      case 'Accountant':
        reportsPermission = true;
        lossProfitPermission = true;
        ledgerPermission = true;
        financialReportsPermission = true;
        salesListPermission = true;
        purchaseListPermission = true;
        addExpensePermission = true;
        treasuryPermission = true;
        debugPrint('✅ Applied Accountant permissions');
        break;

      case 'Custom':
        // لا تغيير - يترك للمستخدم اختيار الصلاحيات يدوياً
        debugPrint('✅ Custom role selected - no auto permissions');
        break;
    }
  }

  /// إعادة تعيين جميع الصلاحيات إلى false
  void _resetAllPermissions() {
    allPermissions = false;
    salePermission = false;
    partiesPermission = false;
    purchasePermission = false;
    productPermission = false;
    profileEditPermission = false;
    addExpensePermission = false;
    lossProfitPermission = false;
    dueListPermission = false;
    stockPermission = false;
    reportsPermission = false;
    salesListPermission = false;
    purchaseListPermission = false;
    ledgerPermission = false;
    aiChatPermission = false;
    aiAssistantPermission = false;
    voiceAssistantPermission = false;
    treasuryPermission = false;
    cashBoxPermission = false;
    deliveryManagementPermission = false;
    hrmPermission = false;
    employeesPermission = false;
    designationPermission = false;
    salariesPermission = false;
    financialReportsPermission = false;
    salesTargetsPermission = false;
    taxReportsPermission = false;
    userLogsPermission = false;
    notificationsPermission = false;
    warrantyPermission = false;
    settingsPermission = false;
    userManagementPermission = false;
    waterFiltersPermission = false;
    // فلاتر المياه - صلاحيات مفصلة
    waterFilterProductsPermission = false;
    waterFilterCustomersPermission = false;
    waterFilterSystemsPermission = false;
    waterFilterMaintenancePermission = false;
    waterFilterInstallmentsPermission = false;
    waterFilterReportsPermission = false;
  }

  /// تعيين جميع الصلاحيات إلى قيمة محددة
  void _setAllPermissions(bool value) {
    allPermissions = value;
    salePermission = value;
    partiesPermission = value;
    purchasePermission = value;
    productPermission = value;
    profileEditPermission = value;
    addExpensePermission = value;
    lossProfitPermission = value;
    dueListPermission = value;
    stockPermission = value;
    reportsPermission = value;
    salesListPermission = value;
    purchaseListPermission = value;
    ledgerPermission = value;
    aiChatPermission = value;
    aiAssistantPermission = value;
    voiceAssistantPermission = value;
    treasuryPermission = value;
    cashBoxPermission = value;
    deliveryManagementPermission = value;
    hrmPermission = value;
    employeesPermission = value;
    designationPermission = value;
    salariesPermission = value;
    financialReportsPermission = value;
    salesTargetsPermission = value;
    taxReportsPermission = value;
    userLogsPermission = value;
    notificationsPermission = value;
    warrantyPermission = value;
    settingsPermission = value;
    userManagementPermission = value;
    waterFiltersPermission = value;
    // فلاتر المياه - صلاحيات مفصلة
    waterFilterProductsPermission = value;
    waterFilterCustomersPermission = value;
    waterFilterSystemsPermission = value;
    waterFilterMaintenancePermission = value;
    waterFilterInstallmentsPermission = value;
    waterFilterReportsPermission = value;
  }
}

void signUp(
    {required BuildContext context,
    required String email,
    required String password,
    required WidgetRef ref,
    required UserRoleModel userRoleModel}) async {
  if (!isDemo) {
    EasyLoading.show(status: 'Registering....');
    try {
      UserCredential userCredential = await FirebaseAuth.instance
          .createUserWithEmailAndPassword(email: email, password: password);
      // ignore: unnecessary_null_comparison
      if (userCredential != null) {
        await FirebaseDatabase.instance
            .ref()
            .child(userRoleModel.databaseId)
            .child('User Role')
            .push()
            .set(userRoleModel.toJson());
        await FirebaseDatabase.instance
            .ref()
            .child('Admin Panel')
            .child('User Role')
            .push()
            .set(userRoleModel.toJson());

        EasyLoading.dismiss();
        await FirebaseAuth.instance.signOut();
        // ignore:
        await showSussesScreenAndLogOut(context: context);
      }
    } on FirebaseAuthException catch (e) {
      EasyLoading.showError('Failed with Error');
      if (e.code == 'weak-password') {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('The password provided is too weak.'),
            duration: Duration(seconds: 3),
          ),
        );
      } else if (e.code == 'email-already-in-use') {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('The account already exists for that email.'),
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      EasyLoading.showError('Failed with Error');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(e.toString()),
          duration: const Duration(seconds: 3),
        ),
      );
    }
  } else {
    EasyLoading.showError(demoText);
  }
}

Future showSussesScreenAndLogOut({required BuildContext context}) {
  return showDialog(
    barrierDismissible: false,
    context: context,
    builder: (BuildContext context) {
      return PopScope(
        canPop: false,
        child: Padding(
          padding: const EdgeInsets.all(30.0),
          child: Center(
            child: Container(
              width: double.infinity,
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.all(Radius.circular(30)),
              ),
              child: Padding(
                padding: const EdgeInsets.all(10.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const SizedBox(height: 20),
                    Text(
                      lang.S.of(context).addSuccessful,
                      style: const TextStyle(fontSize: 22),
                    ),
                    const SizedBox(height: 10),
                    Text(
                      lang.S.of(context).youHaveToReLogin,
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        fontSize: 18,
                      ),
                    ),
                    const SizedBox(height: 20),
                    ButtonGlobalWithoutIcon(
                        buttontext: lang.S.of(context).ok,
                        buttonDecoration:
                            kButtonDecoration.copyWith(color: Colors.green),
                        onPressed: (() {
                          const LoginForm().launch(context, isNewTask: true);
                          // const SplashScreen().launch(context, isNewTask: true);
                        }),
                        buttonTextColor: Colors.white),
                  ],
                ),
              ),
            ),
          ),
        ),
      );
    },
  );
}
