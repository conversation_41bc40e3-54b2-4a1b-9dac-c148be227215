import 'package:flutter/foundation.dart';
import 'package:mobile_pos/services/water_filter_service.dart';
import 'package:mobile_pos/services/water_filter_cache_service.dart';

/// خدمة النسخ الاحتياطي لنظام فلاتر المياه
class WaterFilterBackupService {
  static final WaterFilterBackupService _instance =
      WaterFilterBackupService._internal();
  factory WaterFilterBackupService() => _instance;
  WaterFilterBackupService._internal();

  final WaterFilterCacheService _cacheService = WaterFilterCacheService();

  /// إنشاء نسخة احتياطية شاملة
  Future<Map<String, dynamic>> createFullBackup() async {
    try {
      debugPrint('💾 بدء إنشاء النسخة الاحتياطية الشاملة...');

      final startTime = DateTime.now();

      // جلب جميع البيانات
      final backupData = await Future.wait([
        WaterFilterService.getData('Products'),
        WaterFilterService.getData('Customers'),
        WaterFilterService.getData('Systems'),
        WaterFilterService.getData('Maintenance'),
        WaterFilterService.getData('Installments'),
        WaterFilterService.getData('Expenses'),
      ]);

      final endTime = DateTime.now();
      final backupTime = endTime.difference(startTime).inMilliseconds;

      final backup = {
        'metadata': {
          'version': '1.0',
          'created_at': DateTime.now().toIso8601String(),
          'backup_type': 'full',
          'backup_time_ms': backupTime,
          'total_records': _calculateTotalRecords(backupData),
        },
        'data': {
          'products': backupData[0],
          'customers': backupData[1],
          'systems': backupData[2],
          'maintenance': backupData[3],
          'installments': backupData[4],
          'expenses': backupData[5],
        },
      };

      debugPrint(
          '✅ تم إنشاء النسخة الاحتياطية: ${backup['metadata']!['total_records']} سجل');
      return backup;
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء النسخة الاحتياطية: $e');
      return {
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// إنشاء نسخة احتياطية تدريجية (البيانات المحدثة فقط)
  Future<Map<String, dynamic>> createIncrementalBackup(DateTime since) async {
    try {
      debugPrint('📦 بدء إنشاء النسخة الاحتياطية التدريجية...');

      final startTime = DateTime.now();

      // جلب البيانات المحدثة منذ تاريخ معين
      final allData = await Future.wait([
        WaterFilterService.getData('Products'),
        WaterFilterService.getData('Customers'),
        WaterFilterService.getData('Systems'),
        WaterFilterService.getData('Maintenance'),
        WaterFilterService.getData('Installments'),
        WaterFilterService.getData('Expenses'),
      ]);

      // فلترة البيانات المحدثة
      final filteredData = _filterDataSince(allData, since);

      final endTime = DateTime.now();
      final backupTime = endTime.difference(startTime).inMilliseconds;

      final backup = {
        'metadata': {
          'version': '1.0',
          'created_at': DateTime.now().toIso8601String(),
          'backup_type': 'incremental',
          'since': since.toIso8601String(),
          'backup_time_ms': backupTime,
          'total_records': _calculateTotalRecords(filteredData),
        },
        'data': {
          'products': filteredData[0],
          'customers': filteredData[1],
          'systems': filteredData[2],
          'maintenance': filteredData[3],
          'installments': filteredData[4],
          'expenses': filteredData[5],
        },
      };

      debugPrint(
          '✅ تم إنشاء النسخة التدريجية: ${backup['metadata']!['total_records']} سجل');
      return backup;
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء النسخة التدريجية: $e');
      return {
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// استعادة البيانات من نسخة احتياطية
  Future<bool> restoreFromBackup(Map<String, dynamic> backup) async {
    try {
      debugPrint('🔄 بدء استعادة البيانات من النسخة الاحتياطية...');

      if (!_validateBackup(backup)) {
        debugPrint('❌ النسخة الاحتياطية غير صالحة');
        return false;
      }

      final data = backup['data'] as Map<String, dynamic>;
      final startTime = DateTime.now();

      // استعادة البيانات بشكل متوازي
      final results = await Future.wait([
        _restoreCollection('Products', data['products']),
        _restoreCollection('Customers', data['customers']),
        _restoreCollection('Systems', data['systems']),
        _restoreCollection('Maintenance', data['maintenance']),
        _restoreCollection('Installments', data['installments']),
        _restoreCollection('Expenses', data['expenses']),
      ]);

      final endTime = DateTime.now();
      final restoreTime = endTime.difference(startTime).inMilliseconds;

      final success = results.every((result) => result);

      if (success) {
        // مسح Cache لإعادة تحميل البيانات الجديدة
        _cacheService.clearAllCache();
        debugPrint('✅ تم استعادة البيانات بنجاح في ${restoreTime}ms');
      } else {
        debugPrint('❌ فشل في استعادة بعض البيانات');
      }

      return success;
    } catch (e) {
      debugPrint('❌ خطأ في استعادة البيانات: $e');
      return false;
    }
  }

  /// استعادة مجموعة بيانات محددة
  Future<bool> _restoreCollection(
      String collection, Map<String, dynamic>? data) async {
    try {
      if (data == null || data.isEmpty) {
        debugPrint('⚠️ لا توجد بيانات لاستعادة $collection');
        return true;
      }

      debugPrint('📥 استعادة $collection: ${data.length} عنصر');

      // حذف البيانات الموجودة أولاً
      await _clearCollection(collection);

      // استعادة البيانات الجديدة
      for (final entry in data.entries) {
        final success = await WaterFilterService.saveData(
          '$collection/${entry.key}',
          Map<String, dynamic>.from(entry.value),
        );

        if (!success) {
          debugPrint('❌ فشل في استعادة $collection/${entry.key}');
          return false;
        }
      }

      debugPrint('✅ تم استعادة $collection بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في استعادة $collection: $e');
      return false;
    }
  }

  /// حذف مجموعة بيانات
  Future<void> _clearCollection(String collection) async {
    try {
      await WaterFilterService.deleteData(collection);
      debugPrint('🗑️ تم حذف $collection');
    } catch (e) {
      debugPrint('❌ خطأ في حذف $collection: $e');
    }
  }

  /// فلترة البيانات المحدثة منذ تاريخ معين
  List<Map<String, dynamic>> _filterDataSince(
    List<Map<String, dynamic>> allData,
    DateTime since,
  ) {
    return allData.map((collection) {
      final filtered = <String, dynamic>{};

      collection.forEach((key, value) {
        try {
          final item = Map<String, dynamic>.from(value);
          final updatedAt = item['updatedAt'] as String?;

          if (updatedAt != null) {
            final updateTime = DateTime.parse(updatedAt);
            if (updateTime.isAfter(since)) {
              filtered[key] = value;
            }
          } else {
            // إذا لم يكن هناك تاريخ تحديث، أضف العنصر
            filtered[key] = value;
          }
        } catch (e) {
          // في حالة خطأ في التاريخ، أضف العنصر
          filtered[key] = value;
        }
      });

      return filtered;
    }).toList();
  }

  /// حساب إجمالي السجلات
  int _calculateTotalRecords(List<Map<String, dynamic>> data) {
    return data.fold(0, (sum, collection) => sum + collection.length);
  }

  /// التحقق من صحة النسخة الاحتياطية
  bool _validateBackup(Map<String, dynamic> backup) {
    try {
      // التحقق من وجود البيانات الأساسية
      if (!backup.containsKey('metadata') || !backup.containsKey('data')) {
        return false;
      }

      final metadata = backup['metadata'] as Map<String, dynamic>;
      final data = backup['data'] as Map<String, dynamic>;

      // التحقق من الإصدار
      if (metadata['version'] != '1.0') {
        debugPrint(
            '⚠️ إصدار النسخة الاحتياطية غير مدعوم: ${metadata['version']}');
        return false;
      }

      // التحقق من وجود المجموعات الأساسية
      final requiredCollections = ['products', 'customers', 'systems'];
      for (final collection in requiredCollections) {
        if (!data.containsKey(collection)) {
          debugPrint('⚠️ مجموعة مطلوبة مفقودة: $collection');
          return false;
        }
      }

      return true;
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من النسخة الاحتياطية: $e');
      return false;
    }
  }

  /// الحصول على معلومات النسخة الاحتياطية
  Map<String, dynamic> getBackupInfo(Map<String, dynamic> backup) {
    try {
      if (!_validateBackup(backup)) {
        return {'error': 'نسخة احتياطية غير صالحة'};
      }

      final metadata = backup['metadata'] as Map<String, dynamic>;
      final data = backup['data'] as Map<String, dynamic>;

      final info = {
        'version': metadata['version'],
        'created_at': metadata['created_at'],
        'backup_type': metadata['backup_type'],
        'total_records': metadata['total_records'],
        'collections': {},
      };

      // معلومات كل مجموعة
      data.forEach((collection, items) {
        if (items is Map) {
          info['collections'][collection] = {
            'count': items.length,
            'size_estimate':
                '${(items.toString().length / 1024).toStringAsFixed(2)} KB',
          };
        }
      });

      return info;
    } catch (e) {
      return {'error': 'خطأ في قراءة معلومات النسخة الاحتياطية: $e'};
    }
  }

  /// تصدير البيانات بصيغة JSON
  String exportToJson(Map<String, dynamic> backup) {
    try {
      // تحويل البيانات إلى JSON مع تنسيق جميل
      return backup.toString(); // يمكن استخدام jsonEncode للتنسيق الأفضل
    } catch (e) {
      debugPrint('❌ خطأ في تصدير JSON: $e');
      return '{"error": "فشل في التصدير"}';
    }
  }

  /// جدولة النسخ الاحتياطي التلقائي
  void scheduleAutoBackup() {
    debugPrint('⏰ جدولة النسخ الاحتياطي التلقائي');

    // نسخة احتياطية كل 24 ساعة
    Future.delayed(const Duration(hours: 24), () async {
      await createFullBackup();
      scheduleAutoBackup(); // إعادة جدولة
    });
  }

  /// إحصائيات النسخ الاحتياطي
  Future<Map<String, dynamic>> getBackupStatistics() async {
    try {
      final fullBackup = await createFullBackup();

      if (fullBackup.containsKey('error')) {
        return {'error': fullBackup['error']};
      }

      final metadata = fullBackup['metadata'] as Map<String, dynamic>;
      final data = fullBackup['data'] as Map<String, dynamic>;

      return {
        'total_records': metadata['total_records'],
        'backup_time_ms': metadata['backup_time_ms'],
        'collections_count': data.length,
        'estimated_size':
            '${(fullBackup.toString().length / 1024).toStringAsFixed(2)} KB',
        'last_backup': metadata['created_at'],
        'status': 'نشط',
      };
    } catch (e) {
      return {
        'error': e.toString(),
        'status': 'خطأ',
      };
    }
  }
}
