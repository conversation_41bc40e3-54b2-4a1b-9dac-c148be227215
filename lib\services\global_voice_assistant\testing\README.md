# اختبارات تشخيص مشكلة المستحقات

## نظرة عامة
هذه مجموعة من الاختبارات المصممة لتشخيص مشكلة عدم ظهور بعض فواتير الآجل في شاشة المستحقات.

## الملفات المتضمنة

### 1. `test_menu_screen.dart`
الشاشة الرئيسية لقائمة الاختبارات التي توفر:
- واجهة سهلة الاستخدام للوصول إلى جميع الاختبارات
- تعليمات واضحة للمستخدم
- معلومات حول ما تبحث عنه الاختبارات

### 2. `due_invoice_test.dart`
اختبارات شاملة لفحص بيانات الفواتير والعملاء:

#### الاختبارات المتضمنة:
- **اختبار بيانات العملاء**: فحص العملاء وحساب المديونية
- **اختبار فواتير المبيعات**: تحليل جميع الفواتير وتصنيفها
- **اختبار تطابق البيانات**: مقارنة المديونية المخزنة مع المحسوبة
- **اختبار تطابق أرقام الهواتف**: فحص تطابق أرقام الهواتف بين العملاء والفواتير
- **اختبار حساب المديونية**: تحليل كيفية حساب المديونية من الفواتير

### 3. `due_screen_test.dart`
اختبارات متخصصة لفحص منطق شاشة المستحقات:

#### الاختبارات المتضمنة:
- **اختبار منطق الشاشة**: محاكاة الكود الفعلي لشاشة المستحقات
- **البحث عن الفواتير المفقودة**: تحديد الفواتير الآجلة التي لا تظهر
- **اختبار أخطاء الحساب**: تحديد الأخطاء في حساب المديونية
- **محاكاة فلتر الشاشة**: اختبار آلية الفلترة المستخدمة

## كيفية الوصول إلى الاختبارات

1. افتح التطبيق
2. اذهب إلى **الإعدادات**
3. افتح قائمة **الإعدادات** (ExpansionPanel)
4. اضغط على **اختبارات المستحقات**
5. اختر نوع الاختبار المطلوب

## تفسير النتائج

### الرموز المستخدمة:
- ✅ **نجح**: العملية تمت بنجاح
- ❌ **فشل**: وجدت مشكلة تحتاج إلى إصلاح
- ⚠️ **تحذير**: مشكلة محتملة أو بيانات غير متوقعة
- 🔍 **فحص**: بداية اختبار جديد
- 📊 **إحصائية**: معلومات رقمية
- 📋 **تفاصيل**: معلومات تفصيلية

### المشاكل الشائعة التي قد تجدها:

#### 1. عدم تطابق أرقام الهواتف
- **المشكلة**: رقم الهاتف في الفاتورة مختلف عن رقم الهاتف في بيانات العميل
- **الحل**: توحيد تنسيق أرقام الهواتف

#### 2. أخطاء في حساب المديونية
- **المشكلة**: المديونية المخزنة في جدول العملاء لا تطابق المحسوبة من الفواتير
- **الحل**: إعادة حساب المديونية وتحديث البيانات

#### 3. مشاكل في أنواع البيانات
- **المشكلة**: حقل المديونية محفوظ كنص بدلاً من رقم
- **الحل**: تحويل البيانات إلى النوع الصحيح

#### 4. فواتير آجلة مفقودة
- **المشكلة**: فواتير لها مديونية لكن لا تظهر في شاشة المستحقات
- **الحل**: فحص وإصلاح ربط البيانات

## تعليمات مهمة

### ⚠️ تحذيرات:
- هذه الاختبارات للقراءة فقط ولن تعدل أي بيانات
- تأكد من وجود اتصال بالإنترنت قبل تشغيل الاختبارات
- احفظ نتائج الاختبارات لمراجعتها لاحقاً
- لا تحاول إصلاح المشاكل يدوياً دون استشارة

### 📝 نصائح للاستخدام:
- شغل الاختبارات في أوقات قليلة الاستخدام
- راجع جميع النتائج قبل اتخاذ أي إجراء
- وثق المشاكل المكتشفة مع تفاصيلها
- استخدم النتائج لتحديد أولويات الإصلاح

## الدعم الفني

إذا واجهت مشاكل في:
- تشغيل الاختبارات
- فهم النتائج
- تحديد الحلول المناسبة

يرجى التواصل مع فريق الدعم الفني مع إرفاق:
- لقطات شاشة من نتائج الاختبارات
- وصف تفصيلي للمشكلة
- معلومات عن البيئة (عدد العملاء، الفواتير، إلخ)

## تحديثات مستقبلية

سيتم إضافة اختبارات جديدة لفحص:
- أداء قاعدة البيانات
- تكامل البيانات عبر الجداول المختلفة
- اختبارات الأمان والصلاحيات
- تحليل أنماط الاستخدام

---

**ملاحظة**: هذه الأدوات مصممة للمساعدة في التشخيص فقط. أي تعديلات على البيانات يجب أن تتم بحذر وبعد أخذ نسخة احتياطية.
