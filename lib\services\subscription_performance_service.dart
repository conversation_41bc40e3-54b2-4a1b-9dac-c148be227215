import 'dart:async';
import 'package:flutter/material.dart';
import '../subscription.dart';
import 'subscription_cache_service.dart';

/// خدمة تحسين أداء الاشتراك
/// تستخدم لتحميل بيانات الاشتراك مسبقًا وتحسين الأداء
class SubscriptionPerformanceService {
  // مؤقت لتحديث بيانات الاشتراك دوريًا
  static Timer? _refreshTimer;

  // حالة التحميل المسبق
  static bool _isPreloaded = false;

  /// تهيئة الخدمة وبدء التحميل المسبق
  static Future<void> initialize() async {
    try {
      debugPrint('تهيئة خدمة تحسين أداء الاشتراك...');

      // بدء التحميل المسبق
      await preloadSubscriptionData();

      // بدء التحديث الدوري
      startPeriodicRefresh();

      debugPrint('تم تهيئة خدمة تحسين أداء الاشتراك بنجاح');
    } catch (e) {
      debugPrint('خطأ في تهيئة خدمة تحسين أداء الاشتراك: $e');
    }
  }

  /// تحميل بيانات الاشتراك مسبقًا
  static Future<void> preloadSubscriptionData() async {
    try {
      if (_isPreloaded) {
        debugPrint('تم تحميل بيانات الاشتراك مسبقًا بالفعل');
        return;
      }

      debugPrint('جاري تحميل بيانات الاشتراك مسبقًا...');

      // استدعاء دالة الحصول على بيانات الاشتراك
      // استخدام BuildContext وهمي لأننا لا نحتاج إليه فعليًا
      await Subscription.getUserLimitsData(
        context: _DummyBuildContext(),
        wannaShowMsg: false,
      );

      // تحميل حالة الاشتراك لبعض العناصر الشائعة
      final commonItems = [
        'Sales',
        'Purchase',
        'Customers',
        'Products',
        'Reports'
      ];
      for (final item in commonItems) {
        await Subscription.subscriptionChecker(item: item);
        // تأخير قصير لتجنب الضغط على قاعدة البيانات
        await Future.delayed(const Duration(milliseconds: 100));
      }

      _isPreloaded = true;
      debugPrint('تم تحميل بيانات الاشتراك مسبقًا بنجاح');
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات الاشتراك مسبقًا: $e');
    }
  }

  /// بدء التحديث الدوري لبيانات الاشتراك
  static void startPeriodicRefresh() {
    // إلغاء المؤقت الحالي إذا كان موجودًا
    _refreshTimer?.cancel();

    // إنشاء مؤقت جديد للتحديث كل 5 دقائق
    _refreshTimer = Timer.periodic(const Duration(minutes: 5), (timer) async {
      try {
        debugPrint('جاري تحديث بيانات الاشتراك دوريًا...');

        // مسح التخزين المؤقت
        await SubscriptionCacheService.clearSubscriptionCache();

        // إعادة تحميل البيانات
        await preloadSubscriptionData();

        debugPrint('تم تحديث بيانات الاشتراك دوريًا بنجاح');
      } catch (e) {
        debugPrint('خطأ في تحديث بيانات الاشتراك دوريًا: $e');
      }
    });
  }

  /// إيقاف التحديث الدوري
  static void stopPeriodicRefresh() {
    _refreshTimer?.cancel();
    _refreshTimer = null;
    debugPrint('تم إيقاف التحديث الدوري لبيانات الاشتراك');
  }

  /// التنظيف عند إغلاق التطبيق
  static void dispose() {
    stopPeriodicRefresh();
    _isPreloaded = false;
    debugPrint('تم تنظيف خدمة تحسين أداء الاشتراك');
  }
}

/// فئة BuildContext وهمية للاستخدام في التحميل المسبق
class _DummyBuildContext implements BuildContext {
  @override
  dynamic noSuchMethod(Invocation invocation) {
    return null;
  }
}
