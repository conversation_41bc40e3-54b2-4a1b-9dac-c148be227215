import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'imgbb_service.dart';

/// خدمة رفع الصور إلى Imgur
class ImgurService {
  // معرف العميل (Client ID) الخاص بـ Imgur API
  // يمكنك الحصول على معرف العميل من https://api.imgur.com/oauth2/addclient
  // ملاحظة: هذا المعرف قد يكون منتهي الصلاحية أو غير صحيح
  static const String clientId = '2d2c52237921871'; // معرف عميل Imgur للتطبيق

  // معرفات بديلة للاختبار (يمكن تجربتها إذا فشل المعرف الأساسي)
  static const List<String> fallbackClientIds = [
    '546c25a59c58ad7', // معرف بديل 1
    'c9a6efb3d7932fd', // معرف بديل 2
    '1574a0d6f6b3c2e', // معرف بديل 3
  ];

  /// رفع صورة مع دعم خدمتين: ImgBB (أساسي) و Imgur (بديل)
  /// يعيد رابط الصورة المباشر
  static Future<Map<String, dynamic>> uploadImage(File imageFile) async {
    try {
      debugPrint('🚀 بدء رفع الصورة...');
      debugPrint('📁 مسار الملف: ${imageFile.path}');

      // التحقق من وجود الملف
      if (!await imageFile.exists()) {
        throw Exception('الملف غير موجود');
      }

      // التحقق من حجم الملف
      final fileSize = await imageFile.length();
      debugPrint('📊 حجم الملف: $fileSize bytes');

      // جرب ImgBB أولاً (الخدمة الأساسية)
      debugPrint('🔄 جاري المحاولة مع خدمة ImgBB الأساسية...');
      try {
        final imgbbResult = await ImgBBService.uploadImage(imageFile);
        if (imgbbResult['success'] == true) {
          debugPrint('✅ نجح الرفع مع خدمة ImgBB الأساسية');
          return imgbbResult;
        }
      } catch (e) {
        debugPrint('❌ فشل الرفع مع خدمة ImgBB: $e');
      }

      // إذا فشل ImgBB، جرب Imgur كبديل
      debugPrint('🔄 جاري المحاولة مع خدمة Imgur البديلة...');
      return await _uploadToImgur(imageFile);
    } catch (e) {
      debugPrint('❌ خطأ في رفع الصورة: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// رفع صورة إلى Imgur فقط (للاستخدام الداخلي)
  static Future<Map<String, dynamic>> _uploadToImgur(File imageFile) async {
    try {
      // التحقق من وجود الملف
      if (!await imageFile.exists()) {
        throw Exception('الملف غير موجود');
      }

      // التحقق من حجم الملف (Imgur يدعم حتى 10 ميجابايت)
      final fileSize = await imageFile.length();
      debugPrint('📊 حجم الملف لـ Imgur: $fileSize bytes');

      if (fileSize > 10 * 1024 * 1024) { // 10MB limit
        throw Exception('حجم الملف كبير جداً لـ Imgur (أكثر من 10 ميجابايت)');
      }

      // جرب المعرف الأساسي أولاً
      List<String> clientIdsToTry = [clientId, ...fallbackClientIds];

      for (int i = 0; i < clientIdsToTry.length; i++) {
        final currentClientId = clientIdsToTry[i];
        debugPrint('جاري المحاولة مع Client ID ${i + 1}: $currentClientId');

        try {
          // جرب الطريقة الأولى: multipart/form-data
          try {
            debugPrint('جاري المحاولة بـ multipart/form-data...');
            final result = await _uploadWithMultipart(imageFile, currentClientId);
            if (result['success'] == true) {
              debugPrint('نجح الرفع مع Client ID: $currentClientId');
              return result;
            }
            debugPrint('فشلت طريقة multipart مع Client ID: $currentClientId');
          } catch (e) {
            debugPrint('خطأ في multipart مع Client ID $currentClientId: $e');
          }

          // جرب الطريقة الثانية: base64 JSON
          try {
            debugPrint('جاري المحاولة بـ base64...');
            final result = await _uploadWithBase64(imageFile, currentClientId);
            if (result['success'] == true) {
              debugPrint('نجح الرفع مع Client ID: $currentClientId');
              return result;
            }
            debugPrint('فشلت طريقة base64 مع Client ID: $currentClientId');
          } catch (e) {
            debugPrint('خطأ في base64 مع Client ID $currentClientId: $e');
          }

        } catch (e) {
          debugPrint('خطأ عام مع Client ID $currentClientId: $e');
        }
      }

      // كمحاولة أخيرة، جرب الرفع بدون Client ID (anonymous upload)
      debugPrint('جاري المحاولة بالرفع المجهول (بدون Client ID)...');
      try {
        final result = await _uploadAnonymous(imageFile);
        if (result['success'] == true) {
          debugPrint('نجح الرفع المجهول');
          return result;
        }
      } catch (e) {
        debugPrint('فشل الرفع المجهول: $e');
      }

      // إذا فشلت جميع محاولات Imgur
      throw Exception('فشل في رفع الصورة إلى Imgur مع جميع Client IDs المتاحة.');

    } catch (e) {
      debugPrint('خطأ في رفع الصورة إلى Imgur: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// رفع الصورة باستخدام multipart/form-data
  static Future<Map<String, dynamic>> _uploadWithMultipart(File imageFile, [String? customClientId]) async {
    final currentClientId = customClientId ?? clientId;
    final request = http.MultipartRequest(
      'POST',
      Uri.parse('https://api.imgur.com/3/image'),
    );

    // إضافة الهيدرز
    request.headers.addAll({
      'Authorization': 'Client-ID $currentClientId',
    });

    // إضافة الملف
    request.files.add(await http.MultipartFile.fromPath(
      'image',
      imageFile.path,
    ));

    // إضافة البيانات الإضافية
    request.fields.addAll({
      'type': 'file',
      'title': 'Uploaded from AmrDevPOS',
      'description': 'Image uploaded from AmrDevPOS app',
    });

    debugPrint('جاري إرسال الطلب...');
    final streamedResponse = await request.send();
    final response = await http.Response.fromStream(streamedResponse);

    debugPrint('استجابة multipart: ${response.statusCode}');
    debugPrint('محتوى الاستجابة: ${response.body}');

    return _processResponse(response);
  }

  /// رفع الصورة باستخدام base64 JSON
  static Future<Map<String, dynamic>> _uploadWithBase64(File imageFile, [String? customClientId]) async {
    final currentClientId = customClientId ?? clientId;
    // تحويل الصورة إلى base64
    final bytes = await imageFile.readAsBytes();
    final base64Image = base64Encode(bytes);

    debugPrint('تم تحويل الصورة إلى base64، الحجم: ${base64Image.length} حرف');

    // إعداد طلب الرفع
    final response = await http.post(
      Uri.parse('https://api.imgur.com/3/image'),
      headers: {
        'Authorization': 'Client-ID $currentClientId',
        'Content-Type': 'application/json',
      },
      body: jsonEncode({
        'image': base64Image,
        'type': 'base64',
        'title': 'Uploaded from AmrDevPOS',
        'description': 'Image uploaded from AmrDevPOS app',
      }),
    );

    debugPrint('استجابة base64: ${response.statusCode}');
    debugPrint('محتوى الاستجابة: ${response.body}');

    return _processResponse(response);
  }

  /// رفع الصورة بدون Client ID (anonymous upload)
  static Future<Map<String, dynamic>> _uploadAnonymous(File imageFile) async {
    try {
      // تحويل الصورة إلى base64
      final bytes = await imageFile.readAsBytes();
      final base64Image = base64Encode(bytes);

      debugPrint('جاري الرفع المجهول...');

      // إعداد طلب الرفع بدون Authorization header
      final response = await http.post(
        Uri.parse('https://api.imgur.com/3/image'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'image': base64Image,
          'type': 'base64',
          'title': 'Anonymous upload from AmrDevPOS',
          'description': 'Anonymous image upload',
        }),
      );

      debugPrint('استجابة الرفع المجهول: ${response.statusCode}');
      debugPrint('محتوى الاستجابة: ${response.body}');

      return _processResponse(response);
    } catch (e) {
      debugPrint('خطأ في الرفع المجهول: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// معالجة استجابة الخادم
  static Map<String, dynamic> _processResponse(http.Response response) {
    try {
      // التحقق من نجاح الرفع
      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);

        if (responseData['success'] == true) {
          final data = responseData['data'];
          final directLink = data['link'];

          debugPrint('تم رفع الصورة بنجاح: $directLink');

          return {
            'success': true,
            'directLink': directLink,
            'deleteHash': data['deletehash'],
            'fileName': 'image.jpg',
            'fileType': 'image',
            'fileInfo': data,
          };
        } else {
          final errorMsg = responseData['data']?['error'] ?? 'خطأ غير معروف';
          throw Exception('فشل في رفع الصورة: $errorMsg');
        }
      } else {
        // محاولة قراءة رسالة الخطأ من الاستجابة
        String errorMsg = 'HTTP ${response.statusCode}';
        try {
          final errorData = jsonDecode(response.body);
          if (errorData['data'] != null && errorData['data']['error'] != null) {
            errorMsg += ': ${errorData['data']['error']}';
          }
        } catch (e) {
          // تجاهل أخطاء تحليل JSON
        }
        throw Exception('فشل في رفع الصورة: $errorMsg');
      }
    } catch (e) {
      if (e is Exception) {
        rethrow;
      }
      throw Exception('خطأ في معالجة الاستجابة: $e');
    }
  }

  /// اختبار صحة خدمات رفع الصور
  static Future<Map<String, dynamic>> testImageServices() async {
    try {
      debugPrint('🔍 جاري اختبار خدمات رفع الصور...');

      Map<String, dynamic> results = {
        'imgur': false,
        'imgbb': false,
        'anyWorking': false,
        'details': {},
      };

      // اختبار Imgur
      debugPrint('📡 اختبار خدمة Imgur...');
      try {
        // جرب جميع معرفات العميل المتاحة
        List<String> clientIdsToTest = [clientId, ...fallbackClientIds];

        for (int i = 0; i < clientIdsToTest.length; i++) {
          final currentClientId = clientIdsToTest[i];
          debugPrint('جاري اختبار Client ID ${i + 1}: $currentClientId');

          try {
            final response = await http.get(
              Uri.parse('https://api.imgur.com/3/credits'),
              headers: {
                'Authorization': 'Client-ID $currentClientId',
              },
            );

            if (response.statusCode == 200) {
              final Map<String, dynamic> responseData = jsonDecode(response.body);
              if (responseData['success'] == true) {
                debugPrint('✅ Imgur يعمل مع Client ID: $currentClientId');
                results['imgur'] = true;
                results['details']['imgur'] = 'يعمل بشكل طبيعي';
                break;
              }
            }
          } catch (e) {
            debugPrint('خطأ في اختبار Client ID $currentClientId: $e');
          }
        }

        if (!results['imgur']) {
          results['details']['imgur'] = 'جميع Client IDs غير صحيحة';
        }
      } catch (e) {
        results['details']['imgur'] = 'خطأ في الاتصال: $e';
      }

      // اختبار ImgBB
      debugPrint('📡 اختبار خدمة ImgBB...');
      try {
        final imgbbTest = await ImgBBService.testApiKey();
        results['imgbb'] = imgbbTest;
        results['details']['imgbb'] = imgbbTest ? 'يعمل بشكل طبيعي' : 'API Key غير صحيح';
      } catch (e) {
        results['details']['imgbb'] = 'خطأ في الاتصال: $e';
      }

      // تحديد إذا كان هناك خدمة تعمل
      results['anyWorking'] = results['imgur'] || results['imgbb'];

      debugPrint('📊 نتائج اختبار الخدمات:');
      debugPrint('   - ImgBB (أساسي): ${results['imgbb'] ? "✅" : "❌"}');
      debugPrint('   - Imgur (بديل): ${results['imgur'] ? "✅" : "❌"}');
      debugPrint('   - هناك خدمة تعمل: ${results['anyWorking'] ? "✅" : "❌"}');

      return results;
    } catch (e) {
      debugPrint('❌ خطأ في اختبار خدمات رفع الصور: $e');
      return {
        'imgur': false,
        'imgbb': false,
        'anyWorking': false,
        'details': {'error': e.toString()},
      };
    }
  }

  /// اختبار صحة Client ID (للتوافق مع الكود القديم)
  static Future<bool> testClientId() async {
    final results = await testImageServices();
    return results['anyWorking'] ?? false;
  }

  /// حذف صورة من Imgur
  /// يتطلب deleteHash الذي تم الحصول عليه عند رفع الصورة
  static Future<bool> deleteImage(String deleteHash) async {
    try {
      debugPrint('جاري حذف الصورة من Imgur...');

      // إعداد طلب الحذف
      final response = await http.delete(
        Uri.parse('https://api.imgur.com/3/image/$deleteHash'),
        headers: {
          'Authorization': 'Client-ID $clientId',
        },
      );

      debugPrint('استجابة الحذف: ${response.statusCode}');
      debugPrint('محتوى الاستجابة: ${response.body}');

      // التحقق من نجاح الحذف
      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);

        if (responseData['success'] == true) {
          debugPrint('تم حذف الصورة بنجاح');
          return true;
        } else {
          throw Exception(
              'فشل في حذف الصورة: ${responseData['data']['error']}');
        }
      } else {
        throw Exception('فشل في حذف الصورة: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('خطأ في حذف الصورة من Imgur: $e');
      return false;
    }
  }
}
