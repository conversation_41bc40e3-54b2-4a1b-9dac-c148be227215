import 'dart:async';
import 'package:flutter/material.dart';
import '../widgets/loader_overlay.dart';

/// خدمة عالمية لإدارة شاشة التحميل
/// تستخدم نمط Singleton لضمان وجود نسخة واحدة فقط من الخدمة
class LoaderService {
  // تطبيق نمط Singleton
  static final LoaderService _instance = LoaderService._internal();

  // المُنشئ الخاص
  LoaderService._internal();

  // الحصول على النسخة الوحيدة من الخدمة
  factory LoaderService() => _instance;

  /// مفتاح Navigator العالمي للوصول إلى السياق
  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();

  /// حالة ظهور شاشة التحميل
  bool _isLoaderVisible = false;

  /// مرجع لـ OverlayEntry الحالي
  OverlayEntry? _overlayEntry;

  /// عدد مرات استدعاء showLoader
  int _loaderCounter = 0;

  /// قفل متزامن لمنع تداخل العمليات
  final Completer<void> _mutex = Completer<void>()..complete();

  /// قائمة بالعمليات قيد التنفيذ
  final List<String> _pendingOperations = [];

  /// التحقق من صلاحية السياق
  bool _isContextValid() {
    return navigatorKey.currentState?.mounted == true &&
        navigatorKey.currentContext != null;
  }

  /// طريقة آمنة لإضافة OverlayEntry
  void _safeInsertOverlay(String operationId) {
    // التحقق من صلاحية السياق
    if (!_isContextValid() || _overlayEntry == null) {
      // إعادة تعيين العداد إذا لم يتم عرض شاشة التحميل
      _loaderCounter--;
      if (operationId.isNotEmpty) {
        _pendingOperations.remove(operationId);
      }
      debugPrint('تم إلغاء عرض شاشة التحميل لأن السياق أصبح غير صالح');
      return;
    }

    // الحصول على السياق
    final context = navigatorKey.currentContext!;

    // إضافة OverlayEntry إلى Overlay
    Overlay.of(context, rootOverlay: true).insert(_overlayEntry!);
    _isLoaderVisible = true;
  }

  /// عرض شاشة التحميل
  Future<void> showLoader({String operationId = ''}) async {
    // استخدام قفل متزامن لمنع تداخل العمليات
    final previousMutex = _mutex.future;
    final newMutex = Completer<void>();

    try {
      // انتظار إكمال العمليات السابقة
      await previousMutex;

      // زيادة عداد الاستدعاءات
      _loaderCounter++;

      // إضافة العملية إلى قائمة العمليات قيد التنفيذ
      if (operationId.isNotEmpty) {
        _pendingOperations.add(operationId);
      }

      // إذا كانت شاشة التحميل ظاهرة بالفعل، لا داعي لإظهارها مرة أخرى
      if (_isLoaderVisible) {
        debugPrint('شاشة التحميل ظاهرة بالفعل (العداد: $_loaderCounter)');
        return;
      }

      // التحقق من صلاحية السياق
      if (!_isContextValid()) {
        debugPrint('السياق غير صالح، لا يمكن عرض شاشة التحميل');
        return;
      }

      // إنشاء OverlayEntry جديد
      _overlayEntry = OverlayEntry(
        builder: (builderContext) => const LoaderOverlay(),
      );

      // استخدام طريقة آمنة لإضافة OverlayEntry
      _safeInsertOverlay(operationId);

      debugPrint('تم عرض شاشة التحميل (العداد: $_loaderCounter)');
    } finally {
      // إكمال القفل المتزامن الجديد
      newMutex.complete();
    }
  }

  /// إخفاء شاشة التحميل
  Future<void> hideLoader({String operationId = ''}) async {
    // استخدام قفل متزامن لمنع تداخل العمليات
    final previousMutex = _mutex.future;
    final newMutex = Completer<void>();

    try {
      // انتظار إكمال العمليات السابقة
      await previousMutex;

      // إزالة العملية من قائمة العمليات قيد التنفيذ
      if (operationId.isNotEmpty) {
        _pendingOperations.remove(operationId);
      }

      // تقليل عداد الاستدعاءات
      if (_loaderCounter > 0) {
        _loaderCounter--;
      }

      // إذا كان هناك استدعاءات أخرى لـ showLoader، لا نقوم بإخفاء شاشة التحميل
      if (_loaderCounter > 0 || _pendingOperations.isNotEmpty) {
        debugPrint('لا يزال هناك $_loaderCounter استدعاء لشاشة التحميل');
        debugPrint('العمليات قيد التنفيذ: $_pendingOperations');
        return;
      }

      // إذا كانت شاشة التحميل غير ظاهرة، لا داعي لإخفائها
      if (!_isLoaderVisible) return;

      // التحقق من صلاحية السياق
      if (!_isContextValid()) {
        // إعادة تعيين الحالة في حالة عدم صلاحية السياق
        _isLoaderVisible = false;
        _overlayEntry = null;
        _loaderCounter = 0;
        _pendingOperations.clear();
        debugPrint('السياق غير صالح، تم إعادة تعيين حالة شاشة التحميل');
        return;
      }

      // إزالة OverlayEntry
      _overlayEntry?.remove();
      _overlayEntry = null;
      _isLoaderVisible = false;

      debugPrint('تم إخفاء شاشة التحميل');
    } finally {
      // إكمال القفل المتزامن الجديد
      newMutex.complete();
    }
  }

  /// إجبار إخفاء شاشة التحميل بغض النظر عن عدد الاستدعاءات
  Future<void> forceHideLoader() async {
    // استخدام قفل متزامن لمنع تداخل العمليات
    final previousMutex = _mutex.future;
    final newMutex = Completer<void>();

    try {
      // انتظار إكمال العمليات السابقة
      await previousMutex;

      // إعادة تعيين العدادات
      _loaderCounter = 0;
      _pendingOperations.clear();

      if (!_isLoaderVisible) return;

      // التحقق من صلاحية السياق
      if (!_isContextValid()) {
        // إعادة تعيين الحالة في حالة عدم صلاحية السياق
        _isLoaderVisible = false;
        _overlayEntry = null;
        debugPrint('السياق غير صالح، تم إعادة تعيين حالة شاشة التحميل');
        return;
      }

      // إزالة OverlayEntry
      _overlayEntry?.remove();
      _overlayEntry = null;
      _isLoaderVisible = false;

      debugPrint('تم إجبار إخفاء شاشة التحميل');
    } finally {
      // إكمال القفل المتزامن الجديد
      newMutex.complete();
    }
  }

  /// تنفيذ عملية مع عرض شاشة التحميل
  static Future<T> executeWithLoading<T>(Future<T> Function() operation,
      {String operationId = ''}) async {
    final service = LoaderService();
    final uniqueId = operationId.isEmpty
        ? 'op_${DateTime.now().millisecondsSinceEpoch}'
        : operationId;

    // مؤقت لإغلاق شاشة التحميل تلقائيًا بعد مدة قصوى
    Timer? timeoutTimer;

    try {
      // عرض شاشة التحميل
      await service.showLoader(operationId: uniqueId);

      // إنشاء مؤقت لإغلاق شاشة التحميل تلقائيًا بعد 15 ثانية كحد أقصى
      timeoutTimer = Timer(const Duration(seconds: 15), () {
        if (service.isLoading) {
          debugPrint(
              'تم تجاوز المدة القصوى للتحميل، إغلاق شاشة التحميل تلقائيًا');
          service.forceHideLoader();
        }
      });

      // تنفيذ العملية
      final result = await operation();

      // إلغاء المؤقت بعد نجاح العملية
      timeoutTimer.cancel();

      return result;
    } catch (e) {
      debugPrint('حدث خطأ أثناء تنفيذ العملية $uniqueId: $e');
      rethrow; // إعادة رمي الخطأ للتعامل معه في المستدعي
    } finally {
      // إلغاء المؤقت إذا كان لا يزال نشطًا
      timeoutTimer?.cancel();

      // إخفاء شاشة التحميل
      await service.hideLoader(operationId: uniqueId);

      // تأكد من إغلاق شاشة التحميل في حالة وجود مشكلة
      if (service.isLoading) {
        debugPrint('تم اكتشاف أن شاشة التحميل لا تزال مفتوحة، إغلاقها بالقوة');
        await service.forceHideLoader();
      }
    }
  }

  /// تنفيذ عملية مع عرض شاشة التحميل مع معالجة الأخطاء
  static Future<T?> executeWithLoadingHandleErrors<T>(
    Future<T> Function() operation, {
    String operationId = '',
    String successMessage = 'تمت العملية بنجاح',
    String errorPrefix = 'حدث خطأ: ',
    bool showSuccessMessage = true,
    Function(String)? onError,
    Function(String)? onSuccess,
  }) async {
    final service = LoaderService();
    final uniqueId = operationId.isEmpty
        ? 'op_${DateTime.now().millisecondsSinceEpoch}'
        : operationId;

    // مؤقت لإغلاق شاشة التحميل تلقائيًا بعد مدة قصوى
    Timer? timeoutTimer;

    try {
      // عرض شاشة التحميل
      await service.showLoader(operationId: uniqueId);

      // إنشاء مؤقت لإغلاق شاشة التحميل تلقائيًا بعد 15 ثانية كحد أقصى
      timeoutTimer = Timer(const Duration(seconds: 15), () {
        if (service.isLoading) {
          debugPrint(
              'تم تجاوز المدة القصوى للتحميل، إغلاق شاشة التحميل تلقائيًا');
          service.forceHideLoader();
        }
      });

      // تنفيذ العملية
      final result = await operation();

      // إلغاء المؤقت بعد نجاح العملية
      timeoutTimer.cancel();

      // استدعاء دالة النجاح إذا تم توفيرها
      if (showSuccessMessage && onSuccess != null) {
        onSuccess(successMessage);
      } else if (showSuccessMessage) {
        LoaderService.showSuccessMessage(successMessage);
      }

      return result;
    } catch (e) {
      debugPrint('حدث خطأ أثناء تنفيذ العملية $uniqueId: $e');

      // استدعاء دالة الخطأ إذا تم توفيرها
      if (onError != null) {
        onError('$errorPrefix$e');
      } else {
        LoaderService.showErrorMessage('$errorPrefix$e');
      }

      return null; // إرجاع null في حالة الخطأ
    } finally {
      // إلغاء المؤقت إذا كان لا يزال نشطًا
      timeoutTimer?.cancel();

      // إخفاء شاشة التحميل
      await service.hideLoader(operationId: uniqueId);

      // تأكد من إغلاق شاشة التحميل في حالة وجود مشكلة
      if (service.isLoading) {
        debugPrint('تم اكتشاف أن شاشة التحميل لا تزال مفتوحة، إغلاقها بالقوة');
        await service.forceHideLoader();
      }
    }
  }

  /// عرض رسالة خطأ
  static void showErrorMessage(String message) {
    final context = navigatorKey.currentContext;
    if (context != null && navigatorKey.currentState?.mounted == true) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  /// عرض رسالة نجاح
  static void showSuccessMessage(String message) {
    final context = navigatorKey.currentContext;
    if (context != null && navigatorKey.currentState?.mounted == true) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  /// التحقق من حالة شاشة التحميل
  bool get isLoading => _isLoaderVisible;

  /// الحصول على عدد العمليات قيد التنفيذ
  int get pendingOperationsCount => _pendingOperations.length;

  /// الحصول على عدد مرات استدعاء showLoader
  int get loaderCounter => _loaderCounter;
}
