import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;
import 'package:crypto/crypto.dart';
import 'dart:convert';

/// مدير تخزين مؤقت للصور لتحسين أداء تحميل الصور
class ImageCacheManager {
  static final ImageCacheManager _instance = ImageCacheManager._internal();
  factory ImageCacheManager() => _instance;
  ImageCacheManager._internal();

  // دليل التخزين المؤقت
  Directory? _cacheDir;

  // حالة التهيئة
  bool _isInitialized = false;

  /// تهيئة مدير التخزين المؤقت
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // الحصول على دليل التخزين المؤقت
      _cacheDir = await getTemporaryDirectory();

      // إنشاء دليل فرعي للصور
      final imageDir = Directory('${_cacheDir!.path}/cached_images');
      if (!await imageDir.exists()) {
        await imageDir.create(recursive: true);
      }

      _cacheDir = imageDir;
      _isInitialized = true;

      debugPrint('تم تهيئة مدير التخزين المؤقت للصور بنجاح');
    } catch (e) {
      debugPrint('خطأ في تهيئة مدير التخزين المؤقت للصور: $e');
    }
  }

  /// الحصول على صورة من التخزين المؤقت أو تنزيلها
  Future<File?> getImage(String url) async {
    if (!_isInitialized) await initialize();

    try {
      // إنشاء اسم ملف فريد باستخدام MD5 للرابط
      final fileName = md5.convert(utf8.encode(url)).toString();
      final file = File('${_cacheDir!.path}/$fileName');

      // التحقق من وجود الملف في التخزين المؤقت
      if (await file.exists()) {
        debugPrint('تم العثور على الصورة في التخزين المؤقت: $fileName');
        return file;
      }

      // تنزيل الصورة
      debugPrint('جاري تنزيل الصورة: $url');
      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        // حفظ الصورة في التخزين المؤقت
        await file.writeAsBytes(response.bodyBytes);
        debugPrint('تم حفظ الصورة في التخزين المؤقت: $fileName');
        return file;
      } else {
        debugPrint('فشل في تنزيل الصورة: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('خطأ في الحصول على الصورة: $e');
      return null;
    }
  }

  /// تنظيف التخزين المؤقت
  Future<void> clearCache() async {
    if (!_isInitialized) await initialize();

    try {
      // حذف جميع الملفات في دليل التخزين المؤقت
      final files = await _cacheDir!.list().toList();
      for (final file in files) {
        if (file is File) {
          await file.delete();
        }
      }

      debugPrint('تم تنظيف التخزين المؤقت للصور');
    } catch (e) {
      debugPrint('خطأ في تنظيف التخزين المؤقت للصور: $e');
    }
  }

  /// الحصول على حجم التخزين المؤقت
  Future<int> getCacheSize() async {
    if (!_isInitialized) await initialize();

    try {
      // حساب حجم جميع الملفات في دليل التخزين المؤقت
      int totalSize = 0;
      final files = await _cacheDir!.list().toList();
      for (final file in files) {
        if (file is File) {
          totalSize += await file.length();
        }
      }

      return totalSize;
    } catch (e) {
      debugPrint('خطأ في حساب حجم التخزين المؤقت للصور: $e');
      return 0;
    }
  }

  /// تحميل صورة من الشبكة مع التخزين المؤقت
  static Widget cachedNetworkImage({
    required String imageUrl,
    required Widget placeholder,
    required Widget errorWidget,
    BoxFit fit = BoxFit.cover,
  }) {
    return FutureBuilder<File?>(
      future: ImageCacheManager().getImage(imageUrl),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return placeholder;
        } else if (snapshot.hasError || snapshot.data == null) {
          return errorWidget;
        } else {
          return Image.file(
            snapshot.data!,
            fit: fit,
            errorBuilder: (context, error, stackTrace) => errorWidget,
          );
        }
      },
    );
  }
}
