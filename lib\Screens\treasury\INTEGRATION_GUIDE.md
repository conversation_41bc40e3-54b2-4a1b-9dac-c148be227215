# 🔗 دليل التكامل - إضافة زر الخزينة للواجهة الرئيسية

## 🎯 إضافة زر الخزينة في الشاشة الرئيسية

### 1. **إضافة بطاقة الخزينة في GridView**

إذا كان لديك GridView في الشاشة الرئيسية، أضف هذا الكود:

```dart
// في ملف الشاشة الرئيسية
import 'package:mobile_pos/Screens/treasury/screens/treasury_main_screen.dart';

// داخل GridView.builder أو أي container للبطاقات
GestureDetector(
  onTap: () {
    Navigator.pushNamed(context, '/treasury');
  },
  child: Container(
    padding: const EdgeInsets.all(16),
    decoration: BoxDecoration(
      gradient: LinearGradient(
        colors: [
          const Color(0xFF6C5CE7),
          const Color(0xFF6C5CE7).withOpacity(0.8),
        ],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
      borderRadius: BorderRadius.circular(15),
      boxShadow: [
        BoxShadow(
          color: const Color(0xFF6C5CE7).withOpacity(0.3),
          blurRadius: 10,
          offset: const Offset(0, 5),
        ),
      ],
    ),
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          FeatherIcons.dollarSign,
          color: Colors.white,
          size: 32,
        ),
        const SizedBox(height: 12),
        Text(
          'إدارة الخزينة',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 4),
        Text(
          'الرصيد والمعاملات',
          style: GoogleFonts.cairo(
            color: Colors.white70,
            fontSize: 12,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    ),
  ),
)
```

### 2. **إضافة زر في AppBar**

```dart
AppBar(
  title: Text('الشاشة الرئيسية'),
  actions: [
    IconButton(
      icon: Icon(FeatherIcons.dollarSign),
      onPressed: () {
        Navigator.pushNamed(context, '/treasury');
      },
      tooltip: 'إدارة الخزينة',
    ),
  ],
)
```

### 3. **إضافة عنصر في Drawer**

```dart
Drawer(
  child: ListView(
    children: [
      // عناصر أخرى...
      ListTile(
        leading: Icon(FeatherIcons.dollarSign, color: kMainColor),
        title: Text(
          'إدارة الخزينة',
          style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
        ),
        subtitle: Text(
          'الرصيد والمعاملات المالية',
          style: GoogleFonts.cairo(fontSize: 12),
        ),
        onTap: () {
          Navigator.pop(context); // إغلاق الـ drawer
          Navigator.pushNamed(context, '/treasury');
        },
      ),
    ],
  ),
)
```

### 4. **إضافة FloatingActionButton**

```dart
FloatingActionButton.extended(
  onPressed: () {
    Navigator.pushNamed(context, '/treasury');
  },
  backgroundColor: kMainColor,
  icon: Icon(FeatherIcons.creditCard, color: Colors.white),
  label: Text(
    'الخزينة',
    style: GoogleFonts.cairo(
      color: Colors.white,
      fontWeight: FontWeight.w600,
    ),
  ),
)
```

## 📊 عرض ملخص الخزينة في الشاشة الرئيسية

### **ويدجت ملخص سريع:**

```dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mobile_pos/Screens/treasury/providers/treasury_provider.dart';

class TreasurySummaryWidget extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final balanceAsync = ref.watch(treasuryBalanceProvider);

    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'ملخص الخزينة',
                style: GoogleFonts.cairo(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: kTitleColor,
                ),
              ),
              GestureDetector(
                onTap: () => Navigator.pushNamed(context, '/treasury'),
                child: Icon(
                  FeatherIcons.externalLink,
                  color: kMainColor,
                  size: 20,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          balanceAsync.when(
            data: (balance) => Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'الرصيد الحالي',
                    '${balance.currentBalance} ريال',
                    FeatherIcons.dollarSign,
                    kMainColor,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildSummaryItem(
                    'صافي الربح',
                    '${balance.netAmount} ريال',
                    balance.isProfit ? FeatherIcons.trendingUp : FeatherIcons.trendingDown,
                    balance.isProfit ? Colors.green : Colors.red,
                  ),
                ),
              ],
            ),
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stack) => Text('خطأ في تحميل البيانات'),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
```

## 🚀 استخدام الويدجت في الشاشة الرئيسية

```dart
class HomeScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          children: [
            // محتوى آخر...

            // إضافة ملخص الخزينة
            TreasurySummaryWidget(),

            // محتوى آخر...
          ],
        ),
      ),
    );
  }
}
```

## 🔔 إضافة إشعارات للمعاملات

### **إشعار عند إضافة معاملة جديدة:**

```dart
// في الشاشة الرئيسية
class HomeScreen extends ConsumerStatefulWidget {
  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
  @override
  void initState() {
    super.initState();

    // مراقبة المعاملات الجديدة
    ref.listen(treasuryTransactionsProvider, (previous, next) {
      next.whenData((transactions) {
        if (previous != null && transactions.isNotEmpty) {
          final latestTransaction = transactions.first;

          // عرض إشعار
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'تم إضافة معاملة جديدة: ${latestTransaction.description}',
                style: GoogleFonts.cairo(),
              ),
              action: SnackBarAction(
                label: 'عرض',
                onPressed: () => Navigator.pushNamed(context, '/treasury'),
              ),
            ),
          );
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    // باقي الكود...
  }
}
```

## 📱 إضافة اختصارات سريعة

### **أزرار الإجراءات السريعة:**

```dart
Row(
  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
  children: [
    _buildQuickActionButton(
      'إضافة إيراد',
      FeatherIcons.plus,
      Colors.green,
      () => Navigator.pushNamed(context, '/treasury/add'),
    ),
    _buildQuickActionButton(
      'إضافة مصروف',
      FeatherIcons.minus,
      Colors.red,
      () => Navigator.pushNamed(context, '/treasury/add'),
    ),
    _buildQuickActionButton(
      'التقارير',
      FeatherIcons.barChart3,
      kMainColor,
      () => Navigator.pushNamed(context, '/treasury/reports'),
    ),
  ],
)

Widget _buildQuickActionButton(String title, IconData icon, Color color, VoidCallback onTap) {
  return GestureDetector(
    onTap: onTap,
    child: Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            title,
            style: GoogleFonts.cairo(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    ),
  );
}
```

---

**ملاحظة:** تأكد من إضافة الاستيرادات المطلوبة في أعلى الملف:

```dart
import 'package:flutter_feather_icons/flutter_feather_icons.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/Screens/treasury/providers/treasury_provider.dart';
```
