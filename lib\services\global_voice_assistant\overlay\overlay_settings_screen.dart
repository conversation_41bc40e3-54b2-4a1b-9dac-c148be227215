import 'package:flutter/material.dart';
import '../voice_assistant_exports.dart';

/// شاشة إعدادات النافذة العائمة
class OverlaySettingsScreen extends StatefulWidget {
  const OverlaySettingsScreen({super.key});

  @override
  State<OverlaySettingsScreen> createState() => _OverlaySettingsScreenState();
}

class _OverlaySettingsScreenState extends State<OverlaySettingsScreen> {
  final OverlaySettingsService _overlaySettings = OverlaySettingsService();
  final SimpleOverlayService _simpleOverlay = SimpleOverlayService();

  bool _overlayEnabled = true;
  bool _showOutsideApp = true;
  String _overlayPosition = 'center';
  String _overlaySize = 'medium';

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  /// تحميل الإعدادات الحالية
  void _loadSettings() async {
    await _overlaySettings.loadSettings();
    setState(() {
      _overlayEnabled = _overlaySettings.overlayEnabled;
      _showOutsideApp = _overlaySettings.showOutsideApp;
      _overlayPosition = _overlaySettings.overlayPosition;
      _overlaySize = _overlaySettings.overlaySize;
    });
  }

  /// حفظ الإعدادات
  void _saveSettings() async {
    await _overlaySettings.setOverlayEnabled(_overlayEnabled);
    await _overlaySettings.setShowOutsideApp(_showOutsideApp);
    await _overlaySettings.setOverlayPosition(_overlayPosition);
    await _overlaySettings.setOverlaySize(_overlaySize);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم حفظ الإعدادات بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  /// اختبار النافذة العائمة
  void _testOverlay() async {
    try {
      if (_overlaySettings.canShowOutsideApp()) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('جاري اختبار النافذة العائمة...'),
              backgroundColor: Colors.blue,
            ),
          );
        }

        // تهيئة الخدمة البسيطة
        await _simpleOverlay.initialize();

        // اختبار النافذة العائمة
        final result = await _simpleOverlay.testOverlay();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result
                  ? 'تم إظهار النافذة العائمة بنجاح'
                  : 'فشل في إظهار النافذة العائمة'),
              backgroundColor: result ? Colors.green : Colors.red,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('النافذة العائمة معطلة'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في اختبار النافذة العائمة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        title: const Text(
          'إعدادات النافذة العائمة',
          style: TextStyle(color: Colors.white),
        ),
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            onPressed: _saveSettings,
            icon: const Icon(Icons.save, color: Colors.green),
            tooltip: 'حفظ الإعدادات',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // الإعدادات الأساسية
            _buildBasicSettings(),
            const SizedBox(height: 20),

            // إعدادات المظهر
            _buildAppearanceSettings(),
            const SizedBox(height: 20),

            // أزرار الاختبار
            _buildTestButtons(),
            const SizedBox(height: 20),

            // معلومات إضافية
            _buildInfoSection(),
          ],
        ),
      ),
    );
  }

  /// بناء الإعدادات الأساسية
  Widget _buildBasicSettings() {
    return Card(
      color: Colors.grey[900],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الإعدادات الأساسية',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // تفعيل النافذة العائمة
            SwitchListTile(
              title: const Text(
                'تفعيل النافذة العائمة',
                style: TextStyle(color: Colors.white),
              ),
              subtitle: const Text(
                'تفعيل/إلغاء تفعيل النافذة العائمة بالكامل',
                style: TextStyle(color: Colors.white70),
              ),
              value: _overlayEnabled,
              onChanged: (value) {
                setState(() {
                  _overlayEnabled = value;
                });
              },
              activeColor: Colors.blue,
            ),

            // إظهار خارج التطبيق
            SwitchListTile(
              title: const Text(
                'إظهار خارج التطبيق',
                style: TextStyle(color: Colors.white),
              ),
              subtitle: const Text(
                'إظهار النافذة حتى لو كان التطبيق في الخلفية أو مغلق',
                style: TextStyle(color: Colors.white70),
              ),
              value: _showOutsideApp,
              onChanged: _overlayEnabled
                  ? (value) {
                      setState(() {
                        _showOutsideApp = value;
                      });
                    }
                  : null,
              activeColor: Colors.blue,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء إعدادات المظهر
  Widget _buildAppearanceSettings() {
    return Card(
      color: Colors.grey[900],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إعدادات المظهر',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // حجم النافذة
            const Text(
              'حجم النافذة',
              style: TextStyle(color: Colors.white, fontSize: 16),
            ),
            const SizedBox(height: 8),
            DropdownButtonFormField<String>(
              value: _overlaySize,
              decoration: InputDecoration(
                filled: true,
                fillColor: Colors.grey[800],
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              dropdownColor: Colors.grey[800],
              style: const TextStyle(color: Colors.white),
              items: const [
                DropdownMenuItem(value: 'small', child: Text('صغير (300x400)')),
                DropdownMenuItem(
                    value: 'medium', child: Text('متوسط (380x550)')),
                DropdownMenuItem(value: 'large', child: Text('كبير (450x700)')),
              ],
              onChanged: _overlayEnabled
                  ? (value) {
                      setState(() {
                        _overlaySize = value!;
                      });
                    }
                  : null,
            ),

            const SizedBox(height: 16),

            // موضع النافذة
            const Text(
              'موضع النافذة',
              style: TextStyle(color: Colors.white, fontSize: 16),
            ),
            const SizedBox(height: 8),
            DropdownButtonFormField<String>(
              value: _overlayPosition,
              decoration: InputDecoration(
                filled: true,
                fillColor: Colors.grey[800],
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              dropdownColor: Colors.grey[800],
              style: const TextStyle(color: Colors.white),
              items: const [
                DropdownMenuItem(value: 'center', child: Text('الوسط')),
                DropdownMenuItem(value: 'top', child: Text('الأعلى')),
                DropdownMenuItem(value: 'bottom', child: Text('الأسفل')),
              ],
              onChanged: _overlayEnabled
                  ? (value) {
                      setState(() {
                        _overlayPosition = value!;
                      });
                    }
                  : null,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء أزرار الاختبار
  Widget _buildTestButtons() {
    return Card(
      color: Colors.grey[900],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'اختبار النافذة',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _overlayEnabled ? _testOverlay : null,
                    icon: const Icon(Icons.play_arrow),
                    label: const Text('اختبار النافذة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pushNamed(
                          context, '/InteractiveVoiceAssistant');
                    },
                    icon: const Icon(Icons.mic),
                    label: const Text('اختبار داخلي'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // أزرار اختبار إضافية
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pushNamed(context, '/WakeWordTest');
                    },
                    icon: const Icon(Icons.hearing),
                    label: const Text('اختبار كلمة التفعيل'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pushNamed(context, '/VoiceAssistantMonitor');
                    },
                    icon: const Icon(Icons.monitor),
                    label: const Text('مراقب النظام'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قسم المعلومات
  Widget _buildInfoSection() {
    return Card(
      color: Colors.grey[900],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات مهمة',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              '• النافذة العائمة تظهر خارج التطبيق عند اكتشاف كلمة التفعيل',
              style: TextStyle(color: Colors.white70),
            ),
            const SizedBox(height: 8),
            const Text(
              '• إذا كانت معطلة، ستظهر الشاشة داخل التطبيق فقط',
              style: TextStyle(color: Colors.white70),
            ),
            const SizedBox(height: 8),
            const Text(
              '• تحتاج لإذن "النوافذ العائمة" لتعمل خارج التطبيق',
              style: TextStyle(color: Colors.white70),
            ),
            const SizedBox(height: 8),
            const Text(
              '• يمكن سحب النافذة وتحريكها على الشاشة',
              style: TextStyle(color: Colors.white70),
            ),
          ],
        ),
      ),
    );
  }
}
