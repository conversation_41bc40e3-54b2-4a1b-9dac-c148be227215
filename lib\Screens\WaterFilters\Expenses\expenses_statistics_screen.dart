import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/model/water_filter_models.dart';
import 'package:mobile_pos/services/water_filter_expense_service.dart';

class ExpensesStatisticsScreen extends StatefulWidget {
  const ExpensesStatisticsScreen({super.key});

  @override
  State<ExpensesStatisticsScreen> createState() =>
      _ExpensesStatisticsScreenState();
}

class _ExpensesStatisticsScreenState extends State<ExpensesStatisticsScreen> {
  Map<String, dynamic> _statistics = {};
  List<WaterFilterExpense> _recentExpenses = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadStatistics();
  }

  Future<void> _loadStatistics() async {
    setState(() => _isLoading = true);

    try {
      final stats = await WaterFilterExpenseService.getExpenseStatistics();
      final expenses = await WaterFilterExpenseService.getAllExpenses();

      setState(() {
        _statistics = stats;
        _recentExpenses = expenses.take(5).toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل الإحصائيات: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        title: Text(
          'إحصائيات المصروفات',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: _loadStatistics,
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSectionTitle('نظرة عامة'),
                    const SizedBox(height: 16),

                    // إحصائيات عامة
                    GridView.count(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      crossAxisCount: 2,
                      crossAxisSpacing: 16,
                      mainAxisSpacing: 16,
                      childAspectRatio: 0.9,
                      children: [
                        _buildStatCard(
                          title: 'إجمالي المصروفات',
                          value:
                              '${(_statistics['totalExpenses'] ?? 0).toStringAsFixed(2)} ج.م',
                          icon: Icons.account_balance_wallet,
                          color: Colors.red,
                        ),
                        _buildStatCard(
                          title: 'متوسط المصروف',
                          value:
                              '${(_statistics['averageExpense'] ?? 0).toStringAsFixed(2)} ج.م',
                          icon: Icons.calculate,
                          color: Colors.blue,
                        ),
                        _buildStatCard(
                          title: 'مصروفات هذا الشهر',
                          value:
                              '${(_statistics['thisMonthTotal'] ?? 0).toStringAsFixed(2)} ج.م',
                          icon: Icons.calendar_today,
                          color: Colors.green,
                        ),
                        _buildStatCard(
                          title: 'عدد المصروفات',
                          value: '${_statistics['thisMonthCount'] ?? 0}',
                          icon: Icons.receipt_long,
                          color: Colors.purple,
                        ),
                      ],
                    ),

                    const SizedBox(height: 24),

                    _buildSectionTitle('مقارنة شهرية'),
                    const SizedBox(height: 16),

                    _buildMonthlyComparison(),

                    const SizedBox(height: 24),

                    _buildSectionTitle('توزيع المصروفات حسب الفئة'),
                    const SizedBox(height: 16),

                    _buildCategoryBreakdown(),

                    const SizedBox(height: 24),

                    _buildSectionTitle('آخر المصروفات'),
                    const SizedBox(height: 16),

                    _buildRecentExpenses(),
                  ],
                ),
              ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: GoogleFonts.cairo(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: kMainColor,
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 28),
          const SizedBox(height: 8),
          Flexible(
            child: Text(
              value,
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
          const SizedBox(height: 4),
          Flexible(
            child: Text(
              title,
              style: GoogleFonts.cairo(
                fontSize: 11,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMonthlyComparison() {
    final thisMonth = _statistics['thisMonthTotal'] ?? 0.0;
    final lastMonth = _statistics['lastMonthTotal'] ?? 0.0;
    final change =
        lastMonth > 0 ? ((thisMonth - lastMonth) / lastMonth) * 100 : 0.0;
    final isIncrease = change > 0;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'هذا الشهر',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  Text(
                    '${thisMonth.toStringAsFixed(2)} ج.م',
                    style: GoogleFonts.cairo(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                    ),
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    'الشهر السابق',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  Text(
                    '${lastMonth.toStringAsFixed(2)} ج.م',
                    style: GoogleFonts.cairo(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade700,
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                isIncrease ? Icons.trending_up : Icons.trending_down,
                color: isIncrease ? Colors.red : Colors.green,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                '${change.abs().toStringAsFixed(1)}%',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: isIncrease ? Colors.red : Colors.green,
                ),
              ),
              const SizedBox(width: 4),
              Text(
                isIncrease ? 'زيادة' : 'انخفاض',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryBreakdown() {
    final categoryStats =
        _statistics['categoryStats'] as Map<String, dynamic>? ?? {};
    final sortedCategories = categoryStats.entries.toList()
      ..sort((a, b) => (b.value as double).compareTo(a.value as double));

    if (sortedCategories.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.grey.shade50,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Center(
          child: Text(
            'لا توجد بيانات للفئات',
            style: GoogleFonts.cairo(
              color: Colors.grey.shade600,
            ),
          ),
        ),
      );
    }

    return Column(
      children: sortedCategories.take(5).map((entry) {
        final percentage = _statistics['totalExpenses'] > 0
            ? (entry.value / _statistics['totalExpenses']) * 100
            : 0.0;

        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.shade200,
                blurRadius: 4,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      entry.key,
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${(entry.value as double).toStringAsFixed(2)} ج.م',
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              Text(
                '${percentage.toStringAsFixed(1)}%',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: kMainColor,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildRecentExpenses() {
    if (_recentExpenses.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.grey.shade50,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Center(
          child: Text(
            'لا توجد مصروفات حديثة',
            style: GoogleFonts.cairo(
              color: Colors.grey.shade600,
            ),
          ),
        ),
      );
    }

    return Column(
      children: _recentExpenses.map((expense) {
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.shade200,
                blurRadius: 4,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: _getCategoryColor(expense.category).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  _getCategoryIcon(expense.category),
                  color: _getCategoryColor(expense.category),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      expense.title,
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${expense.date.day}/${expense.date.month}/${expense.date.year}',
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              Text(
                '${expense.amount.toStringAsFixed(2)} ج.م',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Color _getCategoryColor(ExpenseCategory category) {
    switch (category) {
      case ExpenseCategory.transportation:
        return Colors.blue;
      case ExpenseCategory.materials:
        return Colors.orange;
      case ExpenseCategory.tools:
        return Colors.purple;
      case ExpenseCategory.maintenance:
        return Colors.green;
      case ExpenseCategory.marketing:
        return Colors.pink;
      case ExpenseCategory.office:
        return Colors.teal;
      case ExpenseCategory.utilities:
        return Colors.indigo;
      case ExpenseCategory.salaries:
        return Colors.brown;
      case ExpenseCategory.rent:
        return Colors.red;
      case ExpenseCategory.insurance:
        return Colors.cyan;
      case ExpenseCategory.other:
        return Colors.grey;
    }
  }

  IconData _getCategoryIcon(ExpenseCategory category) {
    switch (category) {
      case ExpenseCategory.transportation:
        return Icons.directions_car;
      case ExpenseCategory.materials:
        return Icons.inventory_2;
      case ExpenseCategory.tools:
        return Icons.build;
      case ExpenseCategory.maintenance:
        return Icons.settings;
      case ExpenseCategory.marketing:
        return Icons.campaign;
      case ExpenseCategory.office:
        return Icons.business;
      case ExpenseCategory.utilities:
        return Icons.electrical_services;
      case ExpenseCategory.salaries:
        return Icons.people;
      case ExpenseCategory.rent:
        return Icons.home;
      case ExpenseCategory.insurance:
        return Icons.security;
      case ExpenseCategory.other:
        return Icons.more_horiz;
    }
  }
}
