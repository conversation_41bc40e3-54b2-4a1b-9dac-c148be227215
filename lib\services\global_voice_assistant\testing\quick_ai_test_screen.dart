// بسم الله الرحمن الرحيم
// اختبار QuickAI Assistant - AmrDevPOS

import 'package:flutter/material.dart';
import '../../../widgets/quick_ai_assistant_modal.dart';
import '../voice_assistant_exports.dart';
import '../../../Screens/Chat/services/database_service.dart';

/// شاشة اختبار QuickAI Assistant
class QuickAITestScreen extends StatefulWidget {
  const QuickAITestScreen({super.key});

  @override
  State<QuickAITestScreen> createState() => _QuickAITestScreenState();
}

class _QuickAITestScreenState extends State<QuickAITestScreen> {
  String _testResults = '';
  bool _isRunning = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('🤖 اختبار QuickAI Assistant'),
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // معلومات سريعة
            Card(
              color: Colors.purple.shade50,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '🤖 QuickAI Assistant',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 10),
                    const Text(
                      'المساعد الذكي للمبيعات يساعدك في:\n'
                      '• البحث عن العملاء والموردين\n'
                      '• البحث عن المنتجات\n'
                      '• إنشاء الفواتير بالصوت\n'
                      '• الإجابة على الأسئلة العامة',
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // أزرار الاختبار
            Wrap(
              spacing: 10,
              runSpacing: 10,
              children: [
                ElevatedButton.icon(
                  onPressed: _showQuickAI,
                  icon: const Icon(Icons.assistant),
                  label: const Text('فتح QuickAI Assistant'),
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.purple),
                ),
                ElevatedButton.icon(
                  onPressed: _isRunning ? null : _testVoiceCommands,
                  icon: const Icon(Icons.mic),
                  label: const Text('اختبار الأوامر الصوتية'),
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
                ),
                ElevatedButton.icon(
                  onPressed: _isRunning ? null : _testTextCommands,
                  icon: const Icon(Icons.text_fields),
                  label: const Text('اختبار الأوامر النصية'),
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
                ),
                ElevatedButton.icon(
                  onPressed: _isRunning ? null : _testDatabaseSearch,
                  icon: const Icon(Icons.search),
                  label: const Text('اختبار البحث في قاعدة البيانات'),
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // نتائج الاختبار
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Text(
                            '📋 نتائج الاختبار',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Spacer(),
                          if (_isRunning)
                            const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                        ],
                      ),
                      const SizedBox(height: 10),
                      Expanded(
                        child: SingleChildScrollView(
                          child: Text(
                            _testResults.isEmpty
                                ? 'لم يتم تشغيل أي اختبار بعد'
                                : _testResults,
                            style: const TextStyle(
                              fontFamily: 'monospace',
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showQuickAI() {
    showDialog(
      context: context,
      builder: (context) => const Dialog(
        child: SizedBox(
          width: 400,
          height: 600,
          child: QuickAIAssistantModal(),
        ),
      ),
    );
  }

  void _addResult(String result) {
    setState(() {
      _testResults += '${DateTime.now().toIso8601String()}: $result\n';
    });
  }

  Future<void> _testVoiceCommands() async {
    setState(() => _isRunning = true);
    _addResult('🎤 اختبار الأوامر الصوتية...');

    try {
      _addResult('📝 أمثلة على الأوامر الصوتية:');
      _addResult('   - "ابحث عن أحمد"');
      _addResult('   - "أريد عميل محمد"');
      _addResult('   - "اعرض مورد علي"');
      _addResult('   - "ابحث عن منتج أرز"');
      _addResult('   - "كم مبيعات اليوم؟"');
      _addResult('✅ يمكنك الآن فتح QuickAI وتجربة هذه الأوامر');
    } catch (e) {
      _addResult('❌ خطأ في اختبار الأوامر الصوتية: $e');
    }

    setState(() => _isRunning = false);
  }

  Future<void> _testTextCommands() async {
    setState(() => _isRunning = true);
    _addResult('📝 اختبار الأوامر النصية...');

    try {
      final testCommands = [
        'أحمد',
        'محمد',
        'علي',
        'أرز',
        'سكر',
      ];

      for (String command in testCommands) {
        _addResult('🔍 اختبار الأمر: $command');
        
        final result = await QuickAIAssistantService.processVoiceCommand(command);
        
        if (result['success'] == true) {
          _addResult('✅ نجح: ${result['message']}');
        } else {
          _addResult('❌ فشل: ${result['message']}');
        }
        
        // تأخير قصير بين الاختبارات
        await Future.delayed(const Duration(milliseconds: 500));
      }
    } catch (e) {
      _addResult('❌ خطأ في اختبار الأوامر النصية: $e');
    }

    setState(() => _isRunning = false);
  }

  Future<void> _testDatabaseSearch() async {
    setState(() => _isRunning = true);
    _addResult('🗄️ اختبار البحث في قاعدة البيانات...');

    try {
      // اختبار البحث عن العملاء
      _addResult('👥 اختبار البحث عن العملاء...');
      final customer = await DatabaseService.searchCustomer('أحمد');
      if (customer != null) {
        _addResult('✅ تم العثور على عميل: ${customer['name']}');
      } else {
        _addResult('⚠️ لم يتم العثور على عميل بهذا الاسم');
      }

      // اختبار البحث عن الموردين
      _addResult('🏪 اختبار البحث عن الموردين...');
      final supplier = await DatabaseService.searchSupplier('محمد');
      if (supplier != null) {
        _addResult('✅ تم العثور على مورد: ${supplier['name']}');
      } else {
        _addResult('⚠️ لم يتم العثور على مورد بهذا الاسم');
      }

      // اختبار البحث عن المنتجات
      _addResult('📦 اختبار البحث عن المنتجات...');
      final product = await DatabaseService.searchProduct('أرز');
      if (product != null) {
        _addResult('✅ تم العثور على منتج: ${product['name']}');
      } else {
        _addResult('⚠️ لم يتم العثور على منتج بهذا الاسم');
      }

      _addResult('✅ انتهى اختبار قاعدة البيانات');
    } catch (e) {
      _addResult('❌ خطأ في اختبار قاعدة البيانات: $e');
    }

    setState(() => _isRunning = false);
  }
}
