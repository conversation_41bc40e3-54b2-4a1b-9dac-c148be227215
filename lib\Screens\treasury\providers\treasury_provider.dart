import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/treasury_transaction_model.dart';
import '../models/treasury_balance_model.dart';
import '../services/treasury_service.dart';

// Treasury service provider
final treasuryServiceProvider = Provider<TreasuryService>((ref) {
  return TreasuryService();
});

// Treasury transactions provider
final treasuryTransactionsProvider =
    StreamProvider<List<TreasuryTransactionModel>>((ref) {
  final treasuryService = ref.read(treasuryServiceProvider);
  return treasuryService.listenToTransactions();
});

// Treasury balance provider
final treasuryBalanceProvider = StreamProvider<TreasuryBalanceModel>((ref) {
  final treasuryService = ref.read(treasuryServiceProvider);
  return treasuryService.listenToBalance();
});

// Due amounts provider (المديونية)
final dueAmountsProvider = FutureProvider<Map<String, double>>((ref) async {
  final treasuryService = ref.read(treasuryServiceProvider);
  return await treasuryService.getDueAmounts();
});

// Filtered transactions provider
final filteredTransactionsProvider =
    Provider.family<List<TreasuryTransactionModel>, Map<String, dynamic>>(
        (ref, filters) {
  final transactionsAsync = ref.watch(treasuryTransactionsProvider);

  return transactionsAsync.when(
    data: (transactions) {
      List<TreasuryTransactionModel> filtered = transactions;

      // Filter by type
      if (filters['type'] != null && filters['type'] != 'all') {
        filtered = filtered.where((t) => t.type == filters['type']).toList();
      }

      // Filter by category
      if (filters['category'] != null && filters['category'] != 'all') {
        filtered =
            filtered.where((t) => t.category == filters['category']).toList();
      }

      // Filter by date range
      if (filters['startDate'] != null && filters['endDate'] != null) {
        final startDate = filters['startDate'] as DateTime;
        final endDate = filters['endDate'] as DateTime;

        filtered = filtered.where((t) {
          final transactionDate = t.dateAsDateTime;
          return transactionDate
                  .isAfter(startDate.subtract(const Duration(days: 1))) &&
              transactionDate.isBefore(endDate.add(const Duration(days: 1)));
        }).toList();
      }

      // Filter by search query
      if (filters['searchQuery'] != null &&
          filters['searchQuery'].toString().isNotEmpty) {
        final query = filters['searchQuery'].toString().toLowerCase();
        filtered = filtered.where((t) {
          return t.description.toLowerCase().contains(query) ||
              t.category.toLowerCase().contains(query) ||
              t.notes.toLowerCase().contains(query) ||
              t.referenceNumber.toLowerCase().contains(query);
        }).toList();
      }

      return filtered;
    },
    loading: () => [],
    error: (error, stack) => [],
  );
});

// Transaction categories provider
final transactionCategoriesProvider = Provider<List<String>>((ref) {
  final transactionsAsync = ref.watch(treasuryTransactionsProvider);

  return transactionsAsync.when(
    data: (transactions) {
      final categories = transactions.map((t) => t.category).toSet().toList();
      categories.sort();
      return categories;
    },
    loading: () => [],
    error: (error, stack) => [],
  );
});

// Income categories provider
final incomeCategoriesProvider = Provider<List<String>>((ref) {
  return [
    'مبيعات',
    'خدمات',
    'استثمارات',
    'فوائد',
    'إيرادات أخرى',
    'مكافآت',
    'عمولات',
    'إيجارات',
  ];
});

// Expense categories provider
final expenseCategoriesProvider = Provider<List<String>>((ref) {
  return [
    'رواتب',
    'إيجار',
    'كهرباء',
    'مياه',
    'هاتف وإنترنت',
    'وقود',
    'صيانة',
    'مواد خام',
    'تسويق',
    'مصروفات إدارية',
    'ضرائب',
    'تأمين',
    'مصروفات أخرى',
  ];
});

// Payment methods provider
final paymentMethodsProvider = Provider<List<String>>((ref) {
  return [
    'نقدي',
    'بنكي',
    'شيك',
    'تحويل',
    'بطاقة ائتمان',
    'محفظة إلكترونية',
    'أخرى',
  ];
});

// Treasury statistics provider
final treasuryStatisticsProvider = Provider<Map<String, dynamic>>((ref) {
  final transactionsAsync = ref.watch(treasuryTransactionsProvider);
  final balanceAsync = ref.watch(treasuryBalanceProvider);
  final dueAmountsAsync = ref.watch(dueAmountsProvider);

  return transactionsAsync.when(
    data: (transactions) {
      return balanceAsync.when(
        data: (balance) {
          // Calculate today's transactions
          final today = DateTime.now();
          final todayStart = DateTime(today.year, today.month, today.day);
          final todayEnd =
              DateTime(today.year, today.month, today.day, 23, 59, 59);

          final todayTransactions = transactions.where((t) {
            final transactionDate = t.dateAsDateTime;
            return transactionDate.isAfter(todayStart) &&
                transactionDate.isBefore(todayEnd);
          }).toList();

          double todayIncome = 0;
          double todayExpense = 0;

          for (var transaction in todayTransactions) {
            if (transaction.isIncome) {
              todayIncome += transaction.amountAsDouble;
            } else {
              todayExpense += transaction.amountAsDouble;
            }
          }

          // Calculate this month's transactions
          final monthStart = DateTime(today.year, today.month, 1);
          final monthEnd = DateTime(today.year, today.month + 1, 0, 23, 59, 59);

          final monthTransactions = transactions.where((t) {
            final transactionDate = t.dateAsDateTime;
            return transactionDate.isAfter(monthStart) &&
                transactionDate.isBefore(monthEnd);
          }).toList();

          double monthIncome = 0;
          double monthExpense = 0;

          for (var transaction in monthTransactions) {
            if (transaction.isIncome) {
              monthIncome += transaction.amountAsDouble;
            } else {
              monthExpense += transaction.amountAsDouble;
            }
          }

          // إضافة بيانات المديونية
          final dueAmounts = dueAmountsAsync.when(
            data: (due) => due,
            loading: () => {'customerDue': 0.0, 'supplierDue': 0.0},
            error: (error, stack) => {'customerDue': 0.0, 'supplierDue': 0.0},
          );

          return {
            'currentBalance': balance.currentBalanceAsDouble,
            'totalIncome': balance.totalIncomeAsDouble,
            'totalExpense': balance.totalExpenseAsDouble,
            'netAmount': balance.netAmount,
            'todayIncome': todayIncome,
            'todayExpense': todayExpense,
            'todayNet': todayIncome - todayExpense,
            'monthIncome': monthIncome,
            'monthExpense': monthExpense,
            'monthNet': monthIncome - monthExpense,
            'totalTransactions': transactions.length,
            'todayTransactions': todayTransactions.length,
            'monthTransactions': monthTransactions.length,
            // بيانات المديونية
            'customerDue': dueAmounts['customerDue'] ?? 0.0,
            'supplierDue': dueAmounts['supplierDue'] ?? 0.0,
            'netDue': (dueAmounts['customerDue'] ?? 0.0) -
                (dueAmounts['supplierDue'] ?? 0.0),
          };
        },
        loading: () => {},
        error: (error, stack) => {},
      );
    },
    loading: () => {},
    error: (error, stack) => {},
  );
});

// Add transaction provider
final addTransactionProvider =
    StateNotifierProvider<AddTransactionNotifier, AsyncValue<bool>>((ref) {
  final treasuryService = ref.read(treasuryServiceProvider);
  return AddTransactionNotifier(treasuryService);
});

class AddTransactionNotifier extends StateNotifier<AsyncValue<bool>> {
  final TreasuryService _treasuryService;

  AddTransactionNotifier(this._treasuryService)
      : super(const AsyncValue.data(false));

  Future<void> addTransaction(TreasuryTransactionModel transaction) async {
    state = const AsyncValue.loading();

    try {
      final success = await _treasuryService.addTransaction(transaction);
      state = AsyncValue.data(success);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> updateTransaction(TreasuryTransactionModel transaction) async {
    state = const AsyncValue.loading();

    try {
      final success = await _treasuryService.updateTransaction(transaction);
      state = AsyncValue.data(success);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> deleteTransaction(String transactionId) async {
    state = const AsyncValue.loading();

    try {
      final success = await _treasuryService.deleteTransaction(transactionId);
      state = AsyncValue.data(success);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  void reset() {
    state = const AsyncValue.data(false);
  }
}
