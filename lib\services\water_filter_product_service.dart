import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:mobile_pos/model/water_filter_models.dart';
import 'package:mobile_pos/services/water_filter_service.dart';
import 'package:mobile_pos/services/file_upload_service.dart';
import 'dart:io';

/// خدمة إدارة منتجات فلاتر المياه
class WaterFilterProductService {
  // نمط Singleton
  static final WaterFilterProductService _instance =
      WaterFilterProductService._internal();
  factory WaterFilterProductService() => _instance;
  WaterFilterProductService._internal();

  /// إضافة منتج جديد
  static Future<bool> addProduct(WaterFilterProduct product) async {
    try {
      debugPrint('➕ إضافة منتج فلتر مياه جديد: ${product.name}');

      // التحقق من صحة البيانات
      if (!_validateProduct(product)) {
        debugPrint('❌ بيانات المنتج غير صحيحة');
        return false;
      }

      // إنشاء معرف فريد إذا لم يكن موجوداً
      if (product.id.isEmpty) {
        product.id = WaterFilterService.generateId();
      }

      // إضافة الطوابع الزمنية
      product.createdAt = DateTime.now();
      product.updatedAt = DateTime.now();

      // حفظ المنتج في قاعدة البيانات
      await WaterFilterService.productsRef
          .child(product.id)
          .set(product.toJson());

      debugPrint('✅ تم إضافة المنتج بنجاح: ${product.id}');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في إضافة المنتج: $e');
      return false;
    }
  }

  /// تحديث منتج موجود
  static Future<bool> updateProduct(WaterFilterProduct product) async {
    try {
      debugPrint('🔄 تحديث منتج فلتر المياه: ${product.name}');

      // التحقق من وجود المنتج
      final snapshot =
          await WaterFilterService.productsRef.child(product.id).get();

      if (!snapshot.exists) {
        debugPrint('❌ المنتج غير موجود: ${product.id}');
        return false;
      }

      // التحقق من صحة البيانات
      if (!_validateProduct(product)) {
        debugPrint('❌ بيانات المنتج غير صحيحة');
        return false;
      }

      // تحديث الطابع الزمني
      product.updatedAt = DateTime.now();

      // تحديث المنتج في قاعدة البيانات
      await WaterFilterService.productsRef
          .child(product.id)
          .update(product.toJson());

      debugPrint('✅ تم تحديث المنتج بنجاح: ${product.id}');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في تحديث المنتج: $e');
      return false;
    }
  }

  /// حذف منتج
  static Future<bool> deleteProduct(String productId) async {
    try {
      debugPrint('🗑️ حذف منتج فلتر المياه: $productId');

      // التحقق من وجود المنتج
      final snapshot =
          await WaterFilterService.productsRef.child(productId).get();

      if (!snapshot.exists) {
        debugPrint('❌ المنتج غير موجود: $productId');
        return false;
      }

      // التحقق من عدم استخدام المنتج في أنظمة مركبة
      final systemsSnapshot = await WaterFilterService.systemsRef
          .orderByChild('productId')
          .equalTo(productId)
          .get();

      if (systemsSnapshot.exists) {
        debugPrint('❌ لا يمكن حذف المنتج - مستخدم في أنظمة مركبة');
        return false;
      }

      // حذف المنتج
      await WaterFilterService.productsRef.child(productId).remove();

      debugPrint('✅ تم حذف المنتج بنجاح: $productId');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في حذف المنتج: $e');
      return false;
    }
  }

  /// الحصول على منتج بالمعرف
  static Future<WaterFilterProduct?> getProduct(String productId) async {
    try {
      debugPrint('🔍 جلب منتج فلتر المياه: $productId');

      final snapshot =
          await WaterFilterService.productsRef.child(productId).get();

      if (!snapshot.exists) {
        debugPrint('❌ المنتج غير موجود: $productId');
        return null;
      }

      final data = Map<String, dynamic>.from(snapshot.value as Map);
      final product = WaterFilterProduct.fromJson(data);

      debugPrint('✅ تم جلب المنتج بنجاح: ${product.name}');
      return product;
    } catch (e) {
      debugPrint('❌ خطأ في جلب المنتج: $e');
      return null;
    }
  }

  /// الحصول على جميع المنتجات
  static Future<List<WaterFilterProduct>> getAllProducts() async {
    try {
      debugPrint('📋 جلب جميع منتجات فلاتر المياه...');

      final snapshot = await WaterFilterService.productsRef.get();

      if (!snapshot.exists) {
        debugPrint('ℹ️ لا توجد منتجات');
        return [];
      }

      final products = <WaterFilterProduct>[];

      for (final child in snapshot.children) {
        try {
          final data = Map<String, dynamic>.from(child.value as Map);
          final product = WaterFilterProduct.fromJson(data);
          products.add(product);
        } catch (e) {
          debugPrint('⚠️ خطأ في معالجة منتج: $e');
        }
      }

      // ترتيب المنتجات حسب التاريخ (الأحدث أولاً)
      products.sort((a, b) {
        if (a.createdAt == null || b.createdAt == null) return 0;
        return b.createdAt!.compareTo(a.createdAt!);
      });

      debugPrint('✅ تم جلب ${products.length} منتج');
      return products;
    } catch (e) {
      debugPrint('❌ خطأ في جلب المنتجات: $e');
      return [];
    }
  }

  /// البحث في المنتجات
  static Future<List<WaterFilterProduct>> searchProducts(String query) async {
    try {
      debugPrint('🔍 البحث في المنتجات: $query');

      final allProducts = await getAllProducts();

      if (query.isEmpty) {
        return allProducts;
      }

      final searchQuery = query.toLowerCase().trim();
      final filteredProducts = allProducts.where((product) {
        return product.name.toLowerCase().contains(searchQuery) ||
            product.brand.toLowerCase().contains(searchQuery) ||
            product.description.toLowerCase().contains(searchQuery) ||
            product.category.arabicName.toLowerCase().contains(searchQuery);
      }).toList();

      debugPrint('✅ تم العثور على ${filteredProducts.length} منتج');
      return filteredProducts;
    } catch (e) {
      debugPrint('❌ خطأ في البحث: $e');
      return [];
    }
  }

  /// الحصول على المنتجات حسب الفئة
  static Future<List<WaterFilterProduct>> getProductsByCategory(
      WaterFilterCategory category) async {
    try {
      debugPrint('📂 جلب المنتجات حسب الفئة: ${category.arabicName}');

      final allProducts = await getAllProducts();
      final categoryProducts =
          allProducts.where((product) => product.category == category).toList();

      debugPrint('✅ تم العثور على ${categoryProducts.length} منتج في الفئة');
      return categoryProducts;
    } catch (e) {
      debugPrint('❌ خطأ في جلب المنتجات حسب الفئة: $e');
      return [];
    }
  }

  /// رفع صورة المنتج
  static Future<String?> uploadProductImage(File imageFile) async {
    try {
      debugPrint('📷 رفع صورة المنتج...');

      // استخدام FileUploadService لرفع الصور
      final uploadResult = await FileUploadService().uploadFile(imageFile);

      if (uploadResult['success'] == true) {
        final imageUrl = uploadResult['directLink'];
        debugPrint('✅ تم رفع صورة المنتج بنجاح: $imageUrl');
        return imageUrl;
      } else {
        debugPrint('❌ فشل في رفع صورة المنتج: ${uploadResult['error']}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ خطأ في رفع صورة المنتج: $e');
      return null;
    }
  }

  /// الاستماع للتغييرات في المنتجات
  static Stream<DatabaseEvent> listenToProducts({String? ownerId}) {
    return WaterFilterService.listenToPath(
      'Products',
      ownerId: ownerId ?? 'water_filter_products',
    );
  }

  /// إلغاء الاستماع للمنتجات
  static void cancelProductsListener({String? ownerId}) {
    WaterFilterService.cancelListener(
      'Products',
      ownerId: ownerId ?? 'water_filter_products',
    );
  }

  /// التحقق من صحة بيانات المنتج
  static bool _validateProduct(WaterFilterProduct product) {
    if (product.name.trim().isEmpty) {
      debugPrint('❌ اسم المنتج مطلوب');
      return false;
    }

    if (product.brand.trim().isEmpty) {
      debugPrint('❌ العلامة التجارية مطلوبة');
      return false;
    }

    if (product.price <= 0) {
      debugPrint('❌ سعر المنتج يجب أن يكون أكبر من صفر');
      return false;
    }

    if (product.stock < 0) {
      debugPrint('❌ المخزون لا يمكن أن يكون سالباً');
      return false;
    }

    if (product.maintenanceIntervalMonths <= 0) {
      debugPrint('❌ فترة الصيانة يجب أن تكون أكبر من صفر');
      return false;
    }

    return true;
  }

  /// الحصول على المنتجات منخفضة المخزون
  static Future<List<WaterFilterProduct>> getLowStockProducts() async {
    try {
      debugPrint('⚠️ جلب المنتجات منخفضة المخزون...');

      final allProducts = await getAllProducts();
      final lowStockProducts = allProducts
          .where((product) => product.stock <= 5) // حد أدنى 5 قطع
          .toList();

      debugPrint(
          '✅ تم العثور على ${lowStockProducts.length} منتج منخفض المخزون');
      return lowStockProducts;
    } catch (e) {
      debugPrint('❌ خطأ في جلب المنتجات منخفضة المخزون: $e');
      return [];
    }
  }
}
