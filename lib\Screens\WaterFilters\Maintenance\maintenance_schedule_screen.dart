import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/model/water_filter_models.dart';
import 'package:mobile_pos/services/maintenance_service.dart';
import 'package:mobile_pos/Screens/WaterFilters/Maintenance/add_maintenance_schedule_screen.dart';
import 'package:mobile_pos/Screens/WaterFilters/Maintenance/maintenance_reports_screen.dart';

class MaintenanceScheduleScreen extends StatefulWidget {
  const MaintenanceScheduleScreen({super.key});

  @override
  State<MaintenanceScheduleScreen> createState() =>
      _MaintenanceScheduleScreenState();
}

class _MaintenanceScheduleScreenState extends State<MaintenanceScheduleScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<MaintenanceSchedule> _allSchedules = [];
  List<MaintenanceSchedule> _filteredSchedules = [];
  bool _isLoading = true;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _loadSchedules();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadSchedules() async {
    setState(() => _isLoading = true);

    try {
      final schedules = await MaintenanceService.getAllSchedules();
      setState(() {
        _allSchedules = schedules;
        _filterSchedules();
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('خطأ في تحميل جدولات الصيانة: $e');
      setState(() => _isLoading = false);
    }
  }

  void _filterSchedules() {
    List<MaintenanceSchedule> filtered = [];

    switch (_tabController.index) {
      case 0: // الكل
        filtered = _allSchedules;
        break;
      case 1: // اليوم
        filtered = _allSchedules.where((schedule) {
          return _isSameDay(schedule.scheduledDate, DateTime.now());
        }).toList();
        break;
      case 2: // القادمة
        filtered = _allSchedules.where((schedule) {
          return schedule.scheduledDateTime.isAfter(DateTime.now()) &&
              schedule.status != ScheduleStatus.completed &&
              schedule.status != ScheduleStatus.cancelled;
        }).toList();
        break;
      case 3: // المتأخرة
        filtered =
            _allSchedules.where((schedule) => schedule.isOverdue).toList();
        break;
      case 4: // المكتملة
        filtered = _allSchedules
            .where((schedule) => schedule.status == ScheduleStatus.completed)
            .toList();
        break;
    }

    // تطبيق البحث
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((schedule) {
        return schedule.description
                .toLowerCase()
                .contains(_searchQuery.toLowerCase()) ||
            schedule.systemId
                .toLowerCase()
                .contains(_searchQuery.toLowerCase());
      }).toList();
    }

    setState(() {
      _filteredSchedules = filtered;
    });
  }

  bool _isSameDay(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }

  List<MaintenanceSchedule> _getSchedulesForDay(DateTime day) {
    return _allSchedules.where((schedule) {
      return _isSameDay(schedule.scheduledDate, day);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        title: Text(
          'جدولة الصيانة',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.analytics, color: Colors.white),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const MaintenanceReportsScreen(),
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: _loadSchedules,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          labelStyle: GoogleFonts.cairo(fontWeight: FontWeight.bold),
          onTap: (index) => _filterSchedules(),
          tabs: [
            Tab(text: 'الكل (${_allSchedules.length})'),
            Tab(text: 'اليوم (${_getSchedulesForDay(DateTime.now()).length})'),
            Tab(text: 'القادمة'),
            Tab(text: 'متأخرة'),
            Tab(text: 'مكتملة'),
          ],
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        child: Column(
          children: [
            // شريط البحث
            Container(
              padding: const EdgeInsets.all(16),
              child: TextField(
                onChanged: (value) {
                  setState(() {
                    _searchQuery = value;
                    _filterSchedules();
                  });
                },
                decoration: InputDecoration(
                  hintText: 'البحث في الجدولات...',
                  hintStyle: GoogleFonts.cairo(color: Colors.grey.shade600),
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: Colors.grey.shade300),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: Colors.grey.shade300),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: kMainColor),
                  ),
                ),
                style: GoogleFonts.cairo(),
              ),
            ),

            // قائمة الجدولات
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _filteredSchedules.isEmpty
                      ? _buildEmptyState()
                      : ListView.builder(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          itemCount: _filteredSchedules.length,
                          itemBuilder: (context, index) {
                            final schedule = _filteredSchedules[index];
                            return _buildScheduleCard(schedule);
                          },
                        ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const AddMaintenanceScheduleScreen(),
            ),
          ).then((result) {
            if (result == true) {
              _loadSchedules();
            }
          });
        },
        backgroundColor: kMainColor,
        icon: const Icon(Icons.add, color: Colors.white),
        label: Text(
          'جدولة جديدة',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.event_busy,
            size: 80,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 20),
          Text(
            'لا توجد جدولات صيانة',
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 10),
          Text(
            'اضغط على زر "جدولة جديدة" لإضافة موعد صيانة',
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: Colors.grey.shade500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildScheduleCard(MaintenanceSchedule schedule) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: schedule.status.color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // الصف الأول - النوع والحالة
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: schedule.type.color.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        schedule.type.icon,
                        color: schedule.type.color,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          schedule.type.arabicName,
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: schedule.type.color,
                          ),
                        ),
                        Text(
                          'نظام ${schedule.systemId}',
                          style: GoogleFonts.cairo(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: schedule.status.color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: schedule.status.color.withOpacity(0.3),
                    ),
                  ),
                  child: Text(
                    schedule.status.arabicName,
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: schedule.status.color,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // الوصف
            Text(
              schedule.description,
              style: GoogleFonts.cairo(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),

            const SizedBox(height: 12),

            // معلومات التوقيت والأولوية
            Row(
              children: [
                Expanded(
                  child: _buildInfoChip(
                    icon: Icons.access_time,
                    label: 'الموعد',
                    value:
                        '${schedule.scheduledDate.day}/${schedule.scheduledDate.month} - ${schedule.scheduledTime.format(context)}',
                    color: Colors.blue,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildInfoChip(
                    icon: Icons.priority_high,
                    label: 'الأولوية',
                    value: schedule.priority.arabicName,
                    color: schedule.priority.color,
                  ),
                ),
              ],
            ),

            if (schedule.estimatedCost != null) ...[
              const SizedBox(height: 8),
              _buildInfoChip(
                icon: Icons.attach_money,
                label: 'التكلفة المتوقعة',
                value: '${schedule.estimatedCost!.toStringAsFixed(0)} ج.م',
                color: Colors.green,
              ),
            ],

            // تنبيه للمواعيد المتأخرة
            if (schedule.isOverdue) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.shade200),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.warning_amber,
                      color: Colors.red.shade600,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'متأخر ${DateTime.now().difference(schedule.scheduledDateTime).inDays} يوم',
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Colors.red.shade600,
                      ),
                    ),
                  ],
                ),
              ),
            ],

            // أزرار الإجراءات
            if (schedule.status != ScheduleStatus.completed &&
                schedule.status != ScheduleStatus.cancelled) ...[
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                              content: Text('تعديل الجدولة قيد التطوير')),
                        );
                      },
                      icon: const Icon(Icons.edit, size: 16),
                      label: Text(
                        'تعديل',
                        style: GoogleFonts.cairo(fontSize: 12),
                      ),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.blue,
                        side: const BorderSide(color: Colors.blue),
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                              content: Text('بدء الصيانة قيد التطوير')),
                        );
                      },
                      icon: const Icon(Icons.play_arrow, size: 16),
                      label: Text(
                        'بدء',
                        style: GoogleFonts.cairo(fontSize: 12),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoChip({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Row(
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(width: 6),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: GoogleFonts.cairo(
                  fontSize: 10,
                  color: Colors.grey.shade600,
                ),
              ),
              Text(
                value,
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
