import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:path/path.dart' as path;
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// خدمة Firebase للتعامل مع الصور والإشعارات
/// تحل محل خدمة Back4App السابقة
class FirebaseService {
  /// رفع صورة إلى Firebase Storage
  static Future<String?> uploadImage(File imageFile,
      {String? imageType = 'profile'}) async {
    try {
      EasyLoading.show(
        status: 'جاري رفع الصورة...',
        dismissOnTap: false,
      );

      // إنشاء اسم ملف فريد
      final String uniqueFileName =
          '${DateTime.now().millisecondsSinceEpoch}_${path.basename(imageFile.path)}';

      // تحديد المسار في Firebase Storage
      final storageRef = FirebaseStorage.instance
          .ref()
          .child('${FirebaseAuth.instance.currentUser!.uid}/images')
          .child(imageType ?? 'profile')
          .child(uniqueFileName);

      // رفع الملف
      final uploadTask = storageRef.putFile(imageFile);
      final snapshot = await uploadTask.whenComplete(() => null);

      if (snapshot.state == TaskState.success) {
        // الحصول على رابط الصورة
        final String imageUrl = await snapshot.ref.getDownloadURL();
        debugPrint('تم رفع الصورة بنجاح: $imageUrl');

        // حفظ معلومات الصورة في قاعدة البيانات
        try {
          final userId = FirebaseAuth.instance.currentUser!.uid;
          final imageRef = FirebaseDatabase.instance
              .ref()
              .child(userId)
              .child('UserImages')
              .push();

          await imageRef.set({
            'userId': userId,
            'imageUrl': imageUrl,
            'fileName': uniqueFileName,
            'imageType': imageType,
            'uploadDate': DateTime.now().toIso8601String(),
          });

          debugPrint('تم حفظ معلومات الصورة بنجاح');

          // حفظ رابط الصورة في Firebase للرجوع إليه
          if (imageType == 'profile') {
            await _saveProfileImageUrlToFirebase(imageUrl);

            // حفظ الصورة محليًا للاستخدام بدون اتصال
            await _saveProfileImageLocally(imageFile.path);
          }
        } catch (metadataError) {
          debugPrint('خطأ في حفظ بيانات الصورة: $metadataError');
          // حتى لو فشل حفظ المعلومات، نعيد رابط الصورة لأنها تم رفعها بنجاح
        }

        EasyLoading.dismiss();
        return imageUrl;
      } else {
        debugPrint('فشل في رفع الصورة');
        EasyLoading.dismiss();
        EasyLoading.showError('فشل في رفع الصورة');
        return null;
      }
    } catch (e) {
      debugPrint('خطأ في رفع الصورة: $e');
      EasyLoading.dismiss();
      EasyLoading.showError('خطأ في رفع الصورة: $e');
      return null;
    }
  }

  /// حفظ رابط صورة البروفايل في Firebase
  static Future<void> _saveProfileImageUrlToFirebase(String imageUrl) async {
    try {
      final userId = FirebaseAuth.instance.currentUser!.uid;
      final userRef = FirebaseDatabase.instance
          .ref()
          .child(userId)
          .child('Personal Information');

      // تحديث رابط الصورة في Firebase
      await userRef.update({
        'pictureUrl': imageUrl,
        'pictureSource': 'firebase',
        'lastUpdated': DateTime.now().toIso8601String(),
      });

      debugPrint('تم حفظ رابط الصورة في Firebase');
    } catch (e) {
      debugPrint('خطأ في حفظ رابط الصورة في Firebase: $e');
    }
  }

  /// حفظ رابط صورة البروفايل من مصدر خارجي
  static Future<bool> saveProfileImageUrl(String imageUrl,
      {String source = 'external', String? deleteHash}) async {
    try {
      final userId = FirebaseAuth.instance.currentUser!.uid;
      final imageRef = FirebaseDatabase.instance
          .ref()
          .child(userId)
          .child('UserImages')
          .push();

      final imageData = {
        'userId': userId,
        'imageUrl': imageUrl,
        'fileName':
            '${source}_image_${DateTime.now().millisecondsSinceEpoch}.jpg',
        'imageType': 'profile',
        'uploadDate': DateTime.now().toIso8601String(),
        'source': source,
      };

      // إضافة deleteHash إذا كان متوفرًا (مهم لصور Imgur)
      if (deleteHash != null) {
        imageData['deleteHash'] = deleteHash;
      }

      await imageRef.set(imageData);

      // تحديث رابط الصورة في معلومات المستخدم الشخصية
      final userRef = FirebaseDatabase.instance
          .ref()
          .child(userId)
          .child('Personal Information');

      await userRef.update({
        'pictureUrl': imageUrl,
        'pictureSource': source,
        'lastUpdated': DateTime.now().toIso8601String(),
      });

      debugPrint('تم حفظ رابط الصورة في Firebase');
      return true;
    } catch (e) {
      debugPrint('خطأ في حفظ رابط الصورة في Firebase: $e');
      return false;
    }
  }

  /// حفظ صورة البروفايل محليًا
  static Future<void> _saveProfileImageLocally(String imagePath) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('profileImagePath', imagePath);
      debugPrint('تم حفظ مسار الصورة محليًا: $imagePath');
    } catch (e) {
      debugPrint('خطأ في حفظ مسار الصورة محليًا: $e');
    }
  }

  /// الحصول على صورة المستخدم من Firebase
  static Future<String?> getUserProfileImage({
    bool tryLocalFirst = true,
  }) async {
    try {
      // محاولة الحصول على الصورة من التخزين المحلي أولاً
      if (tryLocalFirst) {
        final localImagePath = await _getLocalProfileImagePath();
        if (localImagePath != null && File(localImagePath).existsSync()) {
          debugPrint('تم الحصول على الصورة من التخزين المحلي');
          return localImagePath;
        }
      }

      // الحصول على صورة البروفايل من Firebase
      final firebaseImageUrl = await _getProfileImageFromFirebase();
      if (firebaseImageUrl != null) {
        debugPrint('تم الحصول على الصورة من Firebase');
        return firebaseImageUrl;
      }

      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على صورة المستخدم: $e');
      return null;
    }
  }

  /// الحصول على مسار الصورة المحلية
  static Future<String?> _getLocalProfileImagePath() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString('profileImagePath');
    } catch (e) {
      debugPrint('خطأ في الحصول على مسار الصورة المحلية: $e');
      return null;
    }
  }

  /// الحصول على صورة البروفايل من Firebase
  static Future<String?> _getProfileImageFromFirebase() async {
    try {
      final userId = FirebaseAuth.instance.currentUser!.uid;
      final userRef = FirebaseDatabase.instance
          .ref()
          .child(userId)
          .child('Personal Information');
      final snapshot = await userRef.get();

      if (snapshot.exists) {
        final data = jsonDecode(jsonEncode(snapshot.value));
        final pictureUrl = data['pictureUrl']?.toString();

        if (pictureUrl != null && pictureUrl.isNotEmpty) {
          return pictureUrl;
        }
      }

      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على صورة البروفايل من Firebase: $e');
      return null;
    }
  }

  /// مزامنة صورة البروفايل بين الأجهزة
  static Future<String?> syncProfileImage() async {
    try {
      // التحقق من وجود صورة محلية
      final localImagePath = await _getLocalProfileImagePath();

      // الحصول على صورة Firebase
      final firebaseImageUrl = await _getProfileImageFromFirebase();

      // إذا كانت هناك صورة في Firebase
      if (firebaseImageUrl != null && firebaseImageUrl.isNotEmpty) {
        return firebaseImageUrl;
      }
      // إذا كانت هناك صورة محلية فقط
      else if (localImagePath != null && File(localImagePath).existsSync()) {
        // رفع الصورة المحلية إلى Firebase
        final imageUrl = await uploadImage(File(localImagePath));
        if (imageUrl != null) {
          debugPrint('تم رفع الصورة المحلية إلى Firebase');
          return imageUrl;
        }
      }

      return null;
    } catch (e) {
      debugPrint('خطأ في مزامنة صورة البروفايل: $e');
      return null;
    }
  }
}
