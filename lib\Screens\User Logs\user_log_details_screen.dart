import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/model/user_log_model.dart';

/// شاشة تفاصيل سجل المستخدم
class UserLogDetailsScreen extends StatelessWidget {
  final UserLogModel log;

  const UserLogDetailsScreen({super.key, required this.log});

  @override
  Widget build(BuildContext context) {
    // الحصول على لون وأيقونة نوع النشاط
    final (color, icon) = _getActionTypeColorAndIcon(log.actionType);

    // تنسيق التاريخ
    final formattedDate =
        DateFormat('yyyy-MM-dd HH:mm:ss').format(log.timestamp);

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(
          'تفاصيل السجل',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        backgroundColor: kMainColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // بطاقة المعلومات الرئيسية
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // أيقونة ونوع النشاط
                    Row(
                      children: [
                        CircleAvatar(
                          backgroundColor: color.withAlpha(
                              51), // استخدام withAlpha بدلاً من withOpacity
                          radius: 24,
                          child: Icon(
                            icon,
                            color: color,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                _getActionTypeDisplayName(log.actionType),
                                style: GoogleFonts.cairo(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: color,
                                ),
                              ),
                              Text(
                                formattedDate,
                                style: GoogleFonts.cairo(
                                  fontSize: 14,
                                  color: Colors.grey,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    const Divider(height: 32),

                    // وصف النشاط
                    Text(
                      'الوصف',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey[700],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      log.description,
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                      ),
                    ),

                    const SizedBox(height: 16),

                    // معلومات المستخدم
                    Text(
                      'المستخدم',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey[700],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        const Icon(
                          Icons.person,
                          color: Colors.grey,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          log.userName,
                          style: GoogleFonts.cairo(
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        const Icon(
                          Icons.fingerprint,
                          color: Colors.grey,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          log.userId,
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // بطاقة البيانات المرتبطة
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'البيانات المرتبطة',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey[700],
                      ),
                    ),
                    const SizedBox(height: 16),

                    // عرض البيانات المرتبطة
                    // استخدام for بدلاً من map().toList() لتجنب التحذير
                    for (var entry in log.data.entries)
                      Padding(
                        padding: const EdgeInsets.only(bottom: 12),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '${entry.key}:',
                              style: GoogleFonts.cairo(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: Colors.grey[600],
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                _formatDataValue(entry.value),
                                style: GoogleFonts.cairo(
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                    // إذا لم تكن هناك بيانات مرتبطة
                    if (log.data.isEmpty)
                      Text(
                        'لا توجد بيانات مرتبطة',
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // معرف السجل
            Center(
              child: Text(
                'معرف السجل: ${log.id}',
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  color: Colors.grey,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// الحصول على لون وأيقونة نوع النشاط
  (Color, IconData) _getActionTypeColorAndIcon(String actionType) {
    if (actionType.startsWith('sales_')) {
      return (Colors.green, Icons.shopping_cart);
    } else if (actionType.startsWith('purchase_')) {
      return (Colors.blue, Icons.shopping_bag);
    } else if (actionType.startsWith('due_')) {
      return (Colors.red, Icons.money_off);
    } else if (actionType.startsWith('inventory_')) {
      return (Colors.orange, Icons.inventory);
    } else if (actionType.startsWith('customer_')) {
      return (Colors.purple, Icons.person);
    } else if (actionType.startsWith('supplier_')) {
      return (Colors.teal, Icons.business);
    } else if (actionType.startsWith('user_')) {
      return (Colors.indigo, Icons.person_pin);
    } else if (actionType.startsWith('system_')) {
      return (Colors.grey, Icons.settings);
    } else {
      return (Colors.black, Icons.info);
    }
  }

  /// الحصول على اسم العرض لنوع النشاط
  String _getActionTypeDisplayName(String actionType) {
    switch (actionType) {
      // أنشطة المبيعات
      case UserLogActionTypes.salesCreated:
        return 'إنشاء فاتورة مبيعات';
      case UserLogActionTypes.salesUpdated:
        return 'تحديث فاتورة مبيعات';
      case UserLogActionTypes.salesDeleted:
        return 'حذف فاتورة مبيعات';
      case UserLogActionTypes.salesPaymentReceived:
        return 'استلام دفعة مبيعات';
      case UserLogActionTypes.salesReturn:
        return 'مرتجع مبيعات';

      // أنشطة المشتريات
      case UserLogActionTypes.purchaseCreated:
        return 'إنشاء فاتورة مشتريات';
      case UserLogActionTypes.purchaseUpdated:
        return 'تحديث فاتورة مشتريات';
      case UserLogActionTypes.purchaseDeleted:
        return 'حذف فاتورة مشتريات';
      case UserLogActionTypes.purchasePaymentMade:
        return 'دفع فاتورة مشتريات';
      case UserLogActionTypes.purchaseReturn:
        return 'مرتجع مشتريات';

      // أنشطة المديونية
      case UserLogActionTypes.dueCreated:
        return 'إنشاء مديونية';
      case UserLogActionTypes.dueUpdated:
        return 'تحديث مديونية';
      case UserLogActionTypes.duePaymentReceived:
        return 'استلام دفعة مديونية';
      case UserLogActionTypes.dueReminder:
        return 'تذكير بمديونية';

      // أنشطة المخزون
      case UserLogActionTypes.inventoryAdded:
        return 'إضافة مخزون';
      case UserLogActionTypes.inventoryUpdated:
        return 'تحديث مخزون';
      case UserLogActionTypes.inventoryRemoved:
        return 'إزالة مخزون';
      case UserLogActionTypes.inventoryAdjusted:
        return 'تعديل مخزون';

      // أنشطة العملاء
      case UserLogActionTypes.customerCreated:
        return 'إنشاء عميل';
      case UserLogActionTypes.customerUpdated:
        return 'تحديث بيانات عميل';
      case UserLogActionTypes.customerDeleted:
        return 'حذف عميل';

      // أنشطة الموردين
      case UserLogActionTypes.supplierCreated:
        return 'إنشاء مورد';
      case UserLogActionTypes.supplierUpdated:
        return 'تحديث بيانات مورد';
      case UserLogActionTypes.supplierDeleted:
        return 'حذف مورد';

      // أنشطة المستخدمين
      case UserLogActionTypes.userLogin:
        return 'تسجيل دخول';
      case UserLogActionTypes.userLogout:
        return 'تسجيل خروج';
      case UserLogActionTypes.userCreated:
        return 'إنشاء مستخدم';
      case UserLogActionTypes.userUpdated:
        return 'تحديث بيانات مستخدم';
      case UserLogActionTypes.userDeleted:
        return 'حذف مستخدم';

      // أنشطة النظام
      case UserLogActionTypes.systemBackup:
        return 'نسخ احتياطي للنظام';
      case UserLogActionTypes.systemRestore:
        return 'استعادة النظام';
      case UserLogActionTypes.systemSettings:
        return 'تغيير إعدادات النظام';

      default:
        // إذا كان نوع النشاط غير معروف، نعرض النوع كما هو
        return actionType.replaceAll('_', ' ');
    }
  }

  /// تنسيق قيمة البيانات للعرض
  String _formatDataValue(dynamic value) {
    if (value == null) {
      return 'غير محدد';
    } else if (value is Map) {
      return value.entries
          .map((e) => '${e.key}: ${_formatDataValue(e.value)}')
          .join(', ');
    } else if (value is List) {
      return value.map((e) => _formatDataValue(e)).join(', ');
    } else if (value is DateTime) {
      return DateFormat('yyyy-MM-dd HH:mm:ss').format(value);
    } else {
      return value.toString();
    }
  }
}
