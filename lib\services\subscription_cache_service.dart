import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// خدمة التخزين المؤقت للاشتراك
/// تستخدم لتخزين بيانات الاشتراك محليًا وتحسين الأداء
class SubscriptionCacheService {
  // مفاتيح التخزين المؤقت
  static const String _cacheKeyPrefix = 'subscription_cache_';
  static const String _lastRefreshKey = 'subscription_cache_last_refresh';

  // مدة صلاحية التخزين المؤقت (30 دقيقة)
  static const Duration cacheDuration = Duration(minutes: 30);

  /// حفظ نتيجة التحقق من الاشتراك في التخزين المؤقت
  static Future<void> cacheSubscriptionStatus(
      String itemKey, bool status) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // حفظ النتيجة
      await prefs.setBool('$_cacheKeyPrefix$itemKey', status);

      // تحديث وقت آخر تحديث
      await prefs.setString(_lastRefreshKey, DateTime.now().toIso8601String());

      debugPrint('تم تخزين حالة الاشتراك لـ $itemKey: $status');
    } catch (e) {
      debugPrint('خطأ في تخزين حالة الاشتراك: $e');
    }
  }

  /// الحصول على نتيجة التحقق من الاشتراك من التخزين المؤقت
  static Future<bool?> getCachedSubscriptionStatus(String itemKey) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // التحقق من وجود وقت آخر تحديث
      final lastRefreshStr = prefs.getString(_lastRefreshKey);
      if (lastRefreshStr == null) {
        debugPrint('لا يوجد تخزين مؤقت للاشتراك');
        return null;
      }

      // التحقق من صلاحية التخزين المؤقت
      final lastRefresh = DateTime.parse(lastRefreshStr);
      if (DateTime.now().difference(lastRefresh) > cacheDuration) {
        debugPrint('انتهت صلاحية التخزين المؤقت للاشتراك');
        return null;
      }

      // الحصول على النتيجة المخزنة
      if (prefs.containsKey('$_cacheKeyPrefix$itemKey')) {
        final status = prefs.getBool('$_cacheKeyPrefix$itemKey');
        debugPrint('تم استرداد حالة الاشتراك المخزنة لـ $itemKey: $status');
        return status;
      }

      return null;
    } catch (e) {
      debugPrint('خطأ في استرداد حالة الاشتراك المخزنة: $e');
      return null;
    }
  }

  /// مسح التخزين المؤقت للاشتراك
  static Future<void> clearSubscriptionCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // الحصول على جميع المفاتيح
      final keys = prefs.getKeys();

      // مسح جميع مفاتيح التخزين المؤقت للاشتراك
      for (final key in keys) {
        if (key.startsWith(_cacheKeyPrefix) || key == _lastRefreshKey) {
          await prefs.remove(key);
        }
      }

      debugPrint('تم مسح التخزين المؤقت للاشتراك');
    } catch (e) {
      debugPrint('خطأ في مسح التخزين المؤقت للاشتراك: $e');
    }
  }

  /// تخزين بيانات الاشتراك كاملة
  static Future<void> cacheSubscriptionData(Map<String, dynamic> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // تحويل البيانات إلى سلسلة JSON
      final jsonData = jsonEncode(data);

      // حفظ البيانات
      await prefs.setString('${_cacheKeyPrefix}full_data', jsonData);

      // تحديث وقت آخر تحديث
      await prefs.setString(_lastRefreshKey, DateTime.now().toIso8601String());

      debugPrint('تم تخزين بيانات الاشتراك كاملة');
    } catch (e) {
      debugPrint('خطأ في تخزين بيانات الاشتراك: $e');
    }
  }

  /// الحصول على بيانات الاشتراك كاملة
  static Future<Map<String, dynamic>?> getCachedSubscriptionData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // التحقق من وجود وقت آخر تحديث
      final lastRefreshStr = prefs.getString(_lastRefreshKey);
      if (lastRefreshStr == null) {
        debugPrint('لا يوجد تخزين مؤقت لبيانات الاشتراك');
        return null;
      }

      // التحقق من صلاحية التخزين المؤقت
      final lastRefresh = DateTime.parse(lastRefreshStr);
      if (DateTime.now().difference(lastRefresh) > cacheDuration) {
        debugPrint('انتهت صلاحية التخزين المؤقت لبيانات الاشتراك');
        return null;
      }

      // الحصول على البيانات المخزنة
      final jsonData = prefs.getString('${_cacheKeyPrefix}full_data');
      if (jsonData != null) {
        final data = jsonDecode(jsonData) as Map<String, dynamic>;
        debugPrint('تم استرداد بيانات الاشتراك المخزنة');
        return data;
      }

      return null;
    } catch (e) {
      debugPrint('خطأ في استرداد بيانات الاشتراك المخزنة: $e');
      return null;
    }
  }
}
