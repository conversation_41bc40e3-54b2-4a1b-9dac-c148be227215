import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/generated/l10n.dart' as lang;

import '../../constant.dart';

class EditSocialmedia extends StatefulWidget {
  const EditSocialmedia({super.key});

  @override
  // ignore: library_private_types_in_public_api
  _EditSocialmediaState createState() => _EditSocialmediaState();
}

class _EditSocialmediaState extends State<EditSocialmedia> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          lang.S.of(context).editSocailMedia,
          style: GoogleFonts.poppins(
            color: Colors.black,
            fontSize: 20.0,
          ),
        ),
        iconTheme: const IconThemeData(color: Colors.black),
        centerTitle: true,
        backgroundColor: Colors.white,
        elevation: 0.0,
      ),
      body: Column(
        children: [
          const SizedBox(
            height: 10.0,
          ),
          Padding(
            padding: const EdgeInsets.only(bottom: 10.0, left: 10.0),
            child: SocialMediaEditCard(
              iconWidget: const Image(
                image: AssetImage('assets/images/fb.png'),
              ),
              socialMediaName: lang.S.of(context).facebok,
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(bottom: 10.0, left: 10.0),
            child: SocialMediaEditCard(
              iconWidget: const Image(
                image: AssetImage('assets/images/twitter.png'),
              ),
              socialMediaName: lang.S.of(context).twitter,
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(bottom: 10.0, left: 10.0),
            child: SocialMediaEditCard(
              iconWidget: const Image(
                image: AssetImage('assets/images/insta.png'),
              ),
              socialMediaName: lang.S.of(context).instragram,
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(bottom: 10.0, left: 10.0),
            child: SocialMediaEditCard(
              iconWidget: const Image(
                image: AssetImage('assets/images/linkedin.png'),
              ),
              socialMediaName: lang.S.of(context).linkedIn,
            ),
          ),
        ],
      ),
    );
  }
}

// ignore: must_be_immutable
class SocialMediaEditCard extends StatelessWidget {
  SocialMediaEditCard({
    super.key,
    required this.iconWidget,
    required this.socialMediaName,
  });

  Widget iconWidget;
  final String socialMediaName;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        iconWidget,
        Padding(
          padding: const EdgeInsets.only(left: 8.0),
          child: Text(
            socialMediaName,
            style: GoogleFonts.poppins(
              color: Colors.black,
              fontSize: 18,
            ),
          ),
        ),
        const Spacer(),
        Container(
          width: 95,
          height: 40,
          padding: const EdgeInsets.only(top: 5.0, bottom: 5.0),
          decoration: kButtonDecoration.copyWith(color: kMainColor),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.add,
                color: Colors.white,
              ),
              Text(
                lang.S.of(context).link,
                style: GoogleFonts.poppins(fontSize: 15.0, color: Colors.white),
              ),
            ],
          ),
        ),
        const SizedBox(
          width: 30.0,
        ),
      ],
    );
  }
}
