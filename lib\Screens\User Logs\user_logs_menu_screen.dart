import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/Provider/user_log_provider.dart';
import 'package:mobile_pos/constant.dart';
// لا حاجة لاستيراد هذه المكتبات

/// شاشة قائمة سجلات المستخدم
class UserLogsMenuScreen extends ConsumerWidget {
  const UserLogsMenuScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        title: Text(
          'سجلات النظام',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        iconTheme: const IconThemeData(color: Colors.white),
        elevation: 0.0,
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(30),
            topRight: Radius.circular(30),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            children: [
              const SizedBox(height: 20),

              // عنوان الصفحة
              Text(
                'اختر نوع السجلات',
                style: GoogleFonts.cairo(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),

              const SizedBox(height: 30),

              // قائمة أنواع السجلات
              Expanded(
                child: GridView.count(
                  crossAxisCount: 2,
                  crossAxisSpacing: 15,
                  mainAxisSpacing: 15,
                  children: [
                    // سجلات المبيعات
                    _buildLogTypeCard(
                      context: context,
                      title: 'سجلات المبيعات',
                      icon: Icons.shopping_cart,
                      color: Colors.green,
                      onTap: () {
                        _navigateToLogScreen(
                          context: context,
                          ref: ref,
                          actionType: 'sales_',
                          title: 'سجلات المبيعات',
                        );
                      },
                    ),

                    // سجلات المشتريات
                    _buildLogTypeCard(
                      context: context,
                      title: 'سجلات المشتريات',
                      icon: Icons.shopping_bag,
                      color: Colors.blue,
                      onTap: () {
                        _navigateToLogScreen(
                          context: context,
                          ref: ref,
                          actionType: 'purchase_',
                          title: 'سجلات المشتريات',
                        );
                      },
                    ),

                    // سجلات المديونية
                    _buildLogTypeCard(
                      context: context,
                      title: 'سجلات المديونية',
                      icon: Icons.money_off,
                      color: Colors.red,
                      onTap: () {
                        _navigateToLogScreen(
                          context: context,
                          ref: ref,
                          actionType: 'due_',
                          title: 'سجلات المديونية',
                        );
                      },
                    ),

                    // سجلات المخزون
                    _buildLogTypeCard(
                      context: context,
                      title: 'سجلات المخزون',
                      icon: Icons.inventory,
                      color: Colors.orange,
                      onTap: () {
                        _navigateToLogScreen(
                          context: context,
                          ref: ref,
                          actionType: 'inventory_',
                          title: 'سجلات المخزون',
                        );
                      },
                    ),

                    // سجلات العملاء
                    _buildLogTypeCard(
                      context: context,
                      title: 'سجلات العملاء',
                      icon: Icons.person,
                      color: Colors.purple,
                      onTap: () {
                        _navigateToLogScreen(
                          context: context,
                          ref: ref,
                          actionType: 'customer_',
                          title: 'سجلات العملاء',
                        );
                      },
                    ),

                    // سجلات الموردين
                    _buildLogTypeCard(
                      context: context,
                      title: 'سجلات الموردين',
                      icon: Icons.business,
                      color: Colors.teal,
                      onTap: () {
                        _navigateToLogScreen(
                          context: context,
                          ref: ref,
                          actionType: 'supplier_',
                          title: 'سجلات الموردين',
                        );
                      },
                    ),

                    // سجلات المستخدمين
                    _buildLogTypeCard(
                      context: context,
                      title: 'سجلات المستخدمين',
                      icon: Icons.person_pin,
                      color: Colors.indigo,
                      onTap: () {
                        _navigateToLogScreen(
                          context: context,
                          ref: ref,
                          actionType: 'user_',
                          title: 'سجلات المستخدمين',
                        );
                      },
                    ),

                    // جميع السجلات
                    _buildLogTypeCard(
                      context: context,
                      title: 'جميع السجلات',
                      icon: Icons.list_alt,
                      color: Colors.grey[700]!,
                      onTap: () {
                        _navigateToLogScreen(
                          context: context,
                          ref: ref,
                          actionType: null,
                          title: 'جميع السجلات',
                        );
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء بطاقة نوع السجل
  Widget _buildLogTypeCard({
    required BuildContext context,
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(15),
          boxShadow: [
            BoxShadow(
              color: Colors.grey
                  .withAlpha(51), // استخدام withAlpha بدلاً من withOpacity
              spreadRadius: 2,
              blurRadius: 5,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircleAvatar(
              backgroundColor:
                  color.withAlpha(51), // استخدام withAlpha بدلاً من withOpacity
              radius: 30,
              child: Icon(
                icon,
                color: color,
                size: 30,
              ),
            ),
            const SizedBox(height: 15),
            Text(
              title,
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// الانتقال إلى شاشة السجلات
  void _navigateToLogScreen({
    required BuildContext context,
    required WidgetRef ref,
    required String? actionType,
    required String title,
  }) {
    // استخدام تاريخ اليوم كتاريخ البداية والنهاية
    final today = DateTime.now();

    // إنشاء فلتر سجلات المستخدم
    final filter = UserLogFilter(
      actionType: actionType,
      startDate: today,
      endDate: today,
      limit: 100,
    );

    // تحديث مزود سجلات المستخدم
    final _ = ref.refresh(userLogsProvider(
        filter)); // استخدام متغير لتجنب تحذير القيمة غير المستخدمة

    // طباعة معلومات تشخيصية
    debugPrint('الانتقال إلى شاشة السجلات: $actionType - $title');
    debugPrint('تاريخ البحث: ${today.toString()}');

    // الانتقال إلى شاشة السجلات
    Navigator.pushNamed(
      context,
      '/UserLogs',
      arguments: {
        'actionType': actionType,
        'title': title,
        'startDate': today,
        'endDate': today,
      },
    );
  }
}
