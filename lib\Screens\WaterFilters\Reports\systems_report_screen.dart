import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/model/water_filter_models.dart';
import 'package:mobile_pos/services/water_filter_service.dart';

class SystemsReportScreen extends StatefulWidget {
  const SystemsReportScreen({super.key});

  @override
  State<SystemsReportScreen> createState() => _SystemsReportScreenState();
}

class _SystemsReportScreenState extends State<SystemsReportScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  List<WaterFilterSystem> _allSystems = [];

  // إحصائيات الأنظمة
  Map<String, dynamic> _stats = {};

  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 1, vsync: this);
    _loadSystemsData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadSystemsData() async {
    setState(() => _isLoading = true);

    try {
      // تحميل الأنظمة
      final systemsData = await WaterFilterService.getData('Systems');
      final systems = <WaterFilterSystem>[];

      systemsData.forEach((key, value) {
        try {
          final system = WaterFilterSystem.fromJson(
            Map<String, dynamic>.from(value),
          );
          systems.add(system);
        } catch (e) {
          debugPrint('خطأ في معالجة نظام: $e');
        }
      });

      setState(() {
        _allSystems = systems;
        _calculateStats();
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات الأنظمة: $e');
      setState(() => _isLoading = false);
    }
  }

  void _calculateStats() {
    final totalSystems = _allSystems.length;

    // إحصائيات حسب الحالة
    final activeSystems =
        _allSystems.where((s) => s.status == FilterSystemStatus.active).length;
    final needsMaintenanceSystems = _allSystems
        .where((s) => s.status == FilterSystemStatus.needsMaintenance)
        .length;
    final overdueSystems =
        _allSystems.where((s) => s.status == FilterSystemStatus.overdue).length;
    final inactiveSystems = _allSystems
        .where((s) => s.status == FilterSystemStatus.inactive)
        .length;

    // إحصائيات الضمان
    final underWarrantySystems =
        _allSystems.where((s) => s.isUnderWarranty).length;
    final expiredWarrantySystems = totalSystems - underWarrantySystems;

    // إحصائيات مالية
    final totalValue =
        _allSystems.fold(0.0, (sum, system) => sum + system.totalCost);
    final totalPaid =
        _allSystems.fold(0.0, (sum, system) => sum + system.paidAmount);
    final totalRemaining =
        _allSystems.fold(0.0, (sum, system) => sum + system.remainingAmount);

    // الأنظمة الجديدة (آخر 30 يوم)
    final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
    final newSystems = _allSystems
        .where(
            (s) => s.createdAt != null && s.createdAt!.isAfter(thirtyDaysAgo))
        .length;

    // إحصائيات الصيانة
    final systemsNeedMaintenance =
        _allSystems.where((s) => s.isMaintenanceOverdue).length;

    setState(() {
      _stats = {
        'total': totalSystems,
        'active': activeSystems,
        'needsMaintenance': needsMaintenanceSystems,
        'overdue': overdueSystems,
        'inactive': inactiveSystems,
        'underWarranty': underWarrantySystems,
        'expiredWarranty': expiredWarrantySystems,
        'totalValue': totalValue,
        'totalPaid': totalPaid,
        'totalRemaining': totalRemaining,
        'newSystems': newSystems,
        'maintenanceOverdue': systemsNeedMaintenance,
      };
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        title: Text(
          'تقرير الأنظمة المركبة',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: _loadSystemsData,
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _buildStatsTab(),
      ),
    );
  }

  Widget _buildStatsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('إحصائيات الأنظمة المركبة'),
          const SizedBox(height: 16),
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 0.9,
            children: [
              _buildStatCard(
                title: 'إجمالي الأنظمة',
                value: '${_stats['total'] ?? 0}',
                icon: Icons.settings,
                color: Colors.blue,
              ),
              _buildStatCard(
                title: 'أنظمة نشطة',
                value: '${_stats['active'] ?? 0}',
                icon: Icons.check_circle,
                color: Colors.green,
              ),
              _buildStatCard(
                title: 'تحتاج صيانة',
                value: '${_stats['needsMaintenance'] ?? 0}',
                icon: Icons.build,
                color: Colors.orange,
              ),
              _buildStatCard(
                title: 'متأخرة عن الصيانة',
                value: '${_stats['overdue'] ?? 0}',
                icon: Icons.warning,
                color: Colors.red,
              ),
            ],
          ),
          const SizedBox(height: 24),
          _buildSectionTitle('الأنظمة الجديدة والضمان'),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildTypeCard(
                  title: 'أنظمة جديدة',
                  subtitle: 'آخر 30 يوم',
                  value: '${_stats['newSystems'] ?? 0}',
                  color: Colors.purple,
                  icon: Icons.new_releases,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildTypeCard(
                  title: 'تحت الضمان',
                  subtitle: 'ضمان ساري',
                  value: '${_stats['underWarranty'] ?? 0}',
                  color: Colors.teal,
                  icon: Icons.verified_user,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Helper Methods
  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: GoogleFonts.cairo(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: kMainColor,
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 28),
          const SizedBox(height: 8),
          Flexible(
            child: Text(
              value,
              style: GoogleFonts.cairo(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(height: 4),
          Flexible(
            child: Text(
              title,
              style: GoogleFonts.cairo(
                fontSize: 11,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTypeCard({
    required String title,
    required String subtitle,
    required String value,
    required Color color,
    required IconData icon,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(width: 8),
              Text(
                title,
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}
