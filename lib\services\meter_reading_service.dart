import 'dart:io';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/material.dart';
import 'package:image/image.dart' as img;

class MeterReadingService {
  static const String _geminiApiKey = 'AIzaSyC48Cc1QaQ-YCkix4k-ERMQjCy3ahlhfqU';
  static const String _geminiApiUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent';

  /// قراءة العداد من الصورة باستخدام Gemini AI
  static Future<Map<String, dynamic>> readMeterFromImage(File imageFile) async {
    try {
      // ضغط الصورة لتقليل الحجم
      final compressedImage = await _compressImage(imageFile);
      
      // تحويل الصورة إلى base64
      final bytes = await compressedImage.readAsBytes();
      final base64Image = base64Encode(bytes);

      // إعداد الطلب للـ API
      final requestBody = {
        "contents": [
          {
            "parts": [
              {
                "text": """
أنت خبير في قراءة عدادات السيارات والمركبات. 
انظر إلى هذه الصورة واقرأ الرقم الظاهر في العداد بدقة.

المطلوب:
1. اقرأ الرقم الكامل الظاهر في العداد
2. تأكد من دقة القراءة
3. إذا كان العداد غير واضح، اذكر ذلك

أرجع النتيجة في صيغة JSON كالتالي:
{
  "reading": "الرقم المقروء",
  "confidence": "نسبة الثقة من 0 إلى 100",
  "isValid": true/false,
  "notes": "أي ملاحظات إضافية"
}
"""
              },
              {
                "inline_data": {
                  "mime_type": "image/jpeg",
                  "data": base64Image
                }
              }
            ]
          }
        ],
        "generationConfig": {
          "temperature": 0.1,
          "topK": 1,
          "topP": 1,
          "maxOutputTokens": 1024,
        }
      };

      // إرسال الطلب
      final response = await http.post(
        Uri.parse('$_geminiApiUrl?key=$_geminiApiKey'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: json.encode(requestBody),
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        final content = responseData['candidates'][0]['content']['parts'][0]['text'];
        
        // استخراج JSON من النص
        final jsonMatch = RegExp(r'\{.*\}', dotAll: true).firstMatch(content);
        if (jsonMatch != null) {
          final jsonString = jsonMatch.group(0)!;
          final result = json.decode(jsonString);
          
          return {
            'success': true,
            'reading': result['reading']?.toString() ?? '',
            'confidence': double.tryParse(result['confidence']?.toString() ?? '0') ?? 0.0,
            'isValid': result['isValid'] ?? false,
            'notes': result['notes']?.toString() ?? '',
          };
        }
      }

      return {
        'success': false,
        'error': 'فشل في قراءة العداد من الصورة',
        'reading': '',
        'confidence': 0.0,
        'isValid': false,
        'notes': 'خطأ في معالجة الصورة',
      };

    } catch (e) {
      debugPrint('خطأ في قراءة العداد: $e');
      return {
        'success': false,
        'error': 'حدث خطأ: $e',
        'reading': '',
        'confidence': 0.0,
        'isValid': false,
        'notes': 'خطأ تقني في المعالجة',
      };
    }
  }

  /// ضغط الصورة لتقليل الحجم
  static Future<File> _compressImage(File imageFile) async {
    try {
      final bytes = await imageFile.readAsBytes();
      final image = img.decodeImage(bytes);
      
      if (image == null) return imageFile;

      // تصغير الصورة إذا كانت كبيرة
      final resized = img.copyResize(
        image,
        width: image.width > 1024 ? 1024 : image.width,
        height: image.height > 1024 ? 1024 : image.height,
      );

      // ضغط الصورة
      final compressedBytes = img.encodeJpg(resized, quality: 85);
      
      // حفظ الصورة المضغوطة
      final compressedFile = File('${imageFile.path}_compressed.jpg');
      await compressedFile.writeAsBytes(compressedBytes);
      
      return compressedFile;
    } catch (e) {
      debugPrint('خطأ في ضغط الصورة: $e');
      return imageFile;
    }
  }

  /// التحقق من صحة قراءة العداد
  static bool validateMeterReading(String reading) {
    if (reading.isEmpty) return false;
    
    // التحقق من أن القراءة تحتوي على أرقام فقط
    final numericRegex = RegExp(r'^\d+(\.\d+)?$');
    return numericRegex.hasMatch(reading.trim());
  }

  /// تنسيق قراءة العداد
  static String formatMeterReading(String reading) {
    try {
      final number = double.parse(reading);
      return number.toStringAsFixed(1);
    } catch (e) {
      return reading;
    }
  }
}
