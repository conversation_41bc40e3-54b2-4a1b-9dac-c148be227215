import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:mobile_pos/Provider/sms_template_provider.dart';
// import 'package:mobile_pos/repository/sms_template_repo.dart'; // غير مستخدم
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/models/sms_template_model.dart';

class WhatsappMarketingScreen extends StatefulWidget {
  const WhatsappMarketingScreen({super.key});

  static const String route = '/whatsapp_marketing';

  @override
  State<WhatsappMarketingScreen> createState() =>
      _WhatsappMarketingScreenState();
}

class _WhatsappMarketingScreenState extends State<WhatsappMarketingScreen> {
  ScrollController mainScroll = ScrollController();
  TextEditingController salesTemplateController = TextEditingController();
  TextEditingController salesReturnTemplateController = TextEditingController();
  TextEditingController quotationTemplateController = TextEditingController();
  TextEditingController purchaseTemplateController = TextEditingController();
  TextEditingController purchaseReturnTemplateController =
      TextEditingController();
  TextEditingController dueTemplateController = TextEditingController();
  TextEditingController bulkTemplateController = TextEditingController();

  // Sales list of strings
  List<String> salesFields = [
    '{{CUSTOMER_NAME}}',
    '{{CUSTOMER_ADDRESS}}',
    '{{CUSTOMER_GST}}',
    '{{INVOICE_NUMBER}}',
    '{{PURCHASE_DATE}}',
    '{{TOTAL_AMOUNT}}',
    '{{DUE_AMOUNT}}',
    '{{SERVICE_CHARGE}}',
    '{{VAT}}',
    '{{DISCOUNT_AMOUNT}}',
    '{{TOTAL_QUANTITY}}',
    '{{PAYMENT_TYPE}}',
  ];

  // Purchase list of strings
  List<String> purchaseFields = [
    '{{CUSTOMER_NAME}}',
    '{{CUSTOMER_ADDRESS}}',
    '{{INVOICE_NUMBER}}',
    '{{PURCHASE_DATE}}',
    '{{TOTAL_AMOUNT}}',
    '{{DUE_AMOUNT}}',
    '{{DISCOUNT_AMOUNT}}',
    '{{PAYMENT_TYPE}}',
  ];

  // Due list of strings
  List<String> dueFields = [
    '{{CUSTOMER_NAME}}',
    '{{CUSTOMER_ADDRESS}}',
    '{{CUSTOMER_GST}}',
    '{{INVOICE_NUMBER}}',
    '{{PURCHASE_DATE}}',
    '{{TOTAL_DUE}}',
    '{{DUE_AMOUNT_AFTER_PAY}}',
    '{{PAY_DUE_AMOUNT}}',
    '{{PAYMENT_TYPE}}',
  ];

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          title: const Text('إدارة رسائل الواتساب'),
          backgroundColor: kMainColor,
          centerTitle: true,
          elevation: 0,
          iconTheme: const IconThemeData(color: Colors.white),
          titleTextStyle: const TextStyle(
              color: Colors.white, fontSize: 20, fontWeight: FontWeight.bold),
          actions: [
            // زر التبديل إلى النسخة المحسنة
            IconButton(
              icon: const Icon(Icons.speed, color: Colors.white),
              tooltip: 'النسخة المحسنة (أداء أفضل)',
              onPressed: () {
                Navigator.pushNamed(context, '/OptimizedWhatsappMarketing');
              },
            ),
          ],
        ),
        body: Scrollbar(
          controller: mainScroll,
          child: SingleChildScrollView(
            controller: mainScroll,
            child: Consumer(builder: (_, ref, watch) {
              final templates = ref.watch(smsTemplateProvider);
              return templates.when(
                data: (templates) {
                  salesTemplateController.text = templates.saleTemplate ?? "";
                  salesReturnTemplateController.text =
                      templates.saleReturnTemplate ?? "";
                  quotationTemplateController.text =
                      templates.quotationTemplate ?? "";
                  purchaseTemplateController.text =
                      templates.purchaseTemplate ?? "";
                  purchaseReturnTemplateController.text =
                      templates.purchaseReturnTemplate ?? "";
                  dueTemplateController.text = templates.dueTemplate ?? "";
                  bulkTemplateController.text = templates.bulkSmsTemplate ?? "";

                  return Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(10),
                            boxShadow: defaultBoxShadow(),
                          ),
                          child: Column(
                            children: [
                              Padding(
                                padding: const EdgeInsets.all(20.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      "قوالب رسائل الواتساب التسويقية",
                                      style: boldTextStyle(size: 20),
                                    ),
                                    20.height,
                                    _buildTemplateSection(
                                      title: "قالب المبيعات:",
                                      controller: salesTemplateController,
                                      hintText: "أدخل قالب المبيعات",
                                      fields: salesFields,
                                    ),
                                    20.height,
                                    _buildTemplateSection(
                                      title: "قالب مرتجع المبيعات:",
                                      controller: salesReturnTemplateController,
                                      hintText: "أدخل قالب مرتجع المبيعات",
                                      fields: salesFields,
                                    ),
                                    20.height,
                                    _buildTemplateSection(
                                      title: "قالب عرض السعر:",
                                      controller: quotationTemplateController,
                                      hintText: "أدخل قالب عرض السعر",
                                      fields: salesFields,
                                    ),
                                    20.height,
                                    _buildTemplateSection(
                                      title: "قالب المشتريات:",
                                      controller: purchaseTemplateController,
                                      hintText: "أدخل قالب المشتريات",
                                      fields: purchaseFields,
                                    ),
                                    20.height,
                                    _buildTemplateSection(
                                      title: "قالب مرتجع المشتريات:",
                                      controller:
                                          purchaseReturnTemplateController,
                                      hintText: "أدخل قالب مرتجع المشتريات",
                                      fields: purchaseFields,
                                    ),
                                    20.height,
                                    _buildTemplateSection(
                                      title: "قالب المديونية:",
                                      controller: dueTemplateController,
                                      hintText: "أدخل قالب المديونية",
                                      fields: dueFields,
                                    ),
                                    20.height,
                                    _buildTemplateSection(
                                      title: "قالب الرسائل الجماعية:",
                                      controller: bulkTemplateController,
                                      hintText: "أدخل قالب الرسائل الجماعية",
                                      fields: salesFields,
                                    ),
                                    20.height,
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        ElevatedButton(
                                          onPressed: () {
                                            _saveTemplates(ref);
                                          },
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: kMainColor,
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 30, vertical: 15),
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                            ),
                                          ),
                                          child: const Text(
                                            "حفظ القوالب",
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                },
                loading: () => const Center(
                  child: CircularProgressIndicator(),
                ),
                error: (error, stackTrace) => Center(
                  child: Text(
                    'حدث خطأ: $error',
                    style: const TextStyle(
                      color: Colors.red,
                    ),
                  ),
                ),
              );
            }),
          ),
        ),
      ),
    );
  }

  Widget _buildTemplateSection({
    required String title,
    required TextEditingController controller,
    required String hintText,
    required List<String> fields,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100,
          child: Text(
            title,
            style: secondaryTextStyle(size: 16),
          ),
        ),
        10.width,
        Expanded(
          child: TextFormField(
            controller: controller,
            maxLines: 5,
            decoration: InputDecoration(
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
              hintText: hintText,
              hintStyle: secondaryTextStyle(size: 16),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          ),
        ),
        10.width,
        Expanded(
          child: Column(
            children: [
              Text("الاختصارات", style: boldTextStyle(size: 16)),
              10.height,
              Wrap(
                children: List.generate(fields.length, (index) {
                  return InkWell(
                    onTap: () {
                      controller.text = controller.text + fields[index];
                    },
                    child: Container(
                      padding: const EdgeInsets.all(5),
                      margin: const EdgeInsets.all(5),
                      decoration: BoxDecoration(
                        color: kMainColor,
                        borderRadius: BorderRadius.circular(5),
                      ),
                      child: Text(
                        fields[index],
                        style: secondaryTextStyle(color: Colors.white),
                      ),
                    ),
                  );
                }),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _saveTemplates(WidgetRef ref) async {
    EasyLoading.show(status: 'جاري الحفظ...');

    try {
      final templates = SmsTemplateModel(
        saleTemplate: salesTemplateController.text,
        saleReturnTemplate: salesReturnTemplateController.text,
        quotationTemplate: quotationTemplateController.text,
        purchaseTemplate: purchaseTemplateController.text,
        purchaseReturnTemplate: purchaseReturnTemplateController.text,
        dueTemplate: dueTemplateController.text,
        bulkSmsTemplate: bulkTemplateController.text,
      );

      await ref
          .read(smsTemplateNotifierProvider.notifier)
          .saveSmsTemplates(templates);

      EasyLoading.dismiss();
      EasyLoading.showSuccess('تم حفظ القوالب بنجاح');
    } catch (e) {
      EasyLoading.dismiss();
      EasyLoading.showError('حدث خطأ أثناء الحفظ: $e');
    }
  }
}
