import 'dart:convert';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:mobile_pos/Screens/Customers/Model/customer_model.dart';
import 'package:mobile_pos/model/transition_model.dart';
import 'package:mobile_pos/currency.dart';

class DueScreenTestScreen extends StatefulWidget {
  const DueScreenTestScreen({super.key});

  @override
  State<DueScreenTestScreen> createState() => _DueScreenTestScreenState();
}

class _DueScreenTestScreenState extends State<DueScreenTestScreen> {
  final List<String> _testResults = [];
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار شاشة المستحقات'),
        backgroundColor: Colors.green,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _testDueScreenLogic,
                    child: const Text('اختبار منطق الشاشة'),
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _testMissingInvoices,
                    child: const Text('البحث عن الفواتير المفقودة'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 10),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _testDueCalculationErrors,
                    child: const Text('اختبار أخطاء الحساب'),
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _simulateDueScreenFilter,
                    child: const Text('محاكاة فلتر الشاشة'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            if (_isLoading) const LinearProgressIndicator(),
            const SizedBox(height: 10),
            Expanded(
              child: ListView.builder(
                itemCount: _testResults.length,
                itemBuilder: (context, index) {
                  return Card(
                    color: _getCardColor(_testResults[index]),
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        _testResults[index],
                        style: const TextStyle(fontSize: 12),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getCardColor(String result) {
    if (result.contains('❌')) return Colors.red.shade100;
    if (result.contains('⚠️')) return Colors.orange.shade100;
    if (result.contains('✅')) return Colors.green.shade100;
    if (result.contains('🔍')) return Colors.blue.shade100;
    return Colors.white;
  }

  // اختبار منطق شاشة المستحقات
  Future<void> _testDueScreenLogic() async {
    setState(() {
      _isLoading = true;
      _testResults.clear();
    });

    _addResult('🔍 اختبار منطق شاشة المستحقات');

    try {
      // محاكاة الكود الفعلي لشاشة المستحقات
      final customersRef =
          FirebaseDatabase.instance.ref(constUserId).child('Customers');
      final snapshot = await customersRef.get();

      if (!snapshot.exists) {
        _addResult('❌ لا توجد بيانات عملاء');
        setState(() => _isLoading = false);
        return;
      }

      List<CustomerModel> allCustomers = [];
      List<CustomerModel> customersWithDue = [];

      // جلب جميع العملاء
      for (var element in snapshot.children) {
        try {
          var customerData = jsonDecode(jsonEncode(element.value));
          var customer = CustomerModel.fromJson(customerData);
          allCustomers.add(customer);

          // تطبيق نفس منطق الفلترة المستخدم في الشاشة الفعلية
          double dueAmount = double.tryParse(customer.dueAmount) ?? 0;
          if (dueAmount > 0) {
            customersWithDue.add(customer);
          }
        } catch (e) {
          _addResult('⚠️ خطأ في معالجة عميل: $e');
        }
      }

      _addResult('✅ إجمالي العملاء في قاعدة البيانات: ${allCustomers.length}');
      _addResult(
          '✅ العملاء الذين يظهرون في شاشة المستحقات: ${customersWithDue.length}');

      // تحليل العملاء الذين لا يظهرون
      List<CustomerModel> hiddenCustomers = allCustomers.where((customer) {
        double dueAmount = double.tryParse(customer.dueAmount) ?? 0;
        return dueAmount <= 0;
      }).toList();

      _addResult(
          '📊 العملاء المخفيون (مديونية = 0): ${hiddenCustomers.length}');

      // فحص العملاء الذين لديهم فواتير آجلة لكن مديونيتهم صفر
      await _checkHiddenCustomersWithDueInvoices(hiddenCustomers);
    } catch (e) {
      _addResult('❌ خطأ في اختبار منطق الشاشة: $e');
    }

    setState(() => _isLoading = false);
  }

  // فحص العملاء المخفيين الذين لديهم فواتير آجلة
  Future<void> _checkHiddenCustomersWithDueInvoices(
      List<CustomerModel> hiddenCustomers) async {
    _addResult('\n🔍 فحص العملاء المخفيين الذين قد يكون لديهم فواتير آجلة');

    final salesRef =
        FirebaseDatabase.instance.ref(constUserId).child('Sales Transition');
    final salesSnapshot = await salesRef.get();

    if (!salesSnapshot.exists) {
      _addResult('❌ لا توجد فواتير مبيعات');
      return;
    }

    Map<String, List<Map<String, dynamic>>> dueInvoicesByPhone = {};

    // جمع الفواتير الآجلة حسب رقم الهاتف
    for (var element in salesSnapshot.children) {
      try {
        var invoiceData = jsonDecode(jsonEncode(element.value));
        double dueAmount =
            double.tryParse(invoiceData['dueAmount']?.toString() ?? '0') ?? 0;

        if (dueAmount > 0) {
          String phone = invoiceData['customerPhone']?.toString() ?? '';
          if (phone.isNotEmpty) {
            if (!dueInvoicesByPhone.containsKey(phone)) {
              dueInvoicesByPhone[phone] = [];
            }
            dueInvoicesByPhone[phone]!.add({
              'invoiceNumber': invoiceData['invoiceNumber']?.toString() ?? '',
              'dueAmount': dueAmount,
              'customerName': invoiceData['customerName']?.toString() ?? '',
            });
          }
        }
      } catch (e) {
        _addResult('⚠️ خطأ في معالجة فاتورة: $e');
      }
    }

    // فحص العملاء المخفيين
    int problematicCustomers = 0;
    for (var customer in hiddenCustomers) {
      if (dueInvoicesByPhone.containsKey(customer.phoneNumber)) {
        problematicCustomers++;
        var invoices = dueInvoicesByPhone[customer.phoneNumber]!;
        double totalDue =
            invoices.fold(0.0, (sum, invoice) => sum + invoice['dueAmount']);

        _addResult(
            '❌ مشكلة: ${customer.customerName} (${customer.phoneNumber})');
        _addResult('   - مديونية مخزنة: ${customer.dueAmount}');
        _addResult('   - مديونية محسوبة من الفواتير: $totalDue');
        _addResult('   - عدد الفواتير الآجلة: ${invoices.length}');
      }
    }

    _addResult(
        '📊 عملاء لديهم مشكلة (مديونية صفر لكن لديهم فواتير آجلة): $problematicCustomers');
  }

  // البحث عن الفواتير المفقودة
  Future<void> _testMissingInvoices() async {
    setState(() {
      _isLoading = true;
      _testResults.clear();
    });

    _addResult('🔍 البحث عن الفواتير المفقودة من شاشة المستحقات');

    try {
      // جلب جميع الفواتير الآجلة
      final salesRef =
          FirebaseDatabase.instance.ref(constUserId).child('Sales Transition');
      final salesSnapshot = await salesRef.get();

      // جلب جميع العملاء
      final customersRef =
          FirebaseDatabase.instance.ref(constUserId).child('Customers');
      final customersSnapshot = await customersRef.get();

      if (!salesSnapshot.exists || !customersSnapshot.exists) {
        _addResult('❌ بيانات غير مكتملة');
        setState(() => _isLoading = false);
        return;
      }

      // جمع الفواتير الآجلة
      List<Map<String, dynamic>> dueInvoices = [];
      for (var element in salesSnapshot.children) {
        try {
          var invoiceData = jsonDecode(jsonEncode(element.value));
          double dueAmount =
              double.tryParse(invoiceData['dueAmount']?.toString() ?? '0') ?? 0;

          if (dueAmount > 0) {
            dueInvoices.add({
              'invoiceNumber': invoiceData['invoiceNumber']?.toString() ?? '',
              'customerName': invoiceData['customerName']?.toString() ?? '',
              'customerPhone': invoiceData['customerPhone']?.toString() ?? '',
              'dueAmount': dueAmount,
              'totalAmount': double.tryParse(
                      invoiceData['totalAmount']?.toString() ?? '0') ??
                  0,
            });
          }
        } catch (e) {
          _addResult('⚠️ خطأ في معالجة فاتورة: $e');
        }
      }

      // جمع العملاء الذين يظهرون في شاشة المستحقات
      Set<String> visibleCustomerPhones = {};
      for (var element in customersSnapshot.children) {
        try {
          var customerData = jsonDecode(jsonEncode(element.value));
          double dueAmount =
              double.tryParse(customerData['due']?.toString() ?? '0') ?? 0;

          if (dueAmount > 0) {
            String phone = customerData['phoneNumber']?.toString() ?? '';
            if (phone.isNotEmpty) {
              visibleCustomerPhones.add(phone);
            }
          }
        } catch (e) {
          _addResult('⚠️ خطأ في معالجة عميل: $e');
        }
      }

      _addResult('✅ إجمالي الفواتير الآجلة: ${dueInvoices.length}');
      _addResult(
          '✅ العملاء المرئيون في شاشة المستحقات: ${visibleCustomerPhones.length}');

      // البحث عن الفواتير المفقودة
      List<Map<String, dynamic>> missingInvoices = [];
      for (var invoice in dueInvoices) {
        if (!visibleCustomerPhones.contains(invoice['customerPhone'])) {
          missingInvoices.add(invoice);
        }
      }

      _addResult(
          '❌ فواتير آجلة لا تظهر في شاشة المستحقات: ${missingInvoices.length}');

      // عرض تفاصيل الفواتير المفقودة
      if (missingInvoices.isNotEmpty) {
        _addResult('\n📋 تفاصيل الفواتير المفقودة:');
        for (int i = 0; i < missingInvoices.length && i < 10; i++) {
          var invoice = missingInvoices[i];
          _addResult(
              '- فاتورة ${invoice['invoiceNumber']}: ${invoice['customerName']} (${invoice['customerPhone']}) - مديونية: ${invoice['dueAmount']}');
        }
      }
    } catch (e) {
      _addResult('❌ خطأ في البحث عن الفواتير المفقودة: $e');
    }

    setState(() => _isLoading = false);
  }

  // اختبار أخطاء الحساب
  Future<void> _testDueCalculationErrors() async {
    setState(() {
      _isLoading = true;
      _testResults.clear();
    });

    _addResult('🔍 اختبار أخطاء حساب المديونية');

    try {
      final customersRef =
          FirebaseDatabase.instance.ref(constUserId).child('Customers');
      final customersSnapshot = await customersRef.get();

      final salesRef =
          FirebaseDatabase.instance.ref(constUserId).child('Sales Transition');
      final salesSnapshot = await salesRef.get();

      if (!customersSnapshot.exists || !salesSnapshot.exists) {
        _addResult('❌ بيانات غير مكتملة');
        setState(() => _isLoading = false);
        return;
      }

      Map<String, double> storedDue = {};
      Map<String, double> calculatedDue = {};
      Map<String, String> customerNames = {};

      // جمع المديونية المخزنة
      for (var element in customersSnapshot.children) {
        try {
          var customerData = jsonDecode(jsonEncode(element.value));
          String phone = customerData['phoneNumber']?.toString() ?? '';
          String name = customerData['customerName']?.toString() ?? '';
          double due =
              double.tryParse(customerData['due']?.toString() ?? '0') ?? 0;

          if (phone.isNotEmpty) {
            storedDue[phone] = due;
            calculatedDue[phone] = 0.0;
            customerNames[phone] = name;
          }
        } catch (e) {
          _addResult('⚠️ خطأ في معالجة عميل: $e');
        }
      }

      // حساب المديونية من الفواتير
      for (var element in salesSnapshot.children) {
        try {
          var invoiceData = jsonDecode(jsonEncode(element.value));
          String phone = invoiceData['customerPhone']?.toString() ?? '';
          double dueAmount =
              double.tryParse(invoiceData['dueAmount']?.toString() ?? '0') ?? 0;

          if (phone.isNotEmpty && calculatedDue.containsKey(phone)) {
            calculatedDue[phone] = calculatedDue[phone]! + dueAmount;
          }
        } catch (e) {
          _addResult('⚠️ خطأ في معالجة فاتورة: $e');
        }
      }

      // تحليل الأخطاء
      List<Map<String, dynamic>> errors = [];
      storedDue.forEach((phone, stored) {
        double calculated = calculatedDue[phone] ?? 0;
        double difference = (stored - calculated).abs();

        if (difference > 0.01) {
          // تسامح في الفروق الصغيرة
          errors.add({
            'phone': phone,
            'name': customerNames[phone] ?? 'غير معروف',
            'stored': stored,
            'calculated': calculated,
            'difference': difference,
          });
        }
      });

      _addResult('✅ إجمالي العملاء المفحوصين: ${storedDue.length}');
      _addResult('❌ عملاء لديهم أخطاء في الحساب: ${errors.length}');

      // ترتيب الأخطاء حسب الفرق
      errors.sort((a, b) => b['difference'].compareTo(a['difference']));

      // عرض أكبر الأخطاء
      if (errors.isNotEmpty) {
        _addResult('\n📋 أكبر أخطاء الحساب:');
        for (int i = 0; i < errors.length && i < 10; i++) {
          var error = errors[i];
          _addResult('- ${error['name']} (${error['phone']}):');
          _addResult(
              '  مخزن: ${error['stored']}, محسوب: ${error['calculated']}, الفرق: ${error['difference']}');
        }
      }
    } catch (e) {
      _addResult('❌ خطأ في اختبار أخطاء الحساب: $e');
    }

    setState(() => _isLoading = false);
  }

  // محاكاة فلتر شاشة المستحقات
  Future<void> _simulateDueScreenFilter() async {
    setState(() {
      _isLoading = true;
      _testResults.clear();
    });

    _addResult('🔍 محاكاة فلتر شاشة المستحقات');

    try {
      final customersRef =
          FirebaseDatabase.instance.ref(constUserId).child('Customers');
      final snapshot = await customersRef.get();

      if (!snapshot.exists) {
        _addResult('❌ لا توجد بيانات عملاء');
        setState(() => _isLoading = false);
        return;
      }

      List<Map<String, dynamic>> allCustomers = [];
      List<Map<String, dynamic>> filteredCustomers = [];

      // جمع جميع العملاء
      for (var element in snapshot.children) {
        try {
          var customerData = jsonDecode(jsonEncode(element.value));
          allCustomers.add({
            'name': customerData['customerName']?.toString() ?? '',
            'phone': customerData['phoneNumber']?.toString() ?? '',
            'dueAmount': customerData['due']?.toString() ?? '0',
            'dueAmountDouble':
                double.tryParse(customerData['due']?.toString() ?? '0') ?? 0,
            'type': customerData['type']?.toString() ?? '',
          });
        } catch (e) {
          _addResult('⚠️ خطأ في معالجة عميل: $e');
        }
      }

      // تطبيق الفلتر (نفس منطق الشاشة الفعلية)
      for (var customer in allCustomers) {
        if (customer['dueAmountDouble'] > 0) {
          filteredCustomers.add(customer);
        }
      }

      _addResult('✅ إجمالي العملاء: ${allCustomers.length}');
      _addResult('✅ العملاء بعد الفلترة: ${filteredCustomers.length}');

      // تحليل أنواع البيانات
      Map<String, int> dataTypes = {};
      for (var customer in allCustomers) {
        String dueAmount = customer['dueAmount'];
        String type = '';

        if (dueAmount == '0' || dueAmount == '0.0') {
          type = 'صفر نصي';
        } else if (dueAmount.isEmpty) {
          type = 'فارغ';
        } else if (double.tryParse(dueAmount) == null) {
          type = 'غير رقمي';
        } else if (double.parse(dueAmount) > 0) {
          type = 'رقم موجب';
        } else {
          type = 'رقم سالب أو صفر';
        }

        dataTypes[type] = (dataTypes[type] ?? 0) + 1;
      }

      _addResult('\n📊 تحليل أنواع بيانات المديونية:');
      dataTypes.forEach((type, count) {
        _addResult('- $type: $count عميل');
      });
    } catch (e) {
      _addResult('❌ خطأ في محاكاة الفلتر: $e');
    }

    setState(() => _isLoading = false);
  }

  void _addResult(String result) {
    setState(() {
      _testResults.add(result);
    });
  }
}
