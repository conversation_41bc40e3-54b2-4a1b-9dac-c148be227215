// // ignore_for_file: avoid_print

// import 'dart:io';
// import 'package:path_provider/path_provider.dart';
// import 'package:syncfusion_flutter_xlsio/xlsio.dart';
// import '../Models/item_report_model.dart';
// import 'package:intl/intl.dart';

// class ExportService {
//   // تصدير التقرير إلى ملف Excel
//   static Future<String?> exportToExcel(List<ItemReportModel> reports) async {
//     try {
//       // إنشاء ملف Excel جديد
//       final Workbook workbook = Workbook();
//       final Worksheet sheet = workbook.worksheets[0];
//       sheet.name = 'تقرير الأصناف';
      
//       // تنسيق العناوين
//       final Style headerStyle = workbook.styles.add('HeaderStyle');
//       headerStyle.bold = true;
//       headerStyle.hAlign = HAlignType.center;
//       headerStyle.backColor = '#CCCCCC';
//       headerStyle.fontSize = 12;
      
//       // إضافة رأس الجدول
//       final headers = [
//         'اسم الصنف',
//         'المخزون الحالي',
//         'إجمالي المشتريات',
//         'متوسط سعر الشراء',
//         'آخر شراء',
//         'إجمالي المبيعات',
//         'متوسط سعر البيع',
//         'آخر بيع',
//         'الربح'
//       ];

//       // كتابة الهيدر
//       for (var i = 0; i < headers.length; i++) {
//         final Range cell = sheet.getRangeByIndex(1, i + 1);
//         cell.setText(headers[i]);
//         cell.cellStyle = headerStyle;
//       }

//       // تنسيق البيانات
//       final Style dataStyle = workbook.styles.add('DataStyle');
//       dataStyle.hAlign = HAlignType.center;
//       dataStyle.fontSize = 11;
      
//       final Style numberStyle = workbook.styles.add('NumberStyle');
//       numberStyle.hAlign = HAlignType.center;
//       numberStyle.fontSize = 11;
//       numberStyle.numberFormat = '#,##0.00';

//       // كتابة البيانات
//       for (var i = 0; i < reports.length; i++) {
//         final report = reports[i];
//         final row = i + 2;

//         // اسم الصنف
//         final nameCell = sheet.getRangeByIndex(row, 1);
//         nameCell.setText(report.productName);
//         nameCell.cellStyle = dataStyle;

//         // المخزون الحالي
//         final stockCell = sheet.getRangeByIndex(row, 2);
//         stockCell.setNumber(report.currentStock);
//         stockCell.cellStyle = numberStyle;

//         // إجمالي المشتريات
//         final purchasesCell = sheet.getRangeByIndex(row, 3);
//         purchasesCell.setNumber(report.totalPurchases);
//         purchasesCell.cellStyle = numberStyle;

//         // متوسط سعر الشراء
//         final avgPurchaseCell = sheet.getRangeByIndex(row, 4);
//         avgPurchaseCell.setNumber(report.averagePurchasePrice);
//         avgPurchaseCell.cellStyle = numberStyle;

//         // آخر شراء
//         final lastPurchaseCell = sheet.getRangeByIndex(row, 5);
//         lastPurchaseCell.setText(DateFormat('yyyy-MM-dd').format(report.lastPurchaseDate));
//         lastPurchaseCell.cellStyle = dataStyle;

//         // إجمالي المبيعات
//         final salesCell = sheet.getRangeByIndex(row, 6);
//         salesCell.setNumber(report.totalSales);
//         salesCell.cellStyle = numberStyle;

//         // متوسط سعر البيع
//         final avgSaleCell = sheet.getRangeByIndex(row, 7);
//         avgSaleCell.setNumber(report.averageSalePrice);
//         avgSaleCell.cellStyle = numberStyle;

//         // آخر بيع
//         final lastSaleCell = sheet.getRangeByIndex(row, 8);
//         lastSaleCell.setText(DateFormat('yyyy-MM-dd').format(report.lastSaleDate));
//         lastSaleCell.cellStyle = dataStyle;

//         // الربح
//         final profitCell = sheet.getRangeByIndex(row, 9);
//         profitCell.setNumber(report.profit);
//         profitCell.cellStyle = numberStyle;
//       }

//       // تنسيق عرض الأعمدة
//       for (var i = 0; i < headers.length; i++) {
//         sheet.autoFitColumn(i + 1);
//       }

//       // الحصول على مسار حفظ الملف
//       final dir = await getApplicationDocumentsDirectory();
//       final dateStr = DateFormat('yyyy-MM-dd-HH-mm').format(DateTime.now());
//       final fileName = 'تقرير_الأصناف_$dateStr.xlsx';
//       final file = File('${dir.path}/$fileName');

//       // حفظ الملف
//       final List<int> bytes = workbook.saveAsStream();
//       await file.writeAsBytes(bytes);
//       workbook.dispose();

//       print('تم تصدير التقرير بنجاح إلى: ${file.path}');
//       return file.path;
//     } catch (e) {
//       print('حدث خطأ أثناء تصدير التقرير: $e');
//       return null;
//     }
//   }
// }
