import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/model/water_filter_models.dart';
import 'package:mobile_pos/services/water_filter_product_service.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

class EditWaterFilterProductScreen extends StatefulWidget {
  final WaterFilterProduct product;

  const EditWaterFilterProductScreen({
    super.key,
    required this.product,
  });

  @override
  State<EditWaterFilterProductScreen> createState() =>
      _EditWaterFilterProductScreenState();
}

class _EditWaterFilterProductScreenState
    extends State<EditWaterFilterProductScreen> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _nameController;
  late final TextEditingController _brandController;
  late final TextEditingController _descriptionController;
  late final TextEditingController _priceController;
  late final TextEditingController _stockController;
  late final TextEditingController _maintenanceIntervalController;
  late final TextEditingController _maintenanceCostController;
  late final TextEditingController _installationCostController;
  late final TextEditingController _specificationsController;

  late WaterFilterCategory _selectedCategory;
  late bool _isInstallationRequired;
  File? _selectedImage;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    _nameController = TextEditingController(text: widget.product.name);
    _brandController = TextEditingController(text: widget.product.brand);
    _descriptionController =
        TextEditingController(text: widget.product.description);
    _priceController =
        TextEditingController(text: widget.product.price.toString());
    _stockController =
        TextEditingController(text: widget.product.stock.toString());
    _maintenanceIntervalController = TextEditingController(
      text: widget.product.maintenanceIntervalMonths.toString(),
    );
    _maintenanceCostController = TextEditingController(
      text: widget.product.maintenanceCost.toString(),
    );
    _installationCostController = TextEditingController(
      text: widget.product.installationCost.toString(),
    );
    _specificationsController = TextEditingController(
      text: widget.product.specifications.join('\n'),
    );

    _selectedCategory = widget.product.category;
    _isInstallationRequired = widget.product.isInstallationRequired;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _brandController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _stockController.dispose();
    _maintenanceIntervalController.dispose();
    _maintenanceCostController.dispose();
    _installationCostController.dispose();
    _specificationsController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 80,
      );

      if (image != null) {
        setState(() {
          _selectedImage = File(image.path);
        });
      }
    } catch (e) {
      EasyLoading.showError('خطأ في اختيار الصورة: $e');
    }
  }

  Future<void> _updateProduct() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() => _isLoading = true);
    EasyLoading.show(status: 'جاري تحديث المنتج...', dismissOnTap: false);

    try {
      // رفع الصورة الجديدة إذا تم اختيارها
      String? imageUrl = widget.product.imageUrl;
      if (_selectedImage != null) {
        final newImageUrl =
            await WaterFilterProductService.uploadProductImage(_selectedImage!);
        if (newImageUrl != null) {
          imageUrl = newImageUrl;
        } else {
          EasyLoading.showError('فشل في رفع الصورة الجديدة');
          setState(() => _isLoading = false);
          return;
        }
      }

      // تحضير قائمة المواصفات
      final specifications = _specificationsController.text
          .split('\n')
          .where((spec) => spec.trim().isNotEmpty)
          .map((spec) => spec.trim())
          .toList();

      // تحديث المنتج
      final updatedProduct = WaterFilterProduct(
        id: widget.product.id,
        name: _nameController.text.trim(),
        brand: _brandController.text.trim(),
        category: _selectedCategory,
        price: double.parse(_priceController.text),
        stock: int.parse(_stockController.text),
        description: _descriptionController.text.trim(),
        specifications: specifications,
        maintenanceIntervalMonths:
            int.parse(_maintenanceIntervalController.text),
        maintenanceCost: double.parse(_maintenanceCostController.text),
        isInstallationRequired: _isInstallationRequired,
        installationCost: double.parse(_installationCostController.text),
        imageUrl: imageUrl,
        createdAt: widget.product.createdAt,
      );

      // حفظ التحديثات
      final success =
          await WaterFilterProductService.updateProduct(updatedProduct);

      EasyLoading.dismiss();
      setState(() => _isLoading = false);

      if (success) {
        EasyLoading.showSuccess('تم تحديث المنتج بنجاح');
        if (mounted) {
          Navigator.pop(context, true);
        }
      } else {
        EasyLoading.showError('فشل في تحديث المنتج');
      }
    } catch (e) {
      EasyLoading.dismiss();
      setState(() => _isLoading = false);
      EasyLoading.showError('خطأ في تحديث المنتج: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        elevation: 0,
        title: Text(
          'تعديل المنتج',
          style: GoogleFonts.cairo(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        child: Form(
          key: _formKey,
          child: ListView(
            padding: const EdgeInsets.all(20),
            children: [
              // صورة المنتج
              _buildImageSection(),
              const SizedBox(height: 20),

              // المعلومات الأساسية
              _buildBasicInfoSection(),
              const SizedBox(height: 20),

              // معلومات السعر والمخزون
              _buildPriceStockSection(),
              const SizedBox(height: 20),

              // معلومات الصيانة
              _buildMaintenanceSection(),
              const SizedBox(height: 20),

              // المواصفات
              _buildSpecificationsSection(),
              const SizedBox(height: 30),

              // زر التحديث
              _buildUpdateButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'صورة المنتج',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: kMainColor,
          ),
        ),
        const SizedBox(height: 10),
        GestureDetector(
          onTap: _pickImage,
          child: Container(
            height: 150,
            width: double.infinity,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300, width: 2),
              borderRadius: BorderRadius.circular(15),
              color: Colors.grey.shade50,
            ),
            child: _selectedImage != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(15),
                    child: Image.file(
                      _selectedImage!,
                      fit: BoxFit.cover,
                    ),
                  )
                : widget.product.imageUrl != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(15),
                        child: Image.network(
                          widget.product.imageUrl!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) =>
                              _buildImagePlaceholder(),
                        ),
                      )
                    : _buildImagePlaceholder(),
          ),
        ),
      ],
    );
  }

  Widget _buildImagePlaceholder() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.add_photo_alternate,
          size: 50,
          color: Colors.grey.shade400,
        ),
        const SizedBox(height: 10),
        Text(
          'اضغط لتغيير الصورة',
          style: GoogleFonts.cairo(
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  Widget _buildBasicInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المعلومات الأساسية',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: kMainColor,
          ),
        ),
        const SizedBox(height: 15),

        // اسم المنتج
        TextFormField(
          controller: _nameController,
          decoration: InputDecoration(
            labelText: 'اسم المنتج *',
            labelStyle: GoogleFonts.cairo(),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
              borderSide: const BorderSide(color: kMainColor),
            ),
          ),
          style: GoogleFonts.cairo(),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'اسم المنتج مطلوب';
            }
            return null;
          },
        ),
        const SizedBox(height: 15),

        // العلامة التجارية
        TextFormField(
          controller: _brandController,
          decoration: InputDecoration(
            labelText: 'العلامة التجارية *',
            labelStyle: GoogleFonts.cairo(),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
              borderSide: const BorderSide(color: kMainColor),
            ),
          ),
          style: GoogleFonts.cairo(),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'العلامة التجارية مطلوبة';
            }
            return null;
          },
        ),
        const SizedBox(height: 15),

        // الفئة
        DropdownButtonFormField<WaterFilterCategory>(
          value: _selectedCategory,
          onChanged: (value) {
            if (value != null) {
              setState(() => _selectedCategory = value);
            }
          },
          decoration: InputDecoration(
            labelText: 'فئة المنتج *',
            labelStyle: GoogleFonts.cairo(),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
              borderSide: const BorderSide(color: kMainColor),
            ),
          ),
          items: WaterFilterCategory.values
              .map(
                (category) => DropdownMenuItem<WaterFilterCategory>(
                  value: category,
                  child: Text(category.arabicName, style: GoogleFonts.cairo()),
                ),
              )
              .toList(),
        ),
        const SizedBox(height: 15),

        // الوصف
        TextFormField(
          controller: _descriptionController,
          maxLines: 3,
          decoration: InputDecoration(
            labelText: 'وصف المنتج',
            labelStyle: GoogleFonts.cairo(),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
              borderSide: const BorderSide(color: kMainColor),
            ),
          ),
          style: GoogleFonts.cairo(),
        ),
      ],
    );
  }

  Widget _buildPriceStockSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'السعر والمخزون',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: kMainColor,
          ),
        ),
        const SizedBox(height: 15),
        Row(
          children: [
            // السعر
            Expanded(
              child: TextFormField(
                controller: _priceController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'السعر (ج.م) *',
                  labelStyle: GoogleFonts.cairo(),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(15),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(15),
                    borderSide: const BorderSide(color: kMainColor),
                  ),
                ),
                style: GoogleFonts.cairo(),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'السعر مطلوب';
                  }
                  if (double.tryParse(value) == null ||
                      double.parse(value) <= 0) {
                    return 'السعر غير صحيح';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 15),

            // المخزون
            Expanded(
              child: TextFormField(
                controller: _stockController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'المخزون *',
                  labelStyle: GoogleFonts.cairo(),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(15),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(15),
                    borderSide: const BorderSide(color: kMainColor),
                  ),
                ),
                style: GoogleFonts.cairo(),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'المخزون مطلوب';
                  }
                  if (int.tryParse(value) == null || int.parse(value) < 0) {
                    return 'المخزون غير صحيح';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMaintenanceSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'معلومات الصيانة والتركيب',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: kMainColor,
          ),
        ),
        const SizedBox(height: 15),

        Row(
          children: [
            // فترة الصيانة
            Expanded(
              child: TextFormField(
                controller: _maintenanceIntervalController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'فترة الصيانة (شهر) *',
                  labelStyle: GoogleFonts.cairo(),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(15),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(15),
                    borderSide: const BorderSide(color: kMainColor),
                  ),
                ),
                style: GoogleFonts.cairo(),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'فترة الصيانة مطلوبة';
                  }
                  if (int.tryParse(value) == null || int.parse(value) <= 0) {
                    return 'فترة الصيانة غير صحيحة';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 15),

            // تكلفة الصيانة
            Expanded(
              child: TextFormField(
                controller: _maintenanceCostController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'تكلفة الصيانة (ج.م) *',
                  labelStyle: GoogleFonts.cairo(),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(15),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(15),
                    borderSide: const BorderSide(color: kMainColor),
                  ),
                ),
                style: GoogleFonts.cairo(),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'تكلفة الصيانة مطلوبة';
                  }
                  if (double.tryParse(value) == null ||
                      double.parse(value) < 0) {
                    return 'تكلفة الصيانة غير صحيحة';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 15),

        // يحتاج تركيب
        CheckboxListTile(
          title: Text(
            'يحتاج تركيب',
            style: GoogleFonts.cairo(),
          ),
          value: _isInstallationRequired,
          onChanged: (value) {
            setState(() => _isInstallationRequired = value ?? false);
          },
          activeColor: kMainColor,
        ),

        // تكلفة التركيب
        if (_isInstallationRequired)
          TextFormField(
            controller: _installationCostController,
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              labelText: 'تكلفة التركيب (ج.م) *',
              labelStyle: GoogleFonts.cairo(),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(15),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(15),
                borderSide: const BorderSide(color: kMainColor),
              ),
            ),
            style: GoogleFonts.cairo(),
            validator: (value) {
              if (_isInstallationRequired) {
                if (value == null || value.trim().isEmpty) {
                  return 'تكلفة التركيب مطلوبة';
                }
                if (double.tryParse(value) == null || double.parse(value) < 0) {
                  return 'تكلفة التركيب غير صحيحة';
                }
              }
              return null;
            },
          ),
      ],
    );
  }

  Widget _buildSpecificationsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المواصفات',
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: kMainColor,
          ),
        ),
        const SizedBox(height: 15),
        TextFormField(
          controller: _specificationsController,
          maxLines: 5,
          decoration: InputDecoration(
            labelText: 'المواصفات (كل مواصفة في سطر منفصل)',
            labelStyle: GoogleFonts.cairo(),
            hintText:
                'مثال:\n- عدد المراحل: 7\n- معدل التدفق: 2 لتر/دقيقة\n- ضغط التشغيل: 1-6 بار',
            hintStyle: GoogleFonts.cairo(color: Colors.grey.shade500),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(15),
              borderSide: const BorderSide(color: kMainColor),
            ),
          ),
          style: GoogleFonts.cairo(),
        ),
      ],
    );
  }

  Widget _buildUpdateButton() {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _updateProduct,
        style: ElevatedButton.styleFrom(
          backgroundColor: kMainColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
        ),
        child: _isLoading
            ? const CircularProgressIndicator(color: Colors.white)
            : Text(
                'تحديث المنتج',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
      ),
    );
  }
}
