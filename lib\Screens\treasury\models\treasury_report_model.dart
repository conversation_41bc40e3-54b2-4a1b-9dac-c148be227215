class TreasuryReportModel {
  late String reportId;
  late String reportType; // 'daily', 'weekly', 'monthly', 'yearly', 'custom'
  late String startDate;
  late String endDate;
  late String totalIncome;
  late String totalExpense;
  late String netAmount;
  late String transactionCount;
  late String generatedAt;
  late String generatedBy;
  late List<TreasuryReportItem> items;

  TreasuryReportModel({
    required this.reportId,
    required this.reportType,
    required this.startDate,
    required this.endDate,
    required this.totalIncome,
    required this.totalExpense,
    required this.netAmount,
    required this.transactionCount,
    required this.generatedAt,
    required this.generatedBy,
    required this.items,
  });

  TreasuryReportModel.fromJson(Map<dynamic, dynamic> json) {
    reportId = json['reportId']?.toString() ?? '';
    reportType = json['reportType']?.toString() ?? '';
    startDate = json['startDate']?.toString() ?? '';
    endDate = json['endDate']?.toString() ?? '';
    totalIncome = json['totalIncome']?.toString() ?? '0';
    totalExpense = json['totalExpense']?.toString() ?? '0';
    netAmount = json['netAmount']?.toString() ?? '0';
    transactionCount = json['transactionCount']?.toString() ?? '0';
    generatedAt = json['generatedAt']?.toString() ?? '';
    generatedBy = json['generatedBy']?.toString() ?? '';
    
    items = [];
    if (json['items'] != null) {
      json['items'].forEach((item) {
        items.add(TreasuryReportItem.fromJson(item));
      });
    }
  }

  Map<dynamic, dynamic> toJson() => <dynamic, dynamic>{
        'reportId': reportId,
        'reportType': reportType,
        'startDate': startDate,
        'endDate': endDate,
        'totalIncome': totalIncome,
        'totalExpense': totalExpense,
        'netAmount': netAmount,
        'transactionCount': transactionCount,
        'generatedAt': generatedAt,
        'generatedBy': generatedBy,
        'items': items.map((item) => item.toJson()).toList(),
      };

  // Helper methods
  double get totalIncomeAsDouble => double.tryParse(totalIncome) ?? 0.0;
  double get totalExpenseAsDouble => double.tryParse(totalExpense) ?? 0.0;
  double get netAmountAsDouble => double.tryParse(netAmount) ?? 0.0;
  int get transactionCountAsInt => int.tryParse(transactionCount) ?? 0;
  
  DateTime get startDateAsDateTime => DateTime.tryParse(startDate) ?? DateTime.now();
  DateTime get endDateAsDateTime => DateTime.tryParse(endDate) ?? DateTime.now();
  DateTime get generatedAtAsDateTime => DateTime.tryParse(generatedAt) ?? DateTime.now();
  
  bool get isProfit => netAmountAsDouble >= 0;
}

class TreasuryReportItem {
  late String category;
  late String type;
  late String amount;
  late String count;
  late String percentage;

  TreasuryReportItem({
    required this.category,
    required this.type,
    required this.amount,
    required this.count,
    required this.percentage,
  });

  TreasuryReportItem.fromJson(Map<dynamic, dynamic> json) {
    category = json['category']?.toString() ?? '';
    type = json['type']?.toString() ?? '';
    amount = json['amount']?.toString() ?? '0';
    count = json['count']?.toString() ?? '0';
    percentage = json['percentage']?.toString() ?? '0';
  }

  Map<dynamic, dynamic> toJson() => <dynamic, dynamic>{
        'category': category,
        'type': type,
        'amount': amount,
        'count': count,
        'percentage': percentage,
      };

  // Helper methods
  double get amountAsDouble => double.tryParse(amount) ?? 0.0;
  int get countAsInt => int.tryParse(count) ?? 0;
  double get percentageAsDouble => double.tryParse(percentage) ?? 0.0;
}
