import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:mobile_pos/services/firebase_database_service.dart';
import 'package:mobile_pos/services/local_notification_service.dart';

class PaymentNotificationService {
  // فحص المدفوعات المتأخرة وإرسال إشعارات
  static Future<void> checkOverduePayments() async {
    try {
      // الحصول على مرجع المبيعات
      final userId = FirebaseDatabaseService.getCurrentUserId();
      final ref = FirebaseDatabaseService.getReference(
          "$userId/Sales Transition",
          keepSynced: true);

      // الحصول على بيانات المبيعات
      final snapshot = await ref.get();

      if (snapshot.exists) {
        // قائمة المدفوعات المتأخرة
        final List<Map<String, dynamic>> overduePayments = [];

        // التاريخ الحالي
        final now = DateTime.now();

        // فحص كل معاملة
        for (var child in snapshot.children) {
          final data = child.value as Map<dynamic, dynamic>;

          // التحقق من وجود مبلغ مستحق
          final dueAmount = (data['dueAmount'] as num?)?.toDouble() ?? 0.0;
          final isPaid = data['isPaid'] as bool? ?? true;

          if (!isPaid && dueAmount > 0) {
            // الحصول على تاريخ الشراء
            final purchaseDate = data['purchaseDate'] as String? ?? '';
            DateTime? transactionDate;

            try {
              // محاولة تحليل التاريخ
              transactionDate = DateFormat('dd-MM-yyyy').parse(purchaseDate);
            } catch (e) {
              debugPrint('خطأ في تحليل التاريخ: $e');
              continue;
            }

            // حساب عدد الأيام منذ المعاملة
            final daysSinceTransaction = now.difference(transactionDate).inDays;

            // إذا مر أكثر من 30 يومًا، نعتبرها متأخرة
            if (daysSinceTransaction > 30) {
              overduePayments.add({
                'customerName': data['customerName'] as String? ?? 'عميل',
                'dueAmount': dueAmount,
                'purchaseDate': purchaseDate,
                'invoiceNumber': data['invoiceNumber'] as String? ?? '',
                'daysSinceTransaction': daysSinceTransaction,
              });
            }
          }
        }

        // إرسال إشعارات للمدفوعات المتأخرة
        for (var payment in overduePayments) {
          await LocalNotificationService.sendLocalNotification(
            title: 'تنبيه: دفعة متأخرة',
            body:
                'العميل "${payment['customerName']}" لديه مبلغ ${payment['dueAmount']} متأخر منذ ${payment['daysSinceTransaction']} يوم',
            data: {
              'type': 'overdue_payment',
              'invoiceNumber': payment['invoiceNumber'],
              'screen': 'due_payments',
            },
          );
        }

        debugPrint('تم فحص المدفوعات وإرسال ${overduePayments.length} إشعار');
      }
    } catch (e) {
      debugPrint('خطأ في فحص المدفوعات المتأخرة: $e');
    }
  }
}
