import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/model/water_filter_models.dart';
import 'package:mobile_pos/services/water_filter_service.dart';

class FinancialReportScreen extends StatefulWidget {
  const FinancialReportScreen({super.key});

  @override
  State<FinancialReportScreen> createState() => _FinancialReportScreenState();
}

class _FinancialReportScreenState extends State<FinancialReportScreen> {
  List<WaterFilterSystem> _allSystems = [];
  List<WaterFilterInstallment> _allInstallments = [];

  // إحصائيات مالية
  Map<String, dynamic> _stats = {};

  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadFinancialData();
  }

  Future<void> _loadFinancialData() async {
    setState(() => _isLoading = true);

    try {
      // تحميل الأنظمة
      final systemsData = await WaterFilterService.getData('Systems');
      final systems = <WaterFilterSystem>[];

      systemsData.forEach((key, value) {
        try {
          final system = WaterFilterSystem.fromJson(
            Map<String, dynamic>.from(value),
          );
          systems.add(system);
        } catch (e) {
          debugPrint('خطأ في معالجة نظام: $e');
        }
      });

      // تحميل الأقساط
      final installmentsData = await WaterFilterService.getData('Installments');
      final installments = <WaterFilterInstallment>[];

      installmentsData.forEach((key, value) {
        try {
          final installment = WaterFilterInstallment.fromJson(
            Map<String, dynamic>.from(value),
          );
          installments.add(installment);
        } catch (e) {
          debugPrint('خطأ في معالجة قسط: $e');
        }
      });

      setState(() {
        _allSystems = systems;
        _allInstallments = installments;
        _calculateStats();
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('خطأ في تحميل البيانات المالية: $e');
      setState(() => _isLoading = false);
    }
  }

  void _calculateStats() {
    // إحصائيات الأنظمة
    final totalRevenue =
        _allSystems.fold(0.0, (sum, system) => sum + system.totalCost);
    final totalPaid =
        _allSystems.fold(0.0, (sum, system) => sum + system.paidAmount);
    final totalRemaining =
        _allSystems.fold(0.0, (sum, system) => sum + system.remainingAmount);

    // إحصائيات الأقساط
    final totalInstallments = _allInstallments.fold(
        0.0, (sum, installment) => sum + installment.amount);
    final paidInstallments = _allInstallments
        .where((i) => i.status == InstallmentStatus.paid)
        .fold(0.0, (sum, installment) => sum + installment.amount);
    final unpaidInstallments = totalInstallments - paidInstallments;
    final overdueInstallments = _allInstallments
        .where((i) => i.status == InstallmentStatus.overdue)
        .fold(0.0, (sum, installment) => sum + installment.amount);

    // إحصائيات شهرية
    final now = DateTime.now();
    final thisMonthRevenue = _allSystems
        .where((s) =>
            s.installationDate.year == now.year &&
            s.installationDate.month == now.month)
        .fold(0.0, (sum, system) => sum + system.totalCost);

    final thisMonthPaid = _allInstallments
        .where((i) =>
            i.status == InstallmentStatus.paid &&
            i.paidDate != null &&
            i.paidDate!.year == now.year &&
            i.paidDate!.month == now.month)
        .fold(0.0, (sum, installment) => sum + installment.amount);

    // نسب مالية
    final collectionRate =
        totalRevenue > 0 ? (totalPaid / totalRevenue) * 100 : 0;
    final installmentCollectionRate = totalInstallments > 0
        ? (paidInstallments / totalInstallments) * 100
        : 0;

    setState(() {
      _stats = {
        'totalRevenue': totalRevenue,
        'totalPaid': totalPaid,
        'totalRemaining': totalRemaining,
        'totalInstallments': totalInstallments,
        'paidInstallments': paidInstallments,
        'unpaidInstallments': unpaidInstallments,
        'overdueInstallments': overdueInstallments,
        'thisMonthRevenue': thisMonthRevenue,
        'thisMonthPaid': thisMonthPaid,
        'collectionRate': collectionRate,
        'installmentCollectionRate': installmentCollectionRate,
      };
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        title: Text(
          'التقرير المالي الشامل',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: _loadFinancialData,
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSectionTitle('نظرة عامة مالية'),
                    const SizedBox(height: 16),

                    // إجمالي الإيرادات
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Colors.green.shade400,
                            Colors.green.shade600
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.green.shade200,
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.account_balance_wallet,
                                color: Colors.white,
                                size: 32,
                              ),
                              const SizedBox(width: 12),
                              Text(
                                'إجمالي الإيرادات',
                                style: GoogleFonts.cairo(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            '${(_stats['totalRevenue'] ?? 0).toStringAsFixed(2)} ج.م',
                            style: GoogleFonts.cairo(
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 20),

                    // إحصائيات المدفوعات
                    GridView.count(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      crossAxisCount: 2,
                      crossAxisSpacing: 16,
                      mainAxisSpacing: 16,
                      childAspectRatio: 0.8,
                      children: [
                        _buildStatCard(
                          title: 'إجمالي المدفوع',
                          value:
                              '${(_stats['totalPaid'] ?? 0).toStringAsFixed(2)} ج.م',
                          icon: Icons.check_circle,
                          color: Colors.green,
                        ),
                        _buildStatCard(
                          title: 'إجمالي المتبقي',
                          value:
                              '${(_stats['totalRemaining'] ?? 0).toStringAsFixed(2)} ج.م',
                          icon: Icons.pending,
                          color: Colors.orange,
                        ),
                        _buildStatCard(
                          title: 'إيرادات الشهر',
                          value:
                              '${(_stats['thisMonthRevenue'] ?? 0).toStringAsFixed(2)} ج.م',
                          icon: Icons.calendar_month,
                          color: Colors.blue,
                        ),
                        _buildStatCard(
                          title: 'مدفوعات الشهر',
                          value:
                              '${(_stats['thisMonthPaid'] ?? 0).toStringAsFixed(2)} ج.م',
                          icon: Icons.payment,
                          color: Colors.purple,
                        ),
                      ],
                    ),

                    const SizedBox(height: 24),

                    _buildSectionTitle('تحليل الأقساط'),
                    const SizedBox(height: 16),

                    GridView.count(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      crossAxisCount: 2,
                      crossAxisSpacing: 16,
                      mainAxisSpacing: 16,
                      childAspectRatio: 0.8,
                      children: [
                        _buildStatCard(
                          title: 'أقساط مدفوعة',
                          value:
                              '${(_stats['paidInstallments'] ?? 0).toStringAsFixed(2)} ج.م',
                          icon: Icons.check_circle,
                          color: Colors.green,
                        ),
                        _buildStatCard(
                          title: 'أقساط غير مدفوعة',
                          value:
                              '${(_stats['unpaidInstallments'] ?? 0).toStringAsFixed(2)} ج.م',
                          icon: Icons.pending,
                          color: Colors.orange,
                        ),
                        _buildStatCard(
                          title: 'أقساط متأخرة',
                          value:
                              '${(_stats['overdueInstallments'] ?? 0).toStringAsFixed(2)} ج.م',
                          icon: Icons.warning,
                          color: Colors.red,
                        ),
                        _buildStatCard(
                          title: 'نسبة التحصيل',
                          value:
                              '${(_stats['installmentCollectionRate'] ?? 0).toStringAsFixed(1)}%',
                          icon: Icons.trending_up,
                          color: Colors.blue,
                        ),
                      ],
                    ),

                    const SizedBox(height: 24),

                    _buildSectionTitle('مؤشرات الأداء'),
                    const SizedBox(height: 16),

                    // نسبة التحصيل العامة
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.shade200,
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'نسبة التحصيل العامة',
                            style: GoogleFonts.cairo(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 12),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                '${(_stats['collectionRate'] ?? 0).toStringAsFixed(1)}%',
                                style: GoogleFonts.cairo(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.green,
                                ),
                              ),
                              Icon(
                                Icons.trending_up,
                                color: Colors.green,
                                size: 32,
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          LinearProgressIndicator(
                            value: (_stats['collectionRate'] ?? 0) / 100,
                            backgroundColor: Colors.grey.shade200,
                            valueColor: const AlwaysStoppedAnimation<Color>(
                                Colors.green),
                            minHeight: 8,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
      ),
    );
  }

  // Helper Methods
  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: GoogleFonts.cairo(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: kMainColor,
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 6),
          Flexible(
            child: Text(
              value,
              style: GoogleFonts.cairo(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
          const SizedBox(height: 4),
          Flexible(
            child: Text(
              title,
              style: GoogleFonts.cairo(
                fontSize: 10,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
        ],
      ),
    );
  }
}
