import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:mobile_pos/Screens/HRM/employees/provider/employee_provider.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/models/hrm_models.dart';

class EmployeeListReport extends ConsumerStatefulWidget {
  const EmployeeListReport({super.key});

  @override
  ConsumerState<EmployeeListReport> createState() => _EmployeeListReportState();
}

class _EmployeeListReportState extends ConsumerState<EmployeeListReport> {
  String searchQuery = '';
  String selectedDepartment = 'الكل';
  List<String> departments = ['الكل'];
  bool isActive = true;

  @override
  Widget build(BuildContext context) {
    final employeesAsyncValue = ref.watch(employeeListProvider);

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('تقرير قائمة الموظفين'),
        backgroundColor: kMainColor,
        centerTitle: true,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        titleTextStyle: const TextStyle(
            color: Colors.white, fontSize: 20, fontWeight: FontWeight.bold),
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: () {
              employeesAsyncValue.whenData((employees) {
                _printReport(employees);
              });
            },
          ),
        ],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                // حقل البحث
                TextField(
                  decoration: const InputDecoration(
                    hintText: 'بحث عن موظف...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    setState(() {
                      searchQuery = value;
                    });
                  },
                ),
                const SizedBox(height: 16),
                // فلاتر إضافية
                Row(
                  children: [
                    Expanded(
                      child: employeesAsyncValue.when(
                        data: (employees) {
                          // استخراج الأقسام الفريدة
                          if (departments.length <= 1) {
                            final uniqueDepartments = employees
                                .map((e) => e.department)
                                .toSet()
                                .toList();
                            departments = ['الكل', ...uniqueDepartments];
                          }

                          return DropdownButtonFormField<String>(
                            decoration: const InputDecoration(
                              labelText: 'القسم',
                              border: OutlineInputBorder(),
                            ),
                            value: selectedDepartment,
                            items: departments.map((department) {
                              return DropdownMenuItem<String>(
                                value: department,
                                child: Text(department),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                selectedDepartment = value!;
                              });
                            },
                          );
                        },
                        loading: () => const CircularProgressIndicator(),
                        error: (_, __) => const Text('خطأ في تحميل الأقسام'),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Row(
                        children: [
                          const Flexible(
                            child: Text(
                              'الموظفين النشطين فقط:',
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          Switch(
                            value: isActive,
                            onChanged: (value) {
                              setState(() {
                                isActive = value;
                              });
                            },
                            activeColor: kMainColor,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Expanded(
            child: employeesAsyncValue.when(
              data: (employees) {
                // تطبيق الفلاتر
                List<EmployeeModel> filteredEmployees =
                    employees.where((employee) {
                  // فلتر البحث
                  final matchesSearch = searchQuery.isEmpty ||
                      employee.name
                          .toLowerCase()
                          .contains(searchQuery.toLowerCase()) ||
                      employee.email
                          .toLowerCase()
                          .contains(searchQuery.toLowerCase()) ||
                      employee.phone.contains(searchQuery);

                  // فلتر القسم
                  final matchesDepartment = selectedDepartment == 'الكل' ||
                      employee.department == selectedDepartment;

                  // فلتر الحالة
                  final matchesStatus = !isActive || employee.isActive;

                  return matchesSearch && matchesDepartment && matchesStatus;
                }).toList();

                if (filteredEmployees.isEmpty) {
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.person_off, size: 64, color: Colors.grey),
                        SizedBox(height: 16),
                        Text(
                          'لا يوجد موظفين مطابقين للبحث',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  itemCount: filteredEmployees.length,
                  itemBuilder: (context, index) {
                    final employee = filteredEmployees[index];
                    return Card(
                      margin: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: kMainColor,
                          child: Text(
                            employee.name.isNotEmpty
                                ? employee.name[0].toUpperCase()
                                : '?',
                            style: const TextStyle(color: Colors.white),
                          ),
                        ),
                        title: Text(
                          employee.name,
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('المسمى الوظيفي: ${employee.designation}'),
                            Text('القسم: ${employee.department}'),
                            Text('الراتب: ${employee.salary}'),
                            Text(
                                'تاريخ التعيين: ${DateFormat.yMd().format(employee.joiningDate)}'),
                          ],
                        ),
                        trailing: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color:
                                employee.isActive ? Colors.green : Colors.red,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            employee.isActive ? 'نشط' : 'غير نشط',
                            style: const TextStyle(color: Colors.white),
                          ),
                        ),
                        isThreeLine: true,
                      ),
                    );
                  },
                );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.error_outline,
                        size: 60, color: Colors.red),
                    const SizedBox(height: 16),
                    const Text(
                      'حدث خطأ أثناء تحميل البيانات',
                      style: TextStyle(color: Colors.red, fontSize: 18),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '$error',
                      style: const TextStyle(color: Colors.grey, fontSize: 14),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        final _ = ref.refresh(employeeListProvider);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: kMainColor,
                      ),
                      child: const Text('إعادة المحاولة'),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _printReport(List<EmployeeModel> employees) {
    // عرض رسالة للمستخدم
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('طباعة التقرير'),
        content: const Text(
            'سيتم دعم طباعة التقرير قريباً. يمكنك حالياً مشاهدة التقرير على الشاشة.'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
}
