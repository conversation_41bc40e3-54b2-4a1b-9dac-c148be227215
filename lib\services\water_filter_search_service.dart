import 'package:flutter/foundation.dart';
import 'package:mobile_pos/model/water_filter_models.dart';
import 'package:mobile_pos/services/water_filter_cache_service.dart';

/// فلاتر البحث للمنتجات
class ProductFilters {
  final WaterFilterCategory? category;
  final double? minPrice;
  final double? maxPrice;
  final int? minStock;
  final int? maxStock;
  final bool? isInstallationRequired;
  final String? brand;

  ProductFilters({
    this.category,
    this.minPrice,
    this.maxPrice,
    this.minStock,
    this.maxStock,
    this.isInstallationRequired,
    this.brand,
  });
}

/// فلاتر البحث للعملاء
class CustomerFilters {
  final String? city;
  final String? area;
  final DateTime? registeredAfter;
  final DateTime? registeredBefore;

  CustomerFilters({
    this.city,
    this.area,
    this.registeredAfter,
    this.registeredBefore,
  });
}

/// فلاتر البحث للأنظمة
class SystemFilters {
  final FilterSystemStatus? status;
  final DateTime? installedAfter;
  final DateTime? installedBefore;
  final bool? isUnderWarranty;
  final bool? needsMaintenance;
  final double? minCost;
  final double? maxCost;

  SystemFilters({
    this.status,
    this.installedAfter,
    this.installedBefore,
    this.isUnderWarranty,
    this.needsMaintenance,
    this.minCost,
    this.maxCost,
  });
}

/// خدمة البحث المتقدم لنظام فلاتر المياه
class WaterFilterSearchService {
  static final WaterFilterSearchService _instance =
      WaterFilterSearchService._internal();
  factory WaterFilterSearchService() => _instance;
  WaterFilterSearchService._internal();

  final WaterFilterCacheService _cacheService = WaterFilterCacheService();

  /// بحث متقدم في المنتجات
  Future<List<WaterFilterProduct>> searchProducts({
    String? query,
    ProductFilters? filters,
    String sortBy = 'name', // name, price, stock, brand
    bool ascending = true,
  }) async {
    try {
      List<WaterFilterProduct> products =
          await _cacheService.getCachedProducts();

      // تطبيق البحث النصي
      if (query != null && query.isNotEmpty) {
        final lowerQuery = query.toLowerCase();
        products = products.where((product) {
          return _fuzzyMatch(product.name.toLowerCase(), lowerQuery) ||
              _fuzzyMatch(product.brand.toLowerCase(), lowerQuery) ||
              _fuzzyMatch(product.description.toLowerCase(), lowerQuery) ||
              _fuzzyMatch(
                  product.category.arabicName.toLowerCase(), lowerQuery) ||
              product.specifications
                  .any((spec) => _fuzzyMatch(spec.toLowerCase(), lowerQuery));
        }).toList();
      }

      // تطبيق الفلاتر
      if (filters != null) {
        products = products.where((product) {
          // فلتر الفئة
          if (filters.category != null &&
              product.category != filters.category) {
            return false;
          }

          // فلتر السعر
          if (filters.minPrice != null && product.price < filters.minPrice!) {
            return false;
          }
          if (filters.maxPrice != null && product.price > filters.maxPrice!) {
            return false;
          }

          // فلتر المخزون
          if (filters.minStock != null && product.stock < filters.minStock!) {
            return false;
          }
          if (filters.maxStock != null && product.stock > filters.maxStock!) {
            return false;
          }

          // فلتر التركيب
          if (filters.isInstallationRequired != null &&
              product.isInstallationRequired !=
                  filters.isInstallationRequired!) {
            return false;
          }

          // فلتر العلامة التجارية
          if (filters.brand != null &&
              !_fuzzyMatch(
                  product.brand.toLowerCase(), filters.brand!.toLowerCase())) {
            return false;
          }

          return true;
        }).toList();
      }

      // تطبيق الترتيب
      products.sort((a, b) {
        int comparison = 0;
        switch (sortBy) {
          case 'name':
            comparison = a.name.compareTo(b.name);
            break;
          case 'price':
            comparison = a.price.compareTo(b.price);
            break;
          case 'stock':
            comparison = a.stock.compareTo(b.stock);
            break;
          case 'brand':
            comparison = a.brand.compareTo(b.brand);
            break;
          default:
            comparison = a.name.compareTo(b.name);
        }
        return ascending ? comparison : -comparison;
      });

      debugPrint('🔍 بحث المنتجات: ${products.length} نتيجة');
      return products;
    } catch (e) {
      debugPrint('❌ خطأ في بحث المنتجات: $e');
      return [];
    }
  }

  /// بحث متقدم في العملاء
  Future<List<WaterFilterCustomer>> searchCustomers({
    String? query,
    CustomerFilters? filters,
    String sortBy = 'name', // name, city, area, registrationDate
    bool ascending = true,
  }) async {
    try {
      List<WaterFilterCustomer> customers =
          await _cacheService.getCachedCustomers();

      // تطبيق البحث النصي
      if (query != null && query.isNotEmpty) {
        final lowerQuery = query.toLowerCase();
        customers = customers.where((customer) {
          return _fuzzyMatch(customer.name.toLowerCase(), lowerQuery) ||
              customer.phone.contains(query) ||
              _fuzzyMatch(customer.address.toLowerCase(), lowerQuery) ||
              _fuzzyMatch(customer.area.toLowerCase(), lowerQuery) ||
              _fuzzyMatch(customer.city.toLowerCase(), lowerQuery) ||
              customer.phone.toLowerCase().contains(lowerQuery);
        }).toList();
      }

      // تطبيق الفلاتر
      if (filters != null) {
        customers = customers.where((customer) {
          // فلتر المدينة
          if (filters.city != null &&
              !_fuzzyMatch(
                  customer.city.toLowerCase(), filters.city!.toLowerCase())) {
            return false;
          }

          // فلتر المنطقة
          if (filters.area != null &&
              !_fuzzyMatch(
                  customer.area.toLowerCase(), filters.area!.toLowerCase())) {
            return false;
          }

          // فلتر تاريخ التسجيل
          if (filters.registeredAfter != null &&
              customer.createdAt != null &&
              customer.createdAt!.isBefore(filters.registeredAfter!)) {
            return false;
          }
          if (filters.registeredBefore != null &&
              customer.createdAt != null &&
              customer.createdAt!.isAfter(filters.registeredBefore!)) {
            return false;
          }

          return true;
        }).toList();
      }

      // تطبيق الترتيب
      customers.sort((a, b) {
        int comparison = 0;
        switch (sortBy) {
          case 'name':
            comparison = a.name.compareTo(b.name);
            break;
          case 'city':
            comparison = a.city.compareTo(b.city);
            break;
          case 'area':
            comparison = a.area.compareTo(b.area);
            break;
          case 'registrationDate':
            if (a.createdAt != null && b.createdAt != null) {
              comparison = a.createdAt!.compareTo(b.createdAt!);
            }
            break;
          default:
            comparison = a.name.compareTo(b.name);
        }
        return ascending ? comparison : -comparison;
      });

      debugPrint('🔍 بحث العملاء: ${customers.length} نتيجة');
      return customers;
    } catch (e) {
      debugPrint('❌ خطأ في بحث العملاء: $e');
      return [];
    }
  }

  /// بحث متقدم في الأنظمة
  Future<List<WaterFilterSystem>> searchSystems({
    String? query,
    SystemFilters? filters,
    String sortBy =
        'installationDate', // installationDate, status, cost, serialNumber
    bool ascending = false,
  }) async {
    try {
      List<WaterFilterSystem> systems = await _cacheService.getCachedSystems();

      // تطبيق البحث النصي
      if (query != null && query.isNotEmpty) {
        final lowerQuery = query.toLowerCase();
        systems = systems.where((system) {
          return _fuzzyMatch(system.serialNumber.toLowerCase(), lowerQuery) ||
              system.id.toLowerCase().contains(lowerQuery) ||
              system.customerId.toLowerCase().contains(lowerQuery) ||
              system.productId.toLowerCase().contains(lowerQuery);
        }).toList();
      }

      // تطبيق الفلاتر
      if (filters != null) {
        systems = systems.where((system) {
          // فلتر الحالة
          if (filters.status != null && system.status != filters.status) {
            return false;
          }

          // فلتر تاريخ التركيب
          if (filters.installedAfter != null &&
              system.installationDate.isBefore(filters.installedAfter!)) {
            return false;
          }
          if (filters.installedBefore != null &&
              system.installationDate.isAfter(filters.installedBefore!)) {
            return false;
          }

          // فلتر الضمان
          if (filters.isUnderWarranty != null &&
              system.isUnderWarranty != filters.isUnderWarranty!) {
            return false;
          }

          // فلتر الصيانة
          if (filters.needsMaintenance != null) {
            final needsMaintenance =
                system.status == FilterSystemStatus.needsMaintenance ||
                    system.nextMaintenanceDate.isBefore(DateTime.now());
            if (needsMaintenance != filters.needsMaintenance!) {
              return false;
            }
          }

          // فلتر التكلفة
          if (filters.minCost != null && system.totalCost < filters.minCost!) {
            return false;
          }
          if (filters.maxCost != null && system.totalCost > filters.maxCost!) {
            return false;
          }

          return true;
        }).toList();
      }

      // تطبيق الترتيب
      systems.sort((a, b) {
        int comparison = 0;
        switch (sortBy) {
          case 'installationDate':
            comparison = a.installationDate.compareTo(b.installationDate);
            break;
          case 'status':
            comparison = a.status.index.compareTo(b.status.index);
            break;
          case 'cost':
            comparison = a.totalCost.compareTo(b.totalCost);
            break;
          case 'serialNumber':
            comparison = a.serialNumber.compareTo(b.serialNumber);
            break;
          default:
            comparison = a.installationDate.compareTo(b.installationDate);
        }
        return ascending ? comparison : -comparison;
      });

      debugPrint('🔍 بحث الأنظمة: ${systems.length} نتيجة');
      return systems;
    } catch (e) {
      debugPrint('❌ خطأ في بحث الأنظمة: $e');
      return [];
    }
  }

  /// بحث ذكي موحد (يبحث في جميع الأنواع)
  Future<Map<String, dynamic>> universalSearch(String query) async {
    if (query.isEmpty) {
      return {
        'products': <WaterFilterProduct>[],
        'customers': <WaterFilterCustomer>[],
        'systems': <WaterFilterSystem>[],
      };
    }

    try {
      final results = await Future.wait([
        searchProducts(query: query),
        searchCustomers(query: query),
        searchSystems(query: query),
      ]);

      return {
        'products': results[0],
        'customers': results[1],
        'systems': results[2],
      };
    } catch (e) {
      debugPrint('❌ خطأ في البحث الموحد: $e');
      return {
        'products': <WaterFilterProduct>[],
        'customers': <WaterFilterCustomer>[],
        'systems': <WaterFilterSystem>[],
      };
    }
  }

  /// بحث ضبابي (Fuzzy Search) للنصوص
  bool _fuzzyMatch(String text, String query) {
    if (query.isEmpty) return true;
    if (text.isEmpty) return false;

    // بحث مباشر
    if (text.contains(query)) return true;

    // بحث بالكلمات المنفصلة
    final queryWords = query.split(' ').where((word) => word.isNotEmpty);
    final textWords = text.split(' ').where((word) => word.isNotEmpty);

    for (final queryWord in queryWords) {
      bool found = false;
      for (final textWord in textWords) {
        if (textWord.contains(queryWord) || queryWord.contains(textWord)) {
          found = true;
          break;
        }
      }
      if (!found) return false;
    }

    return queryWords.isNotEmpty;
  }

  /// الحصول على اقتراحات البحث
  Future<List<String>> getSearchSuggestions(String query, String type) async {
    if (query.length < 2) return [];

    try {
      final suggestions = <String>[];
      final lowerQuery = query.toLowerCase();

      switch (type) {
        case 'products':
          final products = await _cacheService.getCachedProducts();
          for (final product in products) {
            if (product.name.toLowerCase().contains(lowerQuery)) {
              suggestions.add(product.name);
            }
            if (product.brand.toLowerCase().contains(lowerQuery)) {
              suggestions.add(product.brand);
            }
          }
          break;

        case 'customers':
          final customers = await _cacheService.getCachedCustomers();
          for (final customer in customers) {
            if (customer.name.toLowerCase().contains(lowerQuery)) {
              suggestions.add(customer.name);
            }
            if (customer.area.toLowerCase().contains(lowerQuery)) {
              suggestions.add(customer.area);
            }
            if (customer.city.toLowerCase().contains(lowerQuery)) {
              suggestions.add(customer.city);
            }
          }
          break;

        case 'systems':
          final systems = await _cacheService.getCachedSystems();
          for (final system in systems) {
            if (system.serialNumber.toLowerCase().contains(lowerQuery)) {
              suggestions.add(system.serialNumber);
            }
          }
          break;
      }

      // إزالة التكرارات وترتيب النتائج
      final uniqueSuggestions = suggestions.toSet().toList();
      uniqueSuggestions.sort();

      return uniqueSuggestions.take(10).toList();
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على اقتراحات البحث: $e');
      return [];
    }
  }

  /// إحصائيات البحث
  Future<Map<String, dynamic>> getSearchStats() async {
    try {
      final products = await _cacheService.getCachedProducts();
      final customers = await _cacheService.getCachedCustomers();
      final systems = await _cacheService.getCachedSystems();

      return {
        'total_products': products.length,
        'total_customers': customers.length,
        'total_systems': systems.length,
        'product_categories': WaterFilterCategory.values.length,
        'system_statuses': FilterSystemStatus.values.length,
        'searchable_fields': {
          'products': [
            'name',
            'brand',
            'description',
            'category',
            'specifications'
          ],
          'customers': ['name', 'phone', 'email', 'address', 'area', 'city'],
          'systems': ['serialNumber', 'id', 'customerId', 'productId'],
        },
      };
    } catch (e) {
      debugPrint('❌ خطأ في حساب إحصائيات البحث: $e');
      return {};
    }
  }
}
