import 'package:flutter/material.dart';
import 'package:mobile_pos/Screens/Settings/settings_screen.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/generated/l10n.dart' as lang;
import 'package:mobile_pos/widgets/quick_ai_assistant_modal.dart';
import 'package:nb_utils/nb_utils.dart';

class BottomNav extends StatefulWidget {
  const BottomNav({
    super.key,
  });

  @override
  State<BottomNav> createState() => _BottomNavState();
}

class _BottomNavState extends State<BottomNav> {
  int _selectedIndex = 0;

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
      switch (_selectedIndex) {
        case 0:
          Navigator.pushNamed(context, '/home');
          break;
        case 1:
          Navigator.pushNamed(context, '/order');
          break;
        case 2:
          Navigator.pushNamed(context, '/featuredProduct');
          break;
        case 3:
          _showAIAssistant();
          break;
        case 4:
          const SettingScreen().launch(context);
          break;
      }
    });
  }

  void _showAIAssistant() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const QuickAIAssistantModal(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 48, // تم تقليل الارتفاع بنسبة 20% (من 60 إلى 48)
      child: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        elevation: 6.0,
        selectedItemColor: kMainColor,
        selectedFontSize: 10, // تقليل حجم النص المحدد
        unselectedFontSize: 9, // تقليل حجم النص غير المحدد
        iconSize: 24, // تكبير حجم الأيقونات
        // ignore: prefer_const_literals_to_create_immutables
        items: [
          BottomNavigationBarItem(
            icon: const Padding(
              padding: EdgeInsets.only(
                  bottom: 2), // تقليل المسافة بين الأيقونة والنص
              child: Icon(Icons.home, size: 24), // تكبير حجم الأيقونة
            ),
            label: lang.S.of(context).home,
          ),
          BottomNavigationBarItem(
            icon: const Padding(
              padding: EdgeInsets.only(bottom: 2),
              child: Icon(Icons.flare_sharp, size: 24), // تكبير حجم الأيقونة
            ),
            label: lang.S.of(context).maan,
          ),
          BottomNavigationBarItem(
            icon: const Padding(
              padding: EdgeInsets.only(bottom: 2),
              child: Icon(Icons.backpack, size: 24), // تكبير حجم الأيقونة
            ),
            label: lang.S.of(context).pacakge,
          ),
          BottomNavigationBarItem(
            icon: const Padding(
              padding: EdgeInsets.only(bottom: 2),
              child: Icon(Icons.smart_toy, size: 24), // تكبير حجم الأيقونة
            ),
            label: 'المساعد',
          ),
          BottomNavigationBarItem(
            icon: const Padding(
              padding: EdgeInsets.only(bottom: 2),
              child: Icon(Icons.settings, size: 24), // تكبير حجم الأيقونة
            ),
            label: lang.S.of(context).setting,
          ),
        ],
        currentIndex: _selectedIndex,
        onTap: _onItemTapped,
      ),
    );
  }
}
