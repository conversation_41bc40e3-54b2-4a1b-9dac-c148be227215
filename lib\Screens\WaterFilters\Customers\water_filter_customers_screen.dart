import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/model/water_filter_models.dart';
import 'package:mobile_pos/services/water_filter_customer_service.dart';
import 'package:mobile_pos/Screens/WaterFilters/Customers/add_water_filter_customer_screen.dart';
import 'package:mobile_pos/Screens/WaterFilters/Customers/edit_water_filter_customer_screen.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

class WaterFilterCustomersScreen extends StatefulWidget {
  const WaterFilterCustomersScreen({super.key});

  @override
  State<WaterFilterCustomersScreen> createState() => _WaterFilterCustomersScreenState();
}

class _WaterFilterCustomersScreenState extends State<WaterFilterCustomersScreen> {
  List<WaterFilterCustomer> _customers = [];
  List<WaterFilterCustomer> _filteredCustomers = [];
  bool _isLoading = true;
  String _searchQuery = '';
  String? _selectedCity;
  String? _selectedArea;

  @override
  void initState() {
    super.initState();
    _loadCustomers();
  }

  @override
  void dispose() {
    // إلغاء المستمعين
    WaterFilterCustomerService.cancelCustomersListener(
      ownerId: 'water_filter_customers_screen',
    );
    super.dispose();
  }

  Future<void> _loadCustomers() async {
    try {
      setState(() => _isLoading = true);
      
      final customers = await WaterFilterCustomerService.getAllCustomers();
      
      if (mounted) {
        setState(() {
          _customers = customers;
          _filteredCustomers = customers;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        EasyLoading.showError('خطأ في تحميل العملاء: $e');
      }
    }
  }

  void _filterCustomers() {
    setState(() {
      _filteredCustomers = _customers.where((customer) {
        final matchesSearch = _searchQuery.isEmpty ||
            customer.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            customer.phone.contains(_searchQuery) ||
            customer.address.toLowerCase().contains(_searchQuery.toLowerCase());
        
        final matchesCity = _selectedCity == null ||
            customer.city.toLowerCase() == _selectedCity!.toLowerCase();
        
        final matchesArea = _selectedArea == null ||
            customer.area.toLowerCase() == _selectedArea!.toLowerCase();
        
        return matchesSearch && matchesCity && matchesArea;
      }).toList();
    });
  }

  void _onSearchChanged(String query) {
    setState(() => _searchQuery = query);
    _filterCustomers();
  }

  void _onCityChanged(String? city) {
    setState(() {
      _selectedCity = city;
      _selectedArea = null; // إعادة تعيين المنطقة عند تغيير المدينة
    });
    _filterCustomers();
  }

  void _onAreaChanged(String? area) {
    setState(() => _selectedArea = area);
    _filterCustomers();
  }

  List<String> _getUniqueCities() {
    return _customers.map((c) => c.city).toSet().toList()..sort();
  }

  List<String> _getUniqueAreas() {
    if (_selectedCity == null) return [];
    return _customers
        .where((c) => c.city.toLowerCase() == _selectedCity!.toLowerCase())
        .map((c) => c.area)
        .toSet()
        .toList()
      ..sort();
  }

  Future<void> _deleteCustomer(WaterFilterCustomer customer) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'تأكيد الحذف',
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        content: Text(
          'هل أنت متأكد من حذف العميل "${customer.name}"؟',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text('إلغاء', style: GoogleFonts.cairo()),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: Text('حذف', style: GoogleFonts.cairo()),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      EasyLoading.show(status: 'جاري الحذف...', dismissOnTap: false);
      
      final success = await WaterFilterCustomerService.deleteCustomer(customer.id);
      
      EasyLoading.dismiss();
      
      if (success) {
        EasyLoading.showSuccess('تم حذف العميل بنجاح');
        _loadCustomers();
      } else {
        EasyLoading.showError('فشل في حذف العميل');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        elevation: 0,
        title: Text(
          'إدارة العملاء',
          style: GoogleFonts.cairo(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: _loadCustomers,
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        child: Column(
          children: [
            // شريط البحث والفلترة
            _buildSearchAndFilter(),
            
            // قائمة العملاء
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _filteredCustomers.isEmpty
                      ? _buildEmptyState()
                      : _buildCustomersList(),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const AddWaterFilterCustomerScreen(),
            ),
          );
          
          if (result == true) {
            _loadCustomers();
          }
        },
        backgroundColor: kMainColor,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildSearchAndFilter() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            onChanged: _onSearchChanged,
            decoration: InputDecoration(
              hintText: 'البحث في العملاء...',
              hintStyle: GoogleFonts.cairo(color: Colors.grey),
              prefixIcon: const Icon(Icons.search, color: kMainColor),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(15),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(15),
                borderSide: const BorderSide(color: kMainColor),
              ),
              filled: true,
              fillColor: Colors.grey.shade50,
            ),
            style: GoogleFonts.cairo(),
          ),
          
          const SizedBox(height: 15),
          
          // فلاتر المدينة والمنطقة
          Row(
            children: [
              // فلتر المدينة
              Expanded(
                child: DropdownButtonFormField<String?>(
                  value: _selectedCity,
                  onChanged: _onCityChanged,
                  decoration: InputDecoration(
                    labelText: 'المدينة',
                    labelStyle: GoogleFonts.cairo(color: kMainColor),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(15),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(15),
                      borderSide: const BorderSide(color: kMainColor),
                    ),
                    filled: true,
                    fillColor: Colors.grey.shade50,
                  ),
                  items: [
                    DropdownMenuItem<String?>(
                      value: null,
                      child: Text('جميع المدن', style: GoogleFonts.cairo()),
                    ),
                    ..._getUniqueCities().map((city) =>
                      DropdownMenuItem<String?>(
                        value: city,
                        child: Text(city, style: GoogleFonts.cairo()),
                      ),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(width: 15),
              
              // فلتر المنطقة
              Expanded(
                child: DropdownButtonFormField<String?>(
                  value: _selectedArea,
                  onChanged: _onAreaChanged,
                  decoration: InputDecoration(
                    labelText: 'المنطقة',
                    labelStyle: GoogleFonts.cairo(color: kMainColor),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(15),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(15),
                      borderSide: const BorderSide(color: kMainColor),
                    ),
                    filled: true,
                    fillColor: Colors.grey.shade50,
                  ),
                  items: [
                    DropdownMenuItem<String?>(
                      value: null,
                      child: Text('جميع المناطق', style: GoogleFonts.cairo()),
                    ),
                    ..._getUniqueAreas().map((area) =>
                      DropdownMenuItem<String?>(
                        value: area,
                        child: Text(area, style: GoogleFonts.cairo()),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 80,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 20),
          Text(
            'لا يوجد عملاء',
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 10),
          Text(
            'اضغط على زر + لإضافة عميل جديد',
            style: GoogleFonts.cairo(
              fontSize: 14,
              color: Colors.grey.shade500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCustomersList() {
    return ListView.builder(
      padding: const EdgeInsets.all(20),
      itemCount: _filteredCustomers.length,
      itemBuilder: (context, index) {
        final customer = _filteredCustomers[index];
        return _buildCustomerCard(customer);
      },
    );
  }

  Widget _buildCustomerCard(WaterFilterCustomer customer) {
    return Card(
      margin: const EdgeInsets.only(bottom: 15),
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      child: Padding(
        padding: const EdgeInsets.all(15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // أيقونة العميل
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(30),
                    color: kMainColor.withOpacity(0.1),
                  ),
                  child: const Icon(
                    Icons.person,
                    color: kMainColor,
                    size: 30,
                  ),
                ),
                
                const SizedBox(width: 15),
                
                // معلومات العميل
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        customer.name,
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 5),
                      Row(
                        children: [
                          const Icon(Icons.phone, size: 16, color: Colors.grey),
                          const SizedBox(width: 5),
                          Text(
                            customer.phone,
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 5),
                      Row(
                        children: [
                          const Icon(Icons.location_on, size: 16, color: Colors.grey),
                          const SizedBox(width: 5),
                          Expanded(
                            child: Text(
                              '${customer.area}, ${customer.city}',
                              style: GoogleFonts.cairo(
                                fontSize: 12,
                                color: Colors.grey.shade600,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                
                // الإجراءات
                PopupMenuButton<String>(
                  onSelected: (value) async {
                    if (value == 'edit') {
                      final result = await Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => EditWaterFilterCustomerScreen(customer: customer),
                        ),
                      );
                      
                      if (result == true) {
                        _loadCustomers();
                      }
                    } else if (value == 'delete') {
                      _deleteCustomer(customer);
                    }
                  },
                  itemBuilder: (context) => [
                    PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          const Icon(Icons.edit, color: kMainColor),
                          const SizedBox(width: 10),
                          Text('تعديل', style: GoogleFonts.cairo()),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          const Icon(Icons.delete, color: Colors.red),
                          const SizedBox(width: 10),
                          Text('حذف', style: GoogleFonts.cairo()),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            
            const SizedBox(height: 15),
            
            // العنوان
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'العنوان:',
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: kMainColor,
                    ),
                  ),
                  const SizedBox(height: 5),
                  Text(
                    customer.address,
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: Colors.grey.shade700,
                    ),
                  ),
                ],
              ),
            ),
            
            // البريد الإلكتروني إذا كان متوفراً
            if (customer.email.isNotEmpty) ...[
              const SizedBox(height: 10),
              Row(
                children: [
                  const Icon(Icons.email, size: 16, color: Colors.grey),
                  const SizedBox(width: 5),
                  Text(
                    customer.email,
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }
}
