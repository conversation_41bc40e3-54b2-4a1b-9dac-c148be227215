import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/model/water_filter_models.dart';
import 'package:mobile_pos/services/water_filter_service.dart';

class AddSystemScreen extends StatefulWidget {
  const AddSystemScreen({super.key});

  @override
  State<AddSystemScreen> createState() => _AddSystemScreenState();
}

class _AddSystemScreenState extends State<AddSystemScreen> {
  final _formKey = GlobalKey<FormState>();
  final _serialNumberController = TextEditingController();
  final _totalCostController = TextEditingController();
  final _paidAmountController = TextEditingController();
  final _installationNotesController = TextEditingController();

  String? _selectedCustomerId;
  String? _selectedProductId;
  DateTime _installationDate = DateTime.now();
  DateTime _warrantyEndDate = DateTime.now().add(const Duration(days: 365));
  DateTime _nextMaintenanceDate = DateTime.now().add(const Duration(days: 180));

  List<WaterFilterCustomer> _customers = [];
  List<WaterFilterProduct> _products = [];
  bool _isLoading = false;
  bool _isLoadingData = true;

  @override
  void initState() {
    super.initState();
    _loadData();
    _generateSerialNumber();
  }

  @override
  void dispose() {
    _serialNumberController.dispose();
    _totalCostController.dispose();
    _paidAmountController.dispose();
    _installationNotesController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      // تحميل العملاء
      final customersData = await WaterFilterService.getData('Customers');
      final customers = <WaterFilterCustomer>[];

      customersData.forEach((key, value) {
        try {
          final customer = WaterFilterCustomer.fromJson(
            Map<String, dynamic>.from(value),
          );
          customers.add(customer);
        } catch (e) {
          debugPrint('خطأ في معالجة عميل: $e');
        }
      });

      // تحميل المنتجات
      final productsData = await WaterFilterService.getData('Products');
      final products = <WaterFilterProduct>[];

      productsData.forEach((key, value) {
        try {
          final product = WaterFilterProduct.fromJson(
            Map<String, dynamic>.from(value),
          );
          products.add(product);
        } catch (e) {
          debugPrint('خطأ في معالجة منتج: $e');
        }
      });

      setState(() {
        _customers = customers;
        _products = products;
        _isLoadingData = false;
      });
    } catch (e) {
      debugPrint('خطأ في تحميل البيانات: $e');
      setState(() => _isLoadingData = false);
    }
  }

  void _generateSerialNumber() {
    final now = DateTime.now();
    final year = now.year;
    final month = now.month.toString().padLeft(2, '0');
    final day = now.day.toString().padLeft(2, '0');
    final time = now.millisecondsSinceEpoch.toString().substring(8);

    _serialNumberController.text = 'WF$year$month$day$time';
  }

  Future<void> _selectDate(String type) async {
    DateTime initialDate;
    DateTime firstDate;
    DateTime lastDate;

    switch (type) {
      case 'installation':
        initialDate = _installationDate;
        firstDate = DateTime.now().subtract(const Duration(days: 365));
        lastDate = DateTime.now();
        break;
      case 'warranty':
        initialDate = _warrantyEndDate;
        firstDate = _installationDate;
        lastDate = _installationDate.add(const Duration(days: 365 * 3));
        break;
      case 'maintenance':
        initialDate = _nextMaintenanceDate;
        firstDate = DateTime.now();
        lastDate = DateTime.now().add(const Duration(days: 365));
        break;
      default:
        return;
    }

    final date = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: firstDate,
      lastDate: lastDate,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: kMainColor,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (date != null) {
      setState(() {
        switch (type) {
          case 'installation':
            _installationDate = date;
            // تحديث تاريخ انتهاء الضمان تلقائياً
            _warrantyEndDate = date.add(const Duration(days: 365));
            // تحديث تاريخ الصيانة التالية تلقائياً
            _nextMaintenanceDate = date.add(const Duration(days: 180));
            break;
          case 'warranty':
            _warrantyEndDate = date;
            break;
          case 'maintenance':
            _nextMaintenanceDate = date;
            break;
        }
      });
    }
  }

  void _onProductSelected(String? productId) {
    setState(() {
      _selectedProductId = productId;
      if (productId != null) {
        final product = _products.firstWhere((p) => p.id == productId);
        // حساب التكلفة الإجمالية تلقائياً
        final totalCost = product.price + product.installationCost;
        _totalCostController.text = totalCost.toStringAsFixed(2);
      }
    });
  }

  double get _remainingAmount {
    final totalCost = double.tryParse(_totalCostController.text) ?? 0;
    final paidAmount = double.tryParse(_paidAmountController.text) ?? 0;
    return totalCost - paidAmount;
  }

  Future<void> _saveSystem() async {
    if (!_formKey.currentState!.validate() ||
        _selectedCustomerId == null ||
        _selectedProductId == null) {
      return;
    }

    setState(() => _isLoading = true);

    try {
      final totalCost = double.parse(_totalCostController.text);
      final paidAmount = double.parse(_paidAmountController.text);

      final system = WaterFilterSystem(
        id: WaterFilterService.generateId(),
        customerId: _selectedCustomerId!,
        productId: _selectedProductId!,
        serialNumber: _serialNumberController.text.trim(),
        installationDate: _installationDate,
        status: FilterSystemStatus.active,
        nextMaintenanceDate: _nextMaintenanceDate,
        totalCost: totalCost,
        paidAmount: paidAmount,
        remainingAmount: _remainingAmount,
        isUnderWarranty: _warrantyEndDate.isAfter(DateTime.now()),
        warrantyEndDate: _warrantyEndDate,
        installationNotes: _installationNotesController.text.trim().isEmpty
            ? null
            : _installationNotesController.text.trim(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final success = await WaterFilterService.saveData(
        'Systems/${system.id}',
        system.toJson(),
      );

      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إضافة النظام بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context, true);
        }
      } else {
        throw Exception('فشل في حفظ النظام');
      }
    } catch (e) {
      debugPrint('خطأ في حفظ النظام: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ النظام: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        title: Text(
          'إضافة نظام جديد',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        child: _isLoadingData
            ? const Center(child: CircularProgressIndicator())
            : Form(
                key: _formKey,
                child: ListView(
                  padding: const EdgeInsets.all(20),
                  children: [
                    // الرقم التسلسلي
                    _buildSectionTitle('معلومات النظام'),
                    const SizedBox(height: 16),

                    TextFormField(
                      controller: _serialNumberController,
                      decoration: InputDecoration(
                        labelText: 'الرقم التسلسلي',
                        hintText: 'WF20240101001',
                        prefixIcon: const Icon(Icons.qr_code),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        suffixIcon: IconButton(
                          icon: const Icon(Icons.refresh),
                          onPressed: _generateSerialNumber,
                          tooltip: 'إنشاء رقم جديد',
                        ),
                      ),
                      style: GoogleFonts.cairo(),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال الرقم التسلسلي';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 20),

                    // اختيار العميل
                    _buildSectionTitle('بيانات العميل'),
                    const SizedBox(height: 16),

                    Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: DropdownButtonFormField<String>(
                        value: _selectedCustomerId,
                        decoration: const InputDecoration(
                          labelText: 'العميل',
                          prefixIcon: Icon(Icons.person),
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                        ),
                        hint: Text(
                          'اختر العميل',
                          style: GoogleFonts.cairo(color: Colors.grey.shade600),
                        ),
                        items: _customers.map((customer) {
                          return DropdownMenuItem<String>(
                            value: customer.id,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  customer.name,
                                  style: GoogleFonts.cairo(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Text(
                                  customer.phone,
                                  style: GoogleFonts.cairo(
                                    fontSize: 12,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                              ],
                            ),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() => _selectedCustomerId = value);
                        },
                        validator: (value) {
                          if (value == null) {
                            return 'يرجى اختيار العميل';
                          }
                          return null;
                        },
                      ),
                    ),

                    const SizedBox(height: 20),

                    // اختيار المنتج
                    _buildSectionTitle('نوع الفلتر'),
                    const SizedBox(height: 16),

                    Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: DropdownButtonFormField<String>(
                        value: _selectedProductId,
                        decoration: const InputDecoration(
                          labelText: 'نوع الفلتر',
                          prefixIcon: Icon(Icons.water_drop),
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                        ),
                        hint: Text(
                          'اختر نوع الفلتر',
                          style: GoogleFonts.cairo(color: Colors.grey.shade600),
                        ),
                        items: _products.map((product) {
                          return DropdownMenuItem<String>(
                            value: product.id,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  product.name,
                                  style: GoogleFonts.cairo(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Text(
                                  '${product.price.toStringAsFixed(0)} ج.م + ${product.installationCost.toStringAsFixed(0)} ج.م تركيب',
                                  style: GoogleFonts.cairo(
                                    fontSize: 12,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                              ],
                            ),
                          );
                        }).toList(),
                        onChanged: _onProductSelected,
                        validator: (value) {
                          if (value == null) {
                            return 'يرجى اختيار نوع الفلتر';
                          }
                          return null;
                        },
                      ),
                    ),

                    const SizedBox(height: 20),

                    // التكلفة والدفع
                    _buildSectionTitle('التكلفة والدفع'),
                    const SizedBox(height: 16),

                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: _totalCostController,
                            keyboardType: TextInputType.number,
                            decoration: InputDecoration(
                              labelText: 'إجمالي التكلفة',
                              hintText: '0.00',
                              prefixIcon: const Icon(Icons.attach_money),
                              suffixText: 'ج.م',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            style: GoogleFonts.cairo(),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'يرجى إدخال التكلفة';
                              }
                              final cost = double.tryParse(value);
                              if (cost == null || cost <= 0) {
                                return 'يرجى إدخال تكلفة صحيحة';
                              }
                              return null;
                            },
                            onChanged: (value) => setState(() {}),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: TextFormField(
                            controller: _paidAmountController,
                            keyboardType: TextInputType.number,
                            decoration: InputDecoration(
                              labelText: 'المبلغ المدفوع',
                              hintText: '0.00',
                              prefixIcon: const Icon(Icons.payment),
                              suffixText: 'ج.م',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            style: GoogleFonts.cairo(),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'يرجى إدخال المبلغ المدفوع';
                              }
                              final paid = double.tryParse(value);
                              if (paid == null || paid < 0) {
                                return 'يرجى إدخال مبلغ صحيح';
                              }
                              final total =
                                  double.tryParse(_totalCostController.text) ??
                                      0;
                              if (paid > total) {
                                return 'المبلغ المدفوع أكبر من التكلفة';
                              }
                              return null;
                            },
                            onChanged: (value) => setState(() {}),
                          ),
                        ),
                      ],
                    ),

                    // عرض المبلغ المتبقي
                    if (_remainingAmount > 0) ...[
                      const SizedBox(height: 12),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.orange.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.orange.shade200),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.info,
                              color: Colors.orange.shade600,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'المبلغ المتبقي: ${_remainingAmount.toStringAsFixed(2)} ج.م',
                              style: GoogleFonts.cairo(
                                fontWeight: FontWeight.bold,
                                color: Colors.orange.shade600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],

                    const SizedBox(height: 20),

                    // التواريخ
                    _buildSectionTitle('التواريخ المهمة'),
                    const SizedBox(height: 16),

                    // تاريخ التركيب
                    InkWell(
                      onTap: () => _selectDate('installation'),
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.build, color: kMainColor),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'تاريخ التركيب',
                                    style: GoogleFonts.cairo(
                                      fontSize: 12,
                                      color: Colors.grey.shade600,
                                    ),
                                  ),
                                  Text(
                                    '${_installationDate.day}/${_installationDate.month}/${_installationDate.year}',
                                    style: GoogleFonts.cairo(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Icon(Icons.calendar_today,
                                color: Colors.grey.shade400),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 12),

                    // تاريخ انتهاء الضمان
                    InkWell(
                      onTap: () => _selectDate('warranty'),
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.verified_user, color: Colors.green),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'انتهاء الضمان',
                                    style: GoogleFonts.cairo(
                                      fontSize: 12,
                                      color: Colors.grey.shade600,
                                    ),
                                  ),
                                  Text(
                                    '${_warrantyEndDate.day}/${_warrantyEndDate.month}/${_warrantyEndDate.year}',
                                    style: GoogleFonts.cairo(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Icon(Icons.calendar_today,
                                color: Colors.grey.shade400),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 12),

                    // تاريخ الصيانة التالية
                    InkWell(
                      onTap: () => _selectDate('maintenance'),
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.build_circle, color: Colors.orange),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'الصيانة التالية',
                                    style: GoogleFonts.cairo(
                                      fontSize: 12,
                                      color: Colors.grey.shade600,
                                    ),
                                  ),
                                  Text(
                                    '${_nextMaintenanceDate.day}/${_nextMaintenanceDate.month}/${_nextMaintenanceDate.year}',
                                    style: GoogleFonts.cairo(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Icon(Icons.calendar_today,
                                color: Colors.grey.shade400),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 20),

                    // ملاحظات التركيب
                    _buildSectionTitle('ملاحظات التركيب'),
                    const SizedBox(height: 16),

                    TextFormField(
                      controller: _installationNotesController,
                      maxLines: 3,
                      decoration: InputDecoration(
                        labelText: 'ملاحظات (اختياري)',
                        hintText: 'أدخل أي ملاحظات حول التركيب...',
                        prefixIcon: const Icon(Icons.note_add),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      style: GoogleFonts.cairo(),
                    ),

                    const SizedBox(height: 30),

                    // زر الحفظ
                    SizedBox(
                      width: double.infinity,
                      height: 50,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _saveSystem,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: kMainColor,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 2,
                        ),
                        child: _isLoading
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2,
                                ),
                              )
                            : Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Icon(Icons.save, size: 20),
                                  const SizedBox(width: 8),
                                  Text(
                                    'حفظ النظام',
                                    style: GoogleFonts.cairo(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                      ),
                    ),

                    const SizedBox(height: 20),
                  ],
                ),
              ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: GoogleFonts.cairo(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: kMainColor,
      ),
    );
  }
}
