// بسم الله الرحمن الرحيم
// مدير تهيئة المساعد الصوتي الموحد - AmrDevPOS

import 'package:flutter/foundation.dart';
import 'voice_assistant_core_service.dart';
import 'voice_assistant_simplified_service.dart';
import 'voice_assistant_permissions_service.dart';

/// مدير موحد لتهيئة جميع خدمات المساعد الصوتي
/// يضمن عدم تكرار التهيئة ويدير دورة الحياة بشكل صحيح
class VoiceAssistantInitializationManager {
  static final VoiceAssistantInitializationManager _instance =
      VoiceAssistantInitializationManager._internal();
  factory VoiceAssistantInitializationManager() => _instance;
  VoiceAssistantInitializationManager._internal();

  // حالة التهيئة العامة
  bool _isGloballyInitialized = false;
  bool _isInitializing = false;

  // الخدمات الأساسية
  final VoiceAssistantCoreService _coreService = VoiceAssistantCoreService();
  final VoiceAssistantSimplifiedService _simplifiedService =
      VoiceAssistantSimplifiedService();
  final VoiceAssistantPermissionsService _permissionsService =
      VoiceAssistantPermissionsService();

  // عداد المراجع لتتبع الاستخدام
  int _referenceCount = 0;

  /// تهيئة شاملة لجميع خدمات المساعد الصوتي
  Future<bool> initializeGlobalVoiceAssistant() async {
    // إذا كانت مهيأة بالفعل، زيادة عداد المراجع وإرجاع true
    if (_isGloballyInitialized) {
      _referenceCount++;
      debugPrint(
          '🔄 المساعد الصوتي مهيأ بالفعل - عداد المراجع: $_referenceCount');
      return true;
    }

    // إذا كانت قيد التهيئة، انتظار انتهاء التهيئة مع timeout
    if (_isInitializing) {
      debugPrint('⏳ انتظار انتهاء تهيئة المساعد الصوتي...');

      // انتظار مع timeout لتجنب الحلقة اللا نهائية
      int waitCount = 0;
      const maxWaitCount = 100; // 10 ثوانٍ (100 * 100ms)

      while (_isInitializing &&
          !_isGloballyInitialized &&
          waitCount < maxWaitCount) {
        await Future.delayed(const Duration(milliseconds: 100));
        waitCount++;
      }

      if (waitCount >= maxWaitCount) {
        debugPrint('⏰ انتهت مهلة انتظار التهيئة - إعادة تعيين الحالة');
        _isInitializing = false;
        return false;
      }

      if (_isGloballyInitialized) {
        _referenceCount++;
        debugPrint(
            '✅ تم الانضمام للخدمة المهيأة - عداد المراجع: $_referenceCount');
      }
      return _isGloballyInitialized;
    }

    // بدء التهيئة
    _isInitializing = true;
    _referenceCount = 1;

    try {
      // debugPrint('🚀 بدء التهيئة الشاملة للمساعد الصوتي...');

      // تطبيق timeout على عملية التهيئة الكاملة
      final initializationResult = await Future.any([
        _performInitialization(),
        Future.delayed(const Duration(seconds: 10)).then((_) => false),
      ]);

      if (!initializationResult) {
        //debugPrint('⏰ انتهت مهلة التهيئة أو فشلت العملية');
        _isInitializing = false;
        _referenceCount = 0;
        return false;
      }

      _isGloballyInitialized = true;
      _isInitializing = false;

      // debugPrint(
      //     '✅ تم تهيئة المساعد الصوتي بنجاح - عداد المراجع: $_referenceCount');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في التهيئة الشاملة للمساعد الصوتي: $e');
      _isInitializing = false;
      _referenceCount = 0;
      return false;
    }
  }

  /// تنفيذ عملية التهيئة الفعلية
  Future<bool> _performInitialization() async {
    try {
      // 1. التحقق من الأذونات أولاً (محسن)
      final canRun = await _permissionsService.canRunVoiceAssistant();
      if (!canRun) {
        //debugPrint('❌ الأذونات غير متاحة للمساعد الصوتي');

        // محاولة فحص مفصل للأذونات
        final permissionStatus =
            await _permissionsService.checkAllPermissionsEnhanced();
        final summary = permissionStatus['summary'] as Map<String, dynamic>?;

        if (summary != null) {
          //debugPrint('📊 تفاصيل الأذونات:');
          //debugPrint('   - الأساسية: ${summary['essential_granted']}');
          //debugPrint('   - المتقدمة: ${summary['advanced_granted']}');
          //debugPrint('   - النسبة: ${summary['percentage']}%');
        }

        return false;
      }

      // 2. تهيئة الخدمة الأساسية
      final coreInitialized = await _coreService.initialize();
      if (!coreInitialized) {
        debugPrint('❌ فشل في تهيئة الخدمة الأساسية');
        return false;
      }

      // 3. تهيئة الخدمة المبسطة (بدون تكرار للخدمة الأساسية)
      final simplifiedInitialized = await _initializeSimplifiedServiceSafely();
      if (!simplifiedInitialized) {
        debugPrint('⚠️ تحذير: فشل في تهيئة الخدمة المبسطة');
        // لا نوقف التهيئة هنا لأن الخدمة الأساسية تعمل
      }

      return true;
    } catch (e) {
      debugPrint('❌ خطأ في تنفيذ التهيئة: $e');
      return false;
    }
  }

  /// تهيئة آمنة للخدمة المبسطة بدون تكرار
  Future<bool> _initializeSimplifiedServiceSafely() async {
    try {
      // تحقق من أن الخدمة المبسطة لم تهيأ بعد
      if (_simplifiedService.isInitialized) {
        debugPrint('✅ الخدمة المبسطة مهيأة بالفعل');
        return true;
      }

      // تهيئة الخدمة المبسطة بدون إعادة تهيئة الخدمة الأساسية
      return await _simplifiedService.initializeWithoutCore();
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة الخدمة المبسطة: $e');
      return false;
    }
  }

  /// تقليل عداد المراجع وإيقاف الخدمات إذا لزم الأمر
  Future<void> decreaseReference() async {
    if (_referenceCount > 0) {
      _referenceCount--;
      debugPrint('📉 تقليل عداد المراجع: $_referenceCount');
    }

    // إذا لم تعد هناك مراجع، إيقاف الخدمات
    if (_referenceCount <= 0) {
      await _shutdownServices();
    }
  }

  /// إيقاف جميع الخدمات
  Future<void> _shutdownServices() async {
    try {
      debugPrint('🛑 إيقاف جميع خدمات المساعد الصوتي...');

      await _coreService.stop();
      await _simplifiedService.dispose();

      _isGloballyInitialized = false;
      _referenceCount = 0;

      debugPrint('✅ تم إيقاف جميع خدمات المساعد الصوتي');
    } catch (e) {
      debugPrint('❌ خطأ في إيقاف الخدمات: $e');
    }
  }

  /// الحصول على حالة التهيئة
  Map<String, dynamic> getInitializationStatus() {
    return {
      'isGloballyInitialized': _isGloballyInitialized,
      'isInitializing': _isInitializing,
      'referenceCount': _referenceCount,
      'coreServiceStatus': _coreService.getStatus(),
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// إعادة تعيين حالة التهيئة (للاختبار فقط)
  void resetForTesting() {
    _isGloballyInitialized = false;
    _isInitializing = false;
    _referenceCount = 0;
    debugPrint('🔄 تم إعادة تعيين حالة التهيئة للاختبار');
  }

  /// إعادة تعيين فورية في حالة المشاكل
  Future<void> forceReset() async {
    try {
      debugPrint('🔄 إعادة تعيين فورية للمساعد الصوتي...');

      // إيقاف جميع الخدمات
      await _shutdownServices();

      // إعادة تعيين جميع المتغيرات
      _isGloballyInitialized = false;
      _isInitializing = false;
      _referenceCount = 0;

      debugPrint('✅ تم إعادة تعيين المساعد الصوتي بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في إعادة التعيين الفورية: $e');
    }
  }

  /// تهيئة سريعة للاختبار
  Future<bool> quickInitializeForTesting() async {
    try {
      final initialized = await _coreService.initialize();
      if (initialized) {
        _isGloballyInitialized = true;
        _referenceCount = 1;
      }
      return initialized;
    } catch (e) {
      debugPrint('❌ خطأ في التهيئة السريعة: $e');
      return false;
    }
  }

  /// الحصول على الخدمة الأساسية
  VoiceAssistantCoreService get coreService => _coreService;

  /// الحصول على الخدمة المبسطة
  VoiceAssistantSimplifiedService get simplifiedService => _simplifiedService;

  /// الحصول على خدمة الأذونات
  VoiceAssistantPermissionsService get permissionsService =>
      _permissionsService;

  /// التحقق من حالة التهيئة
  bool get isInitialized => _isGloballyInitialized;

  /// التحقق من حالة التهيئة الجارية
  bool get isInitializing => _isInitializing;

  /// الحصول على عداد المراجع
  int get referenceCount => _referenceCount;

  /// طلب الأذونات المطلوبة
  Future<Map<String, dynamic>> requestPermissions() async {
    try {
      debugPrint('🔐 طلب أذونات المساعد الصوتي...');

      final result = await _permissionsService.requestAllPermissionsEnhanced();

      if (result['success'] == true) {
        debugPrint('✅ تم منح الأذونات بنجاح');
      } else {
        debugPrint('❌ فشل في منح بعض الأذونات');
        debugPrint('   الرسالة: ${result['message']}');
      }

      return result;
    } catch (e) {
      debugPrint('❌ خطأ في طلب الأذونات: $e');
      return {
        'success': false,
        'message': 'خطأ في طلب الأذونات: $e',
        'results': <String, bool>{},
      };
    }
  }

  /// فحص حالة الأذونات
  Future<Map<String, dynamic>> checkPermissions() async {
    try {
      debugPrint('🔍 فحص حالة الأذونات...');

      return await _permissionsService.checkAllPermissionsEnhanced();
    } catch (e) {
      debugPrint('❌ خطأ في فحص الأذونات: $e');
      return {
        'permissions': <String, bool>{},
        'details': <String, String>{},
        'summary': {
          'granted_count': 0,
          'total_count': 0,
          'percentage': 0,
          'all_granted': false,
          'essential_granted': false,
          'advanced_granted': false,
        },
        'error': e.toString(),
      };
    }
  }
}
