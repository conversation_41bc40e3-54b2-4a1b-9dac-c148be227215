// ignore_for_file: use_build_context_synchronously, unused_import, use_super_parameters

import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:http/http.dart' as http;
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:mobile_pos/constant.dart';

import 'package:nb_utils/nb_utils.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:mobile_pos/generated/l10n.dart' as lang;

class AppUpdateScreen extends StatefulWidget {
  const AppUpdateScreen({Key? key}) : super(key: key);

  @override
  State<AppUpdateScreen> createState() => _AppUpdateScreenState();
}

class _AppUpdateScreenState extends State<AppUpdateScreen> {
  bool _isLoading = true;
  bool _hasUpdate = false;
  String _currentVersion = '';
  String _latestVersion = '';
  String _updateUrl = '';
  String _releaseNotes = '';
  bool _isForceUpdate = false;

  @override
  void initState() {
    super.initState();
    _checkForUpdates();
  }

  Future<void> _checkForUpdates() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // التحقق من اتصال الإنترنت
      bool hasInternet = await InternetConnectionChecker().hasConnection;
      if (!hasInternet) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('لا يوجد اتصال بالإنترنت'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      // استخدام رقم الإصدار من constant.dart
      _currentVersion = appVersion;

      // الحصول على معلومات أحدث إصدار من Firebase
      // الوصول إلى عقدة AppUpdate تحت Admin Panel
      final updateRef = FirebaseDatabase.instance.ref('Admin Panel/AppUpdate');
      final snapshot = await updateRef.get();

      if (snapshot.exists) {
        final data = snapshot.value as Map<dynamic, dynamic>;
        _latestVersion = data['latestVersion'] ?? '';
        _updateUrl = data['updateUrl'] ?? '';
        _releaseNotes = data['releaseNotes'] ?? '';
        _isForceUpdate = data['forceUpdate'] ?? false;

        // مقارنة الإصدارات
        _hasUpdate = _compareVersions(_currentVersion, _latestVersion) < 0;
      }
    } catch (e) {
      debugPrint('خطأ في التحقق من التحديثات: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // دالة لمقارنة الإصدارات (مثل 1.0.0 و 1.0.1)
  int _compareVersions(String version1, String version2) {
    try {
      List<int> v1Parts = version1.split('.').map(int.parse).toList();
      List<int> v2Parts = version2.split('.').map(int.parse).toList();

      for (int i = 0; i < v1Parts.length && i < v2Parts.length; i++) {
        if (v1Parts[i] < v2Parts[i]) {
          return -1;
        } else if (v1Parts[i] > v2Parts[i]) {
          return 1;
        }
      }

      if (v1Parts.length < v2Parts.length) {
        return -1;
      } else if (v1Parts.length > v2Parts.length) {
        return 1;
      }

      return 0;
    } catch (e) {
      debugPrint('خطأ في مقارنة الإصدارات: $e');
      return 0;
    }
  }

  Future<void> _launchUpdateUrl() async {
    if (_updateUrl.isEmpty) return;

    final Uri url = Uri.parse(_updateUrl);
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يمكن فتح رابط التحديث'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        title: Text(
          'تحديث التطبيق',
          style: GoogleFonts.poppins(
            color: Colors.white,
          ),
        ),
        iconTheme: const IconThemeData(color: Colors.white),
        centerTitle: true,
        backgroundColor: kMainColor,
        elevation: 0.0,
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(30),
            topLeft: Radius.circular(30),
          ),
        ),
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const SizedBox(height: 20),
                    Icon(
                      _hasUpdate ? Icons.system_update : Icons.check_circle,
                      size: 80,
                      color: _hasUpdate ? kMainColor : Colors.green,
                    ),
                    const SizedBox(height: 20),
                    Text(
                      _hasUpdate ? 'يوجد تحديث جديد!' : 'التطبيق محدث بالفعل',
                      style: GoogleFonts.poppins(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: _hasUpdate ? kMainColor : Colors.green,
                      ),
                    ),
                    const SizedBox(height: 10),
                    Text(
                      'الإصدار الحالي: $_currentVersion',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        color: Colors.grey[700],
                      ),
                    ),
                    if (_hasUpdate) ...[
                      Text(
                        'الإصدار الجديد: $_latestVersion',
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          color: Colors.grey[700],
                        ),
                      ),
                      const SizedBox(height: 20),
                      Container(
                        padding: const EdgeInsets.all(15),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'ما الجديد:',
                              style: GoogleFonts.poppins(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 10),
                            Text(
                              _releaseNotes,
                              style: GoogleFonts.poppins(
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 30),
                      ElevatedButton(
                        onPressed: _launchUpdateUrl,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: kMainColor,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 40,
                            vertical: 15,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        child: Text(
                          'تحديث الآن',
                          style: GoogleFonts.poppins(
                            fontSize: 18,
                            color: Colors.white,
                          ),
                        ),
                      ),
                      if (_isForceUpdate) ...[
                        const SizedBox(height: 10),
                        Text(
                          'هذا التحديث إلزامي لمواصلة استخدام التطبيق',
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            color: Colors.red,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ],
                    const SizedBox(height: 20),
                    TextButton(
                      onPressed: _checkForUpdates,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(Icons.refresh, color: kMainColor),
                          const SizedBox(width: 8),
                          Text(
                            'التحقق من التحديثات',
                            style: GoogleFonts.poppins(
                              fontSize: 16,
                              color: kMainColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
      ),
    );
  }
}
