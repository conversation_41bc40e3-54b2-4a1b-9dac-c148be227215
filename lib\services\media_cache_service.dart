import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;
import 'package:crypto/crypto.dart';
import 'dart:convert';

/// خدمة تخزين الوسائط مؤقتًا
class MediaCacheService {
  static final MediaCacheService _instance = MediaCacheService._internal();
  factory MediaCacheService() => _instance;
  MediaCacheService._internal();

  // مسار مجلد التخزين المؤقت
  String? _cacheDir;

  // حد أقصى لحجم التخزين المؤقت (100 ميجابايت)
  static const int _maxCacheSize = 100 * 1024 * 1024; // 100 MB

  // خريطة لتخزين الملفات المحملة حاليًا في الذاكرة
  final Map<String, Uint8List> _memoryCache = {};

  /// تهيئة خدمة التخزين المؤقت
  Future<void> initialize() async {
    try {
      final dir = await getTemporaryDirectory();
      _cacheDir = '${dir.path}/media_cache';

      // إنشاء مجلد التخزين المؤقت إذا لم يكن موجودًا
      final cacheDir = Directory(_cacheDir!);
      if (!await cacheDir.exists()) {
        await cacheDir.create(recursive: true);
      }

      // تنظيف التخزين المؤقت عند بدء التشغيل
      _cleanCache();

      debugPrint('تم تهيئة خدمة تخزين الوسائط المؤقت بنجاح');
    } catch (e) {
      debugPrint('خطأ في تهيئة خدمة تخزين الوسائط المؤقت: $e');
    }
  }

  /// تنظيف التخزين المؤقت
  Future<void> _cleanCache() async {
    try {
      if (_cacheDir == null) return;

      final cacheDir = Directory(_cacheDir!);
      if (!await cacheDir.exists()) return;

      // حساب حجم التخزين المؤقت الحالي
      int totalSize = 0;
      final files = <FileSystemEntity>[];

      await for (final file in cacheDir.list()) {
        if (file is File) {
          final stat = await file.stat();
          totalSize += stat.size;
          files.add(file);
        }
      }

      // إذا كان حجم التخزين المؤقت أكبر من الحد الأقصى، قم بحذف الملفات الأقدم
      if (totalSize > _maxCacheSize) {
        // ترتيب الملفات حسب تاريخ التعديل
        files.sort((a, b) {
          final aTime = (a as File).lastModifiedSync();
          final bTime = (b as File).lastModifiedSync();
          return aTime.compareTo(bTime);
        });

        // حذف الملفات الأقدم حتى يصبح الحجم أقل من الحد الأقصى
        for (final file in files) {
          if (totalSize <= _maxCacheSize) break;

          if (file is File) {
            final size = await file.length();
            await file.delete();
            totalSize -= size;
          }
        }
      }

      debugPrint(
          'تم تنظيف التخزين المؤقت. الحجم الحالي: ${totalSize ~/ 1024} كيلوبايت');
    } catch (e) {
      debugPrint('خطأ في تنظيف التخزين المؤقت: $e');
    }
  }

  /// إنشاء اسم ملف من URL
  String _getFileNameFromUrl(String url) {
    // استخدام MD5 لإنشاء اسم ملف فريد من URL
    final bytes = utf8.encode(url);
    final digest = md5.convert(bytes);
    return digest.toString();
  }

  /// الحصول على ملف من التخزين المؤقت
  Future<File?> getCachedFile(String url) async {
    try {
      if (_cacheDir == null) return null;

      final fileName = _getFileNameFromUrl(url);
      final file = File('$_cacheDir/$fileName');

      if (await file.exists()) {
        return file;
      }

      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على الملف من التخزين المؤقت: $e');
      return null;
    }
  }

  /// الحصول على بيانات الصورة من التخزين المؤقت
  Future<Uint8List?> getCachedImageBytes(String url) async {
    try {
      // التحقق من وجود الصورة في ذاكرة التخزين المؤقت
      if (_memoryCache.containsKey(url)) {
        return _memoryCache[url];
      }

      // التحقق من وجود الصورة في التخزين المؤقت
      final cachedFile = await getCachedFile(url);
      if (cachedFile != null) {
        final bytes = await cachedFile.readAsBytes();

        // تخزين الصورة في ذاكرة التخزين المؤقت
        _memoryCache[url] = bytes;

        return bytes;
      }

      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على بيانات الصورة من التخزين المؤقت: $e');
      return null;
    }
  }

  /// تنزيل وتخزين ملف مؤقتًا
  Future<File?> downloadAndCacheFile(String url) async {
    try {
      if (_cacheDir == null) return null;

      // التحقق من وجود الملف في التخزين المؤقت
      final cachedFile = await getCachedFile(url);
      if (cachedFile != null) {
        return cachedFile;
      }

      // تنزيل الملف
      final response = await http.get(Uri.parse(url));
      if (response.statusCode != 200) {
        debugPrint('فشل في تنزيل الملف: ${response.statusCode}');
        return null;
      }

      // تخزين الملف مؤقتًا
      final fileName = _getFileNameFromUrl(url);
      final file = File('$_cacheDir/$fileName');
      await file.writeAsBytes(response.bodyBytes);

      // تنظيف التخزين المؤقت إذا لزم الأمر
      _cleanCache();

      return file;
    } catch (e) {
      debugPrint('خطأ في تنزيل وتخزين الملف مؤقتًا: $e');
      return null;
    }
  }

  /// تنزيل وتخزين صورة مؤقتًا
  Future<Uint8List?> downloadAndCacheImage(String url) async {
    try {
      // التحقق من وجود الصورة في ذاكرة التخزين المؤقت
      if (_memoryCache.containsKey(url)) {
        return _memoryCache[url];
      }

      // تنزيل وتخزين الصورة مؤقتًا
      final cachedFile = await downloadAndCacheFile(url);
      if (cachedFile != null) {
        final bytes = await cachedFile.readAsBytes();

        // تخزين الصورة في ذاكرة التخزين المؤقت
        _memoryCache[url] = bytes;

        return bytes;
      }

      return null;
    } catch (e) {
      debugPrint('خطأ في تنزيل وتخزين الصورة مؤقتًا: $e');
      return null;
    }
  }

  /// مسح ذاكرة التخزين المؤقت
  void clearMemoryCache() {
    _memoryCache.clear();
  }

  /// مسح التخزين المؤقت بالكامل
  Future<void> clearCache() async {
    try {
      if (_cacheDir == null) return;

      final cacheDir = Directory(_cacheDir!);
      if (await cacheDir.exists()) {
        await cacheDir.delete(recursive: true);
        await cacheDir.create(recursive: true);
      }

      // مسح ذاكرة التخزين المؤقت
      clearMemoryCache();

      debugPrint('تم مسح التخزين المؤقت بالكامل');
    } catch (e) {
      debugPrint('خطأ في مسح التخزين المؤقت: $e');
    }
  }
}
