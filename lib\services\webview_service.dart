// import 'dart:io';
// // import 'package:flutter/foundation.dart';
// import 'package:flutter/material.dart';

// /// خدمة للتعامل مع WebView بشكل متوافق مع جميع المنصات
// /// تتجنب استخدام flutter_inappwebview على ويندوز
// class WebViewService {
//   /// التحقق مما إذا كان WebView متاحًا على المنصة الحالية
//   static bool get isWebViewAvailable {
//     // تعطيل WebView على ويندوز لتجنب مشاكل البناء
//     if (Platform.isWindows) {
//       return false;
//     }

//     // متاح على المنصات الأخرى
//     return true;
//   }

//   /// فتح رابط في المتصفح الخارجي إذا كان WebView غير متاح
//   static void openUrlInExternalBrowser(String url) {
//     // يمكن إضافة كود هنا لفتح الرابط في المتصفح الخارجي
//     debugPrint('فتح الرابط في المتصفح الخارجي: $url');
//   }

//   /// عرض رسالة للمستخدم عندما يكون WebView غير متاح
//   static void showWebViewNotAvailableMessage(BuildContext context) {
//     ScaffoldMessenger.of(context).showSnackBar(
//       const SnackBar(
//         content: Text(
//             'عرض الويب غير متاح على هذه المنصة. يرجى استخدام متصفح خارجي.'),
//         duration: Duration(seconds: 3),
//       ),
//     );
//   }
// }
