import 'package:flutter/material.dart';
import '../voice_assistant_exports.dart';
import '../../../constant.dart';

/// شاشة المساعد الصوتي العائمة (تظهر فوق التطبيقات الأخرى)
class FloatingVoiceAssistantScreen extends StatefulWidget {
  const FloatingVoiceAssistantScreen({super.key});

  @override
  State<FloatingVoiceAssistantScreen> createState() =>
      _FloatingVoiceAssistantScreenState();
}

class _FloatingVoiceAssistantScreenState
    extends State<FloatingVoiceAssistantScreen> with TickerProviderStateMixin {
  final VoiceResponseEngine _responseEngine = VoiceResponseEngine();

  late AnimationController _pulseController;
  late AnimationController _waveController;
  late AnimationController _fadeController;

  AssistantState _currentState = AssistantState.idle;
  String _displayText = 'أهلاً، كيف يمكنني مساعدتك؟';
  String _statusText = 'اضغط للتحدث';

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeServices();
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _waveController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeController.forward();
  }

  Future<void> _initializeServices() async {
    await _responseEngine.initialize();
    await _responseEngine.speakWelcomeMessage();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _waveController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Container(
        decoration: BoxDecoration(
          gradient: _getGradientForState(_currentState),
          borderRadius: BorderRadius.circular(25),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 20,
              spreadRadius: 5,
            ),
          ],
        ),
        child: Column(
          children: [
            _buildHeader(),
            Expanded(child: _buildMainContent()),
            _buildFooter(),
          ],
        ),
      ),
    );
  }

  /// بناء رأس النافذة
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(15),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(25),
          topRight: Radius.circular(25),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.assistant,
            color: Colors.white,
            size: 24,
          ),
          const SizedBox(width: 10),
          const Expanded(
            child: Text(
              'المساعد الصوتي الذكي',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          IconButton(
            onPressed: _closeAssistant,
            icon: const Icon(Icons.close, color: Colors.white),
            iconSize: 20,
          ),
        ],
      ),
    );
  }

  /// بناء المحتوى الرئيسي
  Widget _buildMainContent() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildVoiceIndicator(),
          const SizedBox(height: 20),
          _buildDisplayText(),
          const SizedBox(height: 20),
          _buildStatusText(),
          if (_currentState == AssistantState.responding) ...[
            const SizedBox(height: 20),
            _buildActionButtons(),
          ],
        ],
      ),
    );
  }

  /// بناء مؤشر الصوت المتحرك
  Widget _buildVoiceIndicator() {
    return GestureDetector(
      onTap: _handleVoiceButtonTap,
      child: AnimatedBuilder(
        animation: _pulseController,
        builder: (context, child) {
          return Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white.withOpacity(0.2),
              border: Border.all(
                color: Colors.white.withOpacity(0.5),
                width: 2,
              ),
            ),
            child: Stack(
              alignment: Alignment.center,
              children: [
                // الدوائر المتحركة للاستماع
                if (_currentState == AssistantState.listening) ...[
                  _buildAnimatedCircle(0.8, 0),
                  _buildAnimatedCircle(0.6, 200),
                  _buildAnimatedCircle(0.4, 400),
                ],
                // أيقونة الحالة
                Icon(
                  _getIconForState(_currentState),
                  color: Colors.white,
                  size: 40,
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  /// بناء دائرة متحركة
  Widget _buildAnimatedCircle(double opacity, int delay) {
    return AnimatedBuilder(
      animation: _waveController,
      builder: (context, child) {
        return Transform.scale(
          scale: 1 + (_waveController.value * 0.3),
          child: Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: Colors.white
                    .withOpacity(opacity * (1 - _waveController.value)),
                width: 2,
              ),
            ),
          ),
        );
      },
    );
  }

  /// بناء نص العرض
  Widget _buildDisplayText() {
    return Container(
      padding: const EdgeInsets.all(15),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
      ),
      child: Text(
        _displayText,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 14,
          height: 1.5,
        ),
        textAlign: TextAlign.center,
        textDirection: TextDirection.rtl,
      ),
    );
  }

  /// بناء نص الحالة
  Widget _buildStatusText() {
    return Text(
      _statusText,
      style: TextStyle(
        color: Colors.white.withOpacity(0.8),
        fontSize: 12,
      ),
      textAlign: TextAlign.center,
    );
  }

  /// بناء أزرار الإجراءات
  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildActionButton(
          icon: Icons.refresh,
          label: 'إعادة',
          onTap: _repeatLastAction,
        ),
        _buildActionButton(
          icon: Icons.volume_up,
          label: 'إعادة النطق',
          onTap: _repeatSpeech,
        ),
        _buildActionButton(
          icon: Icons.close,
          label: 'إغلاق',
          onTap: _closeAssistant,
        ),
      ],
    );
  }

  /// بناء زر إجراء
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.2),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: Colors.white, size: 20),
            const SizedBox(height: 4),
            Text(
              label,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 10,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء تذييل النافذة
  Widget _buildFooter() {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(25),
          bottomRight: Radius.circular(25),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.info_outline,
            color: Colors.white.withOpacity(0.7),
            size: 16,
          ),
          const SizedBox(width: 5),
          Text(
            'يمكنني مساعدتك في البحث والاستعلامات',
            style: TextStyle(
              color: Colors.white.withOpacity(0.7),
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  /// الحصول على التدرج اللوني للحالة
  LinearGradient _getGradientForState(AssistantState state) {
    switch (state) {
      case AssistantState.idle:
        return const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF667eea), Color(0xFF764ba2)],
        );
      case AssistantState.listening:
        return const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFFff6b6b), Color(0xFFee5a24)],
        );
      case AssistantState.processing:
        return const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF3742fa), Color(0xFF2f3542)],
        );
      case AssistantState.responding:
        return const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF00b894), Color(0xFF00a085)],
        );
      case AssistantState.error:
        return const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFFe74c3c), Color(0xFFc0392b)],
        );
    }
  }

  /// الحصول على الأيقونة للحالة
  IconData _getIconForState(AssistantState state) {
    switch (state) {
      case AssistantState.idle:
        return Icons.mic;
      case AssistantState.listening:
        return Icons.mic;
      case AssistantState.processing:
        return Icons.hourglass_empty;
      case AssistantState.responding:
        return Icons.volume_up;
      case AssistantState.error:
        return Icons.error;
    }
  }

  /// معالجة ضغط زر الصوت
  Future<void> _handleVoiceButtonTap() async {
    if (_currentState == AssistantState.listening ||
        _currentState == AssistantState.processing) {
      return;
    }

    await _startListening();
  }

  /// بدء الاستماع
  Future<void> _startListening() async {
    setState(() {
      _currentState = AssistantState.listening;
      _displayText = 'أستمع إليك الآن...';
      _statusText = 'تحدث بوضوح';
    });

    _pulseController.repeat();
    _waveController.repeat();

    // محاكاة الاستماع (في التطبيق الحقيقي سيكون هناك تسجيل صوتي)
    await Future.delayed(const Duration(seconds: 3));

    // محاكاة أمر مستلم
    await _processCommand('ابحث عن عميل أحمد');
  }

  /// معالجة الأمر
  Future<void> _processCommand(String command) async {
    setState(() {
      _currentState = AssistantState.processing;
      _displayText = 'جاري معالجة طلبك...';
      _statusText = 'يرجى الانتظار';
    });

    _pulseController.stop();
    _waveController.stop();

    try {
      // معالجة الأمر باستخدام المساعد الذكي المحسن
      final result = await QuickAIAssistantService.processVoiceCommand(command);

      if (result['success']) {
        await _showResult(result['message']);
      } else {
        await _showError(result['message']);
      }
    } catch (e) {
      await _showError('حدث خطأ في معالجة طلبك');
    }
  }

  /// عرض النتيجة
  Future<void> _showResult(String result) async {
    setState(() {
      _currentState = AssistantState.responding;
      _displayText = result;
      _statusText = 'تم العثور على النتيجة';
    });

    // نطق النتيجة
    await _responseEngine.speak(result);

    // إخفاء النافذة بعد فترة
    Future.delayed(const Duration(seconds: 8), () {
      if (mounted) {
        _closeAssistant();
      }
    });
  }

  /// عرض خطأ
  Future<void> _showError(String error) async {
    setState(() {
      _currentState = AssistantState.error;
      _displayText = error;
      _statusText = 'حدث خطأ';
    });

    await _responseEngine.speakErrorMessage(error);

    // العودة للحالة الأولية بعد فترة
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        _resetToIdle();
      }
    });
  }

  /// العودة للحالة الأولية
  void _resetToIdle() {
    setState(() {
      _currentState = AssistantState.idle;
      _displayText = 'أهلاً، كيف يمكنني مساعدتك؟';
      _statusText = 'اضغط للتحدث';
    });
  }

  /// إعادة الإجراء الأخير
  Future<void> _repeatLastAction() async {
    await _startListening();
  }

  /// إعادة النطق
  Future<void> _repeatSpeech() async {
    if (_displayText.isNotEmpty) {
      await _responseEngine.speak(_displayText);
    }
  }

  /// إغلاق المساعد
  void _closeAssistant() {
    _responseEngine.speakGoodbyeMessage();
    Navigator.of(context).pop();
  }
}

/// حالات المساعد الصوتي
enum AssistantState {
  idle,
  listening,
  processing,
  responding,
  error,
}
