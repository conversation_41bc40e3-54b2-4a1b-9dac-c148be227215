// ignore_for_file: deprecated_member_use, prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:mobile_pos/Screens/subscription/payment_bank.dart';

import '../../model/subscription_plan_model.dart';

class PaymentPage extends StatefulWidget {
  const PaymentPage({
    super.key,
    required this.selectedPlan,
    required this.onError,
    required this.totalAmount,
  });

  final String totalAmount;
  final SubscriptionPlanModel selectedPlan;
  final Function onError;

  @override
  State<PaymentPage> createState() => _PaymentPageState();
}

class _PaymentPageState extends State<PaymentPage> {
  final primaryColor = const Color(0xFF27AE60);
  final kGreyTextColor = const Color(0xFF828282);
  String? whichPaymentIsChecked;

  @override
  void initState() {
    setState(() {
      whichPaymentIsChecked = 'Bank Transfer';
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        iconTheme: const IconThemeData(color: Colors.black),
        elevation: 0.0,
        title: const Text(
          'Payment Method',
          style: TextStyle(color: Colors.black),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.only(top: 20),
          child: Container(
            decoration: const BoxDecoration(
                borderRadius: BorderRadius.only(
                    topRight: Radius.circular(30),
                    topLeft: Radius.circular(30)),
                color: Colors.white),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 20),
              child: Column(
                children: [
                  Material(
                    elevation: 0.0,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10.0),
                        side: BorderSide(
                            color: whichPaymentIsChecked == 'Bank Transfer'
                                ? primaryColor
                                : kGreyTextColor.withOpacity(0.2))),
                    color: Colors.white,
                    child: CheckboxListTile(
                      value: whichPaymentIsChecked == 'Bank Transfer',
                      checkboxShape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(30.0)),
                      onChanged: (val) {
                        setState(() {
                          val == true
                              ? whichPaymentIsChecked = 'Bank Transfer'
                              : whichPaymentIsChecked = 'Bank Transfer';
                        });
                      },
                      contentPadding: const EdgeInsets.all(10.0),
                      activeColor: primaryColor,
                      title: const Text(
                        'Bank Transfer',
                        style: TextStyle(
                          color: Colors.black,
                        ),
                      ),
                      secondary: Image.asset(
                        'images/back_card.png',
                        height: 50.0,
                        width: 80.0,
                      ),
                    ),
                  ),
                  const SizedBox(height: 30.0),
                  Container(
                    height: 45,
                    width: MediaQuery.of(context).size.width,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.0),
                      color: primaryColor,
                    ),
                    child: TextButton(
                      onPressed: () {
                        if (whichPaymentIsChecked == 'Bank Transfer') {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => BankPaymentScreen(
                                subscriptionPlanModel: widget.selectedPlan,
                              ),
                            ),
                          );
                        }
                      },
                      child: Text(
                        'Continue',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16.0,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
