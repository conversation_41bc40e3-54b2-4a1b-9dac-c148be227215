import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:mobile_pos/Provider/add_to_cart.dart';
import 'package:mobile_pos/Provider/customer_provider.dart';
import 'package:mobile_pos/Screens/Customers/Model/customer_model.dart';
import 'package:mobile_pos/Screens/Customers/add_customer.dart';
import 'package:mobile_pos/Screens/Sales/add_sales.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/currency.dart';
import 'package:mobile_pos/generated/l10n.dart' as lang;
import 'package:nb_utils/nb_utils.dart';

class SalesContact extends StatefulWidget {
  const SalesContact({super.key, required this.isFromHome});
  final bool isFromHome;

  @override
  // ignore: library_private_types_in_public_api
  _SalesContactState createState() => _SalesContactState();
}

class _SalesContactState extends State<SalesContact>
    with AutomaticKeepAliveClientMixin {
  Color color = Colors.black26;
  String searchCustomer = '';
  bool _disposed = false;

  // تنسيق الأرقام
  final myFormat = NumberFormat('#,##0', 'en_US');

  // مزودات للتحميل التدريجي
  final paginationCountProvider = StateProvider<int>((ref) => 20);
  final isLoadingMoreProvider = StateProvider<bool>((ref) => false);

  // قائمة لتخزين متحكمات التمرير
  final List<ScrollController> _scrollControllers = [];

  @override
  bool get wantKeepAlive => true; // للحفاظ على حالة الشاشة عند التنقل

  @override
  void initState() {
    super.initState();
    _disposed = false;
    debugPrint('تم تهيئة شاشة SalesContact');
  }

  @override
  void dispose() {
    debugPrint('تنظيف موارد SalesContact...');
    _disposed = true;

    // تنظيف جميع متحكمات التمرير
    for (final controller in _scrollControllers) {
      try {
        if (controller.hasClients) {
          controller.dispose();
        }
      } catch (e) {
        debugPrint('خطأ في تنظيف متحكم التمرير: $e');
      }
    }
    _scrollControllers.clear();

    debugPrint('تم تنظيف موارد SalesContact بنجاح');
    super.dispose();
  }

  // دالة للتحقق من حالة الشاشة قبل تحديث الحالة
  void safeSetState(VoidCallback fn) {
    if (mounted && !_disposed) {
      setState(fn);
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(
        context); // استدعاء super.build مطلوب مع AutomaticKeepAliveClientMixin
    return Consumer(builder: (context, ref, __) {
      final cart = ref.watch(cartNotifier);
      return Scaffold(
        backgroundColor: kMainColor,
        appBar: AppBar(
          backgroundColor: kMainColor,
          title: Text(
            "اختيار العميل",
            style: GoogleFonts.poppins(
              color: Colors.white,
            ),
          ),
          centerTitle: true,
          iconTheme: const IconThemeData(color: Colors.white),
          elevation: 0.0,
        ),
        body: Container(
          alignment: Alignment.topCenter,
          decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                  topRight: Radius.circular(30), topLeft: Radius.circular(30))),
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(10.0),
              child: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.all(10.0),
                    child: AppTextField(
                      textFieldType: TextFieldType.NAME,
                      onChanged: (value) {
                        setState(() {
                          searchCustomer = value;
                        });
                      },
                      decoration: InputDecoration(
                        border: const OutlineInputBorder(),
                        floatingLabelBehavior: FloatingLabelBehavior.never,
                        labelText: lang.S.of(context).search,
                        hintText: lang.S.of(context).search,
                        prefixIcon: const Icon(Icons.search),
                      ),
                    ),
                  ),
                  Consumer(
                    builder: (_, ref, watch) {
                      final providerData = ref.watch(customerProvider);
                      return providerData.when(data: (customer) {
                        return Column(
                          children: [
                            // عميل الضيف
                            GestureDetector(
                              onTap: () {
                                CustomerModel guestModel = CustomerModel(
                                  'Guest',
                                  'Guest',
                                  'Guest',
                                  'https://cdn.pixabay.com/photo/2015/10/05/22/37/blank-profile-picture-973460__340.png',
                                  'Guest',
                                  'Guest',
                                  '0',
                                  '0',
                                  '',
                                  '',
                                  gst: '',
                                );
                                AddSalesScreen(customerModel: guestModel)
                                    .launch(context);
                                cart.clearCart();
                              },
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Row(
                                  children: [
                                    const SizedBox(
                                      height: 50.0,
                                      width: 50.0,
                                      child: CircleAvatar(
                                        foregroundColor: Colors.blue,
                                        backgroundColor: kMainColor,
                                        radius: 70.0,
                                        child: Text(
                                          'G',
                                          style: TextStyle(color: Colors.white),
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 10.0),
                                    Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          lang.S.of(context).walkInCustomer,
                                          style: GoogleFonts.poppins(
                                            color: Colors.black,
                                            fontSize: 15.0,
                                          ),
                                        ),
                                        Text(
                                          lang.S.of(context).guest,
                                          style: GoogleFonts.poppins(
                                            color: Colors.grey,
                                            fontSize: 15.0,
                                          ),
                                        ),
                                      ],
                                    ),
                                    const Spacer(),
                                    const SizedBox(width: 20),
                                    const Icon(
                                      Icons.arrow_forward_ios,
                                      color: kGreyTextColor,
                                    ),
                                  ],
                                ),
                              ),
                            ),

                            // خط فاصل
                            Padding(
                              padding:
                                  const EdgeInsets.only(left: 8.0, right: 10.0),
                              child: Divider(
                                height: 1,
                                thickness: 1.0,
                                color: kBorderColor.withAlpha(77),
                              ),
                            ),

                            // قائمة العملاء
                            Builder(
                              builder: (context) {
                                // فلترة العملاء حسب البحث ونوع العميل
                                final filteredCustomers = customer
                                    .where((c) =>
                                        (c.phoneNumber.toLowerCase().contains(
                                                searchCustomer.toLowerCase()) ||
                                            c.customerName
                                                .toLowerCase()
                                                .contains(searchCustomer
                                                    .toLowerCase())) &&
                                        !c.type.contains('Supplier'))
                                    .toList();

                                // تطبيق التحميل التدريجي
                                final paginationCount =
                                    ref.watch(paginationCountProvider);
                                final hasMore =
                                    filteredCustomers.length > paginationCount;
                                final displayedCustomers = filteredCustomers
                                    .take(paginationCount)
                                    .toList();

                                // إنشاء متحكم تمرير لمراقبة موضع التمرير
                                final scrollController = ScrollController();
                                _scrollControllers.add(scrollController);

                                // إضافة مستمع للتمرير لتحميل المزيد من العناصر عند الوصول للنهاية
                                WidgetsBinding.instance
                                    .addPostFrameCallback((_) {
                                  scrollController.addListener(() {
                                    if (hasMore &&
                                        scrollController.position.pixels >=
                                            scrollController
                                                    .position.maxScrollExtent -
                                                200) {
                                      // تحميل المزيد من العناصر عند الاقتراب من النهاية
                                      final currentCount =
                                          ref.read(paginationCountProvider);
                                      if (!ref.read(isLoadingMoreProvider)) {
                                        ref
                                            .read(
                                                isLoadingMoreProvider.notifier)
                                            .state = true;
                                        Future.delayed(
                                            const Duration(milliseconds: 500),
                                            () {
                                          ref
                                              .read(paginationCountProvider
                                                  .notifier)
                                              .state = currentCount + 20;
                                          ref
                                              .read(isLoadingMoreProvider
                                                  .notifier)
                                              .state = false;
                                        });
                                      }
                                    }
                                  });
                                });

                                return SizedBox(
                                  height:
                                      MediaQuery.of(context).size.height - 250,
                                  child: ListView.builder(
                                    padding: const EdgeInsets.only(
                                        left: 8.0, right: 10.0),
                                    shrinkWrap: true,
                                    controller: scrollController,
                                    physics:
                                        const AlwaysScrollableScrollPhysics(),
                                    itemCount: hasMore
                                        ? displayedCustomers.length + 1
                                        : displayedCustomers.length,
                                    itemBuilder: (_, index) {
                                      // إذا وصلنا للعنصر الأخير وهناك المزيد، نعرض مؤشر التحميل
                                      if (index == displayedCustomers.length &&
                                          hasMore) {
                                        return const Padding(
                                          padding: EdgeInsets.symmetric(
                                              vertical: 15),
                                          child: Center(
                                            child: SizedBox(
                                              width: 24,
                                              height: 24,
                                              child: CircularProgressIndicator(
                                                strokeWidth: 2.5,
                                              ),
                                            ),
                                          ),
                                        );
                                      }

                                      // تحديد لون نوع العميل
                                      final currentCustomer =
                                          displayedCustomers[index];

                                      if (currentCustomer.type == 'Retailer') {
                                        color = const Color(0xFF56da87);
                                      } else if (currentCustomer.type ==
                                          'Wholesaler') {
                                        color = const Color(0xFF25a9e0);
                                      } else if (currentCustomer.type ==
                                          'Dealer') {
                                        color = const Color(0xFFff5f00);
                                      } else if (currentCustomer.type ==
                                          'Supplier') {
                                        color = const Color(0xFFA569BD);
                                      } else {
                                        color = Colors.black26;
                                      }

                                      return Column(
                                        children: [
                                          GestureDetector(
                                            onTap: () {
                                              AddSalesScreen(
                                                      customerModel:
                                                          displayedCustomers[
                                                              index])
                                                  .launch(context);
                                              cart.clearCart();
                                            },
                                            child: Column(
                                              children: [
                                                ListTile(
                                                  leading: SizedBox(
                                                    height: 50.0,
                                                    width: 50.0,
                                                    child: CircleAvatar(
                                                      foregroundColor:
                                                          Colors.blue,
                                                      backgroundColor:
                                                          kMainColor,
                                                      radius: 70.0,
                                                      child: Text(
                                                        displayedCustomers[
                                                                    index]
                                                                .customerName
                                                                .isNotEmpty
                                                            ? displayedCustomers[
                                                                    index]
                                                                .customerName
                                                                .substring(0, 1)
                                                            : '',
                                                        style: const TextStyle(
                                                            color:
                                                                Colors.white),
                                                      ),
                                                    ),
                                                  ),
                                                  title: Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    children: [
                                                      Flexible(
                                                        child: Text(
                                                          displayedCustomers[
                                                                      index]
                                                                  .customerName
                                                                  .isNotEmpty
                                                              ? displayedCustomers[
                                                                      index]
                                                                  .customerName
                                                              : displayedCustomers[
                                                                      index]
                                                                  .phoneNumber,
                                                          style: GoogleFonts
                                                              .poppins(
                                                            color: Colors.black,
                                                            fontSize: 15.0,
                                                          ),
                                                          maxLines: 1,
                                                          overflow: TextOverflow
                                                              .ellipsis,
                                                        ),
                                                      ),
                                                      const SizedBox(
                                                          width: 10.0),
                                                      if (displayedCustomers[
                                                                      index]
                                                                  .dueAmount !=
                                                              '' &&
                                                          displayedCustomers[
                                                                      index]
                                                                  .dueAmount !=
                                                              '0')
                                                        Text(
                                                          '$currency ${myFormat.format(int.tryParse(displayedCustomers[index].dueAmount) ?? 0)}',
                                                          style: GoogleFonts
                                                              .poppins(
                                                            color: Colors.black,
                                                            fontSize: 15.0,
                                                          ),
                                                        ),
                                                    ],
                                                  ),
                                                  subtitle: Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    children: [
                                                      Text(
                                                        displayedCustomers[
                                                                index]
                                                            .type,
                                                        style:
                                                            GoogleFonts.poppins(
                                                          color: color,
                                                          fontSize: 15.0,
                                                        ),
                                                      ),
                                                      if (displayedCustomers[
                                                                      index]
                                                                  .dueAmount !=
                                                              '' &&
                                                          displayedCustomers[
                                                                      index]
                                                                  .dueAmount !=
                                                              '0')
                                                        Text(
                                                          lang.S
                                                              .of(context)
                                                              .due,
                                                          style: GoogleFonts
                                                              .poppins(
                                                            color: const Color(
                                                                0xFFff5f00),
                                                            fontSize: 15.0,
                                                          ),
                                                        ),
                                                    ],
                                                  ),
                                                  trailing: const Icon(
                                                    Icons.arrow_forward_ios,
                                                    color: kGreyTextColor,
                                                  ),
                                                  contentPadding:
                                                      EdgeInsets.zero,
                                                  horizontalTitleGap: 10,
                                                )
                                              ],
                                            ),
                                          ),
                                          Divider(
                                            height: 1,
                                            thickness: 1.0,
                                            color: kBorderColor.withAlpha(77),
                                          )
                                        ],
                                      );
                                    },
                                  ),
                                );
                              },
                            ),
                          ],
                        );
                      }, error: (e, stack) {
                        return Text(e.toString());
                      }, loading: () {
                        return const Center(child: CircularProgressIndicator());
                      });
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
        floatingActionButton: FloatingActionButton(
            child: const Icon(Icons.add),
            onPressed: () {
              const AddCustomer(
                fromWhere: 'sales',
              ).launch(context);
            }),
      );
    });
  }
}
