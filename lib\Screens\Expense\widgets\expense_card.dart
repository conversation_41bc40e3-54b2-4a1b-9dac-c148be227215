// بسم الله الرحمن الرحيم
// كارت عرض المصروف المحسن

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:mobile_pos/const_commas.dart';
import 'package:mobile_pos/currency.dart';
import 'package:mobile_pos/model/expense_model.dart';
import '../../../constant.dart';

/// كارت عرض المصروف المحسن
class ExpenseCard extends StatelessWidget {
  final ExpenseModel expense;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final bool showActions;

  const ExpenseCard({
    super.key,
    required this.expense,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.showActions = true,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // الصف الأول: العنوان والمبلغ
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      expense.expanseFor,
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: kMainColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      '$currency${myFormat.format(int.tryParse(expense.amount) ?? 0)}',
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        fontWeight: FontWeight.w700,
                        color: kMainColor,
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // الصف الثاني: الفئة والتاريخ
              Row(
                children: [
                  // أيقونة الفئة
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getCategoryIcon(expense.category),
                      size: 16,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(width: 8),
                  
                  // اسم الفئة
                  Expanded(
                    child: Text(
                      expense.category.isEmpty ? 'غير محدد' : expense.category,
                      style: GoogleFonts.cairo(
                        fontSize: 13,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ),
                  
                  // التاريخ
                  Row(
                    children: [
                      Icon(
                        Icons.calendar_today,
                        size: 14,
                        color: Colors.grey.shade500,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        DateFormat('dd/MM/yyyy').format(
                          DateTime.parse(expense.expenseDate),
                        ),
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // الصف الثالث: طريقة الدفع والملاحظات
              Row(
                children: [
                  // طريقة الدفع
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getPaymentTypeColor(expense.paymentType).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: _getPaymentTypeColor(expense.paymentType).withOpacity(0.3),
                      ),
                    ),
                    child: Text(
                      _getPaymentTypeText(expense.paymentType),
                      style: GoogleFonts.cairo(
                        fontSize: 11,
                        fontWeight: FontWeight.w500,
                        color: _getPaymentTypeColor(expense.paymentType),
                      ),
                    ),
                  ),
                  
                  const SizedBox(width: 8),
                  
                  // الملاحظات (إذا وجدت)
                  if (expense.note.isNotEmpty)
                    Expanded(
                      child: Row(
                        children: [
                          Icon(
                            Icons.note_alt_outlined,
                            size: 14,
                            color: Colors.grey.shade500,
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              expense.note,
                              style: GoogleFonts.cairo(
                                fontSize: 11,
                                color: Colors.grey.shade600,
                                fontStyle: FontStyle.italic,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
              
              // أزرار الإجراءات
              if (showActions) ...[
                const SizedBox(height: 12),
                const Divider(height: 1),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    if (onEdit != null)
                      TextButton.icon(
                        onPressed: onEdit,
                        icon: const Icon(
                          Icons.edit_outlined,
                          size: 16,
                        ),
                        label: Text(
                          'تعديل',
                          style: GoogleFonts.cairo(fontSize: 12),
                        ),
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.blue,
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                        ),
                      ),
                    
                    if (onDelete != null)
                      TextButton.icon(
                        onPressed: onDelete,
                        icon: const Icon(
                          Icons.delete_outline,
                          size: 16,
                        ),
                        label: Text(
                          'حذف',
                          style: GoogleFonts.cairo(fontSize: 12),
                        ),
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.red,
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                        ),
                      ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// الحصول على أيقونة الفئة
  IconData _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'رواتب':
        return Icons.people_outline;
      case 'إيجار':
        return Icons.home_outlined;
      case 'كهرباء':
        return Icons.electrical_services_outlined;
      case 'مياه':
        return Icons.water_drop_outlined;
      case 'هاتف وإنترنت':
        return Icons.phone_outlined;
      case 'وقود':
        return Icons.local_gas_station_outlined;
      case 'صيانة':
        return Icons.build_outlined;
      case 'مواد خام':
        return Icons.inventory_outlined;
      case 'تسويق':
        return Icons.campaign_outlined;
      case 'مصروفات إدارية':
        return Icons.business_outlined;
      case 'ضرائب':
        return Icons.receipt_long_outlined;
      case 'تأمين':
        return Icons.security_outlined;
      default:
        return Icons.category_outlined;
    }
  }

  /// الحصول على لون طريقة الدفع
  Color _getPaymentTypeColor(String paymentType) {
    switch (paymentType.toLowerCase()) {
      case 'cash':
        return Colors.green;
      case 'bank':
        return Colors.blue;
      case 'card':
        return Colors.purple;
      case 'mobile payment':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  /// الحصول على نص طريقة الدفع بالعربية
  String _getPaymentTypeText(String paymentType) {
    switch (paymentType.toLowerCase()) {
      case 'cash':
        return 'نقدي';
      case 'bank':
        return 'بنكي';
      case 'card':
        return 'بطاقة';
      case 'mobile payment':
        return 'دفع إلكتروني';
      case 'snacks':
        return 'وجبات خفيفة';
      default:
        return paymentType;
    }
  }
}
