import 'dart:convert';

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/models/hrm_models.dart';

class EmployeeRepository {
  final DatabaseReference _dbRef = FirebaseDatabase.instance.ref();

  Future<List<EmployeeModel>> getAllEmployees() async {
    List<EmployeeModel> employees = [];

    try {
      final userID = await getUserID();
      final snapshot = await FirebaseDatabase.instance
          .ref(userID)
          .child('Employee')
          .orderByKey()
          .get();

      if (snapshot.exists) {
        for (var element in snapshot.children) {
          var data = jsonDecode(jsonEncode(element.value));
          debugPrint('Employee data: $data'); // للتشخيص

          // تحويل تاريخ الانضمام بشكل صحيح
          DateTime joiningDate;
          try {
            if (data['joiningDate'] is int) {
              joiningDate =
                  DateTime.fromMillisecondsSinceEpoch(data['joiningDate']);
            } else if (data['joiningDate'] is String) {
              joiningDate =
                  DateTime.tryParse(data['joiningDate']) ?? DateTime.now();
            } else {
              joiningDate = DateTime.now();
            }
          } catch (e) {
            debugPrint('Error parsing joiningDate: $e');
            joiningDate = DateTime.now();
          }

          var employee = EmployeeModel(
            id: element.key ?? '',
            name: data['name'] ?? '',
            email: data['email'] ?? '',
            phone: data['phoneNumber'] ?? '', // استخدام phoneNumber من Firebase
            address: data['address'] ?? '',
            designation: data['designation'] ?? '',
            department: data['department'] ?? '',
            salary: (data['salary'] is num) ? data['salary'].toDouble() : 0.0,
            joiningDate: joiningDate,
            imageUrl: data['imageUrl'] ?? '',
            isActive: data['isActive'] ?? true,
          );
          employees.add(employee);
          debugPrint('Added employee: ${employee.name}'); // للتشخيص
        }
      } else {
        debugPrint('No employees found in database'); // للتشخيص
      }
    } catch (e) {
      debugPrint('Error fetching employees: $e'); // للتشخيص
    }

    return employees;
  }

  // Method to save employee
  Future<bool> addEmployee({required EmployeeModel employee}) async {
    try {
      EasyLoading.show(status: 'Loading...', dismissOnTap: false);

      final userID = await getUserID();
      final DatabaseReference employeeRef =
          _dbRef.child(userID).child('Employee').child(employee.id);

      await employeeRef.set({
        'name': employee.name,
        'phoneNumber': employee.phone,
        'email': employee.email,
        'address': employee.address,
        'designation': employee.designation,
        'department': employee.department,
        'salary': employee.salary,
        'joiningDate': employee.joiningDate.millisecondsSinceEpoch,
        'imageUrl': employee.imageUrl,
        'isActive': employee.isActive,
      });

      EasyLoading.showSuccess('Added Successfully',
          duration: const Duration(milliseconds: 500));
      return true;
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception('Failed to add employee: ${e.toString()}');
    }
  }

  Future<bool> updateEmployee({required EmployeeModel employee}) async {
    try {
      EasyLoading.show(status: 'Loading...', dismissOnTap: false);

      final userID = await getUserID();
      final DatabaseReference employeeRef =
          _dbRef.child(userID).child('Employee').child(employee.id);

      await employeeRef.update({
        'name': employee.name,
        'phoneNumber': employee.phone,
        'email': employee.email,
        'address': employee.address,
        'designation': employee.designation,
        'department': employee.department,
        'salary': employee.salary,
        'joiningDate': employee.joiningDate.millisecondsSinceEpoch,
        'imageUrl': employee.imageUrl,
        'isActive': employee.isActive,
      });

      EasyLoading.showSuccess('Updated Successfully');
      return true;
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception('Failed to Updated employee: ${e.toString()}');
    }
  }

  Future<bool> deleteEmployee({required String id}) async {
    try {
      EasyLoading.show(status: 'Deleting...');

      final String userId = await getUserID();
      final DatabaseReference employeeRef =
          _dbRef.child(userId).child('Employee').child(id);

      await employeeRef.remove();

      EasyLoading.showSuccess('Deleted Successfully');
      return true;
    } catch (e) {
      EasyLoading.showError('Error: ${e.toString()}');
      return false;
    }
  }
}
