import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:flutter_tts/flutter_tts.dart';
import 'voice_assistant_core_service.dart';
import 'voice_assistant_permissions_service.dart';
import '../utils/voice_assistant_utils.dart';

/// خدمة مبسطة للمساعد الصوتي - تستبدل الخدمات المعقدة
class VoiceAssistantSimplifiedService {
  static final VoiceAssistantSimplifiedService _instance =
      VoiceAssistantSimplifiedService._internal();
  factory VoiceAssistantSimplifiedService() => _instance;
  VoiceAssistantSimplifiedService._internal();

  // الخدمات الأساسية
  final VoiceAssistantCoreService _coreService = VoiceAssistantCoreService();
  final VoiceAssistantPermissionsService _permissionsService =
      VoiceAssistantPermissionsService();

  // خدمات الصوت
  stt.SpeechToText? _speechToText;
  final FlutterTts _flutterTts = FlutterTts();

  // حالة الخدمة
  bool _isInitialized = false;
  bool _isListening = false;
  String _lastRecognizedText = '';

  // مستمعات الأحداث
  final StreamController<VoiceEvent> _eventController =
      StreamController<VoiceEvent>.broadcast();

  /// تهيئة الخدمة المبسطة
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      VoiceAssistantUtils.logEvent('بدء تهيئة الخدمة المبسطة');

      // التحقق من الأذونات
      if (!await _permissionsService.canRunVoiceAssistant()) {
        VoiceAssistantUtils.logEvent('الأذونات غير متاحة');
        return false;
      }

      // تهيئة الخدمة الأساسية
      if (!await _coreService.initialize()) {
        VoiceAssistantUtils.logEvent('فشل في تهيئة الخدمة الأساسية');
        return false;
      }

      // تهيئة خدمات الصوت
      await _initializeSpeechServices();

      _isInitialized = true;
      VoiceAssistantUtils.logEvent('تم تهيئة الخدمة المبسطة بنجاح');
      _eventController.add(VoiceEvent.initialized());

      return true;
    } catch (e) {
      VoiceAssistantUtils.logEvent('خطأ في تهيئة الخدمة المبسطة',
          data: {'error': e.toString()});
      return false;
    }
  }

  /// تهيئة الخدمة المبسطة بدون إعادة تهيئة الخدمة الأساسية
  Future<bool> initializeWithoutCore() async {
    if (_isInitialized) return true;

    try {
      VoiceAssistantUtils.logEvent(
          'بدء تهيئة الخدمة المبسطة (بدون الخدمة الأساسية)');

      // التحقق من الأذونات
      if (!await _permissionsService.canRunVoiceAssistant()) {
        VoiceAssistantUtils.logEvent('الأذونات غير متاحة');
        return false;
      }

      // تهيئة خدمات الصوت فقط
      await _initializeSpeechServices();

      // الحصول على Speech-to-Text من الخدمة الأساسية
      _speechToText = _coreService.speechToText;

      _isInitialized = true;
      VoiceAssistantUtils.logEvent(
          'تم تهيئة الخدمة المبسطة بنجاح (بدون الخدمة الأساسية)');
      _eventController.add(VoiceEvent.initialized());

      return true;
    } catch (e) {
      VoiceAssistantUtils.logEvent('خطأ في تهيئة الخدمة المبسطة',
          data: {'error': e.toString()});
      return false;
    }
  }

  /// تهيئة خدمات الصوت
  Future<void> _initializeSpeechServices() async {
    try {
      // تهيئة تحويل النص إلى كلام
      await _flutterTts.setLanguage('ar-SA');
      await _flutterTts.setSpeechRate(0.5);
      await _flutterTts.setVolume(0.8);

      VoiceAssistantUtils.logEvent('تم تهيئة خدمات الصوت');
    } catch (e) {
      VoiceAssistantUtils.logEvent('خطأ في تهيئة خدمات الصوت',
          data: {'error': e.toString()});
      rethrow;
    }
  }

  /// بدء الاستماع لكلمة التفعيل
  Future<bool> startListening() async {
    if (!_isInitialized || _isListening) return false;

    try {
      VoiceAssistantUtils.logEvent('بدء الاستماع لكلمة التفعيل');

      if (_speechToText == null) {
        VoiceAssistantUtils.logEvent('Speech-to-Text غير متاح');
        return false;
      }

      final success = await VoiceAssistantUtils.withTimeout(
        () => _speechToText!.listen(
          onResult: _onSpeechResult,
          listenFor: const Duration(seconds: 30),
          pauseFor: const Duration(seconds: 3),
          localeId: 'ar-SA',
          listenMode: stt.ListenMode.confirmation,
        ),
        operationName: 'بدء الاستماع',
      );

      if (success != null) {
        _isListening = true;
        _eventController.add(VoiceEvent.listeningStarted());
        return true;
      }

      return false;
    } catch (e) {
      VoiceAssistantUtils.logEvent('خطأ في بدء الاستماع',
          data: {'error': e.toString()});
      return false;
    }
  }

  /// إيقاف الاستماع
  Future<void> stopListening() async {
    if (!_isListening) return;

    try {
      await _speechToText?.stop();
      _isListening = false;
      _eventController.add(VoiceEvent.listeningStopped());
      VoiceAssistantUtils.logEvent('تم إيقاف الاستماع');
    } catch (e) {
      VoiceAssistantUtils.logEvent('خطأ في إيقاف الاستماع',
          data: {'error': e.toString()});
    }
  }

  /// معالجة نتائج التعرف على الكلام
  void _onSpeechResult(result) {
    final recognizedText = result.recognizedWords.toLowerCase();
    _lastRecognizedText = recognizedText;

    VoiceAssistantUtils.logEvent('تم التعرف على الكلام',
        data: {'text': recognizedText});

    // التحقق من كلمات التفعيل
    if (_isWakeWord(recognizedText)) {
      VoiceAssistantUtils.logEvent('تم اكتشاف كلمة التفعيل');
      _eventController.add(VoiceEvent.wakeWordDetected(recognizedText));
      _handleWakeWordDetected();
    }
  }

  /// التحقق من كلمة التفعيل
  bool _isWakeWord(String text) {
    final wakeWords = [
      'hey app',
      'هاي آب',
      'هي آب',
      'مساعد',
      'مساعدي',
    ];

    for (final wakeWord in wakeWords) {
      if (text.contains(wakeWord)) {
        return true;
      }
    }

    return false;
  }

  /// معالجة اكتشاف كلمة التفعيل
  void _handleWakeWordDetected() async {
    try {
      // إيقاف الاستماع مؤقتاً
      await stopListening();

      // رد صوتي بسيط
      if (_coreService.enableLocalProcessing) {
        await _speak('نعم، كيف يمكنني مساعدتك؟');
      }

      // إعادة بدء الاستماع للأوامر
      await Future.delayed(const Duration(seconds: 2));
      await startListening();
    } catch (e) {
      VoiceAssistantUtils.logEvent('خطأ في معالجة كلمة التفعيل',
          data: {'error': e.toString()});
    }
  }

  /// تحويل النص إلى كلام
  Future<void> _speak(String text) async {
    try {
      if (!_coreService.enableVoiceRecording) return;

      await VoiceAssistantUtils.withTimeout(
        () => _flutterTts.speak(text),
        operationName: 'تحويل النص إلى كلام',
      );

      VoiceAssistantUtils.logEvent('تم نطق النص', data: {'text': text});
    } catch (e) {
      VoiceAssistantUtils.logEvent('خطأ في نطق النص',
          data: {'error': e.toString()});
    }
  }

  /// تفعيل/إلغاء تفعيل الخدمة
  Future<void> setEnabled(bool enabled) async {
    await _coreService.setEnabled(enabled);

    if (enabled && !_isListening) {
      await startListening();
    } else if (!enabled && _isListening) {
      await stopListening();
    }
  }

  /// الحصول على حالة الخدمة
  Map<String, dynamic> getStatus() {
    final coreStatus = _coreService.getStatus();

    return {
      ...coreStatus,
      'isListening': _isListening,
      'lastRecognizedText': _lastRecognizedText,
      'speechToTextAvailable': _speechToText?.isAvailable ?? false,
      'hasPermission': _speechToText?.hasPermission ?? false,
    };
  }

  /// تنظيف الموارد
  Future<void> dispose() async {
    await stopListening();
    await _eventController.close();
    await _coreService.dispose();
    _isInitialized = false;
    VoiceAssistantUtils.logEvent('تم تنظيف موارد الخدمة المبسطة');
  }

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isListening => _isListening;
  bool get isEnabled => _coreService.isEnabled;
  String get lastRecognizedText => _lastRecognizedText;
  Stream<VoiceEvent> get eventStream => _eventController.stream;
}

/// فئة أحداث المساعد الصوتي
class VoiceEvent {
  final VoiceEventType type;
  final String? data;
  final DateTime timestamp;

  VoiceEvent._(this.type, this.data) : timestamp = DateTime.now();

  factory VoiceEvent.initialized() =>
      VoiceEvent._(VoiceEventType.initialized, null);
  factory VoiceEvent.listeningStarted() =>
      VoiceEvent._(VoiceEventType.listeningStarted, null);
  factory VoiceEvent.listeningStopped() =>
      VoiceEvent._(VoiceEventType.listeningStopped, null);
  factory VoiceEvent.wakeWordDetected(String text) =>
      VoiceEvent._(VoiceEventType.wakeWordDetected, text);
  factory VoiceEvent.error(String error) =>
      VoiceEvent._(VoiceEventType.error, error);
}

/// أنواع أحداث المساعد الصوتي
enum VoiceEventType {
  initialized,
  listeningStarted,
  listeningStopped,
  wakeWordDetected,
  error,
}
