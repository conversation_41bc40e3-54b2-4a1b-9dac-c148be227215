import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:mobile_pos/Provider/profile_provider.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/model/transition_model.dart';
import 'package:mobile_pos/models/daily_sales_report_model.dart';
import 'package:mobile_pos/providers/daily_report_printer_provider.dart';
import 'package:mobile_pos/providers/daily_sales_report_provider.dart';
import 'package:nb_utils/nb_utils.dart';

class DailySalesReportScreen extends ConsumerStatefulWidget {
  const DailySalesReportScreen({super.key});

  @override
  ConsumerState<DailySalesReportScreen> createState() =>
      _DailySalesReportScreenState();
}

class _DailySalesReportScreenState
    extends ConsumerState<DailySalesReportScreen> {
  DateTime selectedDate = DateTime.now();
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    // تحميل التقرير للتاريخ الحالي
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(selectedReportDateProvider.notifier).state = selectedDate;
    });
  }

  @override
  Widget build(BuildContext context) {
    final reportAsync = ref.watch(dailySalesReportProvider);
    final printerProvider = ref.watch(dailyReportPrinterProvider);
    final personalInfo = ref.watch(profileDetailsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'تقرير المبيعات اليومي',
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: kMainColor,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          // زر الطباعة
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: reportAsync.hasValue &&
                    reportAsync.value!.salesTransactions.isNotEmpty
                ? () => _printReport(reportAsync.value!)
                : null,
          ),
          // زر إعادة التحميل
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => _refreshReport(),
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط اختيار التاريخ
          _buildDateSelector(),

          // محتوى التقرير
          Expanded(
            child: reportAsync.when(
              data: (report) => _buildReportContent(report),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => _buildErrorWidget(error.toString()),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء شريط اختيار التاريخ
  Widget _buildDateSelector() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.grey[100],
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: _selectDate,
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  border: Border.all(color: kMainColor),
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.white,
                ),
                child: Row(
                  children: [
                    const Icon(Icons.calendar_today, color: kMainColor),
                    const SizedBox(width: 8),
                    Text(
                      DateFormat('yyyy/MM/dd', 'ar').format(selectedDate),
                      style: const TextStyle(fontSize: 16),
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          ElevatedButton(
            onPressed: _refreshReport,
            style: ElevatedButton.styleFrom(
              backgroundColor: kMainColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('تحديث'),
          ),
        ],
      ),
    );
  }

  /// بناء محتوى التقرير
  Widget _buildReportContent(DailySalesReportModel report) {
    if (report.salesTransactions.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.receipt_long, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'لا توجد مبيعات في هذا التاريخ',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // ملخص اليوم
          _buildSummaryCard(report.summary),
          const SizedBox(height: 16),

          // ملخص طرق الدفع
          _buildPaymentSummaryCard(report.paymentSummary),
          const SizedBox(height: 16),

          // قائمة الفواتير
          _buildInvoicesList(report.salesTransactions),
          const SizedBox(height: 16),

          // ملخص الأصناف
          if (report.productsSummary.isNotEmpty)
            _buildProductsSummary(report.productsSummary),
        ],
      ),
    );
  }

  /// بناء بطاقة الملخص
  Widget _buildSummaryCard(DailySalesSummary summary) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'ملخص اليوم',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            _buildSummaryRow('عدد الفواتير', '${summary.totalInvoices}'),
            _buildSummaryRow(
                'إجمالي المبيعات', _formatCurrency(summary.totalSales)),
            _buildSummaryRow(
                'إجمالي الضرائب', _formatCurrency(summary.totalVat)),
            _buildSummaryRow(
                'إجمالي الخصم', _formatCurrency(summary.totalDiscount)),
            const Divider(),
            _buildSummaryRow('صافي المبيعات', _formatCurrency(summary.netSales),
                isTotal: true),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة ملخص طرق الدفع
  Widget _buildPaymentSummaryCard(PaymentMethodSummary paymentSummary) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'ملخص طرق الدفع',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: Column(
                    children: [
                      const Text('مبيعات نقدية',
                          style: TextStyle(fontWeight: FontWeight.bold)),
                      Text(_formatCurrency(paymentSummary.cashSales)),
                      Text('${paymentSummary.cashInvoicesCount} فاتورة'),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    children: [
                      const Text('مبيعات آجلة',
                          style: TextStyle(fontWeight: FontWeight.bold)),
                      Text(_formatCurrency(paymentSummary.creditSales)),
                      Text('${paymentSummary.creditInvoicesCount} فاتورة'),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قائمة الفواتير
  Widget _buildInvoicesList(List<SalesTransitionModel> transactions) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تفاصيل الفواتير (${transactions.length})',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: transactions.length,
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                final transaction = transactions[index];
                final paymentType =
                    (transaction.dueAmount ?? 0) == 0 ? 'نقدي' : 'آجل';
                final time = DateFormat('HH:mm')
                    .format(DateTime.parse(transaction.purchaseDate));

                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor: (transaction.dueAmount ?? 0) == 0
                        ? Colors.green
                        : Colors.orange,
                    child: Text('${index + 1}'),
                  ),
                  title: Text('فاتورة رقم: ${transaction.invoiceNumber}'),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('العميل: ${transaction.customerName}'),
                      Text('الوقت: $time - طريقة الدفع: $paymentType'),
                    ],
                  ),
                  trailing: Text(
                    _formatCurrency(transaction.totalAmount ?? 0),
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  /// بناء ملخص الأصناف
  Widget _buildProductsSummary(List<ProductSalesSummary> productsSummary) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'أهم الأصناف المباعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: productsSummary.take(10).length,
              itemBuilder: (context, index) {
                final product = productsSummary[index];
                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor: kMainColor,
                    child: Text('${index + 1}'),
                  ),
                  title: Text(product.productName),
                  subtitle: Text('الكمية: ${product.totalQuantity}'),
                  trailing: Text(
                    _formatCurrency(product.totalAmount),
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  /// بناء صف الملخص
  Widget _buildSummaryRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              fontSize: isTotal ? 16 : 14,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              fontSize: isTotal ? 16 : 14,
              color: isTotal ? kMainColor : null,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء واجهة الخطأ
  Widget _buildErrorWidget(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            'حدث خطأ في تحميل التقرير',
            style: const TextStyle(fontSize: 18),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: const TextStyle(color: Colors.grey),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _refreshReport,
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  /// اختيار التاريخ
  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      locale: const Locale('ar'),
    );

    if (picked != null && picked != selectedDate) {
      setState(() {
        selectedDate = picked;
      });
      ref.read(selectedReportDateProvider.notifier).state = picked;
    }
  }

  /// إعادة تحميل التقرير
  void _refreshReport() {
    ref.invalidate(dailySalesReportProvider);
  }

  /// طباعة التقرير
  Future<void> _printReport(DailySalesReportModel report) async {
    final personalInfo = ref.read(profileDetailsProvider);

    personalInfo.when(
      data: (data) async {
        final printer = ref.read(dailyReportPrinterProvider);

        // التحقق من الاتصال
        await printer.checkConnection();

        if (!printer.isConnected) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('الطابعة غير متصلة. يرجى التأكد من الاتصال.'),
                backgroundColor: Colors.red,
              ),
            );
          }
          return;
        }

        // طباعة التقرير
        final success = await printer.printDailyReport(
          report: report,
          companyName: data.companyName ?? 'شركة غير محددة',
          companyPhone: data.phoneNumber ?? '',
          companyAddress: data.countryName ?? '',
        );

        if (mounted) {
          if (success) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم طباعة التقرير بنجاح'),
                backgroundColor: Colors.green,
              ),
            );
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(printer.lastError ?? 'فشل في طباعة التقرير'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      },
      loading: () {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('جاري تحميل بيانات الشركة...')),
        );
      },
      error: (error, stack) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('خطأ في تحميل بيانات الشركة'),
            backgroundColor: Colors.red,
          ),
        );
      },
    );
  }

  /// تنسيق العملة
  String _formatCurrency(double amount) {
    final formatter = NumberFormat('#,##0.00', 'ar');
    return '${formatter.format(amount)} جنيه';
  }
}
