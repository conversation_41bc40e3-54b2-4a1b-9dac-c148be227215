import 'dart:developer';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/const_commas.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/currency.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'financial_report_provider.dart';
import 'financial_report_widgets.dart';

/// شاشة التقرير المالي المنظمة
class FinancialReportScreenOrganized extends ConsumerStatefulWidget {
  const FinancialReportScreenOrganized({super.key});

  @override
  ConsumerState<FinancialReportScreenOrganized> createState() =>
      _FinancialReportScreenOrganizedState();
}

class _FinancialReportScreenOrganizedState
    extends ConsumerState<FinancialReportScreenOrganized> {
  @override
  void initState() {
    super.initState();
    // تهيئة الشاشة
    _checkUserData();
  }

  // التحقق من بيانات المستخدم
  Future<void> _checkUserData() async {
    try {
      // التحقق من قيمة constUserId
      log('التحقق من بيانات المستخدم...');
      log('معرف المستخدم الحالي: $constUserId');

      // إذا كان معرف المستخدم فارغًا، حاول الحصول عليه من FirebaseAuth
      if (constUserId.isEmpty) {
        final currentUser = FirebaseAuth.instance.currentUser;
        if (currentUser != null) {
          log('تم العثور على المستخدم الحالي: ${currentUser.uid}');

          // تحديث معرف المستخدم في SharedPreferences
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString('userId', currentUser.uid);

          // تحديث قيمة constUserId
          setState(() {
            constUserId = currentUser.uid;
          });

          log('تم تحديث معرف المستخدم: $constUserId');

          // تحديث التقرير
          await Future.delayed(const Duration(
              milliseconds: 500)); // انتظار لضمان تحديث constUserId
          final _ = await ref.refresh(
              financialReportProvider(ref.read(selectedFinancialMonthProvider))
                  .future);
        } else {
          log('تحذير: لا يوجد مستخدم مسجل الدخول!');
        }
      } else {
        log('معرف المستخدم موجود بالفعل: $constUserId');
        // تحديث التقرير للتأكد من استخدام أحدث البيانات
        final _ = await ref.refresh(
            financialReportProvider(ref.read(selectedFinancialMonthProvider))
                .future);
      }
    } catch (e) {
      log('خطأ في التحقق من بيانات المستخدم: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    // الشهر المختار
    final selectedMonth = ref.watch(selectedFinancialMonthProvider);

    // بيانات التقرير المالي
    final reportData = ref.watch(financialReportProvider(selectedMonth));

    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        title: Text(
          'التقرير المالي',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        backgroundColor: kMainColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Container(
        alignment: Alignment.topCenter,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(30),
            topLeft: Radius.circular(30),
          ),
        ),
        child: Column(
          children: [
            // اختيار الشهر
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Text(
                    'اختر الشهر:',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<DateTime>(
                          value: selectedMonth,
                          isExpanded: true,
                          items: buildMonthDropdownItems(),
                          onChanged: (value) {
                            if (value != null) {
                              // تحديث القيمة المختارة
                              ref
                                  .read(selectedFinancialMonthProvider.notifier)
                                  .state = DateTime(value.year, value.month, 1);
                            }
                          },
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // بيانات التقرير
            Expanded(
              child: reportData.when(
                data: (report) {
                  return SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // ملخص سريع
                        Container(
                          width: double.infinity,
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              colors: [kMainColor, Color(0xFF7986CB)],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            borderRadius: BorderRadius.circular(15),
                            boxShadow: const [
                              BoxShadow(
                                color: Color.fromRGBO(128, 128, 128, 0.3),
                                spreadRadius: 2,
                                blurRadius: 5,
                                offset: Offset(0, 3),
                              ),
                            ],
                          ),
                          padding: const EdgeInsets.all(20),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'ملخص الشهر',
                                style: GoogleFonts.cairo(
                                  color: Colors.white,
                                  fontSize: 22,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 15),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  buildSummaryItem(
                                    context: context,
                                    title: 'صافي الربح',
                                    value:
                                        '${myFormat.format(report.netProfit)} $currency',
                                    icon: Icons.trending_up,
                                    color: report.netProfit >= 0
                                        ? Colors.green.shade100
                                        : Colors.red.shade100,
                                    textColor: report.netProfit >= 0
                                        ? Colors.green.shade800
                                        : Colors.red.shade800,
                                  ),
                                  buildSummaryItem(
                                    context: context,
                                    title: 'المبيعات',
                                    value:
                                        '${myFormat.format(report.totalSales)} $currency',
                                    icon: Icons.shopping_cart,
                                    color: Colors.blue.shade100,
                                    textColor: Colors.blue.shade800,
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 20),

                        // بطاقة ملخص الإيرادات والتكاليف
                        buildFinancialCard(
                          title: 'الإيرادات والتكاليف',
                          icon: Icons.monetization_on,
                          iconColor: kMainColor,
                          children: [
                            buildFinancialItem(
                              title: 'إجمالي المبيعات',
                              value: report.totalSales,
                              color: Colors.green.shade700,
                              icon: Icons.arrow_upward,
                            ),
                            const SizedBox(height: 12),
                            buildFinancialItem(
                              title: 'تكلفة المبيعات',
                              value: report.costOfSales,
                              color: Colors.red.shade700,
                              icon: Icons.arrow_downward,
                            ),
                            const Padding(
                              padding: EdgeInsets.symmetric(vertical: 15),
                              child: Divider(height: 1, thickness: 1),
                            ),
                            buildFinancialItem(
                              title: 'مجمل الربح',
                              value: report.grossProfit,
                              color: Colors.blue.shade700,
                              isBold: true,
                              icon: Icons.calculate,
                            ),
                          ],
                        ),

                        const SizedBox(height: 16),

                        // بطاقة المصروفات
                        buildFinancialCard(
                          title: 'المصروفات',
                          icon: Icons.money_off,
                          iconColor: Colors.red,
                          children: [
                            buildFinancialItem(
                              title: 'إجمالي المصروفات',
                              value: report.totalExpenses,
                              color: Colors.red.shade700,
                              icon: Icons.shopping_bag,
                            ),
                            const SizedBox(height: 12),
                            buildFinancialItem(
                              title: 'إجمالي المرتبات',
                              value: report.totalSalaries,
                              color: Colors.red.shade700,
                              icon: Icons.people,
                            ),
                          ],
                        ),

                        const SizedBox(height: 16),

                        // بطاقة صافي الربح
                        buildFinancialCard(
                          title: 'صافي الربح',
                          icon: report.netProfit >= 0
                              ? Icons.trending_up
                              : Icons.trending_down,
                          iconColor:
                              report.netProfit >= 0 ? Colors.green : Colors.red,
                          children: [
                            buildFinancialItem(
                              title: 'صافي الربح',
                              value: report.netProfit,
                              color: report.netProfit >= 0
                                  ? Colors.green.shade700
                                  : Colors.red.shade700,
                              isBold: true,
                              icon: Icons.account_balance,
                            ),
                            const SizedBox(height: 12),
                            buildFinancialItem(
                              title: 'نسبة الربح',
                              value: report.profitMargin,
                              isPercentage: true,
                              color: report.profitMargin >= 0
                                  ? Colors.green.shade700
                                  : Colors.red.shade700,
                              icon: Icons.percent,
                            ),
                          ],
                        ),

                        const SizedBox(height: 16),

                        // بطاقة الموقف المالي
                        buildFinancialCard(
                          title: 'الموقف المالي',
                          icon: Icons.account_balance_wallet,
                          iconColor: Colors.blue,
                          children: [
                            buildFinancialItem(
                              title: 'إجمالي المشتريات',
                              value: report.totalPurchases,
                              color: Colors.orange.shade700,
                              icon: Icons.shopping_cart_checkout,
                            ),
                            const SizedBox(height: 12),
                            buildFinancialItem(
                              title: 'إجمالي المديونية',
                              value: report.totalDue,
                              color: Colors.red.shade700,
                              icon: Icons.money_off_csred,
                            ),
                            const SizedBox(height: 12),
                            buildFinancialItem(
                              title: 'خزنة الشركة',
                              value: report.companyCash,
                              color: report.companyCash >= 0
                                  ? Colors.green.shade700
                                  : Colors.red.shade700,
                              isBold: true,
                              icon: Icons.savings,
                            ),
                          ],
                        ),

                        // مخطط بياني للمقارنة
                        const SizedBox(height: 30),
                        Text(
                          'ملخص الموقف المالي',
                          style: GoogleFonts.cairo(
                            color: kMainColor,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 10),
                        Container(
                          height: 200,
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(15),
                            boxShadow: const [
                              BoxShadow(
                                color: Color.fromRGBO(0, 0, 0, 0.1),
                                blurRadius: 10,
                                offset: Offset(0, 5),
                              ),
                            ],
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    buildSummaryCircle(
                                      title: 'المبيعات',
                                      value: report.totalSales,
                                      color: Colors.green.shade500,
                                    ),
                                    const SizedBox(height: 10),
                                    buildSummaryCircle(
                                      title: 'المشتريات',
                                      value: report.totalPurchases,
                                      color: Colors.orange.shade500,
                                    ),
                                  ],
                                ),
                              ),
                              Expanded(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    buildSummaryCircle(
                                      title: 'المصروفات',
                                      value: report.totalExpenses +
                                          report.totalSalaries,
                                      color: Colors.red.shade500,
                                    ),
                                    const SizedBox(height: 10),
                                    buildSummaryCircle(
                                      title: 'صافي الربح',
                                      value: report.netProfit,
                                      color: report.netProfit >= 0
                                          ? Colors.blue.shade500
                                          : Colors.red.shade500,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                },
                loading: () => Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const CircularProgressIndicator(
                        color: kMainColor,
                      ),
                      const SizedBox(height: 20),
                      Text(
                        'جاري تحميل البيانات...',
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (constUserId.isEmpty)
                        Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Text(
                            'تحذير: معرف المستخدم غير متوفر. جاري محاولة الحصول عليه...',
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              color: Colors.orange,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                    ],
                  ),
                ),
                error: (error, stackTrace) => Center(
                  child: Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.error_outline,
                          color: Colors.red,
                          size: 60,
                        ),
                        const SizedBox(height: 20),
                        Text(
                          'حدث خطأ أثناء تحميل البيانات',
                          style: GoogleFonts.cairo(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.red,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 10),
                        Text(
                          error.toString(),
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            color: Colors.red.shade700,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 20),
                        ElevatedButton.icon(
                          onPressed: () {
                            // تحديث التقرير
                            final _ = ref.refresh(financialReportProvider(
                                ref.read(selectedFinancialMonthProvider)));
                          },
                          icon: const Icon(Icons.refresh),
                          label: Text(
                            'إعادة المحاولة',
                            style: GoogleFonts.cairo(),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: kMainColor,
                            foregroundColor: Colors.white,
                          ),
                        ),
                        if (constUserId.isEmpty) ...[
                          const SizedBox(height: 20),
                          Text(
                            'معرف المستخدم غير متوفر. تأكد من تسجيل الدخول بشكل صحيح.',
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              color: Colors.orange,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
