// بسم الله الرحمن الرحيم توكلنا على الله

// ignore_for_file: prefer_const_constructors, unnecessary_string_interpolations, deprecated_member_use, unused_local_variable

import 'package:flutter/material.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:mobile_pos/Provider/product_report_provider.dart';
import 'package:mobile_pos/Screens/Home/home.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:firebase_database/firebase_database.dart';
import '../../../../constant.dart';
import '../../../../empty_screen_widget.dart';
import '../../../../features/inventory_management/screens/product_details_screen.dart';
import '../../../../features/inventory_management/models/product_model.dart' as new_model;
import '../../../../features/inventory_management/services/inventory_service.dart';
import 'product_movement_details_screen.dart';

// Provider للمنتجات الجديدة
final allProductsProvider = FutureProvider<List<new_model.ProductModel>>((ref) async {
  final inventoryService = ref.read(inventoryServiceProvider);
  return await inventoryService.getAllProducts();
});

class ProductReportScreen extends StatefulWidget {
  const ProductReportScreen({super.key});

  @override
  State<ProductReportScreen> createState() => _ProductReportScreenState();
}

class _ProductReportScreenState extends State<ProductReportScreen> {
  TextEditingController fromDateController =
      TextEditingController(text: DateFormat.yMMMd().format(DateTime.now()));
  TextEditingController toDateController =
      TextEditingController(text: DateFormat.yMMMd().format(DateTime.now()));
  DateTime fromDate = DateTime.now();
  DateTime toDate = DateTime.now();
  bool isPicked = false;
  String searchText = '';
  bool isSearching = false;

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        return await const Home().launch(context, isNewTask: true);
      },
      child: Scaffold(
        backgroundColor: kMainColor,
        appBar: AppBar(
          title: Text(
            'تقرير حركة الأصناف',
            style: GoogleFonts.poppins(
              color: Colors.white,
              fontSize: 20.0,
            ),
          ),
          iconTheme: const IconThemeData(color: Colors.white),
          centerTitle: true,
          backgroundColor: kMainColor,
          elevation: 0.0,
        ),
        body: Consumer(
          builder: (context, ref, __) {
            final productReports = ref.watch(productReportProvider);
            final uniqueProducts =
                ref.read(productReportProvider.notifier).getUniqueProducts();

            // جلب جميع المنتجات من النظام الجديد أيضاً
            final allProductsFuture = ref.watch(allProductsProvider);

            return Container(
              alignment: Alignment.topCenter,
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topRight: Radius.circular(30),
                  topLeft: Radius.circular(30),
                ),
              ),
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(
                          right: 20.0, left: 20.0, top: 20, bottom: 10),
                      child: Row(
                        children: [
                          Expanded(
                            child: AppTextField(
                              textFieldType: TextFieldType.NAME,
                              readOnly: true,
                              controller: fromDateController,
                              decoration: InputDecoration(
                                floatingLabelBehavior:
                                    FloatingLabelBehavior.always,
                                labelText: 'من تاريخ',
                                border: const OutlineInputBorder(),
                                suffixIcon: IconButton(
                                  onPressed: () async {
                                    final picked = await showDatePicker(
                                      context: context,
                                      initialDate: DateTime.now(),
                                      firstDate: DateTime(2015, 8),
                                      lastDate: DateTime(2101),
                                    );
                                    if (picked != null) {
                                      setState(() {
                                        fromDate = picked;
                                        fromDateController.text =
                                            DateFormat.yMMMd().format(picked);
                                        isPicked = true;
                                      });
                                    }
                                  },
                                  icon: const Icon(FeatherIcons.calendar),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 10),
                          Expanded(
                            child: AppTextField(
                              textFieldType: TextFieldType.NAME,
                              readOnly: true,
                              controller: toDateController,
                              decoration: InputDecoration(
                                floatingLabelBehavior:
                                    FloatingLabelBehavior.always,
                                labelText: 'إلى تاريخ',
                                border: const OutlineInputBorder(),
                                suffixIcon: IconButton(
                                  onPressed: () async {
                                    final picked = await showDatePicker(
                                      context: context,
                                      initialDate: toDate,
                                      firstDate: DateTime(2015, 8),
                                      lastDate: DateTime(2101),
                                    );
                                    if (picked != null) {
                                      setState(() {
                                        toDate = picked;
                                        toDateController.text =
                                            DateFormat.yMMMd().format(picked);
                                        isPicked = true;
                                      });
                                    }
                                  },
                                  icon: const Icon(FeatherIcons.calendar),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 20, right: 20.0),
                      child: Container(
                        height: 120,
                        width: double.infinity,
                        decoration: BoxDecoration(
                          borderRadius:
                              const BorderRadius.all(Radius.circular(15)),
                          color: kMainColor.withOpacity(0.08),
                        ),
                        child: Center(
                          child: ListTile(
                            leading: Container(
                              height: 40,
                              width: 40,
                              decoration: const BoxDecoration(
                                borderRadius:
                                    BorderRadius.all(Radius.circular(80)),
                                image: DecorationImage(
                                  image: AssetImage(
                                      'assets/images/ledger_total_sale.png'),
                                ),
                              ),
                            ),
                            title: Text(
                              '${uniqueProducts.length} منتج',
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            trailing: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Container(
                                  height: 40,
                                  width: 40,
                                  decoration: BoxDecoration(
                                    borderRadius: const BorderRadius.all(
                                        Radius.circular(80)),
                                    color: kMainColor.withOpacity(0.2),
                                  ),
                                  child: const Icon(
                                    Icons.picture_as_pdf_outlined,
                                    color: kMainColor,
                                  ),
                                ),
                                const SizedBox(width: 7),
                                Container(
                                  height: 40,
                                  width: 40,
                                  decoration: BoxDecoration(
                                    borderRadius: const BorderRadius.all(
                                        Radius.circular(80)),
                                    color: Colors.green.withOpacity(0.2),
                                  ),
                                  child: const Icon(
                                    Icons.print,
                                    color: Colors.grey,
                                  ),
                                ),
                              ],
                            ).visible(false),
                            subtitle: Text(
                              'إجمالي المنتجات ${isPicked ? '(من ${fromDate.toString().substring(0, 10)} إلى ${toDate.toString().substring(0, 10)})' : '(هذا الشهر)'}',
                            ),
                          ),
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                          left: 20.0, right: 20.0, top: 10.0),
                      child: AppTextField(
                        textFieldType: TextFieldType.NAME,
                        onChanged: (value) {
                          setState(() {
                            searchText = value;
                          });
                        },
                        decoration: InputDecoration(
                          floatingLabelBehavior: FloatingLabelBehavior.never,
                          labelText: 'بحث',
                          hintText: 'ادخل اسم المنتج او الكود',
                          border: const OutlineInputBorder(),
                          prefixIcon: const Icon(Icons.search),
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(20.0),
                      child: Container(
                        height: 45,
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: kMainColor,
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: TextButton(
                          onPressed: isSearching ? null : () async {
                            setState(() {
                              isSearching = true;
                            });

                            try {
                              ref
                                  .read(productReportProvider.notifier)
                                  .getProductReport(
                                    fromDate: fromDate,
                                    toDate: toDate,
                                  );
                              // إعادة تحميل المنتجات الجديدة أيضاً
                              ref.invalidate(allProductsProvider);

                              // انتظار قليل للتأكد من تحميل البيانات
                              await Future.delayed(const Duration(milliseconds: 500));
                            } finally {
                              if (mounted) {
                                setState(() {
                                  isSearching = false;
                                });
                              }
                            }
                          },
                          child: isSearching
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    color: Colors.white,
                                    strokeWidth: 2,
                                  ),
                                )
                              : Text(
                                  'بحث',
                                  style: kTextStyle.copyWith(color: Colors.white),
                                ),
                        ),
                      ),
                    ),
                    // عرض المنتجات من النظامين
                    allProductsFuture.when(
                      data: (newProducts) {
                        // دمج المنتجات من النظامين
                        return FutureBuilder<List<Map<String, dynamic>>>(
                          future: _combineProducts(uniqueProducts, newProducts),
                          builder: (context, snapshot) {
                            if (snapshot.connectionState == ConnectionState.waiting) {
                              return const Center(child: CircularProgressIndicator());
                            }

                            if (snapshot.hasError) {
                              return Center(child: Text('خطأ: ${snapshot.error}'));
                            }

                            final combinedProducts = snapshot.data ?? [];

                            return combinedProducts.isEmpty
                                ? const EmptyScreenWidget()
                                : Builder(
                                    builder: (context) {
                                      final filteredProducts = combinedProducts
                                          .where((product) =>
                                              searchText.isEmpty ||
                                              product['productName']
                                                  .toLowerCase()
                                                  .contains(searchText.toLowerCase()) ||
                                              product['productCode']
                                                  .toLowerCase()
                                                  .contains(searchText.toLowerCase()))
                                          .toList();

                              return filteredProducts.isEmpty
                                  ? Center(
                                      child: Padding(
                                        padding: const EdgeInsets.all(20.0),
                                        child: Column(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            Icon(
                                              Icons.calendar_today,
                                              size: 64,
                                              color: Colors.grey[400],
                                            ),
                                            const SizedBox(height: 16),
                                            Text(
                                              searchText.isEmpty
                                                  ? 'لا توجد منتجات في الفترة المحددة'
                                                  : 'لا توجد نتائج للبحث',
                                              style: TextStyle(
                                                fontSize: 18,
                                                color: Colors.grey[600],
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                            const SizedBox(height: 8),
                                            Text(
                                              searchText.isEmpty
                                                  ? 'من ${DateFormat('yyyy-MM-dd').format(fromDate)} إلى ${DateFormat('yyyy-MM-dd').format(toDate)}\nجرب تغيير الفترة الزمنية'
                                                  : 'جرب البحث بكلمات مختلفة',
                                              style: TextStyle(
                                                fontSize: 14,
                                                color: Colors.grey[500],
                                              ),
                                              textAlign: TextAlign.center,
                                            ),
                                          ],
                                        ),
                                      ),
                                    )
                                  : ListView.builder(
                                      shrinkWrap: true,
                                      physics:
                                          const NeverScrollableScrollPhysics(),
                                      itemCount: filteredProducts.length,
                                      itemBuilder: (context, index) {
                                        final product = filteredProducts[index];
                                        final totalQuantity = ref
                                            .read(
                                                productReportProvider.notifier)
                                            .calculateTotalQuantity(
                                                product['productCode']);

                                        return Column(
                                          children: [
                                            Container(
                                              padding: const EdgeInsets.all(20),
                                              width: context.width(),
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Row(
                                                    children: [
                                                      Container(
                                                        padding: const EdgeInsets.all(8),
                                                        decoration: BoxDecoration(
                                                          color: kMainColor.withOpacity(0.1),
                                                          borderRadius: BorderRadius.circular(8),
                                                        ),
                                                        child: Icon(
                                                          Icons.inventory_2,
                                                          color: kMainColor,
                                                          size: 20,
                                                        ),
                                                      ),
                                                      const SizedBox(width: 12),
                                                      Expanded(
                                                        child: Column(
                                                          crossAxisAlignment: CrossAxisAlignment.start,
                                                          children: [
                                                            Text(
                                                              product['productName'],
                                                              style: const TextStyle(
                                                                fontSize: 16,
                                                                fontWeight: FontWeight.bold,
                                                              ),
                                                              maxLines: 2,
                                                              overflow: TextOverflow.ellipsis,
                                                            ),
                                                            const SizedBox(height: 4),
                                                            Text(
                                                              'كود المنتج: ${product['productCode']}',
                                                              style: const TextStyle(
                                                                color: Colors.grey,
                                                                fontSize: 12,
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  const SizedBox(height: 10),
                                                  // Sales Information Row
                                                  Container(
                                                    padding: const EdgeInsets
                                                        .symmetric(vertical: 8),
                                                    decoration: BoxDecoration(
                                                      color: Colors.green
                                                          .withOpacity(0.1),
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              8),
                                                    ),
                                                    child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        Padding(
                                                          padding:
                                                              const EdgeInsets
                                                                  .only(
                                                                  right: 8.0,
                                                                  bottom: 4),
                                                          child: Text(
                                                            'معلومات المبيعات',
                                                            style: TextStyle(
                                                              color: Colors
                                                                  .green[700],
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold,
                                                            ),
                                                          ),
                                                        ),
                                                        Row(
                                                          mainAxisAlignment:
                                                              MainAxisAlignment
                                                                  .spaceBetween,
                                                          children: [
                                                            Column(
                                                              crossAxisAlignment:
                                                                  CrossAxisAlignment
                                                                      .start,
                                                              children: [
                                                                Text(
                                                                  'الكمية المباعة',
                                                                  style: const TextStyle(
                                                                      color: Colors
                                                                          .grey),
                                                                ),
                                                                const SizedBox(
                                                                    height: 2),
                                                                Text(
                                                                  '${product['soldQuantity']}',
                                                                  style:
                                                                      const TextStyle(
                                                                    fontSize:
                                                                        16,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .bold,
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                            Column(
                                                              crossAxisAlignment:
                                                                  CrossAxisAlignment
                                                                      .center,
                                                              children: [
                                                                Text(
                                                                  'متوسط سعر البيع',
                                                                  style: const TextStyle(
                                                                      color: Colors
                                                                          .grey),
                                                                ),
                                                                const SizedBox(
                                                                    height: 2),
                                                                Text(
                                                                  '${product['averagePrice'].toStringAsFixed(2)}',
                                                                  style:
                                                                      const TextStyle(
                                                                    fontSize:
                                                                        16,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .bold,
                                                                    color:
                                                                        kMainColor,
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                            Column(
                                                              crossAxisAlignment:
                                                                  CrossAxisAlignment
                                                                      .end,
                                                              children: [
                                                                Text(
                                                                  'عدد الفواتير',
                                                                  style: const TextStyle(
                                                                      color: Colors
                                                                          .grey),
                                                                ),
                                                                const SizedBox(
                                                                    height: 2),
                                                                Text(
                                                                  '${product['invoiceCount']}',
                                                                  style:
                                                                      const TextStyle(
                                                                    fontSize:
                                                                        16,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .bold,
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                          ],
                                                        ),
                                                        const SizedBox(
                                                            height: 8),
                                                        Row(
                                                          mainAxisAlignment:
                                                              MainAxisAlignment
                                                                  .center,
                                                          children: [
                                                            Text(
                                                              'إجمالي المبيعات: ',
                                                              style: const TextStyle(
                                                                  color: Colors
                                                                      .grey),
                                                            ),
                                                            Text(
                                                              '${(product['soldQuantity'] * product['averagePrice']).toStringAsFixed(2)}',
                                                              style: TextStyle(
                                                                fontSize: 16,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold,
                                                                color: Colors
                                                                    .green[700],
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  const SizedBox(height: 12),

                                                  // معلومات إضافية
                                                  Container(
                                                    padding: const EdgeInsets.symmetric(vertical: 8),
                                                    decoration: BoxDecoration(
                                                      color: Colors.blue.withOpacity(0.1),
                                                      borderRadius: BorderRadius.circular(8),
                                                    ),
                                                    child: Row(
                                                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                                                      children: [
                                                        Column(
                                                          children: [
                                                            Text(
                                                              'الرصيد المتبقي',
                                                              style: const TextStyle(
                                                                color: Colors.grey,
                                                                fontSize: 12,
                                                              ),
                                                            ),
                                                            FutureBuilder<int>(
                                                              future: _getCurrentStock(product['productCode'], ref),
                                                              builder: (context, snapshot) {
                                                                if (snapshot.connectionState == ConnectionState.waiting) {
                                                                  return const SizedBox(
                                                                    width: 16,
                                                                    height: 16,
                                                                    child: CircularProgressIndicator(strokeWidth: 2),
                                                                  );
                                                                }

                                                                final currentStock = snapshot.data ?? product['remainingQuantity'];
                                                                final stockColor = currentStock <= 5
                                                                    ? Colors.red
                                                                    : currentStock <= 20
                                                                        ? Colors.orange
                                                                        : Colors.blue;

                                                                final stockIcon = currentStock <= 5
                                                                    ? Icons.warning
                                                                    : currentStock <= 20
                                                                        ? Icons.info
                                                                        : Icons.check_circle;

                                                                final stockStatus = currentStock <= 5
                                                                    ? 'منخفض'
                                                                    : currentStock <= 20
                                                                        ? 'متوسط'
                                                                        : 'جيد';

                                                                return Column(
                                                                  children: [
                                                                    Row(
                                                                      mainAxisSize: MainAxisSize.min,
                                                                      children: [
                                                                        Icon(
                                                                          stockIcon,
                                                                          color: stockColor,
                                                                          size: 16,
                                                                        ),
                                                                        const SizedBox(width: 4),
                                                                        Text(
                                                                          '$currentStock',
                                                                          style: TextStyle(
                                                                            fontWeight: FontWeight.bold,
                                                                            color: stockColor,
                                                                            fontSize: 16,
                                                                          ),
                                                                        ),
                                                                      ],
                                                                    ),
                                                                    const SizedBox(height: 2),
                                                                    Text(
                                                                      stockStatus,
                                                                      style: TextStyle(
                                                                        fontSize: 10,
                                                                        color: stockColor,
                                                                        fontWeight: FontWeight.w500,
                                                                      ),
                                                                    ),
                                                                  ],
                                                                );
                                                              },
                                                            ),
                                                          ],
                                                        ),
                                                        Column(
                                                          children: [
                                                            Text(
                                                              'صافي الربح',
                                                              style: const TextStyle(
                                                                color: Colors.grey,
                                                                fontSize: 12,
                                                              ),
                                                            ),
                                                            Text(
                                                              '${(product['soldQuantity'] * product['averagePrice']).toStringAsFixed(2)}',
                                                              style: TextStyle(
                                                                fontWeight: FontWeight.bold,
                                                                color: Colors.green[700],
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  const SizedBox(height: 8),

                                                  // زر عرض حركة الصنف
                                                  SizedBox(
                                                    width: double.infinity,
                                                    child: ElevatedButton.icon(
                                                      onPressed: () => _openProductMovementDetails(context, ref, product),
                                                      icon: const Icon(Icons.analytics, size: 18),
                                                      label: const Text('عرض حركة الصنف التفصيلية'),
                                                      style: ElevatedButton.styleFrom(
                                                        backgroundColor: kMainColor,
                                                        foregroundColor: Colors.white,
                                                        padding: const EdgeInsets.symmetric(vertical: 10),
                                                        shape: RoundedRectangleBorder(
                                                          borderRadius: BorderRadius.circular(8),
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                  const SizedBox(height: 8),
                                                ],
                                              ),
                                            ),
                                            Container(
                                              height: 0.5,
                                              width: context.width(),
                                              color: Colors.grey,
                                            )
                                          ],
                                        );
                                      },
                                    );
                                },
                              );
                          },
                        );
                      },
                      loading: () => const Center(child: CircularProgressIndicator()),
                      error: (error, stack) => Center(
                        child: Text('خطأ في تحميل المنتجات: $error'),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  // فتح تفاصيل حركة الصنف
  void _openProductMovementDetails(BuildContext context, WidgetRef ref, dynamic product) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProductMovementDetailsScreen(
          productName: product['productName'],
          productCode: product['productCode'],
          fromDate: fromDate,
          toDate: toDate,
          createdDate: product['hasTransactions'] ? null : DateTime.now(), // تاريخ افتراضي للمنتجات الجديدة
          currentQuantity: product['remainingQuantity'],
        ),
      ),
    );
  }

  // دمج المنتجات من النظامين مع فلترة حسب التاريخ
  Future<List<Map<String, dynamic>>> _combineProducts(List<dynamic> oldProducts, List<new_model.ProductModel> newProducts) async {
    List<Map<String, dynamic>> combinedProducts = [];
    Set<String> addedProductCodes = {};

    // إضافة المنتجات من النظام القديم (التي لها معاملات)
    for (var product in oldProducts) {
      combinedProducts.add({
        'productName': product.productName,
        'productCode': product.productCode,
        'soldQuantity': product.soldQuantity,
        'remainingQuantity': product.remainingQuantity,
        'averagePrice': product.averagePrice,
        'invoiceCount': product.invoiceCount,
        'hasTransactions': true,
        'createdDate': null, // سيتم البحث عنه لاحقاً
      });
      addedProductCodes.add(product.productCode);
    }

    // إضافة المنتجات من النظام الجديد (مع فلترة حسب التاريخ)
    for (var product in newProducts) {
      if (!addedProductCodes.contains(product.barcode)) {
        // فلترة حسب تاريخ الإنشاء
        if (_isProductInDateRange(product.createdAt)) {
          combinedProducts.add({
            'productName': product.name,
            'productCode': product.barcode,
            'soldQuantity': 0,
            'remainingQuantity': product.quantity,
            'averagePrice': product.price,
            'invoiceCount': 0,
            'hasTransactions': false,
            'createdDate': product.createdAt,
          });
          addedProductCodes.add(product.barcode);
        }
      }
    }

    // إضافة المنتجات من النظام القديم (مع فلترة حسب التاريخ)
    await _addOldSystemProductsWithDateFilter(combinedProducts, addedProductCodes);

    return combinedProducts;
  }

  // فحص ما إذا كان المنتج في النطاق الزمني المحدد
  bool _isProductInDateRange(DateTime createdDate) {
    // إذا كان تاريخ الإنشاء قبل أو في تاريخ النهاية المحدد
    return createdDate.isBefore(toDate.add(const Duration(days: 1))) ||
           createdDate.isAtSameMomentAs(toDate);
  }

  // حساب الرصيد الحقيقي للمنتج
  Future<int> _getCurrentStock(String productCode, WidgetRef ref) async {
    try {
      // البحث في النظام القديم بدون index
      final productsRef = FirebaseDatabase.instance.ref(constUserId).child('Products');
      final snapshot = await productsRef.get();

      if (snapshot.exists && snapshot.value != null) {
        final data = snapshot.value as Map<dynamic, dynamic>;

        // البحث يدوياً بدون index
        for (var entry in data.entries) {
          final productData = entry.value as Map<dynamic, dynamic>;
          if (productData['productCode'] == productCode) {
            final stockString = productData['productStock']?.toString() ?? '0';
            return int.tryParse(stockString) ?? 0;
          }
        }
      }

      return 0;
    } catch (e) {
      debugPrint('خطأ في حساب الرصيد الحقيقي: $e');
      return 0;
    }
  }

  // إضافة المنتجات من النظام القديم مع فلترة حسب التاريخ
  Future<void> _addOldSystemProductsWithDateFilter(List<Map<String, dynamic>> combinedProducts, Set<String> addedProductCodes) async {
    try {
      final productsRef = FirebaseDatabase.instance.ref(constUserId).child('Products');
      final snapshot = await productsRef.get();

      if (snapshot.exists && snapshot.value != null) {
        final data = snapshot.value as Map<dynamic, dynamic>;

        data.forEach((key, value) {
          try {
            final productData = Map<String, dynamic>.from(value);
            final productCode = productData['productCode'] ?? key;

            if (!addedProductCodes.contains(productCode)) {
              // محاولة الحصول على تاريخ الإنشاء من البيانات
              DateTime? createdDate;
              try {
                final dateString = productData['productAddingDate'] ?? productData['createdAt'];
                if (dateString != null) {
                  createdDate = DateTime.parse(dateString.toString());
                }
              } catch (e) {
                // إذا لم نجد تاريخ صحيح، نفترض أنه منتج قديم
                createdDate = DateTime(2020, 1, 1); // تاريخ افتراضي قديم
              }

              // فلترة حسب التاريخ - إذا كان المنتج تم إنشاؤه قبل أو في تاريخ النهاية
              if (createdDate != null && _isProductInDateRange(createdDate)) {
                combinedProducts.add({
                  'productName': productData['productName'] ?? 'منتج غير محدد',
                  'productCode': productCode,
                  'soldQuantity': 0,
                  'remainingQuantity': int.tryParse(productData['productStock']?.toString() ?? '0') ?? 0,
                  'averagePrice': double.tryParse(productData['productSalePrice']?.toString() ?? '0') ?? 0.0,
                  'invoiceCount': 0,
                  'hasTransactions': false,
                  'createdDate': createdDate,
                });
                addedProductCodes.add(productCode);
              }
            }
          } catch (e) {
            debugPrint('خطأ في معالجة منتج من النظام القديم: $e');
          }
        });
      }
    } catch (e) {
      debugPrint('خطأ في جلب المنتجات من النظام القديم: $e');
    }
  }
}

// الحمد لله رب العالمين
