class PaySalaryModel {
  final String id;
  final String employeeId;
  final String employeeName;
  final double paySalary;
  final double netSalary;
  final String year;
  final String month;
  final DateTime payingDate;
  final String paymentType;
  final String note;

  // Constructor with required and optional fields
  PaySalaryModel({
    required this.id,
    required this.employeeId,
    required this.employeeName,
    required this.paySalary,
    required this.netSalary,
    required this.year,
    required this.month,
    required this.payingDate,
    this.paymentType = 'Cash',
    this.note = '',
  });

  // Factory constructor to create PaySalaryModel from map
  factory PaySalaryModel.fromMap(Map<String, dynamic> map, String id) {
    return PaySalaryModel(
      id: id,
      employeeId: map['employeeId'] ?? '',
      employeeName: map['employeeName'] ?? '',
      paySalary: (map['paySalary'] ?? 0).toDouble(),
      netSalary: (map['netSalary'] ?? 0).toDouble(),
      year: map['year'] ?? '',
      month: map['month'] ?? '',
      payingDate: DateTime.fromMillisecondsSinceEpoch(map['payingDate'] ?? 0),
      paymentType: map['paymentType'] ?? 'Cash',
      note: map['note'] ?? '',
    );
  }

  // Convert PaySalaryModel to map
  Map<String, dynamic> toMap() {
    return {
      'employeeId': employeeId,
      'employeeName': employeeName,
      'paySalary': paySalary,
      'netSalary': netSalary,
      'year': year,
      'month': month,
      'payingDate': payingDate.millisecondsSinceEpoch,
      'paymentType': paymentType,
      'note': note,
    };
  }

  // Create a copy with some fields changed
  PaySalaryModel copyWith({
    String? id,
    String? employeeId,
    String? employeeName,
    double? paySalary,
    double? netSalary,
    String? year,
    String? month,
    DateTime? payingDate,
    String? paymentType,
    String? note,
  }) {
    return PaySalaryModel(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      employeeName: employeeName ?? this.employeeName,
      paySalary: paySalary ?? this.paySalary,
      netSalary: netSalary ?? this.netSalary,
      year: year ?? this.year,
      month: month ?? this.month,
      payingDate: payingDate ?? this.payingDate,
      paymentType: paymentType ?? this.paymentType,
      note: note ?? this.note,
    );
  }
}
