// ignore_for_file: prefer_const_constructors, unused_local_variable, use_super_parameters, prefer_const_constructors_in_immutables, library_private_types_in_public_api, curly_braces_in_flow_control_structures

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:mobile_pos/Provider/product_provider.dart';
import 'package:mobile_pos/Provider/transactions_provider.dart';
import 'package:mobile_pos/model/item_model.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/currency.dart';

class ItemDetailsScreen extends ConsumerStatefulWidget {
  final ItemModel itemModel;

  ItemDetailsScreen({Key? key, required this.itemModel}) : super(key: key);

  @override
  _ItemDetailsScreenState createState() => _ItemDetailsScreenState();
}

class _ItemDetailsScreenState extends ConsumerState<ItemDetailsScreen> {
  DateTime? fromDate;
  DateTime? toDate;
  final NumberFormat myFormat = NumberFormat('#,##0.00', 'en_US');

  // متغيرات لتخزين إحصائيات الصنف
  double totalPurchases = 0;
  double totalSales = 0;
  double totalReturns = 0;
  int remainingStock = 0;
  double startingBalance = 0;
  int purchaseCount = 0;
  int salesCount = 0;
  int returnsCount = 0;

  @override
  void initState() {
    super.initState();
    // تعيين التواريخ الافتراضية
    fromDate = DateTime(2025, 1, 1);
    toDate = DateTime.now();
    // جلب البيانات الأولية
    _fetchItemMovement();
  }

  // دالة لجلب حركة الصنف
  void _fetchItemMovement() async {
    if (fromDate == null || toDate == null) return;

    // استخدام المزودات لجلب البيانات
    final transactions = ref.read(transitionProvider);
    final products = ref.read(productProvider);

    setState(() {
      // إعادة تعيين القيم
      totalPurchases = 0;
      totalSales = 0;
      totalReturns = 0;
      purchaseCount = 0;
      salesCount = 0;
      returnsCount = 0;
      startingBalance = 0;

      // حساب الإحصائيات بناءً على البيانات
      transactions.whenData((transactionList) {
        // حساب الرصيد الافتتاحي
        startingBalance = transactionList
            .where((t) =>
                t.purchaseDate.compareTo(fromDate!.toIso8601String()) < 0)
            .fold(0, (sum, t) {
          if (t.customerType == 'purchase')
            return sum + (t.dueAmount?.toDouble() ?? 0);
          if (t.customerType == 'sale')
            return sum - (t.dueAmount?.toDouble() ?? 0);
          if (t.customerType == 'return')
            return sum + (t.dueAmount?.toDouble() ?? 0);
          return sum;
        });

        // حساب الحركات خلال الفترة
        for (var transaction in transactionList) {
          if (transaction.purchaseDate.compareTo(fromDate!.toIso8601String()) >=
                  0 &&
              transaction.purchaseDate.compareTo(toDate!.toIso8601String()) <=
                  0) {
            if (transaction.customerType == 'purchase') {
              totalPurchases += transaction.dueAmount?.toDouble() ?? 0;
              purchaseCount++;
            } else if (transaction.customerType == 'sale') {
              totalSales += transaction.dueAmount?.toDouble() ?? 0;
              salesCount++;
            } else if (transaction.customerType == 'return') {
              totalReturns += transaction.dueAmount?.toDouble() ?? 0;
              returnsCount++;
            }
          }
        }

        // حساب المخزون المتبقي
        remainingStock = startingBalance.toInt() +
            (totalPurchases - totalSales + totalReturns).toInt();
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('تفاصيل الصنف'),
        backgroundColor: kMainColor,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات الصنف الأساسية
              Text(
                widget.itemModel.itemName.isNotEmpty
                    ? widget.itemModel.itemName
                    : widget.itemModel.itemCode,
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              Text('كود الصنف: ${widget.itemModel.itemCode}'),
              SizedBox(height: 20),

              // اختيار المدة الزمنية
              Text('اختر المدة الزمنية:', style: TextStyle(fontSize: 18)),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () async {
                        DateTime? picked = await showDatePicker(
                          context: context,
                          initialDate: fromDate ?? DateTime.now(),
                          firstDate: DateTime(2000),
                          lastDate: DateTime(2101),
                        );
                        if (picked != null) {
                          setState(() {
                            fromDate = picked;
                            _fetchItemMovement();
                          });
                        }
                      },
                      child: Text(
                          'من: ${fromDate != null ? DateFormat.yMMMd().format(fromDate!) : 'اختر التاريخ'}'),
                    ),
                  ),
                  SizedBox(width: 10),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () async {
                        DateTime? picked = await showDatePicker(
                          context: context,
                          initialDate: toDate ?? DateTime.now(),
                          firstDate: DateTime(2000),
                          lastDate: DateTime(2101),
                        );
                        if (picked != null) {
                          setState(() {
                            toDate = picked;
                            _fetchItemMovement();
                          });
                        }
                      },
                      child: Text(
                          'إلى: ${toDate != null ? DateFormat.yMMMd().format(toDate!) : 'اختر التاريخ'}'),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 30),

              // عرض الإحصائيات
              Container(
                padding: EdgeInsets.all(15),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('ملخص حركة الصنف:',
                        style: TextStyle(
                            fontSize: 18, fontWeight: FontWeight.bold)),
                    SizedBox(height: 15),
                    _buildStatRow('الرصيد الافتتاحي:', startingBalance),
                    Divider(),
                    _buildStatRow('إجمالي المشتريات:', totalPurchases,
                        count: purchaseCount),
                    _buildStatRow('إجمالي المبيعات:', totalSales,
                        count: salesCount),
                    _buildStatRow('إجمالي المرتجعات:', totalReturns,
                        count: returnsCount),
                    Divider(),
                    _buildStatRow('الرصيد النهائي:', remainingStock.toDouble(),
                        isTotal: true),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatRow(String label, double value,
      {bool isTotal = false, int? count}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 16,
                fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
          if (count != null) ...[
            Expanded(
              child: Text(
                'عدد: $count',
                style: TextStyle(fontSize: 14),
                textAlign: TextAlign.center,
              ),
            ),
          ],
          Expanded(
            child: Text(
              '$currency ${myFormat.format(value)}',
              style: TextStyle(
                fontSize: 16,
                fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
                color: isTotal ? kMainColor : null,
              ),
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }
}


cascade

// Process ID (PID): 7928

// Command line: C:\Program Files\PowerShell\7\pwsh.exe '-noexit' '-command' 'try { . "c:\Users\<USER>\AppData\Local\Programs\Windsurf\resources\app\out\vs\workbench\contrib\terminal\common\scripts\shellIntegration.ps1" } catch {}'