// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:mobile_pos/GlobalComponents/button_global.dart';
import 'package:mobile_pos/Screens/Expense/add_erxpense.dart';
import 'package:mobile_pos/const_commas.dart';
import 'package:mobile_pos/currency.dart';
import 'package:mobile_pos/generated/l10n.dart' as lang;
import 'package:mobile_pos/model/expense_model.dart';
import 'package:nb_utils/nb_utils.dart';

import '../../Provider/all_expanse_provider.dart';
import '../../constant.dart';
import 'widgets/expense_card.dart';
import 'widgets/expense_filter_widget.dart';
import 'widgets/expense_stats_widget.dart';
import 'widgets/expense_search_widget.dart';

class ExpenseList extends StatefulWidget {
  const ExpenseList({super.key});

  @override
  // ignore: library_private_types_in_public_api
  _ExpenseListState createState() => _ExpenseListState();
}

class _ExpenseListState extends State<ExpenseList> {
  final dateController = TextEditingController();
  TextEditingController fromDateTextEditingController =
      TextEditingController(text: DateFormat.yMMMd().format(DateTime.now()));
  TextEditingController toDateTextEditingController =
      TextEditingController(text: DateFormat.yMMMd().format(DateTime.now()));
  DateTime fromDate = DateTime(2024);
  DateTime toDate = DateTime.now();
  double totalExpense = 0;

  // متغيرات جديدة للبحث والفلترة
  String _searchQuery = '';
  String _selectedQuickFilter = 'all';
  Map<String, dynamic> _activeFilters = {};
  bool _showAdvancedFilters = false;

  @override
  void dispose() {
    dateController.dispose();
    super.dispose();
  }

  // دالة البحث في المصروفات
  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });
  }

  // دالة الفلترة السريعة
  void _onQuickFilterChanged(String filter) {
    setState(() {
      _selectedQuickFilter = filter;
    });
    _applyQuickFilter(filter);
  }

  // دالة تطبيق الفلتر السريع
  void _applyQuickFilter(String filter) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    switch (filter) {
      case 'today':
        setState(() {
          fromDate = today;
          toDate = today.add(const Duration(days: 1));
          fromDateTextEditingController.text =
              DateFormat.yMMMd().format(fromDate);
          toDateTextEditingController.text = DateFormat.yMMMd().format(toDate);
        });
        break;
      case 'week':
        final startOfWeek = today.subtract(Duration(days: now.weekday - 1));
        setState(() {
          fromDate = startOfWeek;
          toDate = today.add(const Duration(days: 1));
          fromDateTextEditingController.text =
              DateFormat.yMMMd().format(fromDate);
          toDateTextEditingController.text = DateFormat.yMMMd().format(toDate);
        });
        break;
      case 'month':
        final startOfMonth = DateTime(now.year, now.month, 1);
        setState(() {
          fromDate = startOfMonth;
          toDate = today.add(const Duration(days: 1));
          fromDateTextEditingController.text =
              DateFormat.yMMMd().format(fromDate);
          toDateTextEditingController.text = DateFormat.yMMMd().format(toDate);
        });
        break;
      default:
        setState(() {
          fromDate = DateTime(2024);
          toDate = DateTime.now();
          fromDateTextEditingController.text =
              DateFormat.yMMMd().format(DateTime.now());
          toDateTextEditingController.text =
              DateFormat.yMMMd().format(DateTime.now());
        });
    }
  }

  // دالة تطبيق الفلاتر المتقدمة
  void _onFiltersChanged(Map<String, dynamic> filters) {
    setState(() {
      _activeFilters = filters;
    });
  }

  // دالة فلترة المصروفات
  List<ExpenseModel> _filterExpenses(List<ExpenseModel> expenses) {
    List<ExpenseModel> filtered = expenses;

    // فلترة حسب التاريخ
    filtered = filtered.where((expense) {
      DateTime expenseDate = DateTime.parse(expense.expenseDate);
      return (fromDate.isBefore(expenseDate) ||
              fromDate.isAtSameMomentAs(expenseDate)) &&
          (toDate.isAfter(expenseDate) || toDate.isAtSameMomentAs(expenseDate));
    }).toList();

    // فلترة حسب البحث النصي
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((expense) {
        return expense.expanseFor
                .toLowerCase()
                .contains(_searchQuery.toLowerCase()) ||
            expense.category
                .toLowerCase()
                .contains(_searchQuery.toLowerCase()) ||
            expense.note.toLowerCase().contains(_searchQuery.toLowerCase());
      }).toList();
    }

    // فلترة حسب الفلاتر المتقدمة
    if (_activeFilters.isNotEmpty) {
      if (_activeFilters['category'] != null) {
        filtered = filtered
            .where((expense) => expense.category == _activeFilters['category'])
            .toList();
      }

      if (_activeFilters['paymentType'] != null) {
        filtered = filtered
            .where((expense) =>
                expense.paymentType == _activeFilters['paymentType'])
            .toList();
      }
    }

    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    totalExpense = 0;
    return Consumer(builder: (context, ref, __) {
      final expenseData = ref.watch(expenseProvider);

      return Scaffold(
        backgroundColor: kMainColor,
        appBar: AppBar(
          backgroundColor: kMainColor,
          title: Text(
            lang.S.of(context).expenseReport,
            style: GoogleFonts.cairo(
              color: Colors.white,
              fontSize: 20.0,
              fontWeight: FontWeight.w600,
            ),
          ),
          iconTheme: const IconThemeData(color: Colors.white),
          centerTitle: true,
          elevation: 0.0,
          actions: [
            // زر الفلاتر المتقدمة
            IconButton(
              onPressed: () {
                setState(() {
                  _showAdvancedFilters = !_showAdvancedFilters;
                });
              },
              icon: Icon(
                _showAdvancedFilters
                    ? Icons.filter_list_off
                    : Icons.filter_list,
                color: Colors.white,
              ),
            ),
          ],
        ),
        body: Container(
          alignment: Alignment.topCenter,
          decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                  topRight: Radius.circular(30), topLeft: Radius.circular(30))),
          child: Column(
            children: [
              // ويدجت البحث
              ExpenseSearchWidget(
                onSearchChanged: _onSearchChanged,
                hintText: 'البحث في المصروفات...',
              ),

              // الفلاتر السريعة
              QuickExpenseFilterWidget(
                onQuickFilterChanged: _onQuickFilterChanged,
                selectedFilter: _selectedQuickFilter,
              ),

              // الفلاتر المتقدمة (إذا كانت مفعلة)
              if (_showAdvancedFilters)
                ExpenseFilterWidget(
                  onFiltersChanged: _onFiltersChanged,
                  initialFilters: _activeFilters,
                ),

              // محتوى الشاشة الرئيسي
              Expanded(
                child: SingleChildScrollView(
                  child: Padding(
                    padding: const EdgeInsets.all(10.0),
                    child: Column(
                      children: [
                        // حقول التاريخ الأصلية (مخفية أو مبسطة)
                        if (!_showAdvancedFilters)
                          Padding(
                            padding: const EdgeInsets.only(
                                right: 10.0, left: 10.0, top: 10, bottom: 10),
                            child: Row(
                              children: [
                                Expanded(
                                  child: AppTextField(
                                    textFieldType: TextFieldType.NAME,
                                    readOnly: true,
                                    controller: fromDateTextEditingController,
                                    decoration: InputDecoration(
                                      floatingLabelBehavior:
                                          FloatingLabelBehavior.always,
                                      labelText: lang.S.of(context).formDate,
                                      border: const OutlineInputBorder(),
                                      suffixIcon: IconButton(
                                        onPressed: () async {
                                          final DateTime? picked =
                                              await showDatePicker(
                                            initialDate: DateTime.now(),
                                            firstDate: DateTime(2015, 8),
                                            lastDate: DateTime(2101),
                                            context: context,
                                          );
                                          setState(() {
                                            fromDateTextEditingController.text =
                                                DateFormat.yMMMd().format(
                                                    picked ?? DateTime.now());
                                            fromDate = picked!;
                                            totalExpense = 0;
                                          });
                                        },
                                        icon: const Icon(FeatherIcons.calendar),
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 10),
                                Expanded(
                                  child: AppTextField(
                                    textFieldType: TextFieldType.NAME,
                                    readOnly: true,
                                    controller: toDateTextEditingController,
                                    decoration: InputDecoration(
                                      floatingLabelBehavior:
                                          FloatingLabelBehavior.always,
                                      labelText: lang.S.of(context).toDate,
                                      border: const OutlineInputBorder(),
                                      suffixIcon: IconButton(
                                        onPressed: () async {
                                          final DateTime? picked =
                                              await showDatePicker(
                                            initialDate: toDate,
                                            firstDate: DateTime(2015, 8),
                                            lastDate: DateTime(2101),
                                            context: context,
                                          );
                                          setState(() {
                                            toDateTextEditingController.text =
                                                DateFormat.yMMMd().format(
                                                    picked ?? DateTime.now());
                                            picked!.isToday
                                                ? toDate = DateTime.now()
                                                : toDate = picked;
                                            totalExpense = 0;
                                          });
                                        },
                                        icon: const Icon(FeatherIcons.calendar),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        const SizedBox(height: 10),

                        ///__________expense_data_table____________________________________________

                        expenseData.when(data: (mainData) {
                          if (mainData.isNotEmpty) {
                            final List<ExpenseModel> data =
                                mainData.reversed.toList();

                            // تطبيق الفلاتر الجديدة
                            final filteredData = _filterExpenses(data);

                            // حساب المجموع
                            totalExpense = 0;
                            for (var expense in filteredData) {
                              totalExpense +=
                                  double.tryParse(expense.amount) ?? 0;
                            }

                            if (filteredData.isEmpty) {
                              return Padding(
                                padding: const EdgeInsets.all(20),
                                child: Center(
                                  child: Column(
                                    children: [
                                      Icon(
                                        Icons.search_off,
                                        size: 64,
                                        color: Colors.grey.shade400,
                                      ),
                                      const SizedBox(height: 16),
                                      Text(
                                        'لا توجد مصروفات تطابق البحث',
                                        style: GoogleFonts.cairo(
                                          fontSize: 16,
                                          color: Colors.grey.shade600,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            }

                            return Column(
                              children: [
                                // ويدجت الإحصائيات
                                ExpenseStatsWidget(
                                  expenses: filteredData,
                                  period: _selectedQuickFilter == 'all'
                                      ? 'الكل'
                                      : _selectedQuickFilter == 'today'
                                          ? 'اليوم'
                                          : _selectedQuickFilter == 'week'
                                              ? 'هذا الأسبوع'
                                              : _selectedQuickFilter == 'month'
                                                  ? 'هذا الشهر'
                                                  : 'الكل',
                                ),

                                // إحصائيات الفئات
                                ExpenseCategoryStatsWidget(
                                    expenses: filteredData),

                                // إحصائيات طرق الدفع
                                ExpensePaymentStatsWidget(
                                    expenses: filteredData),

                                // قائمة المصروفات بالتصميم الجديد
                                ListView.builder(
                                  shrinkWrap: true,
                                  physics: const NeverScrollableScrollPhysics(),
                                  itemCount: filteredData.length,
                                  itemBuilder: (context, index) {
                                    return ExpenseCard(
                                      expense: filteredData[index],
                                      onTap: () {
                                        // يمكن إضافة وظيفة عرض التفاصيل هنا
                                      },
                                      onEdit: () {
                                        // يمكن إضافة وظيفة التعديل هنا
                                      },
                                      onDelete: () {
                                        // يمكن إضافة وظيفة الحذف هنا
                                      },
                                    );
                                  },
                                ),
                              ],
                            );
                          } else {
                            return Padding(
                              padding: const EdgeInsets.all(20),
                              child: Center(
                                child: Column(
                                  children: [
                                    Icon(
                                      Icons.receipt_long_outlined,
                                      size: 64,
                                      color: Colors.grey.shade400,
                                    ),
                                    const SizedBox(height: 16),
                                    Text(
                                      lang.S.of(context).noDataAvailable,
                                      style: GoogleFonts.cairo(
                                        fontSize: 16,
                                        color: Colors.grey.shade600,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          }
                        }, error: (Object error, StackTrace? stackTrace) {
                          return Padding(
                            padding: const EdgeInsets.all(20),
                            child: Center(
                              child: Column(
                                children: [
                                  Icon(
                                    Icons.error_outline,
                                    size: 64,
                                    color: Colors.red.shade400,
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    'حدث خطأ في تحميل البيانات',
                                    style: GoogleFonts.cairo(
                                      fontSize: 16,
                                      color: Colors.red.shade600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        }, loading: () {
                          return const Padding(
                            padding: EdgeInsets.all(20),
                            child: Center(
                              child: CircularProgressIndicator(),
                            ),
                          );
                        }),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        bottomNavigationBar: Container(
          color: Colors.white,
          child: Padding(
            padding: const EdgeInsets.all(10.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ///_________total______________________________________________
                Container(
                  height: 50,
                  padding: const EdgeInsets.all(10),
                  decoration: const BoxDecoration(color: kDarkWhite),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        lang.S.of(context).totalExpense,
                      ),
                      Text('$currency${myFormat.format(totalExpense)}')
                    ],
                  ),
                ),

                ///________button________________________________________________
                ButtonGlobalWithoutIcon(
                  buttontext: lang.S.of(context).addExpense,
                  buttonDecoration:
                      kButtonDecoration.copyWith(color: kMainColor),
                  onPressed: () {
                    const AddExpense().launch(context);
                  },
                  buttonTextColor: Colors.white,
                ),
              ],
            ),
          ),
        ),
      );
    });
  }
}
