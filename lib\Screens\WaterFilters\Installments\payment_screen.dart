import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/model/water_filter_models.dart';
import 'package:mobile_pos/services/water_filter_service.dart';

class PaymentScreen extends StatefulWidget {
  final WaterFilterInstallment installment;

  const PaymentScreen({super.key, required this.installment});

  @override
  State<PaymentScreen> createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();

  String _selectedPaymentMethod = 'cash';
  DateTime _paymentDate = DateTime.now();
  bool _isLoading = false;

  final List<Map<String, dynamic>> _paymentMethods = [
    {'id': 'cash', 'name': 'نقدي', 'icon': Icons.money},
    {
      'id': 'bank_transfer',
      'name': 'تحويل بنكي',
      'icon': Icons.account_balance
    },
    {'id': 'check', 'name': 'شيك', 'icon': Icons.receipt_long},
    {'id': 'credit_card', 'name': 'بطاقة ائتمان', 'icon': Icons.credit_card},
  ];

  @override
  void initState() {
    super.initState();
    _amountController.text = widget.installment.amount.toStringAsFixed(2);
  }

  @override
  void dispose() {
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _selectPaymentDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _paymentDate,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: kMainColor,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (date != null) {
      setState(() => _paymentDate = date);
    }
  }

  Future<void> _processPayment() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() => _isLoading = true);

    try {
      final paidAmount = double.parse(_amountController.text);

      // تحديث بيانات القسط
      final updatedInstallment = WaterFilterInstallment(
        id: widget.installment.id,
        systemId: widget.installment.systemId,
        installmentNumber: widget.installment.installmentNumber,
        amount: widget.installment.amount,
        dueDate: widget.installment.dueDate,
        status: InstallmentStatus.paid,
        paidDate: _paymentDate,
        paidAmount: paidAmount,
        paymentMethod: _selectedPaymentMethod,
        notes: _notesController.text.trim().isEmpty
            ? widget.installment.notes
            : _notesController.text.trim(),
        createdAt: widget.installment.createdAt,
        updatedAt: DateTime.now(),
      );

      // حفظ التحديث
      final success = await WaterFilterService.updateData(
        'Installments/${widget.installment.id}',
        updatedInstallment.toJson(),
      );

      if (success) {
        // تحديث المبلغ المدفوع في النظام
        await _updateSystemPaidAmount(paidAmount);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم تسجيل الدفع بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context, true);
        }
      } else {
        throw Exception('فشل في تسجيل الدفع');
      }
    } catch (e) {
      debugPrint('خطأ في تسجيل الدفع: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تسجيل الدفع: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _updateSystemPaidAmount(double paidAmount) async {
    try {
      // الحصول على بيانات النظام
      final systemData = await WaterFilterService.getData(
          'Systems/${widget.installment.systemId}');
      if (systemData.isNotEmpty) {
        final system = WaterFilterSystem.fromJson(systemData);

        // تحديث المبلغ المدفوع والمتبقي
        final newPaidAmount = system.paidAmount + paidAmount;
        final newRemainingAmount = system.totalCost - newPaidAmount;

        final updatedSystemData = {
          'paidAmount': newPaidAmount,
          'remainingAmount': newRemainingAmount,
        };

        await WaterFilterService.updateData(
          'Systems/${widget.installment.systemId}',
          updatedSystemData,
        );
      }
    } catch (e) {
      debugPrint('خطأ في تحديث بيانات النظام: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        title: Text(
          'دفع القسط',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        child: Form(
          key: _formKey,
          child: ListView(
            padding: const EdgeInsets.all(20),
            children: [
              // معلومات القسط
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'معلومات القسط',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: kMainColor,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: _buildInfoRow(
                            'رقم القسط',
                            '${widget.installment.installmentNumber}',
                          ),
                        ),
                        Expanded(
                          child: _buildInfoRow(
                            'المبلغ المطلوب',
                            '${widget.installment.amount.toStringAsFixed(2)} ج.م',
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    _buildInfoRow(
                      'تاريخ الاستحقاق',
                      '${widget.installment.dueDate.day}/${widget.installment.dueDate.month}/${widget.installment.dueDate.year}',
                    ),
                    if (widget.installment.isOverdue) ...[
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.red.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.red.shade200),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.warning_amber,
                              color: Colors.red.shade600,
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'متأخر ${widget.installment.daysOverdue} يوم',
                              style: GoogleFonts.cairo(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: Colors.red.shade600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),

              const SizedBox(height: 20),

              // المبلغ المدفوع
              Text(
                'المبلغ المدفوع',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: kMainColor,
                ),
              ),
              const SizedBox(height: 8),
              TextFormField(
                controller: _amountController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  hintText: 'أدخل المبلغ المدفوع',
                  hintStyle: GoogleFonts.cairo(color: Colors.grey.shade600),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: Colors.grey.shade300),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: Colors.grey.shade300),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: kMainColor),
                  ),
                  suffixText: 'ج.م',
                  suffixStyle: GoogleFonts.cairo(color: Colors.grey.shade600),
                ),
                style: GoogleFonts.cairo(),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال المبلغ المدفوع';
                  }
                  final amount = double.tryParse(value);
                  if (amount == null || amount <= 0) {
                    return 'يرجى إدخال مبلغ صحيح';
                  }
                  if (amount > widget.installment.amount) {
                    return 'المبلغ يتجاوز قيمة القسط';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 20),

              // طريقة الدفع
              Text(
                'طريقة الدفع',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: kMainColor,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: _paymentMethods.map((method) {
                    return RadioListTile<String>(
                      value: method['id'],
                      groupValue: _selectedPaymentMethod,
                      onChanged: (value) {
                        setState(() => _selectedPaymentMethod = value!);
                      },
                      title: Row(
                        children: [
                          Icon(
                            method['icon'],
                            color: kMainColor,
                            size: 20,
                          ),
                          const SizedBox(width: 12),
                          Text(
                            method['name'],
                            style: GoogleFonts.cairo(),
                          ),
                        ],
                      ),
                      activeColor: kMainColor,
                    );
                  }).toList(),
                ),
              ),

              const SizedBox(height: 20),

              // تاريخ الدفع
              Text(
                'تاريخ الدفع',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: kMainColor,
                ),
              ),
              const SizedBox(height: 8),
              InkWell(
                onTap: _selectPaymentDate,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 16,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.calendar_today,
                        color: kMainColor,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        '${_paymentDate.day}/${_paymentDate.month}/${_paymentDate.year}',
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          color: Colors.black87,
                        ),
                      ),
                      const Spacer(),
                      Icon(
                        Icons.arrow_drop_down,
                        color: Colors.grey.shade600,
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 20),

              // ملاحظات
              Text(
                'ملاحظات الدفع (اختياري)',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: kMainColor,
                ),
              ),
              const SizedBox(height: 8),
              TextFormField(
                controller: _notesController,
                maxLines: 3,
                decoration: InputDecoration(
                  hintText: 'أدخل أي ملاحظات حول الدفع...',
                  hintStyle: GoogleFonts.cairo(color: Colors.grey.shade600),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: Colors.grey.shade300),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: Colors.grey.shade300),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(color: kMainColor),
                  ),
                ),
                style: GoogleFonts.cairo(),
              ),

              const SizedBox(height: 30),

              // زر تأكيد الدفع
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _processPayment,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 2,
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        )
                      : Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(Icons.payment, size: 20),
                            const SizedBox(width: 8),
                            Text(
                              'تأكيد الدفع',
                              style: GoogleFonts.cairo(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                ),
              ),

              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$label: ',
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: GoogleFonts.cairo(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
