import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/model/device_session_model.dart';
import 'package:mobile_pos/services/device_session_service.dart';

/// شاشة إدارة الأجهزة المتصلة
class DeviceManagementScreen extends StatefulWidget {
  const DeviceManagementScreen({super.key});

  @override
  State<DeviceManagementScreen> createState() => _DeviceManagementScreenState();
}

class _DeviceManagementScreenState extends State<DeviceManagementScreen> {
  final DeviceSessionService _deviceSessionService = DeviceSessionService();
  List<DeviceSessionModel> _activeSessions = [];
  bool _isLoading = true;
  String? _currentDeviceId;

  @override
  void initState() {
    super.initState();
    _loadActiveSessions();
    _getCurrentDeviceId();
  }

  Future<void> _getCurrentDeviceId() async {
    try {
      _currentDeviceId = await DeviceSessionModel.getDeviceId();
    } catch (e) {
      debugPrint('خطأ في الحصول على معرف الجهاز الحالي: $e');
    }
  }

  Future<void> _loadActiveSessions() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser != null) {
        final sessions = await _deviceSessionService.getActiveUserSessions(currentUser.uid);
        setState(() {
          _activeSessions = sessions;
        });
      }
    } catch (e) {
      debugPrint('خطأ في تحميل الجلسات النشطة: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الأجهزة المتصلة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _endSession(DeviceSessionModel session) async {
    try {
      EasyLoading.show(status: 'جاري إنهاء الجلسة...');
      
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser != null) {
        await _deviceSessionService.endDeviceSession(currentUser.uid, session.sessionId);
        
        EasyLoading.showSuccess('تم إنهاء الجلسة بنجاح');
        
        // إعادة تحميل القائمة
        await _loadActiveSessions();
      }
    } catch (e) {
      debugPrint('خطأ في إنهاء الجلسة: $e');
      EasyLoading.showError('فشل في إنهاء الجلسة');
    }
  }

  Future<void> _endAllOtherSessions() async {
    try {
      EasyLoading.show(status: 'جاري إنهاء جميع الجلسات الأخرى...');
      
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser != null) {
        // إنهاء جميع الجلسات عدا الجلسة الحالية
        for (final session in _activeSessions) {
          if (session.deviceId != _currentDeviceId) {
            await _deviceSessionService.endDeviceSession(currentUser.uid, session.sessionId);
          }
        }
        
        EasyLoading.showSuccess('تم إنهاء جميع الجلسات الأخرى بنجاح');
        
        // إعادة تحميل القائمة
        await _loadActiveSessions();
      }
    } catch (e) {
      debugPrint('خطأ في إنهاء الجلسات: $e');
      EasyLoading.showError('فشل في إنهاء الجلسات');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'إدارة الأجهزة المتصلة',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: kMainColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _loadActiveSessions,
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : _activeSessions.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.devices_other,
                        size: 64,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'لا توجد أجهزة متصلة',
                        style: GoogleFonts.poppins(
                          fontSize: 18,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                )
              : Column(
                  children: [
                    // معلومات عامة
                    Container(
                      width: double.infinity,
                      margin: const EdgeInsets.all(16),
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: kMainColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: kMainColor.withOpacity(0.3)),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'معلومات الأجهزة',
                            style: GoogleFonts.poppins(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: kMainColor,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'عدد الأجهزة المتصلة: ${_activeSessions.length}',
                            style: GoogleFonts.poppins(fontSize: 14),
                          ),
                          Text(
                            'الحد الأقصى المسموح: ${DeviceSessionService.maxDevicesPerUser}',
                            style: GoogleFonts.poppins(fontSize: 14),
                          ),
                          if (_activeSessions.length > 1) ...[
                            const SizedBox(height: 12),
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton.icon(
                                onPressed: _endAllOtherSessions,
                                icon: const Icon(Icons.logout, size: 18),
                                label: const Text('إنهاء جميع الجلسات الأخرى'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.orange,
                                  foregroundColor: Colors.white,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                    
                    // قائمة الأجهزة
                    Expanded(
                      child: ListView.builder(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        itemCount: _activeSessions.length,
                        itemBuilder: (context, index) {
                          final session = _activeSessions[index];
                          final isCurrentDevice = session.deviceId == _currentDeviceId;
                          
                          return Card(
                            margin: const EdgeInsets.only(bottom: 12),
                            elevation: 2,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                              side: isCurrentDevice
                                  ? BorderSide(color: kMainColor, width: 2)
                                  : BorderSide.none,
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Icon(
                                        _getPlatformIcon(session.platform),
                                        color: kMainColor,
                                        size: 24,
                                      ),
                                      const SizedBox(width: 12),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              session.deviceName,
                                              style: GoogleFonts.poppins(
                                                fontSize: 16,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                            Text(
                                              session.platform,
                                              style: GoogleFonts.poppins(
                                                fontSize: 12,
                                                color: Colors.grey[600],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      if (isCurrentDevice)
                                        Container(
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 8,
                                            vertical: 4,
                                          ),
                                          decoration: BoxDecoration(
                                            color: Colors.green,
                                            borderRadius: BorderRadius.circular(12),
                                          ),
                                          child: Text(
                                            'الجهاز الحالي',
                                            style: GoogleFonts.poppins(
                                              fontSize: 10,
                                              color: Colors.white,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                  const SizedBox(height: 12),
                                  Row(
                                    children: [
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'وقت تسجيل الدخول:',
                                              style: GoogleFonts.poppins(
                                                fontSize: 12,
                                                color: Colors.grey[600],
                                              ),
                                            ),
                                            Text(
                                              DateFormat('yyyy-MM-dd HH:mm')
                                                  .format(session.loginTime),
                                              style: GoogleFonts.poppins(fontSize: 12),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'آخر نشاط:',
                                              style: GoogleFonts.poppins(
                                                fontSize: 12,
                                                color: Colors.grey[600],
                                              ),
                                            ),
                                            Text(
                                              DateFormat('yyyy-MM-dd HH:mm')
                                                  .format(session.lastActivity),
                                              style: GoogleFonts.poppins(fontSize: 12),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                  if (!isCurrentDevice) ...[
                                    const SizedBox(height: 12),
                                    SizedBox(
                                      width: double.infinity,
                                      child: ElevatedButton.icon(
                                        onPressed: () => _endSession(session),
                                        icon: const Icon(Icons.logout, size: 18),
                                        label: const Text('إنهاء الجلسة'),
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: Colors.red,
                                          foregroundColor: Colors.white,
                                        ),
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
    );
  }

  IconData _getPlatformIcon(String platform) {
    switch (platform.toLowerCase()) {
      case 'android':
        return Icons.android;
      case 'ios':
        return Icons.phone_iphone;
      case 'windows':
        return Icons.computer;
      case 'web':
        return Icons.web;
      case 'macos':
        return Icons.laptop_mac;
      case 'linux':
        return Icons.desktop_windows;
      default:
        return Icons.device_unknown;
    }
  }
}
