import 'dart:async';
import 'dart:convert';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/foundation.dart';
import 'package:mobile_pos/currency.dart';
import 'package:mobile_pos/models/system_notification_model.dart';
import 'package:mobile_pos/services/hidden_notifications_service.dart';
import 'package:mobile_pos/services/local_notification_service.dart';
import 'package:mobile_pos/services/user_log_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// خدمة الإشعارات بين المستخدمين
class SystemNotificationService {
  // مرجع قاعدة البيانات للإشعارات
  static final DatabaseReference _notificationsRef =
      FirebaseDatabase.instance.ref('SystemNotifications');

  // قائمة الإشعارات المحلية
  static List<SystemNotificationModel> _notifications = [];

  // مستمع الإشعارات
  static StreamSubscription<DatabaseEvent>? _notificationsSubscription;

  /// تهيئة الخدمة
  static Future<void> initialize() async {
    try {
      debugPrint('تهيئة خدمة الإشعارات بين المستخدمين...');

      // تهيئة خدمة الإشعارات المخفية
      await HiddenNotificationsService.initialize();

      // تحميل الإشعارات المحلية
      await _loadNotifications();

      // بدء الاستماع للإشعارات الجديدة
      _startListeningForNotifications();

      debugPrint('تم تهيئة خدمة الإشعارات بين المستخدمين بنجاح');
    } catch (e) {
      debugPrint('خطأ في تهيئة خدمة الإشعارات بين المستخدمين: $e');
    }
  }

  /// بدء الاستماع للإشعارات الجديدة
  static void _startListeningForNotifications() {
    try {
      // إلغاء الاشتراك السابق إذا كان موجودًا
      _notificationsSubscription?.cancel();

      // الاستماع للإشعارات الجديدة
      _notificationsSubscription =
          _notificationsRef.onChildAdded.listen((event) {
        if (event.snapshot.value != null) {
          try {
            // تحويل البيانات إلى نموذج الإشعار
            final data = jsonDecode(jsonEncode(event.snapshot.value));
            final notification = SystemNotificationModel.fromJson(data);

            // إضافة الإشعار إلى القائمة المحلية إذا لم يكن موجودًا بالفعل
            if (!_notifications.any((n) => n.id == notification.id)) {
              _notifications.add(notification);

              // حفظ الإشعارات المحلية
              _saveNotifications();

              // إرسال إشعار محلي إذا كان الإشعار جديدًا
              _sendLocalNotification(notification);

              debugPrint('تم استلام إشعار جديد: ${notification.title}');
            }
          } catch (e) {
            debugPrint('خطأ في معالجة الإشعار الجديد: $e');
          }
        }
      });

      debugPrint('تم بدء الاستماع للإشعارات الجديدة');
    } catch (e) {
      debugPrint('خطأ في بدء الاستماع للإشعارات الجديدة: $e');
    }
  }

  /// إرسال إشعار محلي
  static void _sendLocalNotification(SystemNotificationModel notification) {
    try {
      // إرسال إشعار محلي فقط إذا كان الإشعار ليس من المستخدم الحالي
      if (notification.senderId != FirebaseAuth.instance.currentUser?.uid) {
        LocalNotificationService.sendLocalNotification(
          title: notification.title,
          body: notification.message,
          data: notification.data,
        );
      }
    } catch (e) {
      debugPrint('خطأ في إرسال الإشعار المحلي: $e');
    }
  }

  /// تحميل الإشعارات المحلية
  static Future<void> _loadNotifications() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = FirebaseAuth.instance.currentUser?.uid;

      if (userId == null) {
        debugPrint('لم يتم تسجيل الدخول، لا يمكن تحميل الإشعارات');
        return;
      }

      final notificationsJson = prefs.getString('system_notifications_$userId');

      if (notificationsJson != null) {
        final List<dynamic> notificationsList = jsonDecode(notificationsJson);
        _notifications = notificationsList
            .map((json) => SystemNotificationModel.fromJson(json))
            .toList();

        // ترتيب الإشعارات حسب التاريخ (الأحدث أولاً)
        _notifications.sort((a, b) => b.timestamp.compareTo(a.timestamp));

        debugPrint('تم تحميل ${_notifications.length} إشعار من التخزين المحلي');
      } else {
        debugPrint('لا توجد إشعارات محلية مخزنة');
      }
    } catch (e) {
      debugPrint('خطأ في تحميل الإشعارات المحلية: $e');
    }
  }

  /// حفظ الإشعارات المحلية
  static Future<void> _saveNotifications() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = FirebaseAuth.instance.currentUser?.uid;

      if (userId == null) {
        debugPrint('لم يتم تسجيل الدخول، لا يمكن حفظ الإشعارات');
        return;
      }

      final notificationsJson = jsonEncode(
          _notifications.map((notification) => notification.toJson()).toList());

      await prefs.setString('system_notifications_$userId', notificationsJson);

      debugPrint('تم حفظ ${_notifications.length} إشعار في التخزين المحلي');
    } catch (e) {
      debugPrint('خطأ في حفظ الإشعارات المحلية: $e');
    }
  }

  /// إنشاء إشعار جديد
  static Future<bool> createNotification({
    required String type,
    required String title,
    required String message,
    required Map<String, dynamic> data,
  }) async {
    try {
      // الحصول على معرف المستخدم الحالي
      final userId = FirebaseAuth.instance.currentUser?.uid;

      if (userId == null) {
        debugPrint('لم يتم تسجيل الدخول، لا يمكن إنشاء إشعار');
        return false;
      }

      // الحصول على اسم المستخدم الحالي
      final userLogService = UserLogService();
      final userName = await userLogService.getCurrentUserName() ?? 'مستخدم';

      // إنشاء معرف فريد للإشعار
      final notificationId = '${DateTime.now().millisecondsSinceEpoch}_$userId';

      // إنشاء نموذج الإشعار
      final notification = SystemNotificationModel(
        id: notificationId,
        senderId: userId,
        senderName: userName,
        type: type,
        title: title,
        message: message,
        data: data,
        timestamp: DateTime.now(),
      );

      // حفظ الإشعار في قاعدة البيانات
      await _notificationsRef.child(notificationId).set(notification.toJson());

      debugPrint('تم إنشاء إشعار جديد: $title');

      return true;
    } catch (e) {
      debugPrint('خطأ في إنشاء إشعار جديد: $e');
      return false;
    }
  }

  /// الحصول على قائمة الإشعارات
  static List<SystemNotificationModel> getNotifications({
    bool unreadOnly = false,
    String? type,
  }) {
    try {
      // نسخة من قائمة الإشعارات
      List<SystemNotificationModel> filteredNotifications =
          List.from(_notifications);

      // استبعاد الإشعارات المخفية للمستخدم الحالي
      final hiddenNotificationIds =
          HiddenNotificationsService.getHiddenNotificationIds();
      if (hiddenNotificationIds.isNotEmpty) {
        filteredNotifications = filteredNotifications
            .where((notification) =>
                !hiddenNotificationIds.contains(notification.id))
            .toList();
      }

      // ترتيب الإشعارات حسب التاريخ (الأحدث أولاً)
      filteredNotifications.sort((a, b) => b.timestamp.compareTo(a.timestamp));

      // تصفية الإشعارات حسب حالة القراءة
      if (unreadOnly) {
        filteredNotifications = filteredNotifications
            .where((notification) => !notification.isRead)
            .toList();
      }

      // تصفية الإشعارات حسب النوع
      if (type != null) {
        filteredNotifications = filteredNotifications
            .where((notification) => notification.type == type)
            .toList();
      }

      return filteredNotifications;
    } catch (e) {
      debugPrint('خطأ في الحصول على قائمة الإشعارات: $e');
      return [];
    }
  }

  /// تعليم الإشعار كمقروء
  static Future<bool> markAsRead(String notificationId) async {
    try {
      // البحث عن الإشعار في القائمة المحلية
      final index = _notifications
          .indexWhere((notification) => notification.id == notificationId);

      if (index == -1) {
        debugPrint('الإشعار غير موجود: $notificationId');
        return false;
      }

      // تحديث حالة القراءة في القائمة المحلية
      _notifications[index] = _notifications[index].copyWith(isRead: true);

      // حفظ الإشعارات المحلية
      await _saveNotifications();

      // تحديث حالة القراءة في قاعدة البيانات
      await _notificationsRef.child(notificationId).update({'isRead': true});

      debugPrint('تم تعليم الإشعار كمقروء: $notificationId');

      return true;
    } catch (e) {
      debugPrint('خطأ في تعليم الإشعار كمقروء: $e');
      return false;
    }
  }

  /// تعليم جميع الإشعارات كمقروءة
  static Future<bool> markAllAsRead() async {
    try {
      // تحديث حالة القراءة في القائمة المحلية
      _notifications = _notifications
          .map((notification) => notification.copyWith(isRead: true))
          .toList();

      // حفظ الإشعارات المحلية
      await _saveNotifications();

      // تحديث حالة القراءة في قاعدة البيانات
      for (final notification in _notifications) {
        await _notificationsRef.child(notification.id).update({'isRead': true});
      }

      debugPrint('تم تعليم جميع الإشعارات كمقروءة');

      return true;
    } catch (e) {
      debugPrint('خطأ في تعليم جميع الإشعارات كمقروءة: $e');
      return false;
    }
  }

  /// الحصول على عدد الإشعارات غير المقروءة
  static int getUnreadCount() {
    try {
      // استبعاد الإشعارات المخفية للمستخدم الحالي
      final hiddenNotificationIds =
          HiddenNotificationsService.getHiddenNotificationIds();

      return _notifications
          .where((notification) =>
              !notification.isRead &&
              !hiddenNotificationIds.contains(notification.id))
          .length;
    } catch (e) {
      debugPrint('خطأ في الحصول على عدد الإشعارات غير المقروءة: $e');
      return 0;
    }
  }

  /// إنشاء إشعار مبيعات آجلة
  static Future<bool> createCreditSaleNotification({
    required String invoiceNumber,
    required String customerName,
    required double amount,
    required DateTime date,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      // إنشاء بيانات الإشعار
      final data = {
        'invoiceNumber': invoiceNumber,
        'customerName': customerName,
        'amount': amount,
        'date': date.toIso8601String(),
        ...?additionalData,
      };

      // الحصول على اسم المستخدم الحالي
      final userLogService = UserLogService();
      final userName = await userLogService.getCurrentUserName() ?? 'مستخدم';

      // إنشاء الإشعار
      return await createNotification(
        type: SystemNotificationTypes.creditSale,
        title: SystemNotificationTypes.getTitle(
            SystemNotificationTypes.creditSale),
        message:
            'قام $userName بإنشاء فاتورة مبيعات آجلة برقم $invoiceNumber للعميل $customerName بمبلغ $amount $currency',
        data: data,
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء إشعار مبيعات آجلة: $e');
      return false;
    }
  }

  /// إنشاء إشعار مشتريات آجلة
  static Future<bool> createCreditPurchaseNotification({
    required String invoiceNumber,
    required String supplierName,
    required double amount,
    required DateTime date,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      // إنشاء بيانات الإشعار
      final data = {
        'invoiceNumber': invoiceNumber,
        'supplierName': supplierName,
        'amount': amount,
        'date': date.toIso8601String(),
        ...?additionalData,
      };

      // الحصول على اسم المستخدم الحالي
      final userLogService = UserLogService();
      final userName = await userLogService.getCurrentUserName() ?? 'مستخدم';

      // إنشاء الإشعار
      return await createNotification(
        type: SystemNotificationTypes.creditPurchase,
        title: SystemNotificationTypes.getTitle(
            SystemNotificationTypes.creditPurchase),
        message:
            'قام $userName بإنشاء فاتورة مشتريات آجلة برقم $invoiceNumber من المورد $supplierName بمبلغ $amount $currency',
        data: data,
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء إشعار مشتريات آجلة: $e');
      return false;
    }
  }

  /// إنشاء إشعار مصروف
  static Future<bool> createExpenseNotification({
    required String expenseId,
    required String expenseFor,
    required double amount,
    required DateTime date,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      // إنشاء بيانات الإشعار
      final data = {
        'expenseId': expenseId,
        'expenseFor': expenseFor,
        'amount': amount,
        'date': date.toIso8601String(),
        ...?additionalData,
      };

      // الحصول على اسم المستخدم الحالي
      final userLogService = UserLogService();
      final userName = await userLogService.getCurrentUserName() ?? 'مستخدم';

      // إنشاء الإشعار
      return await createNotification(
        type: SystemNotificationTypes.expense,
        title:
            SystemNotificationTypes.getTitle(SystemNotificationTypes.expense),
        message:
            'قام $userName بإضافة مصروف جديد لـ $expenseFor بمبلغ $amount $currency',
        data: data,
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء إشعار مصروف: $e');
      return false;
    }
  }

  /// إنشاء إشعار سداد مديونية
  static Future<bool> createDuePaymentNotification({
    required String invoiceNumber,
    required String customerName,
    required double paidAmount,
    required double remainingAmount,
    required String paymentMethod,
    required DateTime date,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      // إنشاء بيانات الإشعار
      final data = {
        'invoiceNumber': invoiceNumber,
        'customerName': customerName,
        'paidAmount': paidAmount,
        'remainingAmount': remainingAmount,
        'paymentMethod': paymentMethod,
        'date': date.toIso8601String(),
        ...?additionalData,
      };

      // الحصول على اسم المستخدم الحالي
      final userLogService = UserLogService();
      final userName = await userLogService.getCurrentUserName() ?? 'مستخدم';

      // إنشاء الإشعار
      return await createNotification(
        type: SystemNotificationTypes.duePayment,
        title: SystemNotificationTypes.getTitle(
            SystemNotificationTypes.duePayment),
        message:
            'قام $userName باستلام دفعة بمبلغ $paidAmount $currency من العميل $customerName للفاتورة رقم $invoiceNumber، المبلغ المتبقي: $remainingAmount $currency',
        data: data,
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء إشعار سداد مديونية: $e');
      return false;
    }
  }

  /// إيقاف الخدمة
  static void dispose() {
    try {
      // إلغاء الاشتراك في الإشعارات
      _notificationsSubscription?.cancel();
      _notificationsSubscription = null;

      debugPrint('تم إيقاف خدمة الإشعارات بين المستخدمين');
    } catch (e) {
      debugPrint('خطأ في إيقاف خدمة الإشعارات بين المستخدمين: $e');
    }
  }
}
