# 🔗 التكامل مع النظام - وحدة الخزينة المتطورة

## 📊 **مصادر البيانات المتكاملة**

الخزينة الآن تتكامل مع جميع أجزاء النظام وتجمع البيانات من:

### 1. **المبيعات (Sales)**
```
constUserId/Sales/
├── [saleId]/
│   ├── saleDate
│   ├── totalAmount
│   ├── invoiceNumber
│   ├── paymentType
│   └── ...
```
**يتم تحويلها إلى:** معاملات إيراد في الخزينة

### 2. **المشتريات (Purchases)**
```
constUserId/Purchases/
├── [purchaseId]/
│   ├── purchaseDate
│   ├── totalAmount
│   ├── invoiceNumber
│   ├── paymentType
│   └── ...
```
**يتم تحويلها إلى:** معاملات مصروف في الخزينة

### 3. **المصروفات (Expenses)**
```
constUserId/Expenses/
├── [expenseId]/
│   ├── date
│   ├── amount
│   ├── category
│   ├── expenseName
│   ├── referenceNumber
│   └── ...
```
**يتم تحويلها إلى:** معاملات مصروف في الخزينة

### 4. **المديونية (Due Amounts)**
```
constUserId/Customers/
├── [customerId]/
│   ├── dueAmount  # مديونية العملاء (لنا)
│   └── ...

constUserId/Suppliers/
├── [supplierId]/
│   ├── dueAmount  # مديونية الموردين (علينا)
│   └── ...
```

## 🔄 **المزامنة التلقائية**

### **عند بدء تشغيل الخزينة:**
1. **مزامنة فورية** لجميع البيانات الموجودة
2. **بدء المراقبة المستمرة** للتغييرات

### **عند حدوث تغيير:**
- **مبيعة جديدة** ← تُضاف تلقائياً كإيراد للخزينة
- **مشترى جديد** ← يُضاف تلقائياً كمصروف للخزينة  
- **مصروف جديد** ← يُضاف تلقائياً للخزينة
- **تغيير في المديونية** ← تُحدث الإحصائيات فوراً

## 📱 **الواجهة المحدثة**

### **البطاقات الجديدة:**
1. **اليوم** - صافي المعاملات اليومية
2. **هذا الشهر** - صافي المعاملات الشهرية
3. **مديونية العملاء** - المبالغ المستحقة لنا
4. **مديونية الموردين** - المبالغ المستحقة علينا

### **الألوان:**
- 🟢 **أخضر**: الأرباح والإيرادات
- 🔴 **أحمر**: الخسائر والمصروفات
- 🔵 **أزرق**: مديونية العملاء
- 🟠 **برتقالي**: مديونية الموردين

## 🎯 **ربط الأيقونة**

تم ربط أيقونة "CashBox" في الشاشة الرئيسية بالخزينة الجديدة:

```dart
GridItems(
    title: lang.S.of(context).cashBox,
    icon: 'assets/images/test.svg',
    route: 'CashBox'),  // ← يفتح الخزينة الجديدة
```

**المسارات المتاحة:**
- `/CashBox` ← للتوافق مع الكود الموجود
- `/treasury` ← المسار الجديد

## 🔧 **كيفية عمل المزامنة**

### **1. المزامنة الأولية:**
```dart
// عند فتح الخزينة
final treasuryService = ref.read(treasuryServiceProvider);
treasuryService.syncAllData(); // مزامنة جميع البيانات
```

### **2. المراقبة المستمرة:**
```dart
// مراقبة تلقائية للتغييرات
treasuryService.startAutoSync();
```

### **3. منع التكرار:**
- كل معاملة لها معرف فريد: `sale_[id]`, `purchase_[id]`, `expense_[id]`
- التحقق من عدم وجود المعاملة قبل الإضافة
- تجنب المعاملات المكررة

## 📊 **الإحصائيات المتقدمة**

### **البيانات المتاحة:**
```dart
final stats = ref.watch(treasuryStatisticsProvider);

// الرصيد والمعاملات
stats['currentBalance']     // الرصيد الحالي
stats['totalIncome']        // إجمالي الإيرادات
stats['totalExpense']       // إجمالي المصروفات
stats['netAmount']          // صافي المبلغ

// إحصائيات اليوم
stats['todayIncome']        // إيرادات اليوم
stats['todayExpense']       // مصروفات اليوم
stats['todayNet']           // صافي اليوم
stats['todayTransactions']  // عدد معاملات اليوم

// إحصائيات الشهر
stats['monthIncome']        // إيرادات الشهر
stats['monthExpense']       // مصروفات الشهر
stats['monthNet']           // صافي الشهر
stats['monthTransactions']  // عدد معاملات الشهر

// المديونية
stats['customerDue']        // مديونية العملاء (لنا)
stats['supplierDue']        // مديونية الموردين (علينا)
stats['netDue']            // صافي المديونية
```

## 🎨 **عرض البيانات**

### **بطاقة مديونية العملاء:**
```dart
TreasuryCardWidget(
  title: 'مديونية العملاء',
  amount: '${stats['customerDue']} ريال',
  subtitle: 'لنا عند العملاء',
  color: Colors.blue,
  icon: FeatherIcons.users,
)
```

### **بطاقة مديونية الموردين:**
```dart
TreasuryCardWidget(
  title: 'مديونية الموردين', 
  amount: '${stats['supplierDue']} ريال',
  subtitle: 'علينا للموردين',
  color: Colors.orange,
  icon: FeatherIcons.truck,
)
```

## 🔍 **التقارير المحدثة**

### **تقارير تشمل:**
1. **المبيعات** - من نظام المبيعات
2. **المشتريات** - من نظام المشتريات  
3. **المصروفات** - من نظام المصروفات
4. **المعاملات اليدوية** - المضافة مباشرة للخزينة
5. **المديونية** - حالة المديونية الحالية

### **فلترة متقدمة:**
- حسب المصدر: `system_sync` أو `manual`
- حسب النوع: مبيعات، مشتريات، مصروفات، أخرى
- حسب التاريخ والفترة
- حسب المبلغ والفئة

## ⚡ **الأداء والتحسين**

### **تحسينات الأداء:**
- **مزامنة ذكية**: فقط البيانات الجديدة
- **تخزين مؤقت**: للبيانات المستخدمة بكثرة
- **مراقبة انتقائية**: فقط التغييرات المهمة
- **معالجة غير متزامنة**: لا تؤثر على الواجهة

### **إدارة الذاكرة:**
- إلغاء المستمعين عند الخروج
- تنظيف البيانات المؤقتة
- معالجة الأخطاء بشكل صحيح

## 🚀 **الاستخدام العملي**

### **للمطور:**
```dart
// الحصول على جميع البيانات
final treasuryService = TreasuryService();

// مزامنة المبيعات فقط
await treasuryService.syncFromSales();

// مزامنة المشتريات فقط  
await treasuryService.syncFromPurchases();

// مزامنة المصروفات فقط
await treasuryService.syncFromExpenses();

// الحصول على المديونية
final dueAmounts = await treasuryService.getDueAmounts();
```

### **للمستخدم:**
1. **فتح الخزينة** ← يرى جميع البيانات محدثة
2. **عمل مبيعة** ← تظهر فوراً في الخزينة
3. **إضافة مشترى** ← يُحدث الرصيد تلقائياً
4. **تسجيل مصروف** ← يظهر في التقارير فوراً

---

**ملاحظة**: الخزينة الآن مركز مالي شامل يجمع جميع العمليات المالية في مكان واحد مع مزامنة تلقائية وإحصائيات متقدمة! 💰📊
