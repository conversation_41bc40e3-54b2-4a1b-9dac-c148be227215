import 'dart:async';
import 'package:flutter/material.dart';

/// مدير دورة الحياة للتعامل مع الأخطاء وتحسين استقرار دورة الحياة
class LifecycleManager {
  /// التحقق من أن الـ Widget لا يزال مثبتًا قبل تحديث الحالة
  static bool isSafeToUpdateState(State state) {
    return state.mounted;
  }

  /// تنفيذ عملية آمنة مع التعامل مع الأخطاء
  static Future<T?> executeSafely<T>({
    required Future<T> Function() operation,
    required State state,
    Function(dynamic error)? onError,
    T? defaultValue,
  }) async {
    if (!isSafeToUpdateState(state)) {
      return defaultValue;
    }

    try {
      return await operation();
    } catch (e) {
      debugPrint('خطأ في تنفيذ العملية: $e');
      if (onError != null) {
        onError(e);
      }
      return defaultValue;
    }
  }

  /// إنشاء مؤقت دوري آمن
  static Timer createSafePeriodicTimer({
    required Duration duration,
    required void Function(Timer timer) callback,
    required State state,
  }) {
    return Timer.periodic(duration, (timer) {
      if (isSafeToUpdateState(state)) {
        callback(timer);
      } else {
        timer.cancel();
      }
    });
  }

  /// إلغاء المؤقت بشكل آمن
  static void cancelTimerSafely(Timer? timer) {
    timer?.cancel();
  }

  /// إلغاء الاشتراك بشكل آمن
  static void cancelSubscriptionSafely(StreamSubscription? subscription) {
    subscription?.cancel();
  }

  /// التحقق من تهيئة الخدمة قبل استخدامها
  static bool isServiceInitialized(bool isInitialized) {
    if (!isInitialized) {
      debugPrint('تحذير: محاولة استخدام خدمة غير مهيأة');
      return false;
    }
    return true;
  }
}

/// مزيج لإدارة دورة الحياة في الـ State
mixin SafeStateMixin<T extends StatefulWidget> on State<T> {
  final List<Timer> _timers = [];
  final List<StreamSubscription> _subscriptions = [];

  /// إنشاء مؤقت دوري آمن
  Timer createPeriodicTimer(
      Duration duration, void Function(Timer timer) callback) {
    final timer = LifecycleManager.createSafePeriodicTimer(
      duration: duration,
      callback: callback,
      state: this,
    );
    _timers.add(timer);
    return timer;
  }

  /// إنشاء مؤقت عادي آمن
  Timer createTimer(Duration duration, void Function() callback) {
    final timer = Timer(duration, () {
      if (mounted) {
        callback();
      }
    });
    _timers.add(timer);
    return timer;
  }

  /// الاشتراك في تدفق بشكل آمن
  StreamSubscription<E> subscribeToStream<E>(
    Stream<E> stream,
    void Function(E event) onData, {
    Function? onError,
    void Function()? onDone,
    bool? cancelOnError,
  }) {
    final subscription = stream.listen(
      (event) {
        if (mounted) {
          onData(event);
        }
      },
      onError: onError,
      onDone: onDone,
      cancelOnError: cancelOnError,
    );
    _subscriptions.add(subscription);
    return subscription;
  }

  /// تنفيذ عملية آمنة
  Future<R?> executeSafely<R>({
    required Future<R> Function() operation,
    Function(dynamic error)? onError,
    R? defaultValue,
  }) {
    return LifecycleManager.executeSafely(
      operation: operation,
      state: this,
      onError: onError,
      defaultValue: defaultValue,
    );
  }

  @override
  void dispose() {
    // إلغاء جميع المؤقتات
    for (final timer in _timers) {
      timer.cancel();
    }
    _timers.clear();

    // إلغاء جميع الاشتراكات
    for (final subscription in _subscriptions) {
      subscription.cancel();
    }
    _subscriptions.clear();

    super.dispose();
  }
}
