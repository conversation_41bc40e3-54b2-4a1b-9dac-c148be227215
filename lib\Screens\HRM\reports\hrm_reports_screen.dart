import 'package:flutter/material.dart';
import 'package:mobile_pos/constant.dart';

class HRMReportsScreen extends StatelessWidget {
  const HRMReportsScreen({super.key});

  static const String route = '/hrm/reports';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('تقارير الموارد البشرية'),
        backgroundColor: kMainColor,
        centerTitle: true,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        titleTextStyle: const TextStyle(
            color: Colors.white, fontSize: 20, fontWeight: FontWeight.bold),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تقارير الموارد البشرية',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'يمكنك عرض تقارير الموظفين والرواتب من هنا',
              style: TextStyle(
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 32),
            Expanded(
              child: GridView.count(
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                children: [
                  _buildReportCard(
                    context,
                    title: 'تقرير الرواتب',
                    icon: Icons.attach_money,
                    color: Colors.green,
                    onTap: () {
                      // عرض تقرير الرواتب
                    },
                  ),
                  _buildReportCard(
                    context,
                    title: 'تقرير الموظفين',
                    icon: Icons.people,
                    color: Colors.blue,
                    onTap: () {
                      // عرض تقرير الموظفين
                    },
                  ),
                  _buildReportCard(
                    context,
                    title: 'تقرير الحضور',
                    icon: Icons.calendar_today,
                    color: Colors.orange,
                    onTap: () {
                      // عرض تقرير الحضور
                    },
                  ),
                  _buildReportCard(
                    context,
                    title: 'تقرير الأداء',
                    icon: Icons.bar_chart,
                    color: Colors.purple,
                    onTap: () {
                      // عرض تقرير الأداء
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReportCard(
    BuildContext context, {
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Card(
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                color.withAlpha(179),
                color,
              ],
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 48,
                color: Colors.white,
              ),
              const SizedBox(height: 16),
              Text(
                title,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
