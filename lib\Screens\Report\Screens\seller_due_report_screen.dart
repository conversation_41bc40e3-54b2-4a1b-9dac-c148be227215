// شاشة تقرير المديونية الخاصة بالبائع الحالي
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/const_commas.dart';
import 'package:mobile_pos/model/due_transaction_model.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// مزود لاسم البائع الحالي
final currentSellerNameProvider = FutureProvider<String>((ref) async {
  try {
    // الحصول على معلومات المستخدم من التخزين المحلي
    final prefs = await SharedPreferences.getInstance();
    final userTitle = prefs.getString('userTitle') ?? '';

    if (userTitle.isNotEmpty) {
      return userTitle;
    }

    // إذا لم يكن هناك عنوان للمستخدم، استخدم اسم الشركة من الملف الشخصي
    final companyRef = FirebaseDatabase.instance
        .ref('$constUserId/Personal Information/companyName');
    final snapshot = await companyRef.get();
    if (snapshot.exists && snapshot.value != null) {
      return snapshot.value.toString();
    }

    // إذا لم يتم العثور على اسم، استخدم "المستخدم الرئيسي"
    return 'المستخدم الرئيسي';
  } catch (e) {
    debugPrint('خطأ في الحصول على اسم البائع الحالي: $e');
    return 'المستخدم الرئيسي';
  }
});

/// مزود لبيانات المديونية الخاصة بالبائع الحالي
final sellerDueTransactionsProvider =
    FutureProvider.family<List<DueTransactionModel>, String>(
        (ref, sellerName) async {
  List<DueTransactionModel> transactions = [];

  try {
    // الحصول على مرجع لجدول المديونية
    final dueRef =
        FirebaseDatabase.instance.ref('$constUserId/Due Transaction');
    final snapshot = await dueRef.get();

    if (snapshot.exists) {
      for (var child in snapshot.children) {
        final data = child.value as Map<dynamic, dynamic>?;
        if (data != null) {
          // التحقق من وجود حقل sellerName
          final transactionSellerName =
              data['sellerName'] as String? ?? 'غير معروف';

          // إضافة المعاملة فقط إذا كانت تخص البائع الحالي
          if (transactionSellerName == sellerName) {
            transactions.add(DueTransactionModel.fromJson(data));
          }
        }
      }
    }

    // ترتيب المعاملات من الأحدث للأقدم
    transactions.sort((a, b) => DateTime.parse(b.purchaseDate)
        .compareTo(DateTime.parse(a.purchaseDate)));

    return transactions;
  } catch (e) {
    debugPrint('خطأ في الحصول على بيانات المديونية: $e');
    return [];
  }
});

/// شاشة تقرير المديونية الخاصة بالبائع الحالي
class SellerDueReportScreen extends ConsumerWidget {
  const SellerDueReportScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // الحصول على اسم البائع الحالي
    final sellerNameAsync = ref.watch(currentSellerNameProvider);

    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        title: Text(
          'مديونيات البائع الحالي',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        backgroundColor: kMainColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Container(
        alignment: Alignment.topCenter,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(30),
            topLeft: Radius.circular(30),
          ),
        ),
        child: sellerNameAsync.when(
          data: (sellerName) {
            // عرض بيانات المديونية للبائع الحالي
            return _buildDueTransactionsList(context, ref, sellerName);
          },
          loading: () => const Center(
            child: CircularProgressIndicator(
              color: kMainColor,
            ),
          ),
          error: (error, stackTrace) => Center(
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    color: Colors.red,
                    size: 60,
                  ),
                  const SizedBox(height: 20),
                  Text(
                    'حدث خطأ أثناء تحميل البيانات',
                    style: GoogleFonts.cairo(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 10),
                  Text(
                    'يرجى المحاولة مرة أخرى لاحقاً',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      color: kGreyTextColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// بناء قائمة معاملات المديونية
  Widget _buildDueTransactionsList(
      BuildContext context, WidgetRef ref, String sellerName) {
    final transactionsAsync =
        ref.watch(sellerDueTransactionsProvider(sellerName));

    return transactionsAsync.when(
      data: (transactions) {
        if (transactions.isEmpty) {
          return Padding(
            padding: const EdgeInsets.all(20.0),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(
                    'assets/images/empty_screen.png',
                    height: 150,
                    width: 150,
                  ),
                  const SizedBox(height: 20),
                  Text(
                    'لا توجد مديونيات للبائع الحالي',
                    style: GoogleFonts.cairo(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: kGreyTextColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 10),
                  Text(
                    'جميع المعاملات مسددة بالكامل',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: kGreyTextColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          );
        }

        // حساب إجمالي المديونية
        double totalDueAmount = 0;
        double totalPaidAmount = 0;
        double totalRemainingAmount = 0;

        for (var transaction in transactions) {
          totalDueAmount += transaction.totalDue ?? 0;
          totalPaidAmount += transaction.payDueAmount ?? 0;
          totalRemainingAmount += transaction.dueAmountAfterPay ?? 0;
        }

        return Padding(
          padding: const EdgeInsets.all(16),
          child: SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // معلومات البائع
                Card(
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'معلومات البائع',
                          style: GoogleFonts.cairo(
                            color: kMainColor,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 10),
                        Row(
                          children: [
                            const Icon(
                              Icons.person,
                              color: kMainColor,
                              size: 24,
                            ),
                            const SizedBox(width: 10),
                            Text(
                              'اسم البائع: $sellerName',
                              style: GoogleFonts.cairo(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // ملخص المديونية
                Card(
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'ملخص المديونية',
                          style: GoogleFonts.cairo(
                            color: kMainColor,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Row(
                          children: [
                            Expanded(
                              child: _buildSummaryItem(
                                title: 'إجمالي المديونية',
                                value: myFormat.format(totalDueAmount),
                                icon: Icons.account_balance_wallet,
                                color: kMainColor,
                              ),
                            ),
                            Expanded(
                              child: _buildSummaryItem(
                                title: 'المبلغ المدفوع',
                                value: myFormat.format(totalPaidAmount),
                                icon: Icons.payments,
                                color: Colors.green,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 10),
                        Row(
                          children: [
                            Expanded(
                              child: _buildSummaryItem(
                                title: 'المبلغ المتبقي',
                                value: myFormat.format(totalRemainingAmount),
                                icon: Icons.money_off,
                                color: Colors.red,
                              ),
                            ),
                            Expanded(
                              child: _buildSummaryItem(
                                title: 'عدد الفواتير',
                                value: transactions.length.toString(),
                                icon: Icons.receipt_long,
                                color: Colors.blue,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // جدول المديونية
                Card(
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'تفاصيل المديونية',
                          style: GoogleFonts.cairo(
                            color: kMainColor,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),

                        // رأس الجدول
                        Container(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          decoration: BoxDecoration(
                            color: Color.fromRGBO(
                              (kMainColor.r).toInt(),
                              (kMainColor.g).toInt(),
                              (kMainColor.b).toInt(),
                              0.1,
                            ),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                flex: 2,
                                child: Text(
                                  'رقم الفاتورة',
                                  textAlign: TextAlign.center,
                                  style: GoogleFonts.cairo(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              Expanded(
                                flex: 3,
                                child: Text(
                                  'اسم العميل',
                                  textAlign: TextAlign.center,
                                  style: GoogleFonts.cairo(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              Expanded(
                                flex: 2,
                                child: Text(
                                  'التاريخ',
                                  textAlign: TextAlign.center,
                                  style: GoogleFonts.cairo(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              Expanded(
                                flex: 2,
                                child: Text(
                                  'المبلغ',
                                  textAlign: TextAlign.center,
                                  style: GoogleFonts.cairo(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              Expanded(
                                flex: 2,
                                child: Text(
                                  'المتبقي',
                                  textAlign: TextAlign.center,
                                  style: GoogleFonts.cairo(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                        // صفوف الجدول
                        ListView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: transactions.length,
                          itemBuilder: (context, index) {
                            final transaction = transactions[index];
                            final date =
                                DateTime.parse(transaction.purchaseDate);
                            final formattedDate =
                                DateFormat('yyyy/MM/dd').format(date);

                            return Container(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              decoration: BoxDecoration(
                                border: Border(
                                  bottom: BorderSide(
                                    color: Colors.grey.shade200,
                                  ),
                                ),
                              ),
                              child: Row(
                                children: [
                                  Expanded(
                                    flex: 2,
                                    child: Text(
                                      transaction.invoiceNumber,
                                      textAlign: TextAlign.center,
                                      style: GoogleFonts.cairo(),
                                    ),
                                  ),
                                  Expanded(
                                    flex: 3,
                                    child: Text(
                                      transaction.customerName,
                                      textAlign: TextAlign.center,
                                      style: GoogleFonts.cairo(),
                                    ),
                                  ),
                                  Expanded(
                                    flex: 2,
                                    child: Text(
                                      formattedDate,
                                      textAlign: TextAlign.center,
                                      style: GoogleFonts.cairo(),
                                    ),
                                  ),
                                  Expanded(
                                    flex: 2,
                                    child: Text(
                                      myFormat
                                          .format(transaction.totalDue ?? 0),
                                      textAlign: TextAlign.center,
                                      style: GoogleFonts.cairo(),
                                    ),
                                  ),
                                  Expanded(
                                    flex: 2,
                                    child: Text(
                                      myFormat.format(
                                          transaction.dueAmountAfterPay ?? 0),
                                      textAlign: TextAlign.center,
                                      style: GoogleFonts.cairo(
                                        color: (transaction.dueAmountAfterPay ??
                                                    0) >
                                                0
                                            ? Colors.red
                                            : Colors.green,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
      loading: () => const Center(
        child: CircularProgressIndicator(
          color: kMainColor,
        ),
      ),
      error: (error, stackTrace) => Center(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 60,
              ),
              const SizedBox(height: 20),
              Text(
                'حدث خطأ أثناء تحميل البيانات',
                style: GoogleFonts.cairo(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء عنصر ملخص
  Widget _buildSummaryItem({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Color.fromRGBO(
                (color.r).toInt(),
                (color.g).toInt(),
                (color.b).toInt(),
                0.1,
              ),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.cairo(
                    color: Colors.grey.shade700,
                    fontSize: 12,
                  ),
                ),
                Text(
                  value,
                  style: GoogleFonts.cairo(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
