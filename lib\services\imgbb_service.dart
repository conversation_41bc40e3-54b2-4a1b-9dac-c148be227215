import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;

/// خدمة رفع الصور إلى ImgBB
class ImgBBService {
  // مفتاح API الخاص بـ ImgBB
  static const String apiKey = 'e1de49d823d82602cd8cb28432b056de';

  // رابط API للرفع
  static const String uploadUrl = 'https://api.imgbb.com/1/upload';

  /// رفع صورة إلى ImgBB
  /// يعيد رابط الصورة المباشر
  static Future<Map<String, dynamic>> uploadImage(File imageFile) async {
    try {
      debugPrint('🚀 جاري رفع الصورة إلى ImgBB...');
      debugPrint('📁 مسار الملف: ${imageFile.path}');

      // التحقق من وجود الملف
      if (!await imageFile.exists()) {
        throw Exception('الملف غير موجود');
      }

      // التحقق من حجم الملف (ImgBB يدعم حتى 32 ميجابايت)
      final fileSize = await imageFile.length();
      debugPrint('📊 حجم الملف: $fileSize bytes');

      if (fileSize > 32 * 1024 * 1024) { // 32MB limit
        throw Exception('حجم الملف كبير جداً (أكثر من 32 ميجابايت)');
      }

      // تحويل الصورة إلى base64
      final bytes = await imageFile.readAsBytes();
      final base64Image = base64Encode(bytes);

      debugPrint('🔄 تم تحويل الصورة إلى base64، الحجم: ${base64Image.length} حرف');

      // إعداد طلب الرفع
      final response = await http.post(
        Uri.parse(uploadUrl),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: {
          'key': apiKey,
          'image': base64Image,
          'name': 'amrdevpos_${DateTime.now().millisecondsSinceEpoch}',
          'expiration': '0', // بدون انتهاء صلاحية
        },
      );

      debugPrint('📡 استجابة ImgBB: ${response.statusCode}');
      debugPrint('📄 محتوى الاستجابة: ${response.body}');

      // التحقق من نجاح الرفع
      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);

        if (responseData['success'] == true) {
          final data = responseData['data'];
          final directLink = data['url']; // الرابط المباشر
          final displayUrl = data['display_url']; // رابط العرض
          final deleteUrl = data['delete_url']; // رابط الحذف

          debugPrint('✅ تم رفع الصورة بنجاح إلى ImgBB');
          debugPrint('🔗 الرابط المباشر: $directLink');
          debugPrint('🖼️ رابط العرض: $displayUrl');

          return {
            'success': true,
            'directLink': directLink,
            'displayUrl': displayUrl,
            'deleteUrl': deleteUrl,
            'fileName': data['title'] ?? 'image',
            'fileType': 'image',
            'fileSize': fileSize,
            'service': 'imgbb',
            'fileInfo': data,
          };
        } else {
          final errorMsg = responseData['error']?['message'] ?? 'خطأ غير معروف';
          throw Exception('فشل في رفع الصورة: $errorMsg');
        }
      } else {
        // محاولة قراءة رسالة الخطأ من الاستجابة
        String errorMsg = 'HTTP ${response.statusCode}';
        try {
          final errorData = jsonDecode(response.body);
          if (errorData['error'] != null && errorData['error']['message'] != null) {
            errorMsg += ': ${errorData['error']['message']}';
          }
        } catch (e) {
          // تجاهل أخطاء تحليل JSON
        }
        throw Exception('فشل في رفع الصورة: $errorMsg');
      }
    } catch (e) {
      debugPrint('❌ خطأ في رفع الصورة إلى ImgBB: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// اختبار صحة API Key
  static Future<bool> testApiKey() async {
    try {
      debugPrint('🔍 جاري اختبار API Key لـ ImgBB...');

      // إنشاء صورة اختبار صغيرة (1x1 pixel PNG)
      const testImageBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAGAWA0+GQAAAABJRU5ErkJggg==';

      final response = await http.post(
        Uri.parse(uploadUrl),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: {
          'key': apiKey,
          'image': testImageBase64,
          'name': 'test_image',
        },
      );

      debugPrint('📡 استجابة اختبار ImgBB: ${response.statusCode}');
      debugPrint('📄 محتوى الاستجابة: ${response.body}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        if (responseData['success'] == true) {
          debugPrint('✅ API Key صحيح - ImgBB يعمل بشكل طبيعي');

          // حذف الصورة التجريبية إذا أمكن
          try {
            final deleteUrl = responseData['data']['delete_url'];
            if (deleteUrl != null) {
              await http.delete(Uri.parse(deleteUrl));
              debugPrint('🗑️ تم حذف الصورة التجريبية');
            }
          } catch (e) {
            debugPrint('⚠️ لم يتم حذف الصورة التجريبية: $e');
          }

          return true;
        }
      }

      debugPrint('❌ API Key غير صحيح أو هناك مشكلة في الخدمة');
      return false;
    } catch (e) {
      debugPrint('❌ خطأ في اختبار API Key: $e');
      return false;
    }
  }

  /// حذف صورة من ImgBB (إذا كان لديك رابط الحذف)
  static Future<bool> deleteImage(String deleteUrl) async {
    try {
      debugPrint('🗑️ جاري حذف الصورة من ImgBB...');
      debugPrint('🔗 رابط الحذف: $deleteUrl');

      final response = await http.delete(Uri.parse(deleteUrl));

      debugPrint('📡 استجابة الحذف: ${response.statusCode}');

      if (response.statusCode == 200) {
        debugPrint('✅ تم حذف الصورة بنجاح من ImgBB');
        return true;
      } else {
        debugPrint('❌ فشل في حذف الصورة: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ خطأ في حذف الصورة من ImgBB: $e');
      return false;
    }
  }

  /// الحصول على معلومات الخدمة
  static Map<String, dynamic> getServiceInfo() {
    return {
      'serviceName': 'ImgBB',
      'maxFileSize': '32 MB',
      'supportedFormats': ['JPG', 'PNG', 'GIF', 'BMP', 'WEBP'],
      'features': [
        'رفع مجاني',
        'بدون انتهاء صلاحية',
        'روابط مباشرة',
        'دعم الحذف',
        'سرعة عالية',
      ],
      'apiUrl': uploadUrl,
    };
  }
}
