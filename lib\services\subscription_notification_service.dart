import 'package:flutter/material.dart';
import 'package:mobile_pos/services/local_notification_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// نموذج بيانات الاشتراك الوهمية
class _MockSubscriptionData {
  final DateTime expiryDate;
  final int saleNumber;
  final int purchaseNumber;
  final int partiesNumber;

  _MockSubscriptionData(
      {required this.expiryDate,
      required this.saleNumber,
      required this.purchaseNumber,
      required this.partiesNumber});
}

class SubscriptionNotificationService {
  // بيانات الاشتراك الوهمية للاختبار
  static _MockSubscriptionData _getMockSubscriptionData() {
    return _MockSubscriptionData(
        expiryDate: DateTime.now().add(const Duration(days: 5)),
        saleNumber: 8,
        purchaseNumber: 15,
        partiesNumber: 12);
  }

  // فحص حالة الاشتراك وإرسال إشعارات
  static Future<void> checkSubscriptionStatus() async {
    try {
      // الحصول على بيانات الاشتراك
      // للاختبار فقط - سنستخدم بيانات وهمية
      // في التطبيق الفعلي، يجب استدعاء Subscription.getUserLimitsData بشكل صحيح

      // بيانات الاشتراك الوهمية للاختبار
      final subscriptionData = _getMockSubscriptionData();

      if (true) {
        // دائمًا صحيح للاختبار
        // التحقق من تاريخ انتهاء الاشتراك
        final expiryDate = subscriptionData.expiryDate;
        final now = DateTime.now();

        // حساب عدد الأيام المتبقية
        final daysRemaining = expiryDate.difference(now).inDays;

        // التحقق من آخر إشعار تم إرساله
        final prefs = await SharedPreferences.getInstance();
        final lastNotificationDay =
            prefs.getInt('last_subscription_notification_day') ?? 100;

        // إرسال إشعار إذا كان الاشتراك على وشك الانتهاء وتغير عدد الأيام المتبقية
        if (daysRemaining <= 7 && daysRemaining != lastNotificationDay) {
          String message;

          if (daysRemaining <= 0) {
            message =
                'انتهى اشتراكك! قم بتجديد الاشتراك للاستمرار في استخدام جميع الميزات.';
          } else if (daysRemaining == 1) {
            message =
                'سينتهي اشتراكك غدًا! قم بتجديد الاشتراك للاستمرار في استخدام جميع الميزات.';
          } else {
            message =
                'سينتهي اشتراكك بعد $daysRemaining أيام! قم بتجديد الاشتراك للاستمرار في استخدام جميع الميزات.';
          }

          LocalNotificationService.sendLocalNotification(
            title: 'تنبيه الاشتراك',
            body: message,
            data: {
              'type': 'subscription',
              'daysRemaining': daysRemaining,
              'screen': 'subscription',
            },
          );

          // حفظ آخر يوم تم إرسال إشعار فيه
          prefs.setInt('last_subscription_notification_day', daysRemaining);
        }

        // التحقق من حدود الاستخدام
        final saleLimit = subscriptionData.saleNumber;
        final purchaseLimit = subscriptionData.purchaseNumber;
        final partiesLimit = subscriptionData.partiesNumber;

        // إرسال إشعار إذا كانت الحدود منخفضة
        if (saleLimit < 10 || purchaseLimit < 10 || partiesLimit < 10) {
          LocalNotificationService.sendLocalNotification(
            title: 'تنبيه حدود الاستخدام',
            body:
                'أوشكت على الوصول إلى حدود الاستخدام الخاصة بك. قم بترقية اشتراكك للحصول على المزيد.',
            data: {
              'type': 'usage_limits',
              'saleLimit': saleLimit,
              'purchaseLimit': purchaseLimit,
              'partiesLimit': partiesLimit,
              'screen': 'subscription',
            },
          );
        }

        debugPrint('تم فحص حالة الاشتراك وإرسال الإشعارات');
      }
    } catch (e) {
      debugPrint('خطأ في فحص حالة الاشتراك: $e');
    }
  }
}
