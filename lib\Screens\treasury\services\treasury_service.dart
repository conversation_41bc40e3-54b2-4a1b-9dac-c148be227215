import 'dart:convert';
import 'package:firebase_database/firebase_database.dart';
import 'package:mobile_pos/currency.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/services/firebase_database_service.dart';
import '../models/treasury_transaction_model.dart';
import '../models/treasury_balance_model.dart';

class TreasuryService {
  static const String _transactionsPath = 'Treasury/Transactions';
  static const String _balancePath = 'Treasury/Balance';

  // Get all treasury transactions
  Future<List<TreasuryTransactionModel>> getAllTransactions() async {
    List<TreasuryTransactionModel> transactions = [];

    try {
      final ref = FirebaseDatabaseService.getReference(
        '$constUserId/$_transactionsPath',
        keepSynced: true,
      );

      final snapshot = await ref.orderByChild('createdAt').get();

      if (snapshot.exists) {
        for (var element in snapshot.children) {
          final data = jsonDecode(jsonEncode(element.value));
          data['id'] = element.key;
          transactions.add(TreasuryTransactionModel.fromJson(data));
        }
      }
    } catch (e) {
      print('خطأ في جلب معاملات الخزينة: $e');
    }

    return transactions.reversed.toList();
  }

  // Get treasury balance
  Future<TreasuryBalanceModel> getBalance() async {
    try {
      final ref = FirebaseDatabaseService.getReference(
        '$constUserId/$_balancePath',
        keepSynced: true,
      );

      final snapshot = await ref.get();

      if (snapshot.exists) {
        final data = jsonDecode(jsonEncode(snapshot.value));
        return TreasuryBalanceModel.fromJson(data);
      }
    } catch (e) {
      print('خطأ في جلب رصيد الخزينة: $e');
    }

    return TreasuryBalanceModel.empty();
  }

  // Add new transaction
  Future<bool> addTransaction(TreasuryTransactionModel transaction) async {
    try {
      final ref = FirebaseDatabaseService.getReference(
        '$constUserId/$_transactionsPath',
        keepSynced: true,
      );

      // Generate new transaction ID
      final newRef = ref.push();
      transaction.id = newRef.key ?? '';
      transaction.createdAt = DateTime.now().toIso8601String();
      transaction.updatedAt = DateTime.now().toIso8601String();

      // Save transaction
      await newRef.set(Map<String, Object?>.from(transaction.toJson()));

      // Update balance
      await _updateBalance(transaction);

      return true;
    } catch (e) {
      print('خطأ في إضافة معاملة الخزينة: $e');
      return false;
    }
  }

  // Update transaction
  Future<bool> updateTransaction(TreasuryTransactionModel transaction) async {
    try {
      final ref = FirebaseDatabaseService.getReference(
        '$constUserId/$_transactionsPath/${transaction.id}',
        keepSynced: true,
      );

      transaction.updatedAt = DateTime.now().toIso8601String();

      await ref.update(Map<String, Object?>.from(transaction.toJson()));

      // Recalculate balance
      await _recalculateBalance();

      return true;
    } catch (e) {
      print('خطأ في تحديث معاملة الخزينة: $e');
      return false;
    }
  }

  // Delete transaction
  Future<bool> deleteTransaction(String transactionId) async {
    try {
      final ref = FirebaseDatabaseService.getReference(
        '$constUserId/$_transactionsPath/$transactionId',
        keepSynced: true,
      );

      await ref.remove();

      // Recalculate balance
      await _recalculateBalance();

      return true;
    } catch (e) {
      print('خطأ في حذف معاملة الخزينة: $e');
      return false;
    }
  }

  // Listen to transactions changes
  Stream<List<TreasuryTransactionModel>> listenToTransactions() {
    return FirebaseDatabaseService.listenToPath(
      '$constUserId/$_transactionsPath',
      keepSynced: true,
      ownerId: 'treasury_service',
    ).map((event) {
      List<TreasuryTransactionModel> transactions = [];

      if (event.snapshot.exists) {
        for (var element in event.snapshot.children) {
          final data = jsonDecode(jsonEncode(element.value));
          data['id'] = element.key;
          transactions.add(TreasuryTransactionModel.fromJson(data));
        }
      }

      return transactions.reversed.toList();
    });
  }

  // Listen to balance changes
  Stream<TreasuryBalanceModel> listenToBalance() {
    return FirebaseDatabaseService.listenToPath(
      '$constUserId/$_balancePath',
      keepSynced: true,
      ownerId: 'treasury_service',
    ).map((event) {
      if (event.snapshot.exists) {
        final data = jsonDecode(jsonEncode(event.snapshot.value));
        return TreasuryBalanceModel.fromJson(data);
      }
      return TreasuryBalanceModel.empty();
    });
  }

  // Private method to update balance
  Future<void> _updateBalance(TreasuryTransactionModel transaction) async {
    try {
      final currentBalance = await getBalance();

      double newCurrentBalance = currentBalance.currentBalanceAsDouble;
      double newTotalIncome = currentBalance.totalIncomeAsDouble;
      double newTotalExpense = currentBalance.totalExpenseAsDouble;

      if (transaction.isIncome) {
        newCurrentBalance += transaction.amountAsDouble;
        newTotalIncome += transaction.amountAsDouble;
      } else {
        newCurrentBalance -= transaction.amountAsDouble;
        newTotalExpense += transaction.amountAsDouble;
      }

      final updatedBalance = TreasuryBalanceModel(
        currentBalance: newCurrentBalance.toString(),
        totalIncome: newTotalIncome.toString(),
        totalExpense: newTotalExpense.toString(),
        lastUpdated: DateTime.now().toIso8601String(),
        lastTransactionId: transaction.id,
      );

      final ref = FirebaseDatabaseService.getReference(
        '$constUserId/$_balancePath',
        keepSynced: true,
      );

      await ref.set(Map<String, Object?>.from(updatedBalance.toJson()));
    } catch (e) {
      print('خطأ في تحديث رصيد الخزينة: $e');
    }
  }

  // Private method to recalculate balance
  Future<void> _recalculateBalance() async {
    try {
      final transactions = await getAllTransactions();

      double totalIncome = 0;
      double totalExpense = 0;
      String lastTransactionId = '';

      for (var transaction in transactions) {
        if (transaction.isIncome) {
          totalIncome += transaction.amountAsDouble;
        } else {
          totalExpense += transaction.amountAsDouble;
        }

        if (lastTransactionId.isEmpty ||
            transaction.createdAtAsDateTime.isAfter(transactions
                .firstWhere((t) => t.id == lastTransactionId)
                .createdAtAsDateTime)) {
          lastTransactionId = transaction.id;
        }
      }

      final currentBalance = totalIncome - totalExpense;

      final updatedBalance = TreasuryBalanceModel(
        currentBalance: currentBalance.toString(),
        totalIncome: totalIncome.toString(),
        totalExpense: totalExpense.toString(),
        lastUpdated: DateTime.now().toIso8601String(),
        lastTransactionId: lastTransactionId,
      );

      final ref = FirebaseDatabaseService.getReference(
        '$constUserId/$_balancePath',
        keepSynced: true,
      );

      await ref.set(Map<String, Object?>.from(updatedBalance.toJson()));
    } catch (e) {
      print('خطأ في إعادة حساب رصيد الخزينة: $e');
    }
  }

  // Get transactions by date range
  Future<List<TreasuryTransactionModel>> getTransactionsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final allTransactions = await getAllTransactions();

    return allTransactions.where((transaction) {
      final transactionDate = transaction.dateAsDateTime;
      return transactionDate
              .isAfter(startDate.subtract(const Duration(days: 1))) &&
          transactionDate.isBefore(endDate.add(const Duration(days: 1)));
    }).toList();
  }

  // Get transactions by type
  Future<List<TreasuryTransactionModel>> getTransactionsByType(
      String type) async {
    final allTransactions = await getAllTransactions();
    return allTransactions
        .where((transaction) => transaction.type == type)
        .toList();
  }

  // Get transactions by category
  Future<List<TreasuryTransactionModel>> getTransactionsByCategory(
      String category) async {
    final allTransactions = await getAllTransactions();
    return allTransactions
        .where((transaction) => transaction.category == category)
        .toList();
  }

  // Sync data from Sales (المبيعات)
  Future<void> syncFromSales() async {
    try {
      final salesRef = FirebaseDatabaseService.getReference(
        '$constUserId/Sales',
        keepSynced: true,
      );

      final snapshot = await salesRef.get();

      if (snapshot.exists) {
        for (var element in snapshot.children) {
          final data = jsonDecode(jsonEncode(element.value));

          // تحويل بيانات المبيعات إلى معاملة خزينة
          final transaction = TreasuryTransactionModel(
            id: 'sale_${element.key}',
            date: data['saleDate']?.toString() ??
                DateTime.now().toIso8601String(),
            type: 'income',
            category: 'مبيعات',
            description:
                'مبيعات - فاتورة رقم ${data['invoiceNumber'] ?? element.key}',
            amount: data['totalAmount']?.toString() ?? '0',
            paymentMethod: data['paymentType']?.toString() ?? 'نقدي',
            referenceNumber: data['invoiceNumber']?.toString() ?? '',
            notes: 'تم التحويل تلقائياً من المبيعات',
            createdBy: 'system_sync',
            createdAt: data['saleDate']?.toString() ??
                DateTime.now().toIso8601String(),
            updatedAt: DateTime.now().toIso8601String(),
          );

          // التحقق من عدم وجود المعاملة مسبقاً
          final existingTransactions = await getAllTransactions();
          final exists =
              existingTransactions.any((t) => t.id == transaction.id);

          if (!exists) {
            await addTransaction(transaction);
          }
        }
      }
    } catch (e) {
      print('خطأ في مزامنة بيانات المبيعات: $e');
    }
  }

  // Sync data from Purchases (المشتريات)
  Future<void> syncFromPurchases() async {
    try {
      final purchasesRef = FirebaseDatabaseService.getReference(
        '$constUserId/Purchases',
        keepSynced: true,
      );

      final snapshot = await purchasesRef.get();

      if (snapshot.exists) {
        for (var element in snapshot.children) {
          final data = jsonDecode(jsonEncode(element.value));

          // تحويل بيانات المشتريات إلى معاملة خزينة
          final transaction = TreasuryTransactionModel(
            id: 'purchase_${element.key}',
            date: data['purchaseDate']?.toString() ??
                DateTime.now().toIso8601String(),
            type: 'expense',
            category: 'مشتريات',
            description:
                'مشتريات - فاتورة رقم ${data['invoiceNumber'] ?? element.key}',
            amount: data['totalAmount']?.toString() ?? '0',
            paymentMethod: data['paymentType']?.toString() ?? 'نقدي',
            referenceNumber: data['invoiceNumber']?.toString() ?? '',
            notes: 'تم التحويل تلقائياً من المشتريات',
            createdBy: 'system_sync',
            createdAt: data['purchaseDate']?.toString() ??
                DateTime.now().toIso8601String(),
            updatedAt: DateTime.now().toIso8601String(),
          );

          // التحقق من عدم وجود المعاملة مسبقاً
          final existingTransactions = await getAllTransactions();
          final exists =
              existingTransactions.any((t) => t.id == transaction.id);

          if (!exists) {
            await addTransaction(transaction);
          }
        }
      }
    } catch (e) {
      print('خطأ في مزامنة بيانات المشتريات: $e');
    }
  }

  // Sync data from Expenses (المصروفات)
  Future<void> syncFromExpenses() async {
    try {
      final expensesRef = FirebaseDatabaseService.getReference(
        '$constUserId/Expenses',
        keepSynced: true,
      );

      final snapshot = await expensesRef.get();

      if (snapshot.exists) {
        for (var element in snapshot.children) {
          final data = jsonDecode(jsonEncode(element.value));

          // تحويل بيانات المصروفات إلى معاملة خزينة
          final transaction = TreasuryTransactionModel(
            id: 'expense_${element.key}',
            date: data['date']?.toString() ?? DateTime.now().toIso8601String(),
            type: 'expense',
            category: data['category']?.toString() ?? 'مصروفات عامة',
            description: data['expenseName']?.toString() ?? 'مصروف',
            amount: data['amount']?.toString() ?? '0',
            paymentMethod: 'نقدي',
            referenceNumber: data['referenceNumber']?.toString() ?? '',
            notes: 'تم التحويل تلقائياً من المصروفات',
            createdBy: 'system_sync',
            createdAt:
                data['date']?.toString() ?? DateTime.now().toIso8601String(),
            updatedAt: DateTime.now().toIso8601String(),
          );

          // التحقق من عدم وجود المعاملة مسبقاً
          final existingTransactions = await getAllTransactions();
          final exists =
              existingTransactions.any((t) => t.id == transaction.id);

          if (!exists) {
            await addTransaction(transaction);
          }
        }
      }
    } catch (e) {
      print('خطأ في مزامنة بيانات المصروفات: $e');
    }
  }

  // Get Due Amounts (المديونية)
  Future<Map<String, double>> getDueAmounts() async {
    try {
      Map<String, double> dueAmounts = {
        'customerDue': 0.0, // مديونية العملاء (لنا)
        'supplierDue': 0.0, // مديونية الموردين (علينا)
      };

      // جمع مديونية العملاء
      final customersRef = FirebaseDatabaseService.getReference(
        '$constUserId/Customers',
        keepSynced: true,
      );

      final customersSnapshot = await customersRef.get();
      if (customersSnapshot.exists) {
        for (var element in customersSnapshot.children) {
          final data = jsonDecode(jsonEncode(element.value));
          final dueAmount =
              double.tryParse(data['dueAmount']?.toString() ?? '0') ?? 0.0;
          dueAmounts['customerDue'] = dueAmounts['customerDue']! + dueAmount;
        }
      }

      // جمع مديونية الموردين
      final suppliersRef = FirebaseDatabaseService.getReference(
        '$constUserId/Suppliers',
        keepSynced: true,
      );

      final suppliersSnapshot = await suppliersRef.get();
      if (suppliersSnapshot.exists) {
        for (var element in suppliersSnapshot.children) {
          final data = jsonDecode(jsonEncode(element.value));
          final dueAmount =
              double.tryParse(data['dueAmount']?.toString() ?? '0') ?? 0.0;
          dueAmounts['supplierDue'] = dueAmounts['supplierDue']! + dueAmount;
        }
      }

      return dueAmounts;
    } catch (e) {
      print('خطأ في جمع بيانات المديونية: $e');
      return {'customerDue': 0.0, 'supplierDue': 0.0};
    }
  }

  // Sync all data from different sources
  Future<void> syncAllData() async {
    try {
      await syncFromSales();
      await syncFromPurchases();
      await syncFromExpenses();

      // إعادة حساب الرصيد بعد المزامنة
      await _recalculateBalance();

      print('تم مزامنة جميع البيانات مع الخزينة بنجاح');
    } catch (e) {
      print('خطأ في مزامنة البيانات: $e');
    }
  }

  // Listen to changes in other modules and sync automatically
  void startAutoSync() {
    // مراقبة تغييرات المبيعات
    FirebaseDatabaseService.listenToPath(
      '$constUserId/Sales',
      keepSynced: true,
      ownerId: 'treasury_sales_sync',
    ).listen((event) {
      syncFromSales();
    });

    // مراقبة تغييرات المشتريات
    FirebaseDatabaseService.listenToPath(
      '$constUserId/Purchases',
      keepSynced: true,
      ownerId: 'treasury_purchases_sync',
    ).listen((event) {
      syncFromPurchases();
    });

    // مراقبة تغييرات المصروفات
    FirebaseDatabaseService.listenToPath(
      '$constUserId/Expenses',
      keepSynced: true,
      ownerId: 'treasury_expenses_sync',
    ).listen((event) {
      syncFromExpenses();
    });
  }
}
