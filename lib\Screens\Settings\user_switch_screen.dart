import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/Screens/Authentication/login_form.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/services/firebase_database_service.dart';
import 'package:mobile_pos/services/user_accounts_service.dart';
import 'package:restart_app/restart_app.dart';

class UserSwitchScreen extends ConsumerStatefulWidget {
  const UserSwitchScreen({super.key});

  @override
  ConsumerState<UserSwitchScreen> createState() => _UserSwitchScreenState();
}

class _UserSwitchScreenState extends ConsumerState<UserSwitchScreen> {
  List<UserAccount> _savedAccounts = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSavedAccounts();
  }

  Future<void> _loadSavedAccounts() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final accounts = await UserAccountsService.getSavedAccounts();
      if (!mounted) return;

      setState(() {
        _savedAccounts = accounts;
        _isLoading = false;
      });
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ أثناء تحميل الحسابات: $e')),
      );
    }
  }

  Future<void> _switchAccount(UserAccount account) async {
    if (!mounted) return;

    try {
      EasyLoading.show(status: 'جاري تبديل الحساب...');

      // تسجيل الخروج من الحساب الحالي
      await FirebaseAuth.instance.signOut();

      // إلغاء مزامنة جميع المراجع
      FirebaseDatabaseService.clearAllSyncedReferences();

      // حفظ بيانات الحساب المحدد كآخر حساب تم تسجيل الدخول به
      final updatedAccount = UserAccount(
        email: account.email,
        userId: account.userId,
        displayName: account.displayName,
        photoUrl: account.photoUrl,
        lastLogin: DateTime.now(),
      );
      await UserAccountsService.saveAccount(updatedAccount);

      if (!mounted) return;
      EasyLoading.showSuccess('تم تبديل الحساب بنجاح');

      // إعادة تشغيل التطبيق لتطبيق التغييرات
      Future.delayed(const Duration(milliseconds: 1000), () {
        Restart.restartApp();
      });
    } catch (e) {
      EasyLoading.dismiss();
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ أثناء تبديل الحساب: $e')),
      );
    }
  }

  Future<void> _removeAccount(UserAccount account) async {
    if (!mounted) return;

    // التحقق مما إذا كان هذا هو الحساب الحالي
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser != null && currentUser.uid == account.userId) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('لا يمكن حذف الحساب الحالي')),
      );
      return;
    }

    // تأكيد الحذف
    final shouldDelete = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الحساب'),
        content: Text('هل أنت متأكد من حذف حساب ${account.displayName}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );

    if (shouldDelete != true || !mounted) return;

    try {
      EasyLoading.show(status: 'جاري حذف الحساب...');
      await UserAccountsService.removeAccount(account.userId);
      if (!mounted) return;
      EasyLoading.showSuccess('تم حذف الحساب بنجاح');
      _loadSavedAccounts();
    } catch (e) {
      EasyLoading.dismiss();
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ أثناء حذف الحساب: $e')),
      );
    }
  }

  void _addNewAccount() {
    if (!mounted) return;

    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const LoginForm()),
    ).then((_) {
      // تحديث القائمة عند العودة من شاشة تسجيل الدخول
      if (mounted) {
        _loadSavedAccounts();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        title: Text(
          'تبديل المستخدمين',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        iconTheme: const IconThemeData(color: Colors.white),
        centerTitle: true,
        backgroundColor: kMainColor,
        elevation: 0.0,
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(30),
            topLeft: Radius.circular(30),
          ),
        ),
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : Column(
                children: [
                  Expanded(
                    child: _savedAccounts.isEmpty
                        ? Center(
                            child: Text(
                              'لا توجد حسابات محفوظة',
                              style: GoogleFonts.cairo(
                                fontSize: 18,
                                color: kGreyTextColor,
                              ),
                            ),
                          )
                        : ListView.builder(
                            padding: const EdgeInsets.all(16),
                            itemCount: _savedAccounts.length,
                            itemBuilder: (context, index) {
                              final account = _savedAccounts[index];
                              final isCurrentUser =
                                  FirebaseAuth.instance.currentUser?.uid ==
                                      account.userId;

                              return Card(
                                elevation: 3,
                                margin: const EdgeInsets.only(bottom: 16),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(15),
                                ),
                                child: ListTile(
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 16,
                                    vertical: 8,
                                  ),
                                  leading: CircleAvatar(
                                    radius: 25,
                                    backgroundColor: kMainColor.withAlpha(50),
                                    backgroundImage: account.photoUrl != null
                                        ? NetworkImage(account.photoUrl!)
                                        : null,
                                    child: account.photoUrl == null
                                        ? Text(
                                            account.displayName[0]
                                                .toUpperCase(),
                                            style: const TextStyle(
                                              color: kMainColor,
                                              fontWeight: FontWeight.bold,
                                              fontSize: 20,
                                            ),
                                          )
                                        : null,
                                  ),
                                  title: Text(
                                    account.displayName,
                                    style: GoogleFonts.cairo(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16,
                                    ),
                                  ),
                                  subtitle: Text(
                                    account.email,
                                    style: GoogleFonts.cairo(
                                      color: kGreyTextColor,
                                      fontSize: 14,
                                    ),
                                  ),
                                  trailing: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      if (isCurrentUser)
                                        Container(
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 8,
                                            vertical: 4,
                                          ),
                                          decoration: BoxDecoration(
                                            color: kMainColor,
                                            borderRadius:
                                                BorderRadius.circular(10),
                                          ),
                                          child: Text(
                                            'الحالي',
                                            style: GoogleFonts.cairo(
                                              color: Colors.white,
                                              fontSize: 12,
                                            ),
                                          ),
                                        )
                                      else
                                        Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            IconButton(
                                              icon: const Icon(Icons.login,
                                                  color: kMainColor),
                                              onPressed: () =>
                                                  _switchAccount(account),
                                              tooltip: 'تبديل إلى هذا الحساب',
                                            ),
                                            IconButton(
                                              icon: const Icon(Icons.delete,
                                                  color: Colors.red),
                                              onPressed: () =>
                                                  _removeAccount(account),
                                              tooltip: 'حذف الحساب',
                                            ),
                                          ],
                                        ),
                                    ],
                                  ),
                                ),
                              );
                            },
                          ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: ElevatedButton.icon(
                      onPressed: _addNewAccount,
                      icon: const Icon(Icons.add),
                      label: Text(
                        'إضافة حساب جديد',
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: kMainColor,
                        foregroundColor: Colors.white,
                        minimumSize: const Size(double.infinity, 50),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(15),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}
