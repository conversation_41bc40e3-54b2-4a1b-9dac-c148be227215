// نموذج بيانات التقرير المالي
class FinancialReportModel {
  final double totalSales;
  final double costOfSales;
  final double grossProfit;
  final double totalExpenses;
  final double totalSalaries;
  final double netProfit;
  final double profitMargin;
  final double totalPurchases;
  final double totalDue;
  final double companyCash;

  FinancialReportModel({
    required this.totalSales,
    required this.costOfSales,
    required this.grossProfit,
    required this.totalExpenses,
    required this.totalSalaries,
    required this.netProfit,
    required this.profitMargin,
    required this.totalPurchases,
    required this.totalDue,
    required this.companyCash,
  });

  // إنشاء نموذج فارغ
  factory FinancialReportModel.empty() {
    return FinancialReportModel(
      totalSales: 0,
      costOfSales: 0,
      grossProfit: 0,
      totalExpenses: 0,
      totalSalaries: 0,
      netProfit: 0,
      profitMargin: 0.0,
      totalPurchases: 0,
      totalDue: 0,
      companyCash: 0,
    );
  }
}
