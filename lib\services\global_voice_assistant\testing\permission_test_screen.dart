import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../permissions/permission_manager.dart';
import '../permissions/permission_request_dialog.dart';
import '../permissions/permission_settings_guide.dart';

/// شاشة اختبار شاملة لنظام الأذونات المحسن
class PermissionTestScreen extends StatefulWidget {
  const PermissionTestScreen({super.key});

  @override
  State<PermissionTestScreen> createState() => _PermissionTestScreenState();
}

class _PermissionTestScreenState extends State<PermissionTestScreen> {
  final PermissionManager _permissionManager = PermissionManager();

  bool _isLoading = false;
  Map<String, dynamic>? _lastResult;
  String _statusMessage = 'جاهز للاختبار';

  @override
  void initState() {
    super.initState();
    _runInitialCheck();
  }

  /// فحص أولي للأذونات
  Future<void> _runInitialCheck() async {
    await _checkPermissions();
  }

  /// فحص الأذونات
  Future<void> _checkPermissions() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'فحص الأذونات...';
    });

    try {
      final result = await _permissionManager.checkAllPermissions();
      setState(() {
        _lastResult = result;
        _statusMessage = 'تم فحص الأذونات بنجاح';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'خطأ في فحص الأذونات: $e';
        _isLoading = false;
      });
    }
  }

  /// طلب الأذونات
  Future<void> _requestPermissions() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'طلب الأذونات...';
    });

    try {
      final result = await _permissionManager.requestAllPermissions();
      setState(() {
        _lastResult = result;
        _statusMessage = result['success'] == true
            ? 'تم منح الأذونات بنجاح'
            : 'فشل في منح بعض الأذونات';
        _isLoading = false;
      });

      // إعادة فحص الأذونات بعد الطلب
      await Future.delayed(const Duration(milliseconds: 500));
      await _checkPermissions();
    } catch (e) {
      setState(() {
        _statusMessage = 'خطأ في طلب الأذونات: $e';
        _isLoading = false;
      });
    }
  }

  /// عرض حوار طلب الأذونات
  Future<void> _showPermissionDialog() async {
    HapticFeedback.lightImpact();

    final result = await PermissionRequestDialog.show(
      context,
      onPermissionsGranted: () {
        _showSnackBar('تم منح الأذونات بنجاح!', Colors.green);
        _checkPermissions();
      },
      onPermissionsDenied: () {
        _showSnackBar('تم رفض الأذونات', Colors.orange);
      },
    );

    if (result == true) {
      await _checkPermissions();
    }
  }

  /// فتح دليل الإعدادات
  void _openSettingsGuide() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const PermissionSettingsGuide(),
      ),
    );
  }

  /// إعادة تعيين حالة الأذونات
  Future<void> _resetPermissions() async {
    HapticFeedback.mediumImpact();

    setState(() {
      _isLoading = true;
      _statusMessage = 'إعادة تعيين الأذونات...';
    });

    try {
      await _permissionManager.resetUserDeniedStatus();
      setState(() {
        _statusMessage = 'تم إعادة تعيين حالة الأذونات';
        _isLoading = false;
      });

      _showSnackBar('تم إعادة تعيين حالة الأذونات', Colors.blue);
      await _checkPermissions();
    } catch (e) {
      setState(() {
        _statusMessage = 'خطأ في إعادة التعيين: $e';
        _isLoading = false;
      });
    }
  }

  /// فتح إعدادات التطبيق
  Future<void> _openAppSettings() async {
    HapticFeedback.heavyImpact();

    try {
      final opened = await _permissionManager.openAppSettings();
      if (opened) {
        _showSnackBar('تم فتح إعدادات التطبيق', Colors.blue);
      } else {
        _showSnackBar('فشل في فتح إعدادات التطبيق', Colors.red);
      }
    } catch (e) {
      _showSnackBar('خطأ في فتح الإعدادات: $e', Colors.red);
    }
  }

  /// عرض رسالة
  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار الأذونات المحسن'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _isLoading ? null : _checkPermissions,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // بطاقة الحالة
            _buildStatusCard(),

            const SizedBox(height: 16),

            // أزرار الاختبار
            _buildTestButtons(),

            const SizedBox(height: 16),

            // نتائج الاختبار
            if (_lastResult != null) _buildResultsCard(),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة الحالة
  Widget _buildStatusCard() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _isLoading ? Icons.hourglass_empty : Icons.info,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                const Text(
                  'حالة النظام',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (_isLoading) ...[
              const LinearProgressIndicator(),
              const SizedBox(height: 8),
            ],
            Text(
              _statusMessage,
              style: TextStyle(
                color: _isLoading ? Colors.orange : Colors.green,
                fontWeight: FontWeight.w500,
              ),
            ),
            if (_lastResult != null) ...[
              const SizedBox(height: 12),
              _buildQuickSummary(),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء ملخص سريع
  Widget _buildQuickSummary() {
    final summary = _lastResult!['summary'] as Map<String, dynamic>;
    final percentage = summary['percentage'] ?? 0;
    final allGranted = summary['all_granted'] ?? false;

    return Row(
      children: [
        Expanded(
          child: LinearProgressIndicator(
            value: percentage / 100.0,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(
              allGranted ? Colors.green : Colors.orange,
            ),
          ),
        ),
        const SizedBox(width: 12),
        Text(
          '$percentage%',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: allGranted ? Colors.green : Colors.orange,
          ),
        ),
      ],
    );
  }

  /// بناء أزرار الاختبار
  Widget _buildTestButtons() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'أدوات الاختبار',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),

            // الصف الأول
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : _checkPermissions,
                    icon: const Icon(Icons.search),
                    label: const Text('فحص الأذونات'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : _requestPermissions,
                    icon: const Icon(Icons.security),
                    label: const Text('طلب الأذونات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            // الصف الثاني
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _isLoading ? null : _showPermissionDialog,
                    icon: const Icon(Icons.chat_bubble_outline),
                    label: const Text('حوار الأذونات'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _openSettingsGuide,
                    icon: const Icon(Icons.help),
                    label: const Text('دليل الإعدادات'),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            // الصف الثالث
            Row(
              children: [
                Expanded(
                  child: TextButton.icon(
                    onPressed: _isLoading ? null : _resetPermissions,
                    icon: const Icon(Icons.restore),
                    label: const Text('إعادة تعيين'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: TextButton.icon(
                    onPressed: _openAppSettings,
                    icon: const Icon(Icons.settings),
                    label: const Text('إعدادات التطبيق'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة النتائج
  Widget _buildResultsCard() {
    final permissions = _lastResult!['permissions'] as Map<String, bool>;
    final summary = _lastResult!['summary'] as Map<String, dynamic>;

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'نتائج الفحص',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),

            // ملخص النتائج
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: summary['all_granted'] == true
                    ? Colors.green[50]
                    : Colors.orange[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: summary['all_granted'] == true
                      ? Colors.green
                      : Colors.orange,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    summary['all_granted'] == true
                        ? Icons.check_circle
                        : Icons.warning,
                    color: summary['all_granted'] == true
                        ? Colors.green
                        : Colors.orange,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      summary['all_granted'] == true
                          ? 'جميع الأذونات ممنوحة!'
                          : 'بعض الأذونات مطلوبة',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: summary['all_granted'] == true
                            ? Colors.green[700]
                            : Colors.orange[700],
                      ),
                    ),
                  ),
                  Text(
                    '${summary['granted_count']}/${summary['total_count']}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 12),

            // قائمة الأذونات
            ...permissions.entries.map((entry) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: 2),
                  child: Row(
                    children: [
                      Icon(
                        entry.value ? Icons.check_circle : Icons.cancel,
                        color: entry.value ? Colors.green : Colors.red,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _getPermissionDisplayName(entry.key),
                          style: const TextStyle(fontSize: 14),
                        ),
                      ),
                      Text(
                        entry.value ? 'ممنوح' : 'مرفوض',
                        style: TextStyle(
                          fontSize: 12,
                          color: entry.value ? Colors.green : Colors.red,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                )),
          ],
        ),
      ),
    );
  }

  /// الحصول على اسم الإذن للعرض
  String _getPermissionDisplayName(String permission) {
    switch (permission) {
      case 'microphone':
        return 'الميكروفون';
      case 'systemAlertWindow':
        return 'النوافذ العائمة';
      case 'ignoreBatteryOptimizations':
        return 'تحسين البطارية';
      case 'notification':
        return 'الإشعارات';
      case 'storage':
        return 'التخزين';
      default:
        return permission;
    }
  }
}
