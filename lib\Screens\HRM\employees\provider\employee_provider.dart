import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mobile_pos/models/hrm_models.dart';

import '../repo/employee_repo.dart';

// Provider for employee repository
final employeeRepositoryProvider = Provider<EmployeeRepository>((ref) {
  return EmployeeRepository();
});

// Provider for employee list
final employeeListProvider = FutureProvider<List<EmployeeModel>>((ref) async {
  final employeeRepo = ref.watch(employeeRepositoryProvider);
  return await employeeRepo.getAllEmployees();
});

// Provider for selected employee
final selectedEmployeeProvider = StateProvider<EmployeeModel?>((ref) => null);

// Provider for employee notifier
final employeeNotifierProvider =
    StateNotifierProvider<EmployeeNotifier, AsyncValue<void>>((ref) {
  final employeeRepo = ref.watch(employeeRepositoryProvider);
  return EmployeeNotifier(employeeRepo);
});

// Notifier for employee operations
class EmployeeNotifier extends StateNotifier<AsyncValue<void>> {
  final EmployeeRepository _employeeRepo;

  EmployeeNotifier(this._employeeRepo) : super(const AsyncValue.data(null));

  // Add a new employee
  Future<void> addEmployee(EmployeeModel employee) async {
    state = const AsyncValue.loading();
    try {
      await _employeeRepo.addEmployee(employee: employee);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  // Update an existing employee
  Future<void> updateEmployee(EmployeeModel employee) async {
    state = const AsyncValue.loading();
    try {
      await _employeeRepo.updateEmployee(employee: employee);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  // Delete an employee
  Future<void> deleteEmployee(String id) async {
    state = const AsyncValue.loading();
    try {
      await _employeeRepo.deleteEmployee(id: id);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }
}
