import 'dart:async';
import 'dart:convert';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/foundation.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/model/user_log_model.dart';
import 'package:mobile_pos/services/firebase_database_service.dart';
import 'package:mobile_pos/services/system_notification_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

// استيراد getUserID من constant.dart
import 'package:mobile_pos/constant.dart' show getUserID;

/// خدمة تسجيل أنشطة المستخدمين
class UserLogService {
  static final UserLogService _instance = UserLogService._internal();

  /// الحصول على نسخة واحدة من الخدمة (Singleton)
  factory UserLogService() {
    return _instance;
  }

  UserLogService._internal();

  /// تسجيل نشاط جديد
  Future<bool> logUserAction({
    required String actionType,
    required String description,
    Map<String, dynamic>? data,
    String? userId,
    String? userName,
    bool createNotification = false,
  }) async {
    try {
      // طباعة معلومات تشخيصية
      debugPrint('بدء تسجيل نشاط جديد: $actionType - $description');

      // الحصول على معرف المستخدم الحالي إذا لم يتم تحديده
      final currentUserId =
          userId ?? FirebaseAuth.instance.currentUser?.uid ?? await getUserID();

      debugPrint('معرف المستخدم: $currentUserId');

      // الحصول على اسم المستخدم الحالي إذا لم يتم تحديده
      final currentUserName =
          userName ?? await _getCurrentUserName() ?? 'غير معروف';

      debugPrint('اسم المستخدم: $currentUserName');

      // إنشاء معرف فريد للسجل
      final timestamp = DateTime.now();
      final logId = timestamp.millisecondsSinceEpoch.toString();

      debugPrint('معرف السجل: $logId');

      // إنشاء نموذج السجل
      final userLog = UserLogModel(
        id: logId,
        userId: currentUserId,
        userName: currentUserName,
        actionType: actionType,
        description: description,
        data: data ?? {},
        timestamp: timestamp,
      );

      // حفظ السجل في قاعدة البيانات باستخدام مسار منظم حسب التاريخ
      final userIdForLogs = await getUserID();
      final dateStr = _formatDateForPath(timestamp);

      debugPrint('مسار السجل: $userIdForLogs/UserLogs/$dateStr/$logId');

      final ref = FirebaseDatabaseService.getReference(
        '$userIdForLogs/UserLogs/$dateStr',
        keepSynced: false, // عدم استخدام المزامنة لتجنب الخطأ
      );

      // تحويل النموذج إلى JSON
      final jsonData = userLog.toJson();
      debugPrint('بيانات السجل: $jsonData');

      // حفظ السجل في قاعدة البيانات
      await ref.child(logId).set(jsonData);
      debugPrint('تم حفظ السجل في المسار اليومي');

      // حفظ السجل في مسار الفهرس العام (للتوافق مع الإصدارات السابقة)
      final indexRef = FirebaseDatabaseService.getReference(
        '$userIdForLogs/UserLogsIndex',
        keepSynced: false,
      );

      // حفظ معلومات مختصرة في الفهرس
      final indexData = {
        'id': logId,
        'actionType': actionType,
        'timestamp': timestamp.toIso8601String(),
        'path': '$dateStr/$logId',
      };

      await indexRef.child(logId).set(indexData);
      debugPrint('تم حفظ السجل في الفهرس');

      // تنظيف السجلات القديمة في الخلفية
      _cleanupOldLogs(userIdForLogs);

      // إنشاء إشعار إذا كان مطلوبًا
      if (createNotification) {
        await _createNotificationFromLog(userLog);
        debugPrint('تم إنشاء إشعار للسجل');
      }

      // طباعة معلومات السجل في وضع التطوير
      debugPrint('تم تسجيل نشاط المستخدم بنجاح: $description');

      return true;
    } catch (e) {
      // طباعة الخطأ في وضع التطوير
      debugPrint('خطأ في تسجيل نشاط المستخدم: $e');

      // طباعة تفاصيل الخطأ
      debugPrint('تفاصيل الخطأ: ${e.toString()}');
      if (e is Error) {
        debugPrint('مكدس الخطأ: ${e.stackTrace}');
      }

      return false;
    }
  }

  /// تنظيف السجلات القديمة (استدعاء داخلي)
  Future<void> _cleanupOldLogs(String userIdForLogs) async {
    try {
      // تنظيف السجلات القديمة (أكثر من 30 يوم)
      final cutoffDate = DateTime.now().subtract(const Duration(days: 30));
      final cutoffDateStr = _formatDateForPath(cutoffDate);

      // الحصول على قائمة المجلدات اليومية
      final logsRef = FirebaseDatabaseService.getReference(
        '$userIdForLogs/UserLogs',
        keepSynced: false,
      );

      final snapshot = await logsRef.get();

      if (!snapshot.exists) {
        return;
      }

      // فحص كل مجلد يومي
      for (var child in snapshot.children) {
        final folderName = child.key;

        // تخطي المجلدات التي ليست بتنسيق التاريخ
        if (folderName == null || !_isValidDateFormat(folderName)) {
          continue;
        }

        // حذف المجلدات القديمة
        if (folderName.compareTo(cutoffDateStr) < 0) {
          debugPrint('حذف السجلات القديمة من تاريخ: $folderName');
          await logsRef.child(folderName).remove();
        }
      }
    } catch (e) {
      debugPrint('خطأ في تنظيف السجلات القديمة: $e');
    }
  }

  /// التحقق من صحة تنسيق التاريخ
  bool _isValidDateFormat(String dateStr) {
    // التحقق من تنسيق التاريخ (YYYY-MM-DD)
    final regex = RegExp(r'^\d{4}-\d{2}-\d{2}$');
    return regex.hasMatch(dateStr);
  }

  /// إنشاء إشعار من سجل النشاط
  Future<bool> _createNotificationFromLog(UserLogModel log) async {
    try {
      // إضافة معلومات السجل إلى بيانات الإشعار
      Map<String, dynamic> notificationData = Map.from(log.data);
      notificationData['logId'] = log.id;
      notificationData['timestamp'] = log.timestamp.toIso8601String();

      // تحديد نوع الإشعار ومحتواه بناءً على نوع النشاط
      if (log.actionType.startsWith('sales_')) {
        if (log.actionType == UserLogActionTypes.salesCreated) {
          // التحقق مما إذا كانت فاتورة آجلة
          final dueAmount = log.data['dueAmount'] as double? ?? 0.0;

          if (dueAmount > 0) {
            // إنشاء إشعار مبيعات آجلة
            return await SystemNotificationService.createCreditSaleNotification(
              invoiceNumber: log.data['invoiceNumber'] as String,
              customerName: log.data['customerName'] as String,
              amount: log.data['totalAmount'] as double,
              date: log.timestamp,
              additionalData: notificationData,
            );
          }
        }
      } else if (log.actionType.startsWith('purchase_')) {
        if (log.actionType == UserLogActionTypes.purchaseCreated) {
          // التحقق مما إذا كانت فاتورة آجلة
          final dueAmount = log.data['dueAmount'] as double? ?? 0.0;

          if (dueAmount > 0) {
            // إنشاء إشعار مشتريات آجلة
            return await SystemNotificationService
                .createCreditPurchaseNotification(
              invoiceNumber: log.data['invoiceNumber'] as String,
              supplierName: log.data['supplierName'] as String,
              amount: log.data['totalAmount'] as double,
              date: log.timestamp,
              additionalData: notificationData,
            );
          }
        }
      } else if (log.actionType.startsWith('expense_')) {
        if (log.actionType == UserLogActionTypes.expenseCreated) {
          // إنشاء إشعار مصروف
          return await SystemNotificationService.createExpenseNotification(
            expenseId: log.data['expenseId'] as String? ?? log.id,
            expenseFor: log.data['expenseFor'] as String,
            amount: log.data['amount'] as double,
            date: log.timestamp,
            additionalData: notificationData,
          );
        }
      }

      // إذا لم يتم إنشاء إشعار محدد، نعيد true
      return true;
    } catch (e) {
      debugPrint('خطأ في إنشاء إشعار من سجل النشاط: $e');
      return false;
    }
  }

  /// الحصول على اسم المستخدم الحالي
  Future<String?> getCurrentUserName() async {
    return _getCurrentUserName();
  }

  /// الحصول على سجلات المستخدم
  Future<List<UserLogModel>> getUserLogs({
    String? actionType,
    DateTime? startDate,
    DateTime? endDate,
    String? userId,
    int limit = 100,
  }) async {
    // جمع كل السجلات من جميع الأيام المتاحة
    List<UserLogModel> allLogs = [];

    try {
      // إنشاء مرجع لقاعدة البيانات
      final userIdForLogs = await getUserID();

      // استخدام مسار مختلف لكل يوم لتنظيم السجلات
      String logsPath;

      if (startDate != null) {
        // استخدام التاريخ المحدد للفلترة
        final dateStr = _formatDateForPath(startDate);
        logsPath = '$userIdForLogs/UserLogs/$dateStr';
        debugPrint('استخدام مسار محدد للبحث: $logsPath');
      } else {
        // استخدام تاريخ اليوم إذا لم يتم تحديد تاريخ
        final today = _formatDateForPath(DateTime.now());
        logsPath = '$userIdForLogs/UserLogs/$today';
        debugPrint('استخدام مسار اليوم للبحث: $logsPath');
      }

      // طباعة معلومات تشخيصية
      debugPrint('معلمات البحث:');
      debugPrint('- نوع النشاط: $actionType');
      debugPrint('- تاريخ البداية: ${startDate?.toString() ?? "غير محدد"}');
      debugPrint('- تاريخ النهاية: ${endDate?.toString() ?? "غير محدد"}');
      debugPrint('- الحد الأقصى للسجلات: $limit');

      // التحقق من وجود سجلات في التاريخ المحدد
      final ref = FirebaseDatabaseService.getReference(logsPath,
          keepSynced: false // عدم استخدام المزامنة لتجنب الخطأ
          );

      debugPrint('جاري البحث عن سجلات في المسار: $logsPath');

      try {
        // تنفيذ الاستعلام مع timeout
        final snapshot = await ref.limitToLast(limit).get().timeout(
          const Duration(seconds: 3), // تقليل مهلة الانتظار
          onTimeout: () {
            throw TimeoutException('انتهت مهلة الحصول على سجلات المستخدم');
          },
        );

        // إذا وجدت سجلات، أضفها إلى القائمة
        if (snapshot.exists) {
          final logs = _processLogsSnapshot(
              snapshot, actionType, userId, startDate, endDate);
          allLogs.addAll(logs);
          debugPrint('تم العثور على ${logs.length} سجل في المسار: $logsPath');
        } else {
          debugPrint('لم يتم العثور على سجلات في المسار: $logsPath');
        }
      } catch (e) {
        debugPrint('خطأ في البحث عن سجلات في المسار: $logsPath - $e');
        // استمر في البحث في المسارات الأخرى
      }

      // البحث في جميع المجلدات اليومية إذا لم نجد سجلات كافية
      if (allLogs.isEmpty) {
        try {
          // الحصول على مرجع لمجلد السجلات الرئيسي
          final mainRef = FirebaseDatabaseService.getReference(
              '$userIdForLogs/UserLogs',
              keepSynced: false);

          debugPrint('جاري البحث عن سجلات في جميع المجلدات اليومية');

          // الحصول على قائمة المجلدات اليومية
          final foldersSnapshot = await mainRef.get().timeout(
            const Duration(seconds: 5),
            onTimeout: () {
              throw TimeoutException(
                  'انتهت مهلة الحصول على قائمة المجلدات اليومية');
            },
          );

          if (foldersSnapshot.exists) {
            // البحث في كل مجلد يومي
            for (var folderChild in foldersSnapshot.children) {
              final folderName = folderChild.key;

              if (folderName == null || !_isValidDateFormat(folderName)) {
                continue;
              }

              debugPrint('جاري البحث في المجلد اليومي: $folderName');

              try {
                final folderRef = FirebaseDatabaseService.getReference(
                    '$userIdForLogs/UserLogs/$folderName',
                    keepSynced: false);

                final folderSnapshot =
                    await folderRef.limitToLast(limit).get().timeout(
                  const Duration(seconds: 3),
                  onTimeout: () {
                    throw TimeoutException(
                        'انتهت مهلة الحصول على سجلات من المجلد: $folderName');
                  },
                );

                if (folderSnapshot.exists) {
                  final logs = _processLogsSnapshot(
                      folderSnapshot, actionType, userId, startDate, endDate);
                  allLogs.addAll(logs);
                  debugPrint(
                      'تم العثور على ${logs.length} سجل في المجلد: $folderName');

                  // التوقف إذا وصلنا إلى الحد المطلوب
                  if (allLogs.length >= limit) {
                    break;
                  }
                }
              } catch (e) {
                debugPrint('خطأ في البحث عن سجلات في المجلد: $folderName - $e');
              }
            }

            debugPrint(
                'تم العثور على ${allLogs.length} سجل من جميع المجلدات اليومية');
          } else {
            debugPrint('لم يتم العثور على أي مجلدات يومية');
          }
        } catch (e) {
          debugPrint('خطأ في البحث عن سجلات في المجلدات اليومية: $e');
        }
      }

      // البحث في المسار القديم إذا لم نجد سجلات كافية
      if (allLogs.isEmpty) {
        try {
          final oldRef = FirebaseDatabaseService.getReference(
              '$userIdForLogs/UserLogs',
              keepSynced: false);

          debugPrint('جاري البحث عن سجلات في المسار القديم');

          final oldSnapshot = await oldRef.limitToLast(limit).get().timeout(
            const Duration(seconds: 3),
            onTimeout: () {
              throw TimeoutException(
                  'انتهت مهلة الحصول على سجلات المستخدم من المسار القديم');
            },
          );

          if (oldSnapshot.exists) {
            final logs = _processLogsSnapshot(
                oldSnapshot, actionType, userId, startDate, endDate);
            allLogs.addAll(logs);
            debugPrint('تم العثور على ${logs.length} سجل في المسار القديم');
          } else {
            debugPrint('لم يتم العثور على سجلات في المسار القديم');
          }
        } catch (e) {
          debugPrint('خطأ في البحث عن سجلات في المسار القديم: $e');
        }
      }

      // البحث في الفهرس إذا لم نجد سجلات كافية
      if (allLogs.isEmpty) {
        try {
          final indexRef = FirebaseDatabaseService.getReference(
              '$userIdForLogs/UserLogsIndex',
              keepSynced: false);

          debugPrint('جاري البحث عن سجلات في الفهرس');

          final indexSnapshot = await indexRef.limitToLast(limit).get().timeout(
            const Duration(seconds: 3),
            onTimeout: () {
              throw TimeoutException(
                  'انتهت مهلة الحصول على سجلات المستخدم من الفهرس');
            },
          );

          if (indexSnapshot.exists) {
            // استخراج مسارات السجلات من الفهرس
            List<String> logPaths = [];
            for (var child in indexSnapshot.children) {
              try {
                final data = Map<String, dynamic>.from(child.value as Map);
                if (data.containsKey('path')) {
                  final path = data['path'] as String;
                  logPaths.add(path);
                }
              } catch (e) {
                debugPrint('خطأ في معالجة إدخال الفهرس: $e');
              }
            }

            // جلب السجلات من المسارات المستخرجة
            for (var path in logPaths) {
              try {
                final logRef = FirebaseDatabaseService.getReference(
                    '$userIdForLogs/UserLogs/$path',
                    keepSynced: false);

                final logSnapshot = await logRef.get().timeout(
                  const Duration(seconds: 2),
                  onTimeout: () {
                    throw TimeoutException(
                        'انتهت مهلة الحصول على سجل من المسار: $path');
                  },
                );

                if (logSnapshot.exists) {
                  try {
                    final data =
                        Map<String, dynamic>.from(logSnapshot.value as Map);
                    final log = UserLogModel.fromJson(data);

                    // فلترة حسب المعايير
                    bool shouldInclude = true;

                    if (actionType != null &&
                        !log.actionType.startsWith(actionType)) {
                      shouldInclude = false;
                    }

                    if (userId != null && log.userId != userId) {
                      shouldInclude = false;
                    }

                    if (startDate != null) {
                      final startOfDay = DateTime(
                          startDate.year, startDate.month, startDate.day);
                      if (log.timestamp.isBefore(startOfDay)) {
                        shouldInclude = false;
                      }
                    }

                    if (endDate != null) {
                      final endOfDay = DateTime(
                          endDate.year, endDate.month, endDate.day, 23, 59, 59);
                      if (log.timestamp.isAfter(endOfDay)) {
                        shouldInclude = false;
                      }
                    }

                    if (shouldInclude) {
                      allLogs.add(log);
                    }
                  } catch (e) {
                    debugPrint('خطأ في معالجة سجل من المسار: $path - $e');
                  }
                }
              } catch (e) {
                debugPrint('خطأ في جلب سجل من المسار: $path - $e');
              }
            }

            debugPrint('تم العثور على ${allLogs.length} سجل من الفهرس');
          } else {
            debugPrint('لم يتم العثور على سجلات في الفهرس');
          }
        } catch (e) {
          debugPrint('خطأ في البحث عن سجلات في الفهرس: $e');
        }
      }

      // ترتيب السجلات من الأحدث للأقدم
      allLogs.sort((a, b) => b.timestamp.compareTo(a.timestamp));

      // تقليل عدد السجلات إلى الحد المطلوب
      if (allLogs.length > limit) {
        allLogs = allLogs.sublist(0, limit);
      }

      debugPrint('تم العثور على ${allLogs.length} سجل إجمالي');
      return allLogs;
    } catch (e) {
      // طباعة الخطأ في وضع التطوير
      debugPrint('خطأ في الحصول على سجلات المستخدم: $e');
      return [];
    }
  }

  /// معالجة سجلات من Snapshot
  List<UserLogModel> _processLogsSnapshot(
      DataSnapshot snapshot,
      String? actionType,
      String? userId,
      DateTime? startDate,
      DateTime? endDate) {
    final List<UserLogModel> logs = [];

    try {
      for (var child in snapshot.children) {
        try {
          // تحويل البيانات إلى Map
          final Map<String, dynamic> data;
          if (child.value is Map) {
            // إذا كانت البيانات بالفعل من نوع Map
            data = Map<String, dynamic>.from(child.value as Map);
          } else {
            // تحويل البيانات إلى JSON ثم إلى Map
            data = jsonDecode(jsonEncode(child.value)) as Map<String, dynamic>;
          }

          // التأكد من وجود الحقول المطلوبة
          if (!data.containsKey('id') || !data.containsKey('actionType')) {
            continue;
          }

          // إنشاء نموذج السجل
          final log = UserLogModel.fromJson(data);

          // فلترة حسب نوع النشاط إذا تم تحديده
          if (actionType != null && !log.actionType.startsWith(actionType)) {
            continue;
          }

          // فلترة حسب معرف المستخدم إذا تم تحديده
          if (userId != null && log.userId != userId) {
            continue;
          }

          // فلترة حسب تاريخ البداية إذا تم تحديده
          if (startDate != null) {
            final startOfDay =
                DateTime(startDate.year, startDate.month, startDate.day);
            if (log.timestamp.isBefore(startOfDay)) {
              continue;
            }
          }

          // فلترة حسب تاريخ النهاية إذا تم تحديده
          if (endDate != null) {
            final endOfDay =
                DateTime(endDate.year, endDate.month, endDate.day, 23, 59, 59);
            if (log.timestamp.isAfter(endOfDay)) {
              continue;
            }
          }

          logs.add(log);
        } catch (e) {
          // استمر في المعالجة حتى مع وجود خطأ في سجل واحد
          debugPrint('خطأ في معالجة سجل: $e');
          continue;
        }
      }
    } catch (e) {
      debugPrint('خطأ في معالجة السجلات: $e');
      // إرجاع السجلات التي تم معالجتها بنجاح
      return logs;
    }

    // ترتيب السجلات من الأحدث للأقدم
    logs.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    debugPrint('تم العثور على ${logs.length} سجل');
    return logs;
  }

  /// تنسيق التاريخ للاستخدام في مسار قاعدة البيانات
  String _formatDateForPath(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// البحث عن سجلات اليوم
  Future<List<UserLogModel>> getTodayLogs({
    String? actionType,
    String? userId,
    int limit = 100,
  }) async {
    // استخدام تاريخ اليوم
    final today = DateTime.now();
    return getUserLogs(
      actionType: actionType,
      startDate: today,
      endDate: today,
      userId: userId,
      limit: limit,
    );
  }

  /// الحصول على سجلات المستخدم المتعلقة بالمديونية
  Future<List<UserLogModel>> getDueRelatedLogs({
    String? customerPhone,
    DateTime? startDate,
    DateTime? endDate,
    int limit = 100,
  }) async {
    try {
      // الحصول على جميع السجلات
      final allLogs = await getUserLogs(
        startDate: startDate,
        endDate: endDate,
        limit: limit,
      );

      // فلترة السجلات المتعلقة بالمديونية
      final dueRelatedLogs = allLogs.where((log) {
        // التحقق من نوع النشاط
        final isDueRelated = log.actionType.startsWith('due_');

        // التحقق من رقم هاتف العميل إذا تم تحديده
        if (customerPhone != null && isDueRelated) {
          return log.data['customerPhone'] == customerPhone;
        }

        return isDueRelated;
      }).toList();

      return dueRelatedLogs;
    } catch (e) {
      // طباعة الخطأ في وضع التطوير
      if (kDebugMode) {
        print('خطأ في الحصول على سجلات المديونية: $e');
      }

      return [];
    }
  }

  /// الحصول على سجلات المستخدم المتعلقة بالمبيعات
  Future<List<UserLogModel>> getSalesRelatedLogs({
    String? customerPhone,
    String? invoiceNumber,
    DateTime? startDate,
    DateTime? endDate,
    int limit = 100,
  }) async {
    try {
      // الحصول على جميع السجلات
      final allLogs = await getUserLogs(
        startDate: startDate,
        endDate: endDate,
        limit: limit,
      );

      // فلترة السجلات المتعلقة بالمبيعات
      final salesRelatedLogs = allLogs.where((log) {
        // التحقق من نوع النشاط
        final isSalesRelated = log.actionType.startsWith('sales_');

        if (!isSalesRelated) {
          return false;
        }

        // التحقق من رقم هاتف العميل إذا تم تحديده
        if (customerPhone != null) {
          return log.data['customerPhone'] == customerPhone;
        }

        // التحقق من رقم الفاتورة إذا تم تحديده
        if (invoiceNumber != null) {
          return log.data['invoiceNumber'] == invoiceNumber;
        }

        return true;
      }).toList();

      return salesRelatedLogs;
    } catch (e) {
      // طباعة الخطأ في وضع التطوير
      if (kDebugMode) {
        print('خطأ في الحصول على سجلات المبيعات: $e');
      }

      return [];
    }
  }

  /// الحصول على سجلات المستخدم المتعلقة بالمشتريات
  Future<List<UserLogModel>> getPurchaseRelatedLogs({
    String? supplierPhone,
    String? invoiceNumber,
    DateTime? startDate,
    DateTime? endDate,
    int limit = 100,
  }) async {
    try {
      // الحصول على جميع السجلات
      final allLogs = await getUserLogs(
        startDate: startDate,
        endDate: endDate,
        limit: limit,
      );

      // فلترة السجلات المتعلقة بالمشتريات
      final purchaseRelatedLogs = allLogs.where((log) {
        // التحقق من نوع النشاط
        final isPurchaseRelated = log.actionType.startsWith('purchase_');

        if (!isPurchaseRelated) {
          return false;
        }

        // التحقق من رقم هاتف المورد إذا تم تحديده
        if (supplierPhone != null) {
          return log.data['supplierPhone'] == supplierPhone;
        }

        // التحقق من رقم الفاتورة إذا تم تحديده
        if (invoiceNumber != null) {
          return log.data['invoiceNumber'] == invoiceNumber;
        }

        return true;
      }).toList();

      return purchaseRelatedLogs;
    } catch (e) {
      // طباعة الخطأ في وضع التطوير
      if (kDebugMode) {
        print('خطأ في الحصول على سجلات المشتريات: $e');
      }

      return [];
    }
  }

  /// حذف سجلات المستخدم القديمة
  Future<bool> deleteOldLogs({int olderThanDays = 30}) async {
    try {
      // الحصول على معرف المستخدم
      final userIdForLogs = await getUserID();

      // تنظيف السجلات القديمة
      final cutoffDate = DateTime.now().subtract(Duration(days: olderThanDays));
      final cutoffDateStr = _formatDateForPath(cutoffDate);

      debugPrint('بدء حذف السجلات الأقدم من $olderThanDays يوم');

      // الحصول على قائمة المجلدات اليومية
      final logsRef = FirebaseDatabaseService.getReference(
        '$userIdForLogs/UserLogs',
        keepSynced: false,
      );

      final snapshot = await logsRef.get();

      if (!snapshot.exists) {
        debugPrint('لا توجد سجلات للحذف');
        return true;
      }

      int deletedFolders = 0;
      int deletedLogs = 0;

      // فحص كل مجلد يومي
      for (var child in snapshot.children) {
        final folderName = child.key;

        // التعامل مع المجلدات اليومية
        if (folderName != null && _isValidDateFormat(folderName)) {
          // حذف المجلدات القديمة
          if (folderName.compareTo(cutoffDateStr) < 0) {
            debugPrint('حذف السجلات من تاريخ: $folderName');
            await logsRef.child(folderName).remove();
            deletedFolders++;
          }
        } else {
          // التعامل مع السجلات القديمة (قبل تنظيم السجلات حسب التاريخ)
          try {
            final data = jsonDecode(jsonEncode(child.value));
            if (data is Map && data.containsKey('timestamp')) {
              final timestamp = DateTime.parse(data['timestamp'] as String);

              if (timestamp.isBefore(cutoffDate)) {
                await logsRef.child(child.key!).remove();
                deletedLogs++;
              }
            }
          } catch (e) {
            debugPrint('خطأ في معالجة سجل قديم: $e');
            continue;
          }
        }
      }

      // تنظيف الفهرس أيضًا
      final indexRef = FirebaseDatabaseService.getReference(
        '$userIdForLogs/UserLogsIndex',
        keepSynced: false,
      );

      final indexSnapshot = await indexRef.get();

      if (indexSnapshot.exists) {
        int deletedIndexEntries = 0;

        // فحص كل إدخال في الفهرس
        for (var child in indexSnapshot.children) {
          try {
            final data = Map<String, dynamic>.from(child.value as Map);

            // التحقق من وجود طابع زمني
            if (data.containsKey('timestamp')) {
              final timestamp = DateTime.parse(data['timestamp'] as String);

              // حذف الإدخالات القديمة
              if (timestamp.isBefore(cutoffDate)) {
                await indexRef.child(child.key!).remove();
                deletedIndexEntries++;
              }
            }
          } catch (e) {
            debugPrint('خطأ في معالجة إدخال الفهرس: $e');
            continue;
          }
        }

        debugPrint('تم حذف $deletedIndexEntries إدخال من الفهرس');
      }

      debugPrint(
          'تم حذف $deletedFolders مجلد و $deletedLogs سجل من السجلات القديمة');
      return true;
    } catch (e) {
      debugPrint('خطأ في حذف السجلات القديمة: $e');
      return false;
    }
  }

  /// الحصول على اسم المستخدم الحالي
  Future<String?> _getCurrentUserName() async {
    try {
      // الحصول على معرف المستخدم الحالي
      final currentUserId = FirebaseAuth.instance.currentUser?.uid;

      debugPrint('جاري الحصول على اسم المستخدم لـ: $currentUserId');

      if (currentUserId == null) {
        debugPrint('لم يتم العثور على معرف المستخدم الحالي');
        return 'مستخدم غير معروف';
      }

      // الطريقة 1: الحصول على اسم المستخدم من التخزين المحلي (SharedPreferences)
      try {
        final prefs = await SharedPreferences.getInstance();
        final userTitle = prefs.getString('userTitle');

        if (userTitle != null && userTitle.isNotEmpty) {
          debugPrint(
              'تم العثور على اسم المستخدم في التخزين المحلي: $userTitle');
          return userTitle;
        }
      } catch (e) {
        debugPrint('خطأ في الحصول على اسم المستخدم من التخزين المحلي: $e');
      }

      // الطريقة 2: الحصول على بيانات المستخدم من قاعدة البيانات
      final ref = FirebaseDatabaseService.getReference(
        '$currentUserId/Personal Information',
        keepSynced: false, // تغيير إلى false لتجنب الخطأ
      );

      debugPrint(
          'جاري الحصول على بيانات المستخدم من المسار: $currentUserId/Personal Information');

      final snapshot = await ref.get();

      if (snapshot.exists) {
        debugPrint('تم العثور على بيانات المستخدم: ${snapshot.value}');

        // تحويل البيانات إلى Map
        final data = jsonDecode(jsonEncode(snapshot.value));

        // الحصول على اسم الشركة أو رقم الهاتف
        final companyName = data['companyName'] as String?;
        final phoneNumber = data['phoneNumber'] as String?;

        debugPrint('اسم الشركة: $companyName، رقم الهاتف: $phoneNumber');

        if (companyName != null && companyName.isNotEmpty) {
          return companyName;
        }

        if (phoneNumber != null && phoneNumber.isNotEmpty) {
          return phoneNumber;
        }
      } else {
        debugPrint('لم يتم العثور على بيانات المستخدم');
      }

      // الطريقة 3: الحصول على اسم المستخدم من Firebase Auth
      final firebaseName = FirebaseAuth.instance.currentUser?.displayName;
      if (firebaseName != null && firebaseName.isNotEmpty) {
        debugPrint(
            'تم العثور على اسم المستخدم في Firebase Auth: $firebaseName');
        return firebaseName;
      }

      // إذا لم يتم العثور على اسم المستخدم، استخدم اسم افتراضي
      return 'مستخدم';
    } catch (e) {
      // طباعة الخطأ في وضع التطوير
      debugPrint('خطأ في الحصول على اسم المستخدم: $e');

      // طباعة تفاصيل الخطأ
      debugPrint('تفاصيل الخطأ: ${e.toString()}');
      if (e is Error) {
        debugPrint('مكدس الخطأ: ${e.stackTrace}');
      }

      return 'مستخدم';
    }
  }
}
