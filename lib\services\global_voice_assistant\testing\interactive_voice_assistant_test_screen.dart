import 'dart:async';
import 'package:flutter/material.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter_tts/flutter_tts.dart';
import '../voice_assistant_exports.dart';

/// شاشة اختبار المساعد الصوتي التفاعلي
class InteractiveVoiceAssistantTestScreen extends StatefulWidget {
  const InteractiveVoiceAssistantTestScreen({super.key});

  @override
  State<InteractiveVoiceAssistantTestScreen> createState() =>
      _InteractiveVoiceAssistantTestScreenState();
}

class _InteractiveVoiceAssistantTestScreenState
    extends State<InteractiveVoiceAssistantTestScreen> {
  final SpeechToText _speechToText = SpeechToText();
  final VoiceResponseEngine _responseEngine = VoiceResponseEngine();
  final VoiceFingerprintService _fingerprintService = VoiceFingerprintService();
  final FlutterTts _flutterTts = FlutterTts();

  bool _isListening = false;
  bool _isInitialized = false;
  bool _isProcessing = false;
  bool _isSpeaking = false;
  bool _isContinuousMode = false; // الوضع المستمر
  String _recognizedText = '';
  final List<Map<String, dynamic>> _conversationHistory = [];

  // مؤقتات للتحكم في التوقيت
  Timer? _silenceTimer;
  Timer? _restartTimer;

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  /// تهيئة الخدمات
  Future<void> _initializeServices() async {
    try {
      debugPrint('🎤 تهيئة المساعد التفاعلي...');

      // طلب إذن الميكروفون
      final permission = await Permission.microphone.request();
      if (permission != PermissionStatus.granted) {
        _showMessage('❌ إذن الميكروفون مطلوب', Colors.red);
        return;
      }

      // تهيئة خدمة التعرف على الكلام
      final available = await _speechToText.initialize(
        onError: (error) {
          debugPrint('❌ خطأ في التعرف على الكلام: $error');
          _showMessage(
              'خطأ في التعرف على الكلام: ${error.errorMsg}', Colors.red);
        },
        onStatus: (status) {
          debugPrint('📊 حالة التعرف على الكلام: $status');
        },
      );

      if (!available) {
        _showMessage('❌ خدمة التعرف على الكلام غير متاحة', Colors.red);
        return;
      }

      // تهيئة خدمات أخرى
      await _responseEngine.initialize();
      await _fingerprintService.initialize();

      setState(() {
        _isInitialized = true;
      });

      _showMessage('✅ تم تهيئة المساعد التفاعلي بنجاح', Colors.green);
      await _responseEngine
          .speak('مرحباً! أنا المساعد الذكي. كيف يمكنني مساعدتك؟');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة المساعد التفاعلي: $e');
      _showMessage('خطأ في التهيئة: $e', Colors.red);
    }
  }

  /// بدء الاستماع الذكي
  Future<void> _startListening() async {
    if (!_isInitialized || _isListening || _isSpeaking) return;

    try {
      setState(() {
        _isListening = true;
        _recognizedText = '';
      });

      await _speechToText.listen(
        onResult: (result) {
          setState(() {
            _recognizedText = result.recognizedWords;
          });

          // إلغاء مؤقت الصمت السابق
          _silenceTimer?.cancel();

          if (result.finalResult) {
            // النتيجة النهائية - معالجة فورية
            _processUserInput(_recognizedText);
          } else {
            // نتيجة جزئية - انتظار الصمت لمدة 2 ثانية
            _silenceTimer = Timer(const Duration(seconds: 2), () {
              if (_recognizedText.isNotEmpty) {
                _processUserInput(_recognizedText);
              }
            });
          }
        },
        listenFor: const Duration(hours: 1), // استماع مستمر
        pauseFor: const Duration(milliseconds: 500),
        partialResults: true,
        localeId: 'ar-SA',
      );

      _showMessage('🎤 جاري الاستماع... تحدث الآن', Colors.blue);
    } catch (e) {
      debugPrint('❌ خطأ في بدء الاستماع: $e');
      _showMessage('خطأ في بدء الاستماع: $e', Colors.red);
      setState(() => _isListening = false);
    }
  }

  /// إيقاف الاستماع
  Future<void> _stopListening() async {
    if (!_isListening) return;

    try {
      await _speechToText.stop();
      setState(() => _isListening = false);
      _showMessage('⏹️ تم إيقاف الاستماع', Colors.orange);
    } catch (e) {
      debugPrint('❌ خطأ في إيقاف الاستماع: $e');
    }
  }

  /// معالجة مدخلات المستخدم بشكل ذكي
  Future<void> _processUserInput(String userInput) async {
    if (userInput.trim().isEmpty) return;

    try {
      debugPrint('🗣️ المستخدم قال: $userInput');

      // إيقاف الاستماع فوراً
      await _stopListening();

      setState(() {
        _isProcessing = true;
        _conversationHistory.add({
          'type': 'user',
          'text': userInput,
          'timestamp': DateTime.now(),
        });
      });

      // معالجة الطلب وإنشاء رد
      final response = await _generateResponse(userInput);

      setState(() {
        _conversationHistory.add({
          'type': 'assistant',
          'text': response,
          'timestamp': DateTime.now(),
        });
        _isProcessing = false;
      });

      // نطق الرد بشكل ذكي
      await _speakResponse(response);
    } catch (e) {
      debugPrint('❌ خطأ في معالجة المدخلات: $e');
      _showMessage('خطأ في المعالجة: $e', Colors.red);
      setState(() {
        _isProcessing = false;
      });

      // إعادة تشغيل الاستماع في حالة الخطأ
      if (_isContinuousMode) {
        _scheduleListeningRestart();
      }
    }
  }

  /// إنشاء رد المساعد باستخدام Gemini
  Future<String> _generateResponse(String userInput) async {
    try {
      debugPrint('🤖 معالجة الأمر: $userInput');

      // استخدام GeminiVoiceChatService للمعالجة الذكية
      final geminiService = GeminiVoiceChatService();
      final response = await geminiService.processVoiceRequest(userInput);

      if (response['success'] == true) {
        return response['message'] ?? 'تم معالجة طلبك بنجاح';
      } else {
        return response['message'] ?? 'حدث خطأ في معالجة طلبك';
      }
    } catch (e) {
      debugPrint('❌ خطأ في معالجة الأمر: $e');
      return 'حدث خطأ في معالجة طلبك. يرجى المحاولة مرة أخرى.';
    }
  }

  /// عرض رسالة
  void _showMessage(String message, Color color) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: color,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  /// نطق الرد بشكل ذكي مع إعادة تشغيل الاستماع
  Future<void> _speakResponse(String response) async {
    try {
      setState(() {
        _isSpeaking = true;
      });

      // تهيئة TTS
      await _flutterTts.setLanguage('ar-SA');
      await _flutterTts.setSpeechRate(0.5);
      await _flutterTts.setVolume(0.8);
      await _flutterTts.setPitch(1.0);

      // نطق النص
      await _flutterTts.speak(response);

      // انتظار انتهاء النطق
      await _waitForSpeechCompletion();

      setState(() {
        _isSpeaking = false;
      });

      // إعادة تشغيل الاستماع بعد ثانية واحدة
      if (_isContinuousMode) {
        _scheduleListeningRestart();
      }
    } catch (e) {
      debugPrint('❌ خطأ في نطق الرد: $e');
      setState(() {
        _isSpeaking = false;
      });

      if (_isContinuousMode) {
        _scheduleListeningRestart();
      }
    }
  }

  /// انتظار انتهاء النطق
  Future<void> _waitForSpeechCompletion() async {
    // محاكاة وقت النطق بناءً على طول النص
    // يمكن تحسينها باستخدام completion callback من TTS
    await Future.delayed(const Duration(seconds: 3));
  }

  /// جدولة إعادة تشغيل الاستماع
  void _scheduleListeningRestart() {
    _restartTimer?.cancel();
    _restartTimer = Timer(const Duration(seconds: 1), () {
      if (mounted && !_isListening && !_isSpeaking && _isContinuousMode) {
        _startListening();
      }
    });
  }

  /// تفعيل/إلغاء الوضع المستمر
  void _toggleContinuousMode() {
    setState(() {
      _isContinuousMode = !_isContinuousMode;
    });

    if (_isContinuousMode) {
      _showMessage('🔄 تم تفعيل الوضع المستمر', Colors.green);
      if (!_isListening && !_isSpeaking) {
        _startListening();
      }
    } else {
      _showMessage('⏹️ تم إيقاف الوضع المستمر', Colors.orange);
      _stopListening();
      _restartTimer?.cancel();
    }
  }

  /// مسح تاريخ المحادثة
  void _clearHistory() {
    setState(() {
      _conversationHistory.clear();
      _recognizedText = '';
    });

    // مسح ذاكرة Gemini أيضاً
    GeminiVoiceChatService.clearConversationHistory();
    _showMessage('تم مسح تاريخ المحادثة وذاكرة المساعد', Colors.green);
  }

  /// الحصول على لون الزر حسب الحالة
  Color _getButtonColor() {
    if (_isListening) return Colors.red;
    if (_isSpeaking) return Colors.orange;
    if (_isProcessing) return Colors.purple;
    return Colors.blue;
  }

  /// الحصول على أيقونة الزر حسب الحالة
  IconData _getButtonIcon() {
    if (_isListening) return Icons.mic;
    if (_isSpeaking) return Icons.volume_up;
    if (_isProcessing) return Icons.hourglass_empty;
    return Icons.mic_none;
  }

  /// الحصول على لون مؤشر الحالة
  Color _getStatusColor() {
    if (_isListening) return Colors.red;
    if (_isSpeaking) return Colors.orange;
    if (_isProcessing) return Colors.purple;
    return Colors.grey;
  }

  /// الحصول على نص الحالة
  String _getStatusText() {
    if (_isListening) return 'يستمع...';
    if (_isSpeaking) return 'يتحدث...';
    if (_isProcessing) return 'يعالج...';
    return 'جاهز';
  }

  @override
  void dispose() {
    _speechToText.stop();
    _flutterTts.stop();
    _silenceTimer?.cancel();
    _restartTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار المساعد التفاعلي'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.clear_all),
            onPressed: _clearHistory,
            tooltip: 'مسح المحادثة',
          ),
        ],
      ),
      body: Column(
        children: [
          // حالة الخدمة
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            color: _isInitialized ? Colors.green[50] : Colors.red[50],
            child: Row(
              children: [
                Icon(
                  _isInitialized ? Icons.check_circle : Icons.error,
                  color: _isInitialized ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 8),
                Text(
                  _isInitialized ? 'المساعد جاهز للاستخدام' : 'جاري التهيئة...',
                  style: TextStyle(
                    color: _isInitialized ? Colors.green[700] : Colors.red[700],
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

          // تاريخ المحادثة
          Expanded(
            child: _conversationHistory.isEmpty
                ? const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.chat_bubble_outline,
                            size: 64, color: Colors.grey),
                        SizedBox(height: 16),
                        Text(
                          'اضغط على الميكروفون وابدأ المحادثة',
                          style: TextStyle(fontSize: 16, color: Colors.grey),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: _conversationHistory.length,
                    itemBuilder: (context, index) {
                      final message = _conversationHistory[index];
                      final isUser = message['type'] == 'user';

                      return Container(
                        margin: const EdgeInsets.symmetric(vertical: 4),
                        child: Row(
                          mainAxisAlignment: isUser
                              ? MainAxisAlignment.end
                              : MainAxisAlignment.start,
                          children: [
                            if (!isUser) ...[
                              const CircleAvatar(
                                radius: 16,
                                backgroundColor: Colors.blue,
                                child: Icon(Icons.smart_toy,
                                    size: 16, color: Colors.white),
                              ),
                              const SizedBox(width: 8),
                            ],
                            Flexible(
                              child: Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: isUser
                                      ? Colors.blue[100]
                                      : Colors.grey[200],
                                  borderRadius: BorderRadius.circular(16),
                                ),
                                child: Text(
                                  message['text'],
                                  style: const TextStyle(fontSize: 14),
                                ),
                              ),
                            ),
                            if (isUser) ...[
                              const SizedBox(width: 8),
                              const CircleAvatar(
                                radius: 16,
                                backgroundColor: Colors.green,
                                child: Icon(Icons.person,
                                    size: 16, color: Colors.white),
                              ),
                            ],
                          ],
                        ),
                      );
                    },
                  ),
          ),

          // النص المتعرف عليه حالياً
          if (_recognizedText.isNotEmpty)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              color: Colors.blue[50],
              child: Text(
                'جاري التعرف: $_recognizedText',
                style: const TextStyle(fontStyle: FontStyle.italic),
              ),
            ),

          // أزرار التحكم
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // زر الوضع المستمر
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      _isContinuousMode ? Icons.autorenew : Icons.touch_app,
                      color: _isContinuousMode ? Colors.green : Colors.grey,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'الوضع المستمر',
                      style: TextStyle(
                        color: _isContinuousMode ? Colors.green : Colors.grey,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Switch(
                      value: _isContinuousMode,
                      onChanged: _isInitialized
                          ? (value) => _toggleContinuousMode()
                          : null,
                      activeColor: Colors.green,
                    ),
                  ],
                ),
                const SizedBox(height: 20),

                // أزرار الميكروفون والحالة
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // زر الميكروفون
                    GestureDetector(
                      onTap: _isInitialized
                          ? (_isListening ? _stopListening : _startListening)
                          : null,
                      child: Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: _getButtonColor(),
                          boxShadow: _isListening
                              ? [
                                  BoxShadow(
                                    color: Colors.red.withOpacity(0.3),
                                    blurRadius: 20,
                                    spreadRadius: 5,
                                  ),
                                ]
                              : _isSpeaking
                                  ? [
                                      BoxShadow(
                                        color: Colors.orange.withOpacity(0.3),
                                        blurRadius: 20,
                                        spreadRadius: 5,
                                      ),
                                    ]
                                  : null,
                        ),
                        child: Icon(
                          _getButtonIcon(),
                          size: 40,
                          color: Colors.white,
                        ),
                      ),
                    ),

                    // مؤشر الحالة
                    Column(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: _getStatusColor(),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Text(
                            _getStatusText(),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        if (_isContinuousMode)
                          const Padding(
                            padding: EdgeInsets.only(top: 4),
                            child: Text(
                              '🔄 مستمر',
                              style: TextStyle(
                                fontSize: 10,
                                color: Colors.green,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
