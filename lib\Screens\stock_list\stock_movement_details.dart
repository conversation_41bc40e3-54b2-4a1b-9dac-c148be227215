import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:firebase_database/firebase_database.dart';

import '../../Provider/product_provider.dart';
import '../../constant.dart';
import '../../model/product_model.dart';
import '../../model/stock_movement_model.dart';

/// شاشة تفاصيل حركة المخزون
class StockMovementDetails extends ConsumerStatefulWidget {
  final ProductModel product;
  final String warehouseId;
  final String warehouseName;

  const StockMovementDetails({
    super.key,
    required this.product,
    required this.warehouseId,
    required this.warehouseName,
  });

  @override
  ConsumerState<StockMovementDetails> createState() =>
      _StockMovementDetailsState();
}

class _StockMovementDetailsState extends ConsumerState<StockMovementDetails> {
  List<StockMovementModel> _movements = [];
  bool _isLoading = true;
  String _errorMessage = '';

  // فترة البحث
  DateTime _fromDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _toDate = DateTime.now();

  // متغيرات تسوية المخزون
  final TextEditingController _newQuantityController = TextEditingController();
  final TextEditingController _reasonController = TextEditingController();
  bool _isAdjusting = false;

  @override
  void initState() {
    super.initState();
    _loadMovements();
  }

  /// تحميل حركة المخزون
  Future<void> _loadMovements() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      // تنفيذ استعلام مباشر لجلب حركات المخزون
      final List<StockMovementModel> movements = [];

      // استعلام عن المبيعات
      await _loadSalesMovements(movements);

      // استعلام عن المشتريات
      await _loadPurchaseMovements(movements);

      // استعلام عن تسويات المخزون
      await _loadAdjustmentMovements(movements);

      // ترتيب الحركات حسب التاريخ (الأحدث أولاً)
      movements.sort((a, b) => b.date.compareTo(a.date));

      setState(() {
        _movements = movements;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'حدث خطأ أثناء تحميل البيانات: $e';
        _isLoading = false;
      });
    }
  }

  /// تحميل حركات المبيعات
  Future<void> _loadSalesMovements(List<StockMovementModel> movements) async {
    try {
      final salesRef = FirebaseDatabase.instance
          .ref(constUserId)
          .child('Sales')
          .orderByChild('date')
          .startAt(DateFormat('yyyy-MM-dd').format(_fromDate))
          .endAt(DateFormat('yyyy-MM-dd').format(_toDate));

      final snapshot = await salesRef.get();

      if (snapshot.exists) {
        for (var element in snapshot.children) {
          var saleData = jsonDecode(jsonEncode(element.value));
          var products =
              List<Map<String, dynamic>>.from(saleData['productList'] ?? []);

          for (var product in products) {
            if (product['productCode'] == widget.product.productCode) {
              // فلترة حسب المخزن إذا تم تحديده
              if (widget.warehouseId != 'الكل' &&
                  product['warehouseId'] != widget.warehouseId) {
                continue;
              }

              movements.add(StockMovementModel(
                  id: element.key ?? '',
                  productId: product['productCode'] ?? '',
                  productName: product['productName'] ?? '',
                  warehouseId: product['warehouseId'] ?? '',
                  warehouseName: product['warehouseName'] ?? '',
                  quantity: int.tryParse(product['quantity'].toString()) ?? 0,
                  type: 'sale',
                  date: DateFormat('yyyy-MM-dd').parse(saleData['date'] ??
                      DateFormat('yyyy-MM-dd').format(DateTime.now())),
                  referenceId: saleData['invoiceNumber'] ?? '',
                  notes: 'بيع للعميل: ${saleData['customerName'] ?? ''}'));
            }
          }
        }
      }
    } catch (e) {
      debugPrint('خطأ في تحميل حركات المبيعات: $e');
    }
  }

  /// تحميل حركات المشتريات
  Future<void> _loadPurchaseMovements(
      List<StockMovementModel> movements) async {
    try {
      final purchaseRef = FirebaseDatabase.instance
          .ref(constUserId)
          .child('Purchase')
          .orderByChild('date')
          .startAt(DateFormat('yyyy-MM-dd').format(_fromDate))
          .endAt(DateFormat('yyyy-MM-dd').format(_toDate));

      final snapshot = await purchaseRef.get();

      if (snapshot.exists) {
        for (var element in snapshot.children) {
          var purchaseData = jsonDecode(jsonEncode(element.value));
          var products = List<Map<String, dynamic>>.from(
              purchaseData['productList'] ?? []);

          for (var product in products) {
            if (product['productCode'] == widget.product.productCode) {
              // فلترة حسب المخزن إذا تم تحديده
              if (widget.warehouseId != 'الكل' &&
                  product['warehouseId'] != widget.warehouseId) {
                continue;
              }

              movements.add(StockMovementModel(
                  id: element.key ?? '',
                  productId: product['productCode'] ?? '',
                  productName: product['productName'] ?? '',
                  warehouseId: product['warehouseId'] ?? '',
                  warehouseName: product['warehouseName'] ?? '',
                  quantity: int.tryParse(product['quantity'].toString()) ?? 0,
                  type: 'purchase',
                  date: DateFormat('yyyy-MM-dd').parse(purchaseData['date'] ??
                      DateFormat('yyyy-MM-dd').format(DateTime.now())),
                  referenceId: purchaseData['invoiceNumber'] ?? '',
                  notes:
                      'شراء من المورد: ${purchaseData['customerName'] ?? ''}'));
            }
          }
        }
      }
    } catch (e) {
      debugPrint('خطأ في تحميل حركات المشتريات: $e');
    }
  }

  /// تحميل حركات تسويات المخزون
  Future<void> _loadAdjustmentMovements(
      List<StockMovementModel> movements) async {
    try {
      final adjustmentRef = FirebaseDatabase.instance
          .ref(constUserId)
          .child('StockAdjustments')
          .orderByChild('date')
          .startAt(DateFormat('yyyy-MM-dd').format(_fromDate))
          .endAt(DateFormat('yyyy-MM-dd').format(_toDate));

      final snapshot = await adjustmentRef.get();

      if (snapshot.exists) {
        for (var element in snapshot.children) {
          var adjustmentData = jsonDecode(jsonEncode(element.value));

          if (adjustmentData['productCode'] == widget.product.productCode) {
            // فلترة حسب المخزن إذا تم تحديده
            if (widget.warehouseId != 'الكل' &&
                adjustmentData['warehouseId'] != widget.warehouseId) {
              continue;
            }

            movements.add(StockMovementModel(
                id: element.key ?? '',
                productId: adjustmentData['productCode'] ?? '',
                productName: adjustmentData['productName'] ?? '',
                warehouseId: adjustmentData['warehouseId'] ?? '',
                warehouseName: adjustmentData['warehouseName'] ?? '',
                quantity:
                    int.tryParse(adjustmentData['quantity'].toString()) ?? 0,
                type: 'adjustment',
                date: DateFormat('yyyy-MM-dd').parse(adjustmentData['date'] ??
                    DateFormat('yyyy-MM-dd').format(DateTime.now())),
                referenceId: adjustmentData['referenceId'] ?? '',
                notes: adjustmentData['notes'] ?? 'تسوية مخزون'));
          }
        }
      }
    } catch (e) {
      debugPrint('خطأ في تحميل حركات تسويات المخزون: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'حركة المخزون: ${widget.product.productName}',
          style: GoogleFonts.poppins(
            color: Colors.white,
          ),
        ),
        iconTheme: const IconThemeData(color: Colors.white),
        centerTitle: true,
        backgroundColor: kMainColor,
        elevation: 0.0,
        actions: [
          // زر تسوية المخزون
          IconButton(
            icon: const Icon(Icons.edit, color: Colors.white),
            tooltip: 'تسوية المخزون',
            onPressed: _showAdjustmentDialog,
          ),
          // زر تحديث البيانات
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            tooltip: 'تحديث البيانات',
            onPressed: _loadMovements,
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(30),
            topLeft: Radius.circular(30),
          ),
        ),
        child: Column(
          children: [
            // معلومات المنتج والمخزن
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(30),
                  topLeft: Radius.circular(30),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'المخزن: ${widget.warehouseName}',
                        style: GoogleFonts.poppins(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        'الكمية: ${widget.product.productStock}',
                        style: GoogleFonts.poppins(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: int.parse(widget.product.productStock) <=
                                  widget.product.lowerStockAlert
                              ? Colors.red
                              : Colors.green,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'الفئة: ${widget.product.productCategory}',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'سعر البيع: ${(double.parse(widget.product.productSalePrice))}',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),

            // فلتر التاريخ
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: TextButton.icon(
                      icon: const Icon(Icons.calendar_today),
                      label: Text(
                        'من: ${DateFormat('yyyy-MM-dd').format(_fromDate)}',
                      ),
                      onPressed: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: _fromDate,
                          firstDate: DateTime(2020),
                          lastDate: DateTime.now(),
                        );
                        if (date != null) {
                          setState(() {
                            _fromDate = date;
                          });
                          _loadMovements();
                        }
                      },
                    ),
                  ),
                  Expanded(
                    child: TextButton.icon(
                      icon: const Icon(Icons.calendar_today),
                      label: Text(
                        'إلى: ${DateFormat('yyyy-MM-dd').format(_toDate)}',
                      ),
                      onPressed: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: _toDate,
                          firstDate: DateTime(2020),
                          lastDate: DateTime.now(),
                        );
                        if (date != null) {
                          setState(() {
                            _toDate = date;
                          });
                          _loadMovements();
                        }
                      },
                    ),
                  ),
                ],
              ),
            ),

            // قائمة الحركات
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _errorMessage.isNotEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(Icons.error,
                                  color: Colors.red, size: 48),
                              const SizedBox(height: 16),
                              Text(_errorMessage),
                              const SizedBox(height: 16),
                              ElevatedButton(
                                onPressed: _loadMovements,
                                child: const Text('إعادة المحاولة'),
                              ),
                            ],
                          ),
                        )
                      : _movements.isEmpty
                          ? const Center(
                              child: Text('لا توجد حركات مخزون في هذه الفترة'),
                            )
                          : ListView.builder(
                              itemCount: _movements.length,
                              itemBuilder: (context, index) {
                                final movement = _movements[index];
                                return Card(
                                  margin: const EdgeInsets.symmetric(
                                    horizontal: 16,
                                    vertical: 8,
                                  ),
                                  elevation: 2,
                                  child: ListTile(
                                    leading: Container(
                                      width: 40,
                                      height: 40,
                                      decoration: BoxDecoration(
                                        color: Color(movement.getTypeColor()),
                                        shape: BoxShape.circle,
                                      ),
                                      child: Center(
                                        child: Text(
                                          movement.getTypeIcon(),
                                          style: const TextStyle(
                                            color: Colors.white,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                    ),
                                    title: Text(
                                      movement.getTypeInArabic(),
                                      style: GoogleFonts.poppins(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    subtitle: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'التاريخ: ${DateFormat('yyyy-MM-dd').format(movement.date)}',
                                        ),
                                        Text(
                                          'المرجع: ${movement.referenceId}',
                                        ),
                                        Text(
                                          movement.notes,
                                        ),
                                      ],
                                    ),
                                    trailing: Text(
                                      '${movement.type == 'sale' ? '-' : '+'} ${movement.quantity}',
                                      style: GoogleFonts.poppins(
                                        fontWeight: FontWeight.bold,
                                        color: movement.type == 'sale'
                                            ? Colors.red
                                            : Colors.green,
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
            ),
          ],
        ),
      ),
    );
  }

  /// عرض مربع حوار تسوية المخزون
  void _showAdjustmentDialog() {
    // تعيين القيمة الافتراضية للكمية الجديدة
    _newQuantityController.text = widget.product.productStock;
    _reasonController.text = '';

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('تسوية المخزون', textAlign: TextAlign.center),
          content: _isAdjusting
              ? const Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(height: 16),
                      Text('جاري تسوية المخزون...'),
                    ],
                  ),
                )
              : Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'المنتج: ${widget.product.productName}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'الكمية الحالية: ${widget.product.productStock}',
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _newQuantityController,
                      decoration: const InputDecoration(
                        labelText: 'الكمية الجديدة',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _reasonController,
                      decoration: const InputDecoration(
                        labelText: 'سبب التسوية',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 2,
                    ),
                  ],
                ),
          actions: _isAdjusting
              ? null
              : [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('إلغاء'),
                  ),
                  ElevatedButton(
                    onPressed: () => _adjustStock(setState),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: kMainColor,
                    ),
                    child: const Text('تسوية'),
                  ),
                ],
        ),
      ),
    );
  }

  /// تنفيذ تسوية المخزون
  Future<void> _adjustStock(StateSetter dialogSetState) async {
    // التحقق من صحة المدخلات
    if (_newQuantityController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إدخال الكمية الجديدة')),
      );
      return;
    }

    if (_reasonController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إدخال سبب التسوية')),
      );
      return;
    }

    // تحويل الكمية إلى رقم
    int? newQuantity = int.tryParse(_newQuantityController.text);
    if (newQuantity == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('الكمية غير صالحة')),
      );
      return;
    }

    // تحديث حالة الحوار
    dialogSetState(() {
      _isAdjusting = true;
    });

    try {
      // تنفيذ تسوية المخزون مباشرة
      bool success = await _adjustProductStock(
        widget.product.productCode,
        widget.warehouseId,
        newQuantity,
        _reasonController.text,
      );

      // التحقق من أن الشاشة لا تزال مرتبطة بالسياق
      if (!mounted) return;

      // إغلاق الحوار
      Navigator.pop(context);

      if (success) {
        // عرض رسالة نجاح
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تسوية المخزون بنجاح'),
            backgroundColor: Colors.green,
          ),
        );

        // تحديث البيانات
        _loadMovements();

        // تحديث بيانات المنتج في الشاشة الرئيسية
        ref.invalidate(productProvider);
      } else {
        // عرض رسالة خطأ
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('حدث خطأ أثناء تسوية المخزون'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      // التحقق من أن الشاشة لا تزال مرتبطة بالسياق
      if (!mounted) return;

      // إغلاق الحوار
      Navigator.pop(context);

      // عرض رسالة خطأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      // إعادة تعيين حالة الحوار إذا كانت الشاشة لا تزال مرتبطة بالسياق
      if (mounted) {
        setState(() {
          _isAdjusting = false;
        });
      }
    }
  }

  /// تسوية مخزون المنتج
  Future<bool> _adjustProductStock(
    String productCode,
    String warehouseId,
    int newQuantity,
    String reason,
  ) async {
    try {
      // 1. تحديث مخزون المنتج
      final productsRef =
          FirebaseDatabase.instance.ref(constUserId).child('Products');
      productsRef.keepSynced(true);

      // البحث عن المنتج بواسطة الكود
      final snapshot = await productsRef
          .orderByChild('productCode')
          .equalTo(productCode)
          .get();

      if (snapshot.children.isEmpty) {
        return false;
      }

      // الحصول على مفتاح المنتج والكمية الحالية
      final productSnapshot = snapshot.children.first;
      final String productKey = productSnapshot.key!;
      final int oldQuantity = int.tryParse(
              productSnapshot.child('productStock').value.toString()) ??
          0;

      // تحديث المخزون
      await productsRef
          .child(productKey)
          .update({'productStock': newQuantity.toString()});

      // 2. تسجيل حركة تسوية المخزون
      final adjustmentRef =
          FirebaseDatabase.instance.ref(constUserId).child('StockAdjustments');
      adjustmentRef.keepSynced(true);

      final String adjustmentId =
          DateTime.now().millisecondsSinceEpoch.toString();

      await adjustmentRef.child(adjustmentId).set({
        'productCode': productCode,
        'productName': widget.product.productName,
        'warehouseId': warehouseId,
        'warehouseName': widget.warehouseName,
        'oldQuantity': oldQuantity,
        'newQuantity': newQuantity,
        'quantity': newQuantity - oldQuantity, // الفرق (يمكن أن يكون سالباً)
        'date': DateFormat('yyyy-MM-dd').format(DateTime.now()),
        'referenceId': 'ADJ-$adjustmentId',
        'notes': reason,
        'adjustedBy': 'المستخدم الحالي', // يمكن تحديثه لاحقاً
        'timestamp': ServerValue.timestamp,
      });

      return true;
    } catch (e) {
      debugPrint('خطأ في تعديل كمية المخزون: $e');
      return false;
    }
  }
}
