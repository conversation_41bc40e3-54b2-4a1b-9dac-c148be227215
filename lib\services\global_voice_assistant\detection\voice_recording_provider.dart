import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mobile_pos/services/global_voice_assistant/voice_assistant_exports.dart';

/// مزود خدمة تسجيل الصوت
final voiceRecordingServiceProvider = Provider<VoiceRecordingService>((ref) {
  final service = VoiceRecordingService();

  // التخلص من الموارد عند إعادة إنشاء المزود
  ref.onDispose(() {
    service.dispose();
  });

  return service;
});

/// مزود حالة التسجيل
final isRecordingProvider = StateProvider<bool>((ref) => false);

/// مزود مسار التسجيل الحالي
final currentRecordingPathProvider = StateProvider<String?>((ref) => null);
