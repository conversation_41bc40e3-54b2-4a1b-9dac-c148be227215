// بسم الله الرحمن الرحيم
// شاشة تشخيص الصوت والمساعد الصوتي - AmrDevPOS

import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:flutter_tts/flutter_tts.dart';
import '../core/voice_assistant_initialization_manager.dart';
import '../voice_assistant_exports.dart';

/// شاشة تشخيص شاملة للصوت والمساعد الصوتي
class VoiceDiagnosticScreen extends StatefulWidget {
  const VoiceDiagnosticScreen({super.key});

  @override
  State<VoiceDiagnosticScreen> createState() => _VoiceDiagnosticScreenState();
}

class _VoiceDiagnosticScreenState extends State<VoiceDiagnosticScreen> {
  final VoiceAssistantInitializationManager _initManager =
      VoiceAssistantInitializationManager();

  // خدمات الصوت للاختبار
  final stt.SpeechToText _speechToText = stt.SpeechToText();
  final FlutterTts _flutterTts = FlutterTts();
  final VoiceRecordingService _voiceService = VoiceRecordingService();

  String _diagnosticResults = '';
  bool _isRunning = false;
  bool _isListening = false;
  String _recognizedText = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('🔊 تشخيص الصوت'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // معلومات سريعة
            Card(
              color: Colors.blue.shade50,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '🎤 حالة الصوت السريعة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 10),
                    _buildQuickStatus(),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // أزرار الاختبار
            Wrap(
              spacing: 10,
              runSpacing: 10,
              children: [
                ElevatedButton.icon(
                  onPressed: _isRunning ? null : _runFullDiagnostic,
                  icon: const Icon(Icons.medical_services),
                  label: const Text('تشخيص شامل'),
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
                ),
                ElevatedButton.icon(
                  onPressed: _isRunning ? null : _testPermissions,
                  icon: const Icon(Icons.security),
                  label: const Text('اختبار الأذونات'),
                  style:
                      ElevatedButton.styleFrom(backgroundColor: Colors.orange),
                ),
                ElevatedButton.icon(
                  onPressed: _isRunning ? null : _testSpeechToText,
                  icon: const Icon(Icons.mic),
                  label: const Text('اختبار التعرف على الكلام'),
                  style:
                      ElevatedButton.styleFrom(backgroundColor: Colors.green),
                ),
                ElevatedButton.icon(
                  onPressed: _isRunning ? null : _testTextToSpeech,
                  icon: const Icon(Icons.volume_up),
                  label: const Text('اختبار تحويل النص لكلام'),
                  style:
                      ElevatedButton.styleFrom(backgroundColor: Colors.purple),
                ),
                ElevatedButton.icon(
                  onPressed: _isRunning ? null : _testVoiceRecording,
                  icon: const Icon(Icons.record_voice_over),
                  label: const Text('اختبار التسجيل الصوتي'),
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                ),
                ElevatedButton.icon(
                  onPressed: _isRunning ? null : _testVoiceAssistant,
                  icon: const Icon(Icons.assistant),
                  label: const Text('اختبار المساعد الصوتي'),
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.teal),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // منطقة اختبار التعرف على الكلام
            if (_isListening)
              Card(
                color: Colors.green.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      const Text(
                        '🎤 أستمع إليك الآن...',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                      const SizedBox(height: 10),
                      Text(
                        _recognizedText.isEmpty
                            ? 'تحدث الآن...'
                            : 'سمعت: $_recognizedText',
                        style: const TextStyle(fontSize: 14),
                      ),
                      const SizedBox(height: 10),
                      ElevatedButton(
                        onPressed: _stopListening,
                        child: const Text('إيقاف الاستماع'),
                      ),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 20),

            // نتائج التشخيص
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Text(
                            '📋 نتائج التشخيص',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Spacer(),
                          if (_isRunning)
                            const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                        ],
                      ),
                      const SizedBox(height: 10),
                      Expanded(
                        child: SingleChildScrollView(
                          child: Text(
                            _diagnosticResults.isEmpty
                                ? 'لم يتم تشغيل أي اختبار بعد'
                                : _diagnosticResults,
                            style: const TextStyle(
                              fontFamily: 'monospace',
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStatus() {
    return FutureBuilder<Map<String, dynamic>>(
      future: _getQuickStatus(),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const CircularProgressIndicator();
        }

        final status = snapshot.data!;
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildStatusRow('المساعد الصوتي', status['voiceAssistant']),
            _buildStatusRow('إذن الميكروفون', status['micPermission']),
            _buildStatusRow('Speech-to-Text', status['speechToText']),
            _buildStatusRow('Text-to-Speech', status['textToSpeech']),
          ],
        );
      },
    );
  }

  Widget _buildStatusRow(String label, bool? status) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Text('$label: '),
          Icon(
            status == true ? Icons.check_circle : Icons.error,
            color: status == true ? Colors.green : Colors.red,
            size: 16,
          ),
          const SizedBox(width: 5),
          Text(
            status == true ? 'يعمل' : 'لا يعمل',
            style: TextStyle(
              color: status == true ? Colors.green : Colors.red,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Future<Map<String, dynamic>> _getQuickStatus() async {
    try {
      final micPermission = await Permission.microphone.isGranted;
      final voiceAssistantStatus = _initManager.isInitialized;

      return {
        'voiceAssistant': voiceAssistantStatus,
        'micPermission': micPermission,
        'speechToText': true, // سنختبرها لاحقاً
        'textToSpeech': true, // سنختبرها لاحقاً
      };
    } catch (e) {
      return {
        'voiceAssistant': false,
        'micPermission': false,
        'speechToText': false,
        'textToSpeech': false,
      };
    }
  }

  void _addResult(String result) {
    setState(() {
      _diagnosticResults += '${DateTime.now().toIso8601String()}: $result\n';
    });
  }

  Future<void> _runFullDiagnostic() async {
    setState(() => _isRunning = true);
    _addResult('🚀 بدء التشخيص الشامل...');

    await _testPermissions();
    await _testVoiceAssistant();
    await _testTextToSpeech();
    await _testVoiceRecording();

    _addResult('🎉 انتهى التشخيص الشامل!');
    setState(() => _isRunning = false);
  }

  Future<void> _testPermissions() async {
    _addResult('🔐 اختبار الأذونات...');

    try {
      final micStatus = await Permission.microphone.status;
      _addResult('🎤 إذن الميكروفون: $micStatus');

      if (micStatus != PermissionStatus.granted) {
        _addResult('⚠️ طلب إذن الميكروفون...');
        final newStatus = await Permission.microphone.request();
        _addResult('🎤 إذن الميكروفون الجديد: $newStatus');
      }
    } catch (e) {
      _addResult('❌ خطأ في اختبار الأذونات: $e');
    }
  }

  Future<void> _testSpeechToText() async {
    if (_isListening) return;

    _addResult('🎤 اختبار التعرف على الكلام...');

    try {
      final available = await _speechToText.initialize();
      _addResult('🎤 Speech-to-Text متاح: $available');

      if (available) {
        setState(() {
          _isListening = true;
          _recognizedText = '';
        });

        await _speechToText.listen(
          onResult: (result) {
            setState(() {
              _recognizedText = result.recognizedWords;
            });
            if (result.finalResult) {
              _addResult('🎤 تم التعرف على: ${result.recognizedWords}');
              _stopListening();
            }
          },
          localeId: 'ar-SA',
          listenFor: const Duration(seconds: 10),
        );
      }
    } catch (e) {
      _addResult('❌ خطأ في اختبار التعرف على الكلام: $e');
      setState(() => _isListening = false);
    }
  }

  void _stopListening() {
    if (_isListening) {
      _speechToText.stop();
      setState(() => _isListening = false);
      _addResult('⏹️ تم إيقاف الاستماع');
    }
  }

  Future<void> _testTextToSpeech() async {
    _addResult('🔊 اختبار تحويل النص إلى كلام...');

    try {
      await _flutterTts.setLanguage('ar-SA');
      await _flutterTts.setSpeechRate(0.8);
      await _flutterTts.setVolume(1.0);

      _addResult('🔊 إعدادات TTS تمت بنجاح');

      await _flutterTts.speak('مرحباً، هذا اختبار للصوت العربي');
      _addResult('🔊 تم تشغيل النص الصوتي');
    } catch (e) {
      _addResult('❌ خطأ في اختبار TTS: $e');
    }
  }

  Future<void> _testVoiceRecording() async {
    _addResult('🎙️ اختبار التسجيل الصوتي...');

    try {
      await _voiceService.initialize();
      _addResult('🎙️ تم تهيئة خدمة التسجيل');

      // اختبار قصير للتسجيل
      final micPermission = await Permission.microphone.isGranted;
      _addResult('🎙️ إذن التسجيل: $micPermission');
    } catch (e) {
      _addResult('❌ خطأ في اختبار التسجيل: $e');
    }
  }

  Future<void> _testVoiceAssistant() async {
    _addResult('🤖 اختبار المساعد الصوتي...');

    try {
      final success = await _initManager.initializeGlobalVoiceAssistant();
      _addResult('🤖 تهيئة المساعد الصوتي: $success');

      final status = _initManager.getInitializationStatus();
      _addResult('🤖 حالة المساعد: ${status['isGloballyInitialized']}');
      _addResult('🤖 عداد المراجع: ${status['referenceCount']}');
    } catch (e) {
      _addResult('❌ خطأ في اختبار المساعد الصوتي: $e');
    }
  }
}
