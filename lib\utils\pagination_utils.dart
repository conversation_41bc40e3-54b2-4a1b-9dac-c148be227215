import 'package:flutter/material.dart';

/// مساعد للتمرير اللانهائي
class PaginationUtils {
  /// حجم الصفحة الافتراضي
  static const int defaultPageSize = 20;

  /// إنشاء مراقب تمرير للتحميل اللانهائي
  static ScrollController createScrollController({
    required Function loadMoreCallback,
    double threshold = 0.8, // نسبة التمرير التي عندها يتم تحميل المزيد من البيانات
  }) {
    final ScrollController controller = ScrollController();
    
    controller.addListener(() {
      // التحقق مما إذا كان المستخدم قد وصل إلى نهاية القائمة تقريبًا
      if (controller.position.pixels >= 
          controller.position.maxScrollExtent * threshold) {
        // استدعاء دالة تحميل المزيد من البيانات
        loadMoreCallback();
      }
    });
    
    return controller;
  }
  
  /// تحديد ما إذا كان يجب عرض مؤشر التحميل في نهاية القائمة
  static bool shouldShowLoadingIndicator({
    required bool isLoading,
    required bool hasMoreData,
  }) {
    return isLoading && hasMoreData;
  }
  
  /// إنشاء عنصر تحميل للعرض في نهاية القائمة
  static Widget buildLoadingIndicator() {
    return const Padding(
      padding: EdgeInsets.all(16.0),
      child: Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
  
  /// إنشاء رسالة "لا توجد المزيد من البيانات" للعرض في نهاية القائمة
  static Widget buildNoMoreDataIndicator() {
    return const Padding(
      padding: EdgeInsets.all(16.0),
      child: Center(
        child: Text(
          'لا توجد المزيد من البيانات',
          style: TextStyle(
            color: Colors.grey,
            fontStyle: FontStyle.italic,
          ),
        ),
      ),
    );
  }
}
