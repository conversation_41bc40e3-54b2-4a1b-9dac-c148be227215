import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';

/// خدمة تشغيل الأصوات في التطبيق
class AudioService {
  static final AudioService _instance = AudioService._internal();
  factory AudioService() => _instance;
  AudioService._internal();

  // مشغل الصوت
  final AudioPlayer _audioPlayer = AudioPlayer();

  // حالة التهيئة
  bool _isInitialized = false;

  // مسارات الأصوات
  final Map<String, String> _soundPaths = {};

  // أسماء الأصوات
  static const String ringSound = 'ring';
  static const String connectSound = 'connect';
  static const String busySound = 'busy';
  static const String errorSound = 'error';
  static const String endCallSound = 'end_call';

  /// تهيئة خدمة الصوت
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // إنشاء مجلد الأصوات إذا لم يكن موجودًا
      final appDir = await getApplicationDocumentsDirectory();
      final soundsDir = Directory('${appDir.path}/sounds');
      if (!await soundsDir.exists()) {
        await soundsDir.create(recursive: true);
      }

      // نسخ الأصوات من الأصول إلى مجلد التطبيق
      await _copySoundAsset(
          'assets/sounds/ring.wav', '${soundsDir.path}/ring.wav');
      await _copySoundAsset(
          'assets/sounds/connect.wav', '${soundsDir.path}/connect.wav');
      await _copySoundAsset(
          'assets/sounds/busy.wav', '${soundsDir.path}/busy.wav');
      await _copySoundAsset(
          'assets/sounds/error.wav', '${soundsDir.path}/error.wav');
      await _copySoundAsset(
          'assets/sounds/end_call.wav', '${soundsDir.path}/end_call.wav');

      // تخزين مسارات الأصوات
      _soundPaths[ringSound] = '${soundsDir.path}/ring.wav';
      _soundPaths[connectSound] = '${soundsDir.path}/connect.wav';
      _soundPaths[busySound] = '${soundsDir.path}/busy.wav';
      _soundPaths[errorSound] = '${soundsDir.path}/error.wav';
      _soundPaths[endCallSound] = '${soundsDir.path}/end_call.wav';

      // تهيئة مشغل الصوت
      await _audioPlayer.setReleaseMode(ReleaseMode.loop); // وضع التكرار للرنين

      _isInitialized = true;
      debugPrint('تم تهيئة خدمة الصوت بنجاح');
    } catch (e) {
      debugPrint('خطأ في تهيئة خدمة الصوت: $e');
    }
  }

  /// نسخ ملف صوت من الأصول إلى مجلد التطبيق
  Future<void> _copySoundAsset(String assetPath, String localPath) async {
    try {
      // التحقق من وجود الملف المحلي
      final file = File(localPath);
      if (await file.exists()) {
        return; // الملف موجود بالفعل
      }

      // نسخ الملف من الأصول
      final data = await rootBundle.load(assetPath);
      final bytes = data.buffer.asUint8List();
      await file.writeAsBytes(bytes);

      debugPrint('تم نسخ ملف الصوت: $assetPath إلى $localPath');
    } catch (e) {
      // إذا لم يكن الملف موجودًا في الأصول، نستخدم ملف صوت افتراضي
      debugPrint('خطأ في نسخ ملف الصوت $assetPath: $e');

      // إنشاء ملف صوت فارغ
      final file = File(localPath);
      if (!await file.exists()) {
        await file.create(recursive: true);
      }
    }
  }

  /// تشغيل صوت الرنين
  Future<void> playRingtone() async {
    await _playSound(ringSound, loop: true);
  }

  /// تشغيل صوت الاتصال
  Future<void> playConnectSound() async {
    await _playSound(connectSound);
  }

  /// تشغيل صوت مشغول
  Future<void> playBusySound() async {
    await _playSound(busySound);
  }

  /// تشغيل صوت خطأ
  Future<void> playErrorSound() async {
    await _playSound(errorSound);
  }

  /// تشغيل صوت إنهاء المكالمة
  Future<void> playEndCallSound() async {
    await _playSound(endCallSound);
  }

  /// تشغيل صوت معين
  Future<void> _playSound(String soundName, {bool loop = false}) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      // إيقاف أي صوت قيد التشغيل
      await _audioPlayer.stop();

      // تعيين وضع التشغيل
      await _audioPlayer
          .setReleaseMode(loop ? ReleaseMode.loop : ReleaseMode.release);

      // التحقق من وجود الصوت
      final soundPath = _soundPaths[soundName];
      if (soundPath == null) {
        debugPrint('الصوت $soundName غير موجود');
        return;
      }

      // تشغيل الصوت
      await _audioPlayer.play(DeviceFileSource(soundPath));
      debugPrint('تم تشغيل الصوت: $soundName');
    } catch (e) {
      debugPrint('خطأ في تشغيل الصوت $soundName: $e');
    }
  }

  /// إيقاف تشغيل الصوت
  Future<void> stopSound() async {
    try {
      await _audioPlayer.stop();
      debugPrint('تم إيقاف تشغيل الصوت');
    } catch (e) {
      debugPrint('خطأ في إيقاف تشغيل الصوت: $e');
    }
  }

  /// التخلص من الموارد
  Future<void> dispose() async {
    try {
      await _audioPlayer.dispose();
      debugPrint('تم التخلص من موارد خدمة الصوت');
    } catch (e) {
      debugPrint('خطأ في التخلص من موارد خدمة الصوت: $e');
    }
  }
}
