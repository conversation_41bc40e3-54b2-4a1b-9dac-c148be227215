import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mobile_pos/model/user_log_model.dart';
import 'package:mobile_pos/services/user_log_service.dart';

/// أداة اختبار لتسجيل الأنشطة
class LogTestTool {
  /// تسجيل نشاط اختباري
  static Future<bool> logTestAction({
    required WidgetRef ref,
    required BuildContext context,
  }) async {
    try {
      final userLogService = UserLogService();

      // تسجيل نشاط اختباري للنظام
      final result = await userLogService.logUserAction(
        actionType: UserLogActionTypes.systemSettings,
        description: 'تم اختبار نظام تسجيل الأنشطة',
        data: {
          'testTime': DateTime.now().toIso8601String(),
          'testResult': 'نجاح',
        },
      );

      // تجنب استخدام ScaffoldMessenger لأنه قد يسبب مشاكل
      debugPrint('نتيجة تسجيل النشاط الاختباري: $result');

      return result;
    } catch (e) {
      debugPrint('خطأ في تسجيل النشاط الاختباري: $e');
      return false;
    }
  }

  /// تسجيل مجموعة من الأنشطة الاختبارية
  static Future<int> logMultipleTestActions({
    required WidgetRef ref,
    required BuildContext context,
    int count = 5,
  }) async {
    try {
      final userLogService = UserLogService();
      int successCount = 0;

      // قائمة بأنواع الأنشطة المختلفة للاختبار
      final actionTypes = [
        UserLogActionTypes.systemSettings,
        UserLogActionTypes.salesCreated,
        UserLogActionTypes.purchaseCreated,
        UserLogActionTypes.duePaymentReceived,
        UserLogActionTypes.customerCreated,
        UserLogActionTypes.supplierCreated,
        UserLogActionTypes.inventoryUpdated,
        UserLogActionTypes.expenseCreated,
      ];

      // قائمة بأوصاف الأنشطة المختلفة للاختبار
      final descriptions = [
        'تم تغيير إعدادات النظام',
        'تم إنشاء فاتورة مبيعات جديدة',
        'تم إنشاء فاتورة مشتريات جديدة',
        'تم تسجيل دفعة سداد مديونية',
        'تم إضافة عميل جديد',
        'تم إضافة مورد جديد',
        'تم تحديث المخزون',
        'تم تسجيل مصروف جديد',
      ];

      // تسجيل عدة أنشطة اختبارية
      for (int i = 0; i < count; i++) {
        // اختيار نوع نشاط عشوائي
        final actionTypeIndex = i % actionTypes.length;
        final actionType = actionTypes[actionTypeIndex];
        final description = '${descriptions[actionTypeIndex]} #$i';

        // بيانات إضافية حسب نوع النشاط
        Map<String, dynamic> data = {
          'testIndex': i,
          'testTime': DateTime.now().toIso8601String(),
        };

        // إضافة بيانات خاصة بنوع النشاط
        switch (actionType) {
          case UserLogActionTypes.salesCreated:
            data['invoiceNumber'] = 'INV-TEST-$i';
            data['customerName'] = 'عميل اختباري';
            data['totalAmount'] = 100.0 + (i * 10);
            break;
          case UserLogActionTypes.purchaseCreated:
            data['invoiceNumber'] = 'PUR-TEST-$i';
            data['supplierName'] = 'مورد اختباري';
            data['totalAmount'] = 200.0 + (i * 15);
            break;
          case UserLogActionTypes.duePaymentReceived:
            data['paymentId'] = 'PAY-TEST-$i';
            data['customerName'] = 'عميل اختباري';
            data['amount'] = 50.0 + (i * 5);
            data['remainingAmount'] = 100.0 - (i * 5);
            break;
          case UserLogActionTypes.expenseCreated:
            data['expenseId'] = 'EXP-TEST-$i';
            data['expenseFor'] = 'مصروف اختباري';
            data['amount'] = 30.0 + (i * 2);
            break;
        }

        // تسجيل النشاط
        final result = await userLogService.logUserAction(
          actionType: actionType,
          description: description,
          data: data,
          createNotification:
              actionType == UserLogActionTypes.duePaymentReceived,
        );

        if (result) {
          successCount++;
        }

        // انتظار لحظة قبل تسجيل النشاط التالي
        await Future.delayed(const Duration(milliseconds: 300));
      }

      // طباعة معلومات تشخيصية
      debugPrint('تم تسجيل $successCount من $count نشاط اختباري بنجاح');

      return successCount;
    } catch (e) {
      debugPrint('خطأ في تسجيل الأنشطة الاختبارية: $e');
      return 0;
    }
  }

  /// تسجيل سجلات اختبارية لتاريخ محدد
  static Future<int> logTestActionsForDate({
    required WidgetRef ref,
    required BuildContext context,
    required DateTime date,
    int count = 3,
  }) async {
    try {
      final userLogService = UserLogService();
      int successCount = 0;

      // تسجيل أنشطة اختبارية لتاريخ محدد
      for (int i = 0; i < count; i++) {
        // إنشاء وقت عشوائي في التاريخ المحدد
        final hour = 8 + (i % 12); // ساعات من 8 صباحًا إلى 8 مساءً
        final minute = (i * 7) % 60; // دقائق عشوائية
        final timestamp = DateTime(
          date.year,
          date.month,
          date.day,
          hour,
          minute,
        );

        // اختيار نوع نشاط
        final actionType = i % 2 == 0
            ? UserLogActionTypes.salesCreated
            : UserLogActionTypes.systemSettings;

        final description = i % 2 == 0
            ? 'تم إنشاء فاتورة مبيعات بتاريخ ${date.day}/${date.month}/${date.year}'
            : 'تم تغيير إعدادات النظام بتاريخ ${date.day}/${date.month}/${date.year}';

        // تسجيل النشاط مع التاريخ المحدد
        final result = await userLogService.logUserAction(
          actionType: actionType,
          description: description,
          data: {
            'testIndex': i,
            'testDate': date.toIso8601String(),
            'customTimestamp': timestamp.toIso8601String(),
          },
          // تمرير التاريخ المخصص (غير مدعوم حاليًا في الخدمة)
        );

        if (result) {
          successCount++;
        }

        // انتظار لحظة قبل تسجيل النشاط التالي
        await Future.delayed(const Duration(milliseconds: 200));
      }

      debugPrint(
          'تم تسجيل $successCount من $count نشاط اختباري لتاريخ ${date.toString()}');
      return successCount;
    } catch (e) {
      debugPrint('خطأ في تسجيل الأنشطة الاختبارية لتاريخ محدد: $e');
      return 0;
    }
  }
}
