import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/model/water_filter_models.dart';
import 'package:mobile_pos/services/water_filter_service.dart';
import 'package:mobile_pos/Screens/WaterFilters/Installments/add_installment_screen.dart';
import 'package:mobile_pos/Screens/WaterFilters/Installments/payment_screen.dart';
// import 'package:mobile_pos/Screens/WaterFilters/Installments/installment_details_screen.dart';
// import 'package:mobile_pos/Screens/WaterFilters/Installments/installments_reports_screen.dart';

class InstallmentsTrackingScreen extends StatefulWidget {
  const InstallmentsTrackingScreen({super.key});

  @override
  State<InstallmentsTrackingScreen> createState() =>
      _InstallmentsTrackingScreenState();
}

class _InstallmentsTrackingScreenState extends State<InstallmentsTrackingScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<WaterFilterInstallment> _allInstallments = [];
  List<WaterFilterInstallment> _pendingInstallments = [];
  List<WaterFilterInstallment> _overdueInstallments = [];
  List<WaterFilterInstallment> _paidInstallments = [];
  bool _isLoading = true;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadInstallments();
  }

  @override
  void dispose() {
    _tabController.dispose();
    WaterFilterService.cancelListenersByOwner('installments_tracking');
    super.dispose();
  }

  Future<void> _loadInstallments() async {
    setState(() => _isLoading = true);

    try {
      // استخدام getData بدلاً من listenToData مؤقتاً
      final data = await WaterFilterService.getData('Installments');
      if (mounted) {
        _processInstallmentsData(data);
      }
    } catch (e) {
      debugPrint('خطأ في تحميل الأقساط: $e');
      setState(() => _isLoading = false);
    }
  }

  void _processInstallmentsData(Map<String, dynamic> data) {
    final installments = <WaterFilterInstallment>[];

    data.forEach((key, value) {
      try {
        final installment = WaterFilterInstallment.fromJson(
          Map<String, dynamic>.from(value),
        );
        installments.add(installment);
      } catch (e) {
        debugPrint('خطأ في معالجة قسط: $e');
      }
    });

    // ترتيب الأقساط حسب تاريخ الاستحقاق
    installments.sort((a, b) => a.dueDate.compareTo(b.dueDate));

    // تصنيف الأقساط
    final pending = <WaterFilterInstallment>[];
    final overdue = <WaterFilterInstallment>[];
    final paid = <WaterFilterInstallment>[];

    for (final installment in installments) {
      switch (installment.status) {
        case InstallmentStatus.pending:
          if (installment.isOverdue) {
            overdue.add(installment);
          } else {
            pending.add(installment);
          }
          break;
        case InstallmentStatus.overdue:
          overdue.add(installment);
          break;
        case InstallmentStatus.paid:
          paid.add(installment);
          break;
        case InstallmentStatus.cancelled:
          // يمكن إضافة تبويب منفصل للملغية إذا لزم الأمر
          break;
      }
    }

    setState(() {
      _allInstallments = installments;
      _pendingInstallments = pending;
      _overdueInstallments = overdue;
      _paidInstallments = paid;
      _isLoading = false;
    });
  }

  List<WaterFilterInstallment> _getFilteredInstallments(
      List<WaterFilterInstallment> installments) {
    if (_searchQuery.isEmpty) return installments;

    return installments.where((installment) {
      return installment.systemId
              .toLowerCase()
              .contains(_searchQuery.toLowerCase()) ||
          installment.id.toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        title: Text(
          'متابعة الأقساط',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.analytics_outlined, color: Colors.white),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('شاشة التقارير قيد التطوير')),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.add, color: Colors.white),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const AddInstallmentScreen(),
                ),
              ).then((result) {
                if (result == true) {
                  _loadInstallments();
                }
              });
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          labelStyle: GoogleFonts.cairo(fontWeight: FontWeight.bold),
          tabs: [
            Tab(
              text: 'الكل (${_allInstallments.length})',
              icon: const Icon(Icons.list_alt),
            ),
            Tab(
              text: 'معلقة (${_pendingInstallments.length})',
              icon: const Icon(Icons.pending_actions),
            ),
            Tab(
              text: 'متأخرة (${_overdueInstallments.length})',
              icon: const Icon(Icons.warning_amber),
            ),
            Tab(
              text: 'مدفوعة (${_paidInstallments.length})',
              icon: const Icon(Icons.check_circle),
            ),
          ],
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        child: Column(
          children: [
            // شريط البحث
            Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: TextField(
                onChanged: (value) => setState(() => _searchQuery = value),
                decoration: InputDecoration(
                  hintText: 'البحث في الأقساط...',
                  hintStyle: GoogleFonts.cairo(color: Colors.grey.shade600),
                  prefixIcon: Icon(Icons.search, color: Colors.grey.shade600),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
                style: GoogleFonts.cairo(),
              ),
            ),

            // المحتوى
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : TabBarView(
                      controller: _tabController,
                      children: [
                        _buildInstallmentsList(
                            _getFilteredInstallments(_allInstallments)),
                        _buildInstallmentsList(
                            _getFilteredInstallments(_pendingInstallments)),
                        _buildInstallmentsList(
                            _getFilteredInstallments(_overdueInstallments)),
                        _buildInstallmentsList(
                            _getFilteredInstallments(_paidInstallments)),
                      ],
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInstallmentsList(List<WaterFilterInstallment> installments) {
    if (installments.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.payment_outlined,
              size: 80,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 20),
            Text(
              'لا توجد أقساط',
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 10),
            Text(
              'لم يتم العثور على أقساط في هذه الفئة',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.grey.shade500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: installments.length,
      itemBuilder: (context, index) {
        final installment = installments[index];
        return _buildInstallmentCard(installment);
      },
    );
  }

  Widget _buildInstallmentCard(WaterFilterInstallment installment) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: installment.status.color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: () {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تفاصيل القسط قيد التطوير')),
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // الصف الأول - رقم القسط والحالة
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'قسط رقم ${installment.installmentNumber}',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: kMainColor,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: installment.status.color.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: installment.status.color.withOpacity(0.3),
                      ),
                    ),
                    child: Text(
                      installment.status.arabicName,
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: installment.status.color,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // معلومات القسط
              Row(
                children: [
                  Expanded(
                    child: _buildInfoItem(
                      icon: Icons.attach_money,
                      label: 'المبلغ',
                      value: '${installment.amount.toStringAsFixed(2)} ج.م',
                      color: Colors.green,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildInfoItem(
                      icon: Icons.calendar_today,
                      label: 'تاريخ الاستحقاق',
                      value: _formatDate(installment.dueDate),
                      color: installment.isOverdue ? Colors.red : Colors.blue,
                    ),
                  ),
                ],
              ),

              if (installment.isOverdue) ...[
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.red.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.red.shade200),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.warning_amber,
                        color: Colors.red.shade600,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'متأخر ${installment.daysOverdue} يوم',
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.red.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              if (installment.status == InstallmentStatus.paid) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      Icons.check_circle,
                      color: Colors.green.shade600,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'تم الدفع في ${_formatDate(installment.paidDate!)}',
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: Colors.green.shade600,
                      ),
                    ),
                  ],
                ),
              ],

              // أزرار الإجراءات
              if (installment.status != InstallmentStatus.paid) ...[
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => PaymentScreen(
                                installment: installment,
                              ),
                            ),
                          ).then((result) {
                            if (result == true) {
                              _loadInstallments();
                            }
                          });
                        },
                        icon: const Icon(Icons.payment, size: 16),
                        label: Text(
                          'دفع',
                          style: GoogleFonts.cairo(fontSize: 12),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                                content: Text('تعديل القسط قيد التطوير')),
                          );
                        },
                        icon: const Icon(Icons.edit, size: 16),
                        label: Text(
                          'تعديل',
                          style: GoogleFonts.cairo(fontSize: 12),
                        ),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: kMainColor,
                          side: BorderSide(color: kMainColor),
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Row(
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(width: 6),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: GoogleFonts.cairo(
                  fontSize: 10,
                  color: Colors.grey.shade600,
                ),
              ),
              Text(
                value,
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
