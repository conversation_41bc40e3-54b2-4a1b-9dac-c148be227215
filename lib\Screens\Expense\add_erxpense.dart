// ignore_for_file: unused_result

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:mobile_pos/GlobalComponents/button_global.dart';
import 'package:mobile_pos/Screens/Expense/expense_category_list.dart';
import 'package:mobile_pos/generated/l10n.dart' as lang;
import 'package:mobile_pos/model/daily_transaction_model.dart';
import 'package:mobile_pos/model/user_log_model.dart';
import 'package:mobile_pos/services/user_log_service.dart';
// import 'package:mobile_pos/model/daily_transaction_model.dart';
import 'package:nb_utils/nb_utils.dart';

import '../../Provider/all_expanse_provider.dart';
import '../../const_commas.dart';
import '../../constant.dart';
// import '../../model/DailyTransactionModel.dart';
import '../../model/expense_model.dart';
import 'widgets/enhanced_add_expense_form.dart';

// ignore: must_be_immutable
class AddExpense extends StatefulWidget {
  const AddExpense({
    super.key,
  });

  @override
  // ignore: library_private_types_in_public_api
  _AddExpenseState createState() => _AddExpenseState();
}

class _AddExpenseState extends State<AddExpense> {
  @override
  Widget build(BuildContext context) {
    return Consumer(builder: (context, ref, __) {
      return Scaffold(
        backgroundColor: kMainColor,
        appBar: AppBar(
          backgroundColor: kMainColor,
          title: Text(
            lang.S.of(context).addExpense,
            style: GoogleFonts.cairo(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.w600,
            ),
          ),
          centerTitle: true,
          iconTheme: const IconThemeData(color: Colors.white),
          elevation: 0.0,
        ),
        body: Container(
          alignment: Alignment.topCenter,
          decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                  topRight: Radius.circular(30), topLeft: Radius.circular(30))),
          child: SingleChildScrollView(
            child: SizedBox(
              width: context.width(),
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // العنوان
                    Text(
                      'إضافة مصروف جديد',
                      style: GoogleFonts.cairo(
                        fontSize: 24,
                        fontWeight: FontWeight.w700,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'املأ البيانات التالية لإضافة مصروف جديد',
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    const SizedBox(height: 30),

                    // النموذج المحسن
                    EnhancedAddExpenseForm(
                      onSubmit: _handleFormSubmit,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      );
    });
  }

  // دالة معالجة إرسال النموذج
  void _handleFormSubmit(Map<String, dynamic> data) async {
    try {
      EasyLoading.show(status: 'جارٍ الحفظ...', dismissOnTap: false);

      // إنشاء نموذج المصروف
      ExpenseModel expense = ExpenseModel(
        expenseDate: data['expenseDate'],
        category: data['category'],
        account: data['account'],
        amount: data['amount'],
        expanseFor: data['expanseFor'],
        paymentType: data['paymentType'],
        referenceNo: data['referenceNo'],
        note: data['note'],
      );

      // حفظ في Firebase
      final DatabaseReference expanseRef =
          FirebaseDatabase.instance.ref().child(constUserId).child('Expense');
      expanseRef.keepSynced(true);
      await expanseRef.push().set(expense.toJson());

      // إنشاء معاملة يومية
      DailyTransactionModel dailyTransaction = DailyTransactionModel(
        name: expense.expanseFor,
        date: expense.expenseDate,
        type: 'Expense',
        total: expense.amount.toDouble(),
        paymentIn: 0,
        paymentOut: expense.amount.toDouble(),
        remainingBalance: expense.amount.toDouble(),
        id: expense.expenseDate,
        expenseModel: expense,
      );
      postDailyTransaction(dailyTransactionModel: dailyTransaction);

      // تسجيل نشاط المستخدم
      try {
        final userLogService = UserLogService();
        await userLogService.logUserAction(
          actionType: UserLogActionTypes.expenseCreated,
          description: 'تم إنشاء مصروف جديد: ${expense.expanseFor}',
          data: {
            'expenseId': expense.referenceNo,
            'expenseFor': expense.expanseFor,
            'category': expense.category,
            'amount': double.parse(expense.amount),
            'paymentType': expense.paymentType,
            'date': expense.expenseDate,
          },
          createNotification: true,
        );
      } catch (e) {
        debugPrint('خطأ في تسجيل نشاط المستخدم: $e');
      }

      // تحديث البيانات
      if (mounted) {
        final container = ProviderScope.containerOf(context);
        container.invalidate(expenseProvider);
      }

      EasyLoading.showSuccess('تم حفظ المصروف بنجاح');

      if (mounted) {
        Navigator.pop(context);
      }
    } catch (e) {
      EasyLoading.dismiss();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'حدث خطأ أثناء حفظ المصروف: ${e.toString()}',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
