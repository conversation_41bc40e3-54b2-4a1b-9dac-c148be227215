import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mobile_pos/constant.dart';
import 'package:mobile_pos/model/water_filter_models.dart';
import 'package:mobile_pos/services/water_filter_service.dart';

class CustomersReportScreen extends StatefulWidget {
  const CustomersReportScreen({super.key});

  @override
  State<CustomersReportScreen> createState() => _CustomersReportScreenState();
}

class _CustomersReportScreenState extends State<CustomersReportScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  List<WaterFilterCustomer> _allCustomers = [];
  List<WaterFilterCustomer> _filteredCustomers = [];

  // إحصائيات العملاء
  Map<String, dynamic> _stats = {};

  // فلاتر البحث
  String _searchQuery = '';
  String _selectedCity = 'الكل';
  String _selectedArea = 'الكل';

  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadCustomersData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadCustomersData() async {
    setState(() => _isLoading = true);

    try {
      final data = await WaterFilterService.getData('Customers');
      final customers = <WaterFilterCustomer>[];

      data.forEach((key, value) {
        try {
          final customer = WaterFilterCustomer.fromJson(
            Map<String, dynamic>.from(value),
          );
          customers.add(customer);
        } catch (e) {
          debugPrint('خطأ في معالجة عميل: $e');
        }
      });

      setState(() {
        _allCustomers = customers;
        _filteredCustomers = customers;
        _calculateStats();
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('خطأ في تحميل العملاء: $e');
      setState(() => _isLoading = false);
    }
  }

  void _calculateStats() {
    final totalCustomers = _allCustomers.length;

    // إحصائيات حسب المدينة
    final cities = <String, int>{};
    for (final customer in _allCustomers) {
      cities[customer.city] = (cities[customer.city] ?? 0) + 1;
    }

    // إحصائيات حسب المنطقة
    final areas = <String, int>{};
    for (final customer in _allCustomers) {
      areas[customer.area] = (areas[customer.area] ?? 0) + 1;
    }

    // العملاء الجدد (آخر 30 يوم)
    final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
    final newCustomers = _allCustomers
        .where(
            (c) => c.createdAt != null && c.createdAt!.isAfter(thirtyDaysAgo))
        .length;

    // العملاء الذين لديهم موبايل
    final customersWithPhone =
        _allCustomers.where((c) => c.phone.isNotEmpty).length;

    setState(() {
      _stats = {
        'total': totalCustomers,
        'withPhone': customersWithPhone,
        'withoutPhone': totalCustomers - customersWithPhone,
        'cities': cities,
        'areas': areas,
        'newCustomers': newCustomers,
      };
    });
  }

  void _applyFilters() {
    setState(() {
      _filteredCustomers = _allCustomers.where((customer) {
        // فلتر البحث
        if (_searchQuery.isNotEmpty) {
          final query = _searchQuery.toLowerCase();
          if (!customer.name.toLowerCase().contains(query) &&
              !customer.phone.toLowerCase().contains(query) &&
              !customer.address.toLowerCase().contains(query)) {
            return false;
          }
        }

        // فلتر المدينة
        if (_selectedCity != 'الكل' && customer.city != _selectedCity) {
          return false;
        }

        // فلتر المنطقة
        if (_selectedArea != 'الكل' && customer.area != _selectedArea) {
          return false;
        }

        return true;
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kMainColor,
      appBar: AppBar(
        backgroundColor: kMainColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        title: Text(
          'تقرير العملاء',
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: _loadCustomersData,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          labelStyle: GoogleFonts.cairo(fontWeight: FontWeight.bold),
          tabs: const [
            Tab(text: 'الإحصائيات', icon: Icon(Icons.analytics)),
            Tab(text: 'قائمة العملاء', icon: Icon(Icons.list)),
            Tab(text: 'التوزيع الجغرافي', icon: Icon(Icons.map)),
            Tab(text: 'التحليل', icon: Icon(Icons.insights)),
          ],
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : TabBarView(
                controller: _tabController,
                children: [
                  _buildStatsTab(),
                  _buildCustomersListTab(),
                  _buildGeographicTab(),
                  _buildAnalysisTab(),
                ],
              ),
      ),
    );
  }

  Widget _buildStatsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('إحصائيات العملاء'),
          const SizedBox(height: 16),
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.2,
            children: [
              _buildStatCard(
                title: 'إجمالي العملاء',
                value: '${_stats['total'] ?? 0}',
                icon: Icons.people,
                color: Colors.blue,
              ),
              _buildStatCard(
                title: 'لديهم موبايل',
                value: '${_stats['withPhone'] ?? 0}',
                icon: Icons.phone,
                color: Colors.green,
              ),
              _buildStatCard(
                title: 'بدون موبايل',
                value: '${_stats['withoutPhone'] ?? 0}',
                icon: Icons.phone_disabled,
                color: Colors.red,
              ),
              _buildStatCard(
                title: 'عملاء جدد',
                value: '${_stats['newCustomers'] ?? 0}',
                icon: Icons.person_add,
                color: Colors.purple,
              ),
            ],
          ),
          const SizedBox(height: 24),
          _buildSectionTitle('توزيع العملاء حسب البيانات'),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildTypeCard(
                  title: 'لديهم موبايل',
                  value: '${_stats['withPhone'] ?? 0}',
                  total: '${_stats['total'] ?? 0}',
                  color: Colors.teal,
                  icon: Icons.phone,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildTypeCard(
                  title: 'بدون موبايل',
                  value: '${_stats['withoutPhone'] ?? 0}',
                  total: '${_stats['total'] ?? 0}',
                  color: Colors.orange,
                  icon: Icons.phone_disabled,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCustomersListTab() {
    return Column(
      children: [
        // شريط البحث والفلاتر
        Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // شريط البحث
              TextField(
                decoration: InputDecoration(
                  hintText: 'البحث بالاسم، الموبايل، أو العنوان...',
                  hintStyle: GoogleFonts.cairo(),
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                onChanged: (value) {
                  setState(() {
                    _searchQuery = value;
                    _applyFilters();
                  });
                },
              ),

              const SizedBox(height: 12),

              // فلاتر سريعة
              Row(
                children: [
                  Expanded(
                    child: DropdownButtonFormField<String>(
                      value: _selectedCity,
                      decoration: InputDecoration(
                        labelText: 'المدينة',
                        labelStyle: GoogleFonts.cairo(),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      items: [
                        'الكل',
                        ...(_stats['cities'] as Map<String, int>? ?? {}).keys
                      ]
                          .map((city) => DropdownMenuItem(
                                value: city,
                                child: Text(city, style: GoogleFonts.cairo()),
                              ))
                          .toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedCity = value ?? 'الكل';
                          _applyFilters();
                        });
                      },
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: DropdownButtonFormField<String>(
                      value: _selectedArea,
                      decoration: InputDecoration(
                        labelText: 'المنطقة',
                        labelStyle: GoogleFonts.cairo(),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      items: [
                        'الكل',
                        ...(_stats['areas'] as Map<String, int>? ?? {}).keys
                      ]
                          .map((area) => DropdownMenuItem(
                                value: area,
                                child: Text(area, style: GoogleFonts.cairo()),
                              ))
                          .toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedArea = value ?? 'الكل';
                          _applyFilters();
                        });
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        // عداد النتائج
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              Text(
                'عرض ${_filteredCustomers.length} من ${_allCustomers.length} عميل',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 8),

        // قائمة العملاء
        Expanded(
          child: _filteredCustomers.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.search_off,
                        size: 80,
                        color: Colors.grey.shade400,
                      ),
                      const SizedBox(height: 20),
                      Text(
                        'لا توجد عملاء تطابق البحث',
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: _filteredCustomers.length,
                  itemBuilder: (context, index) {
                    final customer = _filteredCustomers[index];
                    return _buildCustomerCard(customer);
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildGeographicTab() {
    final cities = _stats['cities'] as Map<String, int>? ?? {};
    final sortedCities = cities.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('التوزيع الجغرافي للعملاء'),
          const SizedBox(height: 16),
          if (sortedCities.isEmpty)
            Center(
              child: Text(
                'لا توجد بيانات جغرافية',
                style: GoogleFonts.cairo(
                  fontSize: 16,
                  color: Colors.grey.shade600,
                ),
              ),
            )
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: sortedCities.length,
              itemBuilder: (context, index) {
                final cityEntry = sortedCities[index];
                final percentage =
                    (cityEntry.value / _allCustomers.length * 100);

                return Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.shade200,
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            cityEntry.key,
                            style: GoogleFonts.cairo(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            '${cityEntry.value} عميل',
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      LinearProgressIndicator(
                        value: percentage / 100,
                        backgroundColor: Colors.grey.shade200,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          _getColorForIndex(index),
                        ),
                        minHeight: 8,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${percentage.toStringAsFixed(1)}%',
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
        ],
      ),
    );
  }

  Widget _buildAnalysisTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('تحليل العملاء'),
          const SizedBox(height: 16),

          // معدل النمو
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.shade200,
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'العملاء الجدد (آخر 30 يوم)',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Icon(
                      Icons.trending_up,
                      color: Colors.green,
                      size: 32,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      '${_stats['newCustomers'] ?? 0} عميل جديد',
                      style: GoogleFonts.cairo(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // معدل الإيميلات
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.shade200,
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'معدل العملاء الذين لديهم موبايل',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '${((_stats['withPhone'] ?? 0) / (_stats['total'] ?? 1) * 100).toStringAsFixed(1)}%',
                      style: GoogleFonts.cairo(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
                    Icon(
                      Icons.phone,
                      color: Colors.blue,
                      size: 32,
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: (_stats['withPhone'] ?? 0) / (_stats['total'] ?? 1),
                  backgroundColor: Colors.grey.shade200,
                  valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
                  minHeight: 8,
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // توزيع الأنواع
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.shade200,
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'توزيع العملاء حسب البيانات',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        children: [
                          Icon(
                            Icons.phone,
                            color: Colors.teal,
                            size: 32,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'لديهم موبايل',
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            '${_stats['withPhone'] ?? 0}',
                            style: GoogleFonts.cairo(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.teal,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      width: 1,
                      height: 60,
                      color: Colors.grey.shade300,
                    ),
                    Expanded(
                      child: Column(
                        children: [
                          Icon(
                            Icons.phone_disabled,
                            color: Colors.orange,
                            size: 32,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'بدون موبايل',
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            '${_stats['withoutPhone'] ?? 0}',
                            style: GoogleFonts.cairo(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.orange,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Helper Methods
  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: GoogleFonts.cairo(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: kMainColor,
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 12),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTypeCard({
    required String title,
    required String value,
    required String total,
    required Color color,
    required IconData icon,
  }) {
    final percentage = int.tryParse(total) != null && int.parse(total) > 0
        ? (int.parse(value) / int.parse(total) * 100)
        : 0.0;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(width: 8),
              Text(
                title,
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            '$value من $total',
            style: GoogleFonts.cairo(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: percentage / 100,
            backgroundColor: Colors.grey.shade200,
            valueColor: AlwaysStoppedAnimation<Color>(color),
            minHeight: 6,
          ),
          const SizedBox(height: 4),
          Text(
            '${percentage.toStringAsFixed(1)}%',
            style: GoogleFonts.cairo(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCustomerCard(WaterFilterCustomer customer) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: customer.phone.isNotEmpty
              ? Colors.green.withOpacity(0.3)
              : Colors.orange.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: customer.phone.isNotEmpty
                            ? Colors.teal.withOpacity(0.1)
                            : Colors.orange.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        customer.phone.isNotEmpty
                            ? Icons.phone
                            : Icons.phone_disabled,
                        color: customer.phone.isNotEmpty
                            ? Colors.teal
                            : Colors.orange,
                        size: 16,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      customer.name,
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: customer.phone.isNotEmpty
                        ? Colors.green.withOpacity(0.1)
                        : Colors.orange.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    customer.phone.isNotEmpty ? 'لديه موبايل' : 'بدون موبايل',
                    style: GoogleFonts.cairo(
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      color: customer.phone.isNotEmpty
                          ? Colors.green
                          : Colors.orange,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.phone, size: 14, color: Colors.grey.shade600),
                const SizedBox(width: 4),
                Text(
                  customer.phone,
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
                const SizedBox(width: 16),
                Icon(Icons.location_on, size: 14, color: Colors.grey.shade600),
                const SizedBox(width: 4),
                Text(
                  customer.city,
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
            if (customer.area.isNotEmpty) ...[
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(Icons.location_city,
                      size: 14, color: Colors.grey.shade600),
                  const SizedBox(width: 4),
                  Text(
                    customer.area,
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Color _getColorForIndex(int index) {
    final colors = [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.teal,
      Colors.red,
      Colors.indigo,
      Colors.pink,
    ];
    return colors[index % colors.length];
  }
}
