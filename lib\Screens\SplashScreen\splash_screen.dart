// ignore_for_file: use_build_context_synchronously

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:mobile_pos/Screens/SplashScreen/on_board.dart';
import 'package:mobile_pos/constant.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import '../../Language/language_provider.dart';
import '../../currency.dart';
import '../Home/home.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  // ignore: library_private_types_in_public_api
  _SplashScreenState createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  String newUpdateVersion = '6.3';
  var currentUser = FirebaseAuth.instance.currentUser;
  bool isUpdateAvailable = false;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey();

  void showSnack(String text) {
    if (_scaffoldKey.currentContext != null) {
      ScaffoldMessenger.of(
        _scaffoldKey.currentContext!,
      ).showSnackBar(SnackBar(content: Text(text)));
    }
  }

  void getPermission() async {
    await [
      Permission.bluetoothScan,
      Permission.bluetoothConnect,
      Permission.notification,
      Permission.storage,
    ].request();
  }

  Future<void> updateNotifier() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      isRtl = prefs.getBool('isRtl') ?? false;

      // تقليل وقت الانتظار من 3 ثواني إلى 2 ثانية
      await Future.delayed(const Duration(seconds: 2));

      // التأكد من أن الشاشة لا تزال مثبتة
      if (!mounted) return;

      // تهيئة الخدمات المتبقية في الخلفية قبل الانتقال إلى الشاشة الرئيسية
      // لا ننتظر اكتمال التهيئة لتسريع الانتقال
      // ServiceInitializer.initializeRemainingServices();

      isPrintEnable = prefs.getBool('isPrintEnable') ?? false;

      if (currentUser != null) {
        const Home().launch(context, isNewTask: true);
      } else {
        const OnBoard().launch(context, isNewTask: true);
      }
    } catch (e) {
      debugPrint('خطأ في تحديث الإشعارات: $e');
      // في حالة حدوث خطأ، ننتقل إلى الشاشة الرئيسية أو شاشة التعريف
      if (mounted) {
        if (currentUser != null) {
          const Home().launch(context, isNewTask: true);
        } else {
          const OnBoard().launch(context, isNewTask: true);
        }
      }
    }
  }

  final CurrentUserData currentUserData = CurrentUserData();

  @override
  void initState() {
    super.initState();
    checkUser();
    getPermission();
    setLanguage();

    // getCurrency();
    currentUserData.getUserData();
    // تحميل بيانات المستخدم من التخزين المحلي
    getUserDataFromLocal();
  }

  Future<void> checkUser() async {
    try {
      // التحقق من الاتصال بالإنترنت
      bool hasInternet = await InternetConnectionChecker().hasConnection;

      // طباعة حالة الاتصال للتشخيص
      debugPrint(
        'حالة الاتصال بالإنترنت: ${hasInternet ? 'متصل' : 'غير متصل'}',
      );

      // في كلتا الحالتين، نقوم بتحديث الإشعارات
      // لكن يمكننا استخدام حالة الاتصال لاحقًا إذا احتجنا إلى ذلك
      updateNotifier();
    } catch (e) {
      debugPrint('خطأ في التحقق من المستخدم: $e');
      // في حالة حدوث خطأ، نستمر في تحديث الإشعارات
      updateNotifier();
    }
  }

  void setLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    String selectedLanguage = prefs.getString('savedLanguage') ?? 'Arabic';
    // دايماً نبدأ بالعربي كلغة افتراضية
    context.read<LanguageChangeProvider>().changeLocale("ar");
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Directionality(
        textDirection: isRtl ? TextDirection.rtl : TextDirection.ltr,
        child: Scaffold(
          backgroundColor: kMainColor,
          body: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const Spacer(),
              Container(
                height: 220,
                width: 220,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  image: DecorationImage(
                    image: AssetImage(splashScreenLogo),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              const Spacer(),
              Column(
                children: [
                  Center(
                    child: Text(
                      'Powered By $appName',
                      style: GoogleFonts.poppins(
                        color: Colors.white,
                        fontWeight: FontWeight.normal,
                        fontSize: 17,
                      ),
                    ),
                  ),
                  Center(
                    child: Text(
                      'V $appVersion',
                      style: GoogleFonts.poppins(
                        color: Colors.white,
                        fontWeight: FontWeight.normal,
                      ),
                    ),
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
