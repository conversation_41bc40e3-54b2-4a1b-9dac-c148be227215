import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

/// خدمة إعادة تشغيل التطبيق وإظهاره
class AppLauncherService {
  static final AppLauncherService _instance = AppLauncherService._internal();
  factory AppLauncherService() => _instance;
  AppLauncherService._internal();

  static const MethodChannel _channel = MethodChannel('app_launcher');

  /// إعادة التطبيق للمقدمة مع إظهار شاشة معينة
  Future<bool> bringAppToForegroundWithScreen(String screenRoute) async {
    try {
      debugPrint('🚀 محاولة إعادة التطبيق للمقدمة مع الشاشة: $screenRoute');

      // محاولة إعادة التطبيق للمقدمة
      final result = await _channel.invokeMethod('bringToForeground', {
        'screenRoute': screenRoute,
      });

      if (result == true) {
        debugPrint('✅ تم إعادة التطبيق للمقدمة بنجاح');
        return true;
      } else {
        debugPrint('❌ فشل في إعادة التطبيق للمقدمة');
        return false;
      }
    } catch (e) {
      debugPrint('❌ خطأ في إعادة التطبيق للمقدمة: $e');
      return false;
    }
  }

  /// إرسال إشعار للمستخدم
  Future<bool> sendNotification({
    required String title,
    required String message,
    String? screenRoute,
  }) async {
    try {
      debugPrint('📢 إرسال إشعار: $title');

      final result = await _channel.invokeMethod('sendNotification', {
        'title': title,
        'message': message,
        'screenRoute': screenRoute,
      });

      if (result == true) {
        debugPrint('✅ تم إرسال الإشعار بنجاح');
        return true;
      } else {
        debugPrint('❌ فشل في إرسال الإشعار');
        return false;
      }
    } catch (e) {
      debugPrint('❌ خطأ في إرسال الإشعار: $e');
      return false;
    }
  }

  /// فتح التطبيق مع شاشة محددة
  Future<bool> openAppWithScreen(String screenRoute) async {
    try {
      debugPrint('📱 فتح التطبيق مع الشاشة: $screenRoute');

      // أولاً: محاولة إعادة التطبيق للمقدمة
      bool success = await bringAppToForegroundWithScreen(screenRoute);

      if (!success) {
        // ثانياً: إرسال إشعار للمستخدم
        success = await sendNotification(
          title: 'المساعد الصوتي',
          message: 'اضغط لفتح المساعد التفاعلي',
          screenRoute: screenRoute,
        );
      }

      return success;
    } catch (e) {
      debugPrint('❌ خطأ في فتح التطبيق: $e');
      return false;
    }
  }

  /// التحقق من حالة التطبيق
  Future<bool> isAppInForeground() async {
    try {
      final result = await _channel.invokeMethod('isAppInForeground');
      return result == true;
    } catch (e) {
      debugPrint('❌ خطأ في فحص حالة التطبيق: $e');
      return false;
    }
  }

  /// إعداد معالج الإشعارات
  void setupNotificationHandler() {
    _channel.setMethodCallHandler((call) async {
      switch (call.method) {
        case 'onNotificationTapped':
          final screenRoute = call.arguments['screenRoute'] as String?;
          if (screenRoute != null) {
            debugPrint('📱 تم النقر على الإشعار - فتح الشاشة: $screenRoute');
            // يمكن إضافة منطق التنقل هنا
          }
          break;
        default:
          debugPrint('⚠️ استدعاء غير معروف: ${call.method}');
      }
    });
  }
}
